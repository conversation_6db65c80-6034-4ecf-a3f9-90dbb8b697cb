package com.zyhl.yun.member.mcdmc.activation.dto;

import com.zyhl.yun.member.mcdmc.activation.req.SyncOrderReq;
import lombok.Data;

import java.io.Serializable;

@Data
public class InstDataSyncEventResponse implements Serializable {
    /**
     * 请求入参
     * */
    private SyncOrderReq data;
    private boolean success=true;
    /**
     * 可以直接返回操作结果
     * */
    private String resultCode;
    /**
     * 可以直接返回操作结果
     * */
    private String message;

}
