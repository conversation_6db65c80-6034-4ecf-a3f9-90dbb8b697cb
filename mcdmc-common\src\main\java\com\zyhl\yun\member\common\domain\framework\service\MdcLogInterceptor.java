package com.zyhl.yun.member.common.domain.framework.service;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <日志拦截器>
 *
 * <AUTHOR>
 * @version 2020/6/16 0016
 * @see [相关类/方法]
 * @since [m4c-java]
 */
@Slf4j
@Component
public class MdcLogInterceptor implements HandlerInterceptor, Ordered {
    public static final String CUSTOM_TRACE_ID = "x-yun-tid";
    public static final String TRACE_ID = "trace_id";
    @Value("${spring.application.name}")
    private String applicationName;

    public static String getCurrentTraceId() {

        String curTraceId = MDC.get(LogConstants.TRACE_ID);
        if (StringUtils.hasLength(curTraceId)) {
            if (curTraceId.contains("_")) {
                return curTraceId.split("_")[0];
            }
        }
        return curTraceId;
    }

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, HttpServletResponse response,
                             @NonNull Object handler) {
        //防止重复触发拦截器
        if (CharSequenceUtil.isNotEmpty(request.getHeader(CUSTOM_TRACE_ID)) || CharSequenceUtil.isNotEmpty((String) request.getAttribute(CUSTOM_TRACE_ID))) {
            return true;
        }
        String traceId = MDC.get(LogConstants.TRACE_ID);
        if (CharSequenceUtil.isEmpty(traceId)) {
            traceId = IdUtil.getSnowflake(1, 1).nextIdStr();
        }
        String customTraceId = traceId + "_" + applicationName;
        response.addHeader(CUSTOM_TRACE_ID, ObjectUtil.defaultIfNull(customTraceId, customTraceId));
        MDC.put(LogConstants.TRACE_ID, ObjectUtil.defaultIfNull(traceId, traceId));
        return true;
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
                                @NonNull Object handler, Exception ex) {
        // 清除MDC内容，避免线程复用
        MDC.clear();
    }

    @Override
    public int getOrder() {
        return -4;
    }
}
