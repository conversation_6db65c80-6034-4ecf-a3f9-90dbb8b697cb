package com.zyhl.yun.member.mcdmc.activation.callback.notify.base;


import java.util.HashMap;
import java.util.Map;

public class CallbackRoute {

    private CallbackRoute() {
    }


    /**
     * url->impls
     */
    protected static final Map<String, String> CALLBACK_URLS_SERVICE_REL = new HashMap<>();

    static {
        // TODO-2024/7/8: 修改成动态配置
        // 一级能开免流订购回调
        CALLBACK_URLS_SERVICE_REL.put("/cloudSEE/openApi/salesorderResult", "openFreeFlowSyncCallback");
        // 畅影权益订购回调
        CALLBACK_URLS_SERVICE_REL.put("/cloudSEE/openApi/notifyMovieEquityRightsResult", "raiMovieSubCallback");
        // 权益中心订购回调
        CALLBACK_URLS_SERVICE_REL.put("/cloudSEE/openApi/raiNotifyEquityResult", "raiNormalSubCallback");
        //权益中心退订、续订回调
        CALLBACK_URLS_SERVICE_REL.put("/cloudSEE/openApi/raiPayResultNotify", "raiPayResultCallback");
        // 一级能开退订回调
        CALLBACK_URLS_SERVICE_REL.put("/cloudSEE/openApi/flowCardOrderResult", "openReturnCallback");
        // 权益退费回调
        CALLBACK_URLS_SERVICE_REL.put("/cloudSEE/openApi/notifyRightsSalesReturnResult", "raiRefundComCallback");
        // 流量包订购回调
        CALLBACK_URLS_SERVICE_REL.put("/cloudSEE/openApi/subscribeFlowSpecNotify", "subscribeFlowSpecCallback");
    }

    public static String getNotifyNameByUrl(String url) {
        return CALLBACK_URLS_SERVICE_REL.get(url);
    }
}
