package com.zyhl.yun.member.common.domain.framework.constants;

/**
 * @Description: 领域常量
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/4/25
 */
public interface DomainConstant {
    /**
     * 订单领域
     */
    String ORDER_DOMAIN = "member-order";
    /**
     * 用户领域
     */
    String USER_DOMAIN = "member-vip";
    /**
     * 商品领域
     */
    String MEMBER_PRODUCT = "member-product";

    String ORDER_DETAIL_DOMAIN = "member-order";

    String PAYMENT_DETAIL_DOMAIN = "member-payment";

}
