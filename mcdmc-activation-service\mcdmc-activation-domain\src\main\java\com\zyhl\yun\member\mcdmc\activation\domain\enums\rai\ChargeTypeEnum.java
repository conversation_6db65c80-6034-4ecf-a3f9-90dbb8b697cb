package com.zyhl.yun.member.mcdmc.activation.domain.enums.rai;

import cn.hutool.core.util.NumberUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 权益计费类型
 *
 * <AUTHOR>
 * @since 2024/07/06 17:50
 */
@Getter
@AllArgsConstructor
public enum ChargeTypeEnum {
    /**
     * 按次
     */
    ONCE(0, "按次/点播"),
    /**
     * 包月
     */
    MONTHLY(1, "包月");
    private final Integer code;
    private final String desc;

    public boolean codeEquals(Integer code) {
        return this.code.equals(code);
    }

    public boolean codeEquals(String code) {
        return NumberUtil.isNumber(code) && this.code.equals(Integer.parseInt(code));
    }
}
