package com.zyhl.yun.member.mcdmc.activation.domains.conditions;

import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 仅通过工单属性相关信息来查询工单属性
 *
 * <AUTHOR>
 * @since 2025/01/06 21:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class QueryWorkAttrCondition extends BaseCondition {

    /**
     * 用户id
     */
    private String userId;
    /**
     * 工单id列表
     */
    private List<String> workIdList;

    /**
     * 属性key列表
     */
    private List<String> attrKeyList;
}
