package com.zyhl.yun.member.mcdmc.activation.callback.rsp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 退订回调接口统一响应体
 *
 * <AUTHOR>
 * @since 2024/06/22 11:05
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReturnComNotifyRsp {
    @JsonProperty("contractRoot")
    private ContractRoot contractRoot;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContractRoot {
        /**
         * 会话控制内容
         */
        @JsonProperty("head")
        private Head head;
        /**
         * 业务内容
         */
        @JsonProperty("body")
        private Body body;

        @Override
        public String toString() {
            return "ContractRoot{" +
                    "head=" + head +
                    ", body=" + body +
                    '}';
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Head {
        @JsonProperty("reqTime")
        private String reqTime;
        @JsonProperty("sign")
        private String sign;
        @JsonProperty("transactionId")
        private String transactionId;


        @Override
        public String toString() {
            return "HeadDTO{" +
                    "reqTime='" + reqTime + '\'' +
                    ", sign='" + sign + '\'' +
                    ", transactionId='" + transactionId + '\'' +
                    '}';
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Body {
        /**
         * 处理结果状态码(错误代码)
         */
        @JsonProperty("resultCode")
        private String resultCode;

        /**
         * 处理结果的信息
         */
        @JsonProperty("resultMsg")
        private String resultMsg;

        @Override
        public String toString() {
            return "BodyDTO{" +
                    "resultCode='" + resultCode + '\'' +
                    ", resultMsg='" + resultMsg + '\'' +
                    '}';
        }
    }


    @Override
    public String toString() {
        return "ParkingTicketCallbackResp{" +
                "contractRoot=" + contractRoot +
                '}';
    }
}
