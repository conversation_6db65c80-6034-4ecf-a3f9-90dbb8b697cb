package com.zyhl.yun.member.common.cache;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2024/11/20 10:29
 */
public interface EntityCache {


    /**
     * 根据key获取单个实体
     *
     * @param key
     * @param clazz
     * @param dataGetter
     * @param <T>
     * @return
     */
    <T extends EntityCacheable> T getByKey(Serializable key, Class<T> clazz, Function<Serializable, T> dataGetter);


    /**
     * 根据key获取实体列表
     *
     * @param key
     * @param clazz
     * @param dataGetter
     * @param <T>
     * @return
     */
    <T extends EntityCacheable> List<T> getListByKey(String key, Class<T> clazz, Function<String, List<T>> dataGetter);


    /**
     * 根据key列表获取实体列表
     *
     * @param keyList
     * @param clazz
     * @param dataByKeyGetter
     * @param <T>
     * @return
     */
    <T extends EntityCacheable> List<T> getByKeyList(List<Serializable> keyList,
                                                     Class<T> clazz,
                                                     Function<List<? extends Serializable>, List<T>> dataByKeyGetter);

    /**
     * 根据条件获取实体列表
     *
     * @param condition
     * @param clazz
     * @param dataByKeyGetter
     * @param dataByConditionGetter
     * @param <C>
     * @param <T>
     * @return
     */
    <C extends ConditionCacheable, T extends EntityCacheable> List<T> getByCondition(C condition,
                                                                                     Class<T> clazz,
                                                                                     Function<List<? extends Serializable>, List<T>> dataByKeyGetter,
                                                                                     Function<C, List<T>> dataByConditionGetter);


    /**
     * 根据条件获取实体数量
     *
     * @param condition
     * @param totalCountByConditionGetter
     * @param <C>
     * @param <N>
     * @return
     */
    default <C extends ConditionCacheable, N extends Number> Long getCountByCondition(C condition, Function<C, N> totalCountByConditionGetter) {

        return getCountByCondition(condition, null, totalCountByConditionGetter);
    }


    @Deprecated
    <C extends ConditionCacheable, T extends EntityCacheable> Long getCountByCondition(C condition,
                                                                                       Class<T> clazz,
                                                                                       Function<C, ? extends Number> totalCountByConditionGetter);


    /**
     * 删除实体缓存和条件缓存
     *
     * @param keyList
     * @param condition
     */
    @Deprecated
    void deleteByKeyList(List<Serializable> keyList,
                         ConditionCacheable condition);


    /**
     * 修改后并删除缓存
     * （修改删除操作相关推荐此方法）
     *
     * @param key
     * @param dataGetter
     * @param dataUpdater
     * @param conditionBuilder
     * @param <C>
     * @param <T>
     * @return
     */
    <C extends ConditionCacheable, T extends EntityCacheable> Long deleteByKeyForUpdate(String key,
                                                                                        Function<String, T> dataGetter,
                                                                                        Callable<? extends Number> dataUpdater,
                                                                                        Function<T, C> conditionBuilder);

    /**
     * 插入后并删除缓存
     * （插入操作相关推荐此方法）
     *
     * @param entity           删除的实体
     * @param dataInserter     插入操作
     * @param conditionBuilder 构建缓存条件（用于删除条件缓存）
     * @param <C>
     * @param <T>
     * @return
     */
    <C extends ConditionCacheable, T extends EntityCacheable> Long deleteByKeyForInsert(T entity,
                                                                                        Function<T, ? extends Number> dataInserter,
                                                                                        Function<T, C> conditionBuilder);


    /**
     * 根据缓存key删除缓存
     * （有相关条件缓存需要缓存的实体操作，不推荐此方法）
     *
     * @param key 缓存key
     */
    default void deleteByKey(String key) {
        deleteByKeyList(Arrays.asList(key), null);
    }

    /**
     * 根据缓存key删除缓存
     * （有相关条件缓存需要缓存的实体操作，不推荐此方法）
     *
     * @param keyList 缓存key列表
     */
    default void deleteByKeyList(List<Serializable> keyList) {
        deleteByKeyList(keyList, null);
    }

    /**
     * 根据条件删除缓存
     *
     * @param condition
     */
    @Deprecated
    void deleteByCondition(ConditionCacheable condition);


    /**
     * 获取缓存名称
     *
     * @return
     */
    String getCacheName();


    /**
     * @param entity
     * @param conditionBuilder
     */
    <C extends ConditionCacheable, T extends EntityCacheable> void put(T entity,
                                                                       Function<T, C> conditionBuilder);

}
