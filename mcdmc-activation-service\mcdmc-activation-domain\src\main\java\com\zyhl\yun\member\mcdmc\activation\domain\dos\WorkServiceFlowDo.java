package com.zyhl.yun.member.mcdmc.activation.domain.dos;

import com.zyhl.yun.member.common.domain.framework.BaseDo;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.RetryPolicyEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 服务流程接口配置表
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class WorkServiceFlowDo extends BaseDo implements Serializable {
    /**
     * 接口id
     */
    private String workServiceFlowId;
    /**
     * 服务id
     */
    private String serviceCode;

    /**
     * 流程描述
     */
    private String flowDesc;

    /**
     * 业务流程类名
     */
    private String serviceFlowClass;

    /**
     * 是否并行
     */
    private String syncFlag;

    /**
     * 重试次数，负数表示不限制重试次数
     */
    private int retryCount;

    /**
     * 重试策略
     *
     * @see RetryPolicyEnum RetryTypeEnum
     */
    private int retryPolicy;

    /**
     * 重试时间，根据retryType决定该值的含义
     */
    private String retryTime;
    /**
     * 状态
     */
    private Integer state;

    /**
     * 生效的接口列表
     */
    private List<WorkServiceFlowIFaceDo> iFaceDoList;

    public WorkServiceFlowDo(DomainServiceContext domainServiceContext) {
        this.domainServiceContext = domainServiceContext;
    }

    public boolean isNeedRetry() {
        return RetryPolicyEnum.NOT_RETRY.typeEquals(this.retryPolicy) && this.retryCount > 0;
    }
}
