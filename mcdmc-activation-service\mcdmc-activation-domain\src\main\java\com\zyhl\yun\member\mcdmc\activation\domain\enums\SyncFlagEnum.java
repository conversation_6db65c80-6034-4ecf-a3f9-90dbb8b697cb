package com.zyhl.yun.member.mcdmc.activation.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/06/11 10:10
 */
@Getter
@AllArgsConstructor
public enum SyncFlagEnum {

    PARALLEL("1", "并行执行"),
    SERIAL("0", "串行执行");

    private final String code;
    private final String msg;

    public boolean codeEquals(String anotherCode) {
        return this.code.equals(anotherCode);
    }
}
