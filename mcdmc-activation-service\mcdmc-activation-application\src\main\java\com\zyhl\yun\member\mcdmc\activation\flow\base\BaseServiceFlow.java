package com.zyhl.yun.member.mcdmc.activation.flow.base;

import cn.hutool.extra.spring.SpringUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.enums.MessageServiceId;
import com.zyhl.yun.member.consumer.common.producer.MqProducer;
import com.zyhl.yun.member.mcdmc.activation.constants.WorkAttrCodeConstant;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowDo;
import com.zyhl.yun.member.mcdmc.activation.domain.mq.properties.FlowRetryMqProperties;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.FlowStaticConfig;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.ActivationContextUtil;
import com.zyhl.yun.member.mcdmc.activation.serviceid.LocalServiceId;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.RetryPolicyEnum;
import com.zyhl.yun.member.mcdmc.activation.enums.WorkOrderStateEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IServiceFlow;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.message.domain.ActivationMqMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * 业务流程基础模板
 *
 * <AUTHOR>
 * @since 2024/07/03 11:31
 */
@Slf4j
public abstract class BaseServiceFlow implements IServiceFlow {

    protected FlowRetryMqProperties flowRetryMqProperties;
    protected MqProducer mqProducer;


    @PostConstruct
    private void initBean() {
        flowRetryMqProperties = SpringUtil.getBean(FlowRetryMqProperties.class);
        mqProducer = SpringUtil.getBean(MqProducer.class);
    }

    /**
     * 流程前置逻辑
     *
     * @param workOrderDo 工单信息
     * @return boolean
     * @apiNote 如果为true，则直接以成功结束工单流程，不会生成接口日志
     */
    protected IFlowResult beforeFlow(WorkOrderDo workOrderDo) {
        return IFlowResult.result(NextHintEnum.NEXT, null);
    }


    /**
     * 流程成功后执行动作
     */
    protected void doFlowSuccess(WorkOrderDo workOrderDo, boolean isFinally, IFlowResult lastFaceResult) {
        // 所有流程都走完要更新工单状态
        ActivationContextUtil.updateFinallyWorkOrder(workOrderDo, isFinally);
        if (lastFaceResult != null && StringUtils.isNotBlank(lastFaceResult.getMessage())) {
            // 如果是提前结束，更新工单属性
            ActivationContextUtil.upsertWorkAttr(workOrderDo.getUserId(), workOrderDo.getWorkId(),
                    WorkAttrCodeConstant.EARLY_FINISH_MSG, lastFaceResult.getMessage());
        }
    }

    /**
     * 流程超过重试次数后最终失败执行的动作
     *
     * @param workOrderDo 工单
     */
    protected void doFlowFail(WorkOrderDo workOrderDo) {

    }

    /**
     * 异常或失败重试
     *
     * @param workOrderDo 工单实体
     */
    protected void doRetry(WorkOrderDo workOrderDo, String errorMsg, boolean isNeedRetry) {

        DomainServiceContext context = new DomainServiceContext(LocalServiceId.UPDATE_BY_ID_OPERATION);
        // 失败次数+1
        workOrderDo.setFailCount(workOrderDo.getFailCount() + 1);
        int flowRetryCount = FlowStaticConfig.getFlowRetryCount(workOrderDo.getServiceCode());
        if (workOrderDo.getFailCount() >= flowRetryCount || Boolean.FALSE.equals(isNeedRetry)) {
            this.doFlowFail(workOrderDo);
            // 重试次数达到上限或不需要重试，直接结束,更新工单失败
            workOrderDo.setState(WorkOrderStateEnum.FAILED);
        } else {
            //未达到失败次数上限，发送重试mq消息
            doSendRetryMessage(workOrderDo);
        }
        context.putInstance(workOrderDo);
        context.writeAndFlush();
        // 记录失败原因
        String retryErrorMsgKey = WorkAttrCodeConstant.getRetryErrorMsgKey(workOrderDo.getFailCount());
        ActivationContextUtil.insertWorkAttr(workOrderDo.getUserId(), workOrderDo.getWorkId(), retryErrorMsgKey, errorMsg);
    }

    /**
     * 发送mq重试消息
     *
     * @param workOrderDo 工单实体
     */
    protected void doSendRetryMessage(WorkOrderDo workOrderDo) {
        //未达到失败次数上限，发送重试mq消息
        ActivationMqMessage messageDo = new ActivationMqMessage();
        messageDo.setTraceId(workOrderDo.getTraceId());
        // 添加后缀，防止重复
        messageDo.setMsgId(workOrderDo.getTransactionId() + "#" + workOrderDo.getFailCount());
        messageDo.setContext(workOrderDo.getWorkAttrs());
        messageDo.setTransactionId(workOrderDo.getTransactionId());
        messageDo.setOperatorType(MessageServiceId.Operator.CREATE);
        // 获取流程配置
        WorkServiceFlowDo workServiceFlowDo = FlowStaticConfig.getServiceFlowDo(workOrderDo.getServiceCode());
        messageDo.setOperateTime(this.getNextExecuteTime(workServiceFlowDo));
        messageDo.setUserId(workOrderDo.getUserId());
        messageDo.setOrderId(workOrderDo.getOrderId());
        messageDo.setSopenServiceId(workOrderDo.getServiceCode());
        mqProducer.sendNewMessage(flowRetryMqProperties.getRetryTopic(), flowRetryMqProperties.getRetryTag(), messageDo);
    }


    /**
     * 异常重试时获取下次执行时间
     */
    protected Date getNextExecuteTime(WorkServiceFlowDo workServiceFlowDo) {
        RetryPolicyEnum retryPolicyEnum = RetryPolicyEnum.getByType(workServiceFlowDo.getRetryPolicy());
        if (null == retryPolicyEnum || retryPolicyEnum == RetryPolicyEnum.CUSTOM_RETRY) {
            // 没有配置或自定义重试都按自定义重试执行
            return this.getCustomRetryTime();
        }
        return retryPolicyEnum.getRetryTime(workServiceFlowDo.getRetryTime());
    }

    /**
     * 当retryPolicy为CUSTOM_RETRY(99)时，则通过该方法获取重试时间
     */
    protected Date getCustomRetryTime() {
        return new Date();
    }

    /**
     * 是否允许重试时继续之前的流程
     */
    protected boolean isAllowContinueFlow() {
        return false;
    }

}
