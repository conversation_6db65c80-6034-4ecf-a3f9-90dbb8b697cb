package com.zyhl.yun.member.mcdmc.activation.redis;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;
import org.springframework.util.NumberUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RedisComponent {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    public <T> T eval(String script, List<String> keys, List<Object> args, Class<T> resultType) {
        RedisScript<T> redisScript = new DefaultRedisScript<>(script, resultType);
        return redisTemplate.execute(redisScript, keys, args.toArray());
    }

    public boolean setNx(String key, String value) {
        return Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, value));
    }

    public boolean setNX(String key, String value, long time, TimeUnit timeUnit) {
        return Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, value, time, timeUnit));
    }

    public void set(String key, String value, long expireTime, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, expireTime, timeUnit);
    }

    public void set(String key, String value, long seconds) {
        redisTemplate.opsForValue().set(key, value, seconds, TimeUnit.SECONDS);
    }

    public void set(String key, String value) {
        redisTemplate.opsForValue().set(key, value);
    }

    public String get(String key) {
        return redisTemplate.opsForValue().get(key);

    }

    public String get(String key, String defaultValue) {
        String value = this.get(key);
        return null == value ? defaultValue : value;
    }

    public String get(String key, boolean null2Empty) {
        if (null2Empty) {
            this.get(key, "");
        }
        return get(key);
    }
    public void setJson(String key, Object value, long expireTimeSec) {
        redisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(value), expireTimeSec, TimeUnit.SECONDS);
    }
    public void setJson(String key, Object value, long expireTime, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(value), expireTime, timeUnit);
    }

    /**
     * 根据传入类获取对应的bean实体
     *
     * @param key    redisKey
     * @param tClass json字符串对应的bean实体类
     * @apiNote 存在redis的必须是json字符串，否则返回null
     */
    public <T> T getJsonBean(String key, Class<T> tClass) {
        String value = get(key);
        if (!JSONUtil.isTypeJSON(value)) {
            return null;
        }
        return JSONUtil.toBean(value, tClass);
    }

    /**
     * 根据传入类获取对应的bean实体列表
     *
     * @param key    redisKey
     * @param tClass json字符串对应的bean实体类
     * @apiNote 存在redis的必须是jsonArray字符串，否则返回null
     */
    public <T> List<T> getJsonBeanList(String key, Class<T> tClass) {
        String value = get(key);
        if (!JSONUtil.isTypeJSONArray(value)) {
            return Collections.emptyList();
        }
        return JSONUtil.toList(value, tClass);
    }

    /**
     * 根据传入类获取对应的数字对象
     *
     * @param key    redisKey
     * @param tClass Number类
     * @apiNote 存在redis的必须是数字字符串，否则会报错
     */
    public <T extends Number> T getNumber(String key, Class<T> tClass) {
        String value = get(key);
        if (null == value) {
            return null;
        }
        return NumberUtils.parseNumber(value, tClass);
    }


    public boolean delete(String key) {
        return Boolean.TRUE.equals(redisTemplate.delete(key));
    }

    public boolean hasKey(String key) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }

    public Long increment(String key, long delta) {
        return redisTemplate.opsForValue().decrement(key, delta);
    }
}
