@startuml
'https://plantuml.com/activity-diagram-beta

start
:开始分流;
if (uri=syncAppOrder？) then (true)
  :获取用户信息;
  if (全局开关打开？) then (true)
    : 转发到旧服务;
    stop
  endif
  if (用户为白名单?) then (yes)
  else (no)
    : 转发旧vsbo;
    stop
  endif
  :获取入参Subplace;
  if (Subplace=7？) then (yes)
    :转发新vsbo;
    : 新vsbo在action不等于1和订单号判断是否新vsbo生成，\n若是旧vsbo生成规则，则调用旧vsbo\n若是新vsbo生成规则，则直接处理;
    stop
  else (false)
    :获取订单id;
    if (订单id为新vsbo生成规则?) then (yes)
      : 转发新vsbo;
    else
      : 转发旧vsbo;
    endif
      stop
  endif
endif
: 根据对应接口映射规则处理转发;
stop
@enduml
