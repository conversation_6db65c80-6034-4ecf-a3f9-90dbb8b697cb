/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.zyhl.yun.member.mcdmc.activation.domain.enums;

/**
 * 产品类型枚举类
 *
 * <AUTHOR>
 * @version [版本号, 2017年2月23日]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
public enum ProductTypeEnum {
    /**
     * 1：空间产品
     */
    iSpace(1),

    /**
     * 2：会员权益
     */
    member(2);

    private int type;

    ProductTypeEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }
}
