package com.zyhl.yun.member.common.domain.framework;

import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.yun.member.common.domain.mono.ProcessByThrowExceptionUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 领域上下文
 *
 * <AUTHOR>
 * @date 2024/05/17 9:17
 */
@Slf4j
public class DomainServiceContext implements Serializable {


    private static final long serialVersionUID = 8026105439555954621L;

    /**
     * 服务Id
     */
    @Getter
    private String serviceId;


    /**
     * 请求时间
     */
    @Getter
    private Date requestTime;

    /**
     * 日志id
     */
    @Getter
    @Setter
    private String traceId;

    /**
     * 扩展信息
     */
    @Getter
    @Setter
    private Map<String, Object> extData = new HashMap<>();


    /**
     * 领域缓存
     * key:类名
     * value:DomainObjectInfoMap
     * DomainObjectInfo key: 主键
     * DomainObjectInfo value: EntityBean
     */
    private transient Map<String, Map<Serializable, DomainEntityInfo>> domainCache = new HashMap<>();


    /**
     * 获取不到主键的领域缓存
     * key:类名
     * value:DomainObjectInfoList
     */
    private transient Map<String, List<DomainEntityInfo>> domainCacheWithoutPk = new HashMap<>();

    /**
     * 条件缓存
     * key:类名
     * value: 条件Map
     * 条件Map key: 条件摘要
     * 条件Map value: 主键列表
     */
    private transient Map<String, Map<String, List<Serializable>>> conditionCache = new HashMap<>();

    @Deprecated
    public DomainServiceContext(Class domainClass, String serviceId) {
        this.serviceId = serviceId;
        this.traceId = MDC.get(LogConstants.TRACE_ID);
    }

    public DomainServiceContext(String serviceId) {
        this(serviceId, null);
    }

    public DomainServiceContext(String serviceId, Date requestTime) {
        this.serviceId = serviceId;
        this.requestTime = requestTime == null ? new Date() : requestTime;
        this.traceId = MDC.get(LogConstants.TRACE_ID);
    }

    public DomainServiceContext(String serviceId, Date requestTime, Map<String, Object> extData) {
        this(serviceId, requestTime);
        this.extData = extData;
    }

    /**
     * 创建子上下文
     *
     * @param childServiceId
     * @return
     */
    public DomainServiceContext createChildContext(String childServiceId) {
        return new DomainServiceContext(childServiceId, this.requestTime, this.extData);
    }


    /**
     * 创建新领域对象
     *
     * @param clazz
     * @param constructor
     * @param persistenceDriver
     * @return
     */
    public <C extends Serializable> C newInstance(Class<C> clazz, Callable<C> constructor, IDriver<? extends C> persistenceDriver) {
        Object resultInst = ProcessByThrowExceptionUtil.runThrowExceptionProcessByReturn(() -> {

            C domainEntity = constructor.call();
            addToDomainCache(domainEntity, persistenceDriver);

            return domainEntity;
        });
        return (C) resultInst;
    }


    /**
     * 创建新领域对象
     *
     * @param clazz
     * @param persistenceDriver
     * @return
     */
    public <C extends Serializable> C newInstance(Class<C> clazz, IDriver<? extends C> persistenceDriver) {
        return newInstance(clazz, () -> {
            Constructor<C> c = clazz.getDeclaredConstructor();
            if (c == null) {
                log.error("[FRAMEWORK] not supported type");
                new DomainException("not supported type");
            }

            return c.newInstance();
        }, persistenceDriver);
    }


    /**
     * 创建新领域对象
     *
     * @param clazz
     * @return
     */
    public <C extends Serializable> C newInstance(Class<C> clazz) {
        return newInstance(clazz, (IDriver<? extends C>) null);
    }


    /**
     * 创建新领域对象
     *
     * @param clazz
     * @param constructor
     * @return
     */
    public <C extends Serializable> C newInstance(Class<C> clazz, Callable<C> constructor) {
        return newInstance(clazz, constructor, null);
    }


    /**
     * 放入领域对象
     *
     * @param domainEntity
     * @param <C>
     */
    public <C extends Serializable> void putInstance(C domainEntity) {
        this.putInstance(domainEntity, null);
    }


    /**
     * 放入领域对象
     *
     * @param domainEntity
     * @param persistenceDriver
     * @param <C>
     */
    public <C extends Serializable> void putInstance(C domainEntity, IDriver<? extends C> persistenceDriver) {
        ProcessByThrowExceptionUtil.runNotThrowExceptionProcess(() -> {
            addToDomainCache(domainEntity, persistenceDriver);
        });
    }


    /**
     * 使用主键标记删除领域对象
     * writeAndFlush的时候触发删除
     *
     * @param pkList
     * @param domainClass
     * @param persistenceDriver
     * @param <C>
     */
    public <C extends Serializable> void markDeleteByPk(List<Serializable> pkList, Class<C> domainClass, IDriver<? extends C> persistenceDriver) {
        if (CollectionUtils.isEmpty(pkList)) {
            return;
        }

        for (Serializable pk : pkList) {
            markDeleteByPk(pk, domainClass, persistenceDriver);
        }
    }


    /**
     * 使用主键标记删除领域对象
     * writeAndFlush的时候触发删除
     *
     * @param pkList
     * @param domainClass
     * @param <C>
     */
    public <C extends Serializable> void markDeleteByPk(List<Serializable> pkList, Class<C> domainClass) {
        markDeleteByPk(pkList, domainClass, null);
    }


    /**
     * 使用主键标记删除领域对象
     * writeAndFlush的时候触发删除
     *
     * @param pk
     * @param domainClass
     * @return
     */
    public <C> void markDeleteByPk(Serializable pk, Class<C> domainClass) {
        markDeleteByPk(pk, domainClass, null);
    }


    /**
     * 使用主键标记删除领域对象
     * writeAndFlush的时候触发删除
     *
     * @param pk
     * @param domainClass
     * @param persistenceDriver
     * @return
     */
    public <C> void markDeleteByPk(Serializable pk, Class<C> domainClass, IDriver<? extends C> persistenceDriver) {
        ProcessByThrowExceptionUtil.runNotThrowExceptionProcess(() -> {
            DomainEntityInfo domainEntityInfo = getFromDomainCache(pk, domainClass, persistenceDriver);
            if (domainEntityInfo == null) {
                domainEntityInfo = buildDomainEntityInfo(pk, domainClass, persistenceDriver);
                addToDomainCache(domainEntityInfo);
            }
            domainEntityInfo.setDeleteMark(true);
        });
    }


    /**
     * 标记删除领域对象
     * writeAndFlush的时候触发删除
     *
     * @param domainEntityList
     * @return
     */
    public <C extends Serializable> void markDelete(List<? extends C> domainEntityList) {
        markDelete(domainEntityList, null);
    }


    /**
     * 标记删除领域对象
     * writeAndFlush的时候触发删除
     *
     * @param domainEntityList
     * @param persistenceDriver
     * @return
     */
    public <C extends Serializable> void markDelete(List<? extends C> domainEntityList, IDriver<? extends C> persistenceDriver) {
        if (CollectionUtils.isEmpty(domainEntityList)) {
            return;
        }

        for (Serializable domainEntity : domainEntityList) {
            markDelete(domainEntity, persistenceDriver);
        }
    }


    /**
     * 标记删除领域对象
     * writeAndFlush的时候触发删除
     *
     * @param domainEntity
     * @return
     */
    public <C extends Serializable> void markDelete(C domainEntity) {
        markDelete(domainEntity, null);
    }


    /**
     * 标记删除领域对象
     * writeAndFlush的时候触发删除
     *
     * @param domainEntity
     * @param persistenceDriver
     * @return
     */
    public <C extends Serializable> void markDelete(C domainEntity, IDriver<? extends C> persistenceDriver) {
        ProcessByThrowExceptionUtil.runNotThrowExceptionProcess(() -> {
            DomainEntityInfo domainEntityInfo = getFromDomainCache(domainEntity, persistenceDriver);
            if (domainEntityInfo == null) {
                domainEntityInfo = buildDomainEntityInfo(domainEntity, persistenceDriver);
                addToDomainCache(domainEntityInfo);
            }
            domainEntityInfo.setDeleteMark(true);
        });
    }


    /**
     * 使用主键查询领域对象
     *
     * @param pk
     * @param domainClass
     * @return
     */
    public <C extends Serializable> C read(Serializable pk, Class<C> domainClass) {
        return read(pk, domainClass, null);
    }


    /**
     * 使用主键查询领域对象
     *
     * @param pk
     * @param domainClass
     * @param persistenceDriver
     * @return
     */
    public <C extends Serializable> C read(Serializable pk, Class<C> domainClass, IDriver<? extends C> persistenceDriver) {

        Object resultDomainEntity = ProcessByThrowExceptionUtil.runThrowExceptionProcessByReturn(() -> {

            IDriver<? extends C> driver = persistenceDriver;
            if (driver == null) {
                driver = getDriver(domainClass);
            }

            C domainEntity = driver.readByPk(new DomainEntityPersistenceWrapper(pk,
                    domainClass, null, this));
            if (domainEntity == null) {
                return null;
            }

            addToDomainCache(domainEntity, driver);

            return domainEntity;
        });
        return (C) resultDomainEntity;
    }


    /**
     * 根据条件获取总数
     *
     * @param condition
     * @param domainClass
     * @return
     */
    public Long getCount(BaseCondition condition, Class domainClass) {
        return getCount(condition, domainClass, null);
    }


    /**
     * 根据条件获取总数
     *
     * @param condition
     * @param domainClass
     * @param persistenceDriver
     * @return
     */
    public <C extends Serializable> Long getCount(BaseCondition condition, Class domainClass, IDriver<C> persistenceDriver) {
        IDriver<C> driver = persistenceDriver;
        if (driver == null) {
            driver = getDriver(domainClass);
        }

        AroundPersistenceHandler aroundHandlerToTrigger = AroundHandlerManager.getHandler(domainClass, serviceId);
        return driver.getCount(new DomainEntityPersistenceWrapper(condition, domainClass,
                aroundHandlerToTrigger, this));

    }

    /**
     * 读取返回结果第一条
     *
     * @param condition   查询条件
     * @param domainClass 领域类
     * @param <C>         返回结果类型
     * @return 满足条件的第一条记录
     */
    public <C extends Serializable> C readFirst(BaseCondition condition, Class<C> domainClass) {
        return this.readFirst(condition, domainClass, null);
    }

    /**
     * 读取返回结果第一条
     *
     * @param condition    查询条件
     * @param domainClass  领域类
     * @param defaultValue 结果为空时的默认返回值
     * @param <C>          返回结果类型
     * @return 满足条件的第一条记录
     */
    public <C extends Serializable> C readFirst(BaseCondition condition, Class<C> domainClass, C defaultValue) {
        return this.readFirst(condition, domainClass, null, defaultValue);
    }

    /**
     * 读取返回结果第一条
     *
     * @param condition         查询条件
     * @param domainClass       领域类
     * @param persistenceDriver 驱动类
     * @param defaultValue      结果为空时的默认返回值
     * @param <C>               返回结果类型
     * @return 满足条件的第一条记录
     */
    public <C extends Serializable> C readFirst(BaseCondition condition, Class<C> domainClass, IDriver<C> persistenceDriver, C defaultValue) {
        PageCondition pageCondition = new PageCondition();
        // readFirst默认查第一页第一条
        pageCondition.setPageSize(1);
        pageCondition.setPageNo(1);
        condition.setPageCondition(pageCondition);
        List<C> resList = this.read(condition, domainClass, persistenceDriver);
        if (CollectionUtils.isEmpty(resList)) {
            return defaultValue;
        }
        return resList.get(0);
    }

    /**
     * 读取返回结果最后一条
     *
     * @param condition   查询条件
     * @param domainClass 领域类
     * @param <C>         返回结果类型
     * @return 满足条件的最后一条记录
     */
    public <C extends Serializable> C readLast(BaseCondition condition, Class<C> domainClass) {
        return this.readLast(condition, domainClass, null);
    }

    /**
     * 读取返回结果最后一条
     *
     * @param condition    查询条件
     * @param domainClass  领域类
     * @param defaultValue 结果为空时的默认返回值
     * @param <C>          返回结果类型
     * @return 满足条件的最后一条记录
     */
    public <C extends Serializable> C readLast(BaseCondition condition, Class<C> domainClass, C defaultValue) {
        return this.readLast(condition, domainClass, null, defaultValue);
    }

    /**
     * 读取返回结果最后一条
     *
     * @param condition         查询条件
     * @param domainClass       领域类
     * @param persistenceDriver 驱动类
     * @param defaultValue      结果为空时的默认返回值
     * @param <C>               返回结果类型
     * @return 满足条件的最后一条记录
     */
    public <C extends Serializable> C readLast(BaseCondition condition, Class<C> domainClass, IDriver<C> persistenceDriver, C defaultValue) {
        List<C> resList = this.read(condition, domainClass, persistenceDriver);
        if (CollectionUtils.isEmpty(resList)) {
            return defaultValue;
        }
        return resList.get(resList.size() - 1);
    }

    /**
     * 使用条件查询
     *
     * @param condition
     * @param domainClass
     * @return
     */
    public <C extends Serializable> List<C> read(BaseCondition condition, Class<C> domainClass) {
        return read(condition, domainClass, null);
    }


    /**
     * 使用条件查询
     *
     * @param condition
     * @return
     */
    public <C extends Serializable> List<C> read(BaseCondition condition, Class<C> domainClass, IDriver<C> persistenceDriver) {
        IDriver<C> driver = persistenceDriver;
        if (driver == null) {
            driver = getDriver(domainClass);
        }

        AroundPersistenceHandler aroundHandlerToTrigger = AroundHandlerManager.getHandler(domainClass, serviceId);
        List<? extends C> instList = driver.readByCondition(new DomainEntityPersistenceWrapper(condition,
                domainClass, aroundHandlerToTrigger, this));

        if (CollectionUtils.isEmpty(instList)) {
            return Collections.emptyList();
        }

        List<C> resList = new ArrayList<>(instList.size());
        for (C inst : instList) {
            if (inst instanceof IBaseDo) {
                ((IBaseDo) inst).setDomainServiceContext(this);
            }
            //addPersistenceEntityBean(domainClass.getSimpleName(), new EntityBean(inst, driver));
            resList.add(inst);
        }

        return resList;
    }


    /**
     * 持久化
     *
     * @return
     */
    public int writeAndFlush() {
        return writeAndFlush(null);
    }


    /**
     * 持久化
     *
     * @param aroundPersistenceHandler 环绕处理器
     * @return
     */
    public int writeAndFlush(AroundPersistenceHandler aroundPersistenceHandler) {

        if (CollectionUtils.isEmpty(domainCache) &&
                CollectionUtils.isEmpty(domainCacheWithoutPk)) {
            log.warn("[FRAMEWORK] no domainEntity to persistence");
            return 0;
        }

        AtomicInteger updateCount = new AtomicInteger(0);
        Map<String, List<DomainEntityPersistenceWrapper>> writeWrapperCache = new HashMap<>();
        Map<String, List<DomainEntityPersistenceWrapper>> deleteWrapperCache = new HashMap<>();
        Map<String, List<DomainEntityPersistenceWrapper>> deleteByPkWrapperCache = new HashMap<>();

        Set<IDriver> driverCache = new HashSet<>();

        this.domainCache.forEach((domainName, entityInfoMap) -> {

            if (CollectionUtils.isEmpty(entityInfoMap)) {
                return;
            }
            prepareWrapperCache(entityInfoMap.values(),
                    aroundPersistenceHandler,
                    driverCache,
                    writeWrapperCache,
                    deleteWrapperCache,
                    deleteByPkWrapperCache);

        });

        this.domainCacheWithoutPk.forEach((domainName, entityInfoList) -> {

            if (CollectionUtils.isEmpty(entityInfoList)) {
                return;
            }
            prepareWrapperCache(entityInfoList,
                    aroundPersistenceHandler,
                    driverCache,
                    writeWrapperCache,
                    deleteWrapperCache,
                    deleteByPkWrapperCache);

        });


        this.domainCache.clear();
        this.domainCacheWithoutPk.clear();

        for (IDriver driver : driverCache) {

            if (!CollectionUtils.isEmpty(writeWrapperCache.get(driver.getClass().getName()))) {
                updateCount.getAndAdd(driver.writeList(writeWrapperCache.get(driver.getClass().getName())));
            }

            if (!CollectionUtils.isEmpty(deleteWrapperCache.get(driver.getClass().getName()))) {
                updateCount.getAndAdd(driver.deleteList(deleteWrapperCache.get(driver.getClass().getName())));
            }

            if (!CollectionUtils.isEmpty(deleteByPkWrapperCache.get(driver.getClass().getName()))) {
                updateCount.getAndAdd(driver.deleteByPkList(deleteByPkWrapperCache.get(driver.getClass().getName())));
            }

        }

        return updateCount.get();
    }


    /**
     * 根据传入的类获取扩展信息，若传入的类和实际值类型不匹配，则会报类型匹配错误问题
     */
    public <T> T getExtDate(String key, Class<T> tClass) {
        if (null == this.extData) {
            this.extData = new HashMap<>();
        }
        Object value = this.extData.get(key);
        return tClass.cast(value);
    }

    /**
     * 获取扩展信息
     *
     * @param key
     * @return
     */
    public Object getExtDate(String key) {
        if (null == this.extData) {
            this.extData = new HashMap<>();
        }
        return this.extData.get(key);
    }


    /**
     * 放入扩展信息
     *
     * @param key
     * @param value
     * @param <T>
     */
    public <T> void putExtDate(String key, T value) {
        if (null == this.extData) {
            this.extData = new HashMap<>();
        }
        this.extData.put(key, value);
    }


    /**
     * 清理扩展信息
     */
    public void clearExtData() {
        if (this.extData != null) {
            this.extData.clear();
        }
    }


    /**
     * 清理缓存
     */
    public void clearDomainCache() {
        if (domainCache != null) {
            domainCache.clear();
        }
        if (conditionCache != null) {
            conditionCache.clear();
        }
        if (domainCacheWithoutPk != null) {
            domainCacheWithoutPk.clear();
        }
    }

    /**
     * 构建领域对象信息
     *
     * @param pk
     * @param domainClass
     * @param persistenceDriver
     * @param <C>
     * @return
     */
    private <C> DomainEntityInfo buildDomainEntityInfo(Serializable pk, Class<C> domainClass,
                                                       IDriver<? extends C> persistenceDriver) {
        IDriver<? extends C> driver = persistenceDriver;
        if (driver == null) {
            driver = getDriver(domainClass);
        }
        return new DomainEntityInfo(pk, domainClass, driver);
    }


    /**
     * 构建领域对象信息
     *
     * @param domainEntity
     * @param persistenceDriver
     * @param <C>
     * @return
     */
    private <C> DomainEntityInfo buildDomainEntityInfo(Serializable domainEntity, IDriver<? extends C> persistenceDriver) {
        // 设置上下文
        if (domainEntity instanceof IBaseDo) {
            ((IBaseDo) domainEntity).setDomainServiceContext(this);
        }
        IDriver<? extends C> driver = persistenceDriver;
        if (driver == null) {
            driver = getDriver(domainEntity.getClass());
        }

        return new DomainEntityInfo(domainEntity, driver);
    }


    /**
     * 补充领域信息
     *
     * @param domainEntityInfo
     * @param persistenceDriver
     * @param <C>
     */
    private <C> void fillDomainEntityInfo(DomainEntityInfo domainEntityInfo, IDriver<? extends C> persistenceDriver) {

        IDriver<? extends C> driver = persistenceDriver;
        if (domainEntityInfo.getDomainEntity() != null) {
            if (domainEntityInfo.getDomainEntity() instanceof IBaseDo) {
                ((IBaseDo) domainEntityInfo.getDomainEntity()).setDomainServiceContext(this);
            }

            if (driver == null) {
                driver = getDriver(domainEntityInfo.getDomainEntity().getClass());
            }

        }

        if (driver != null) {
            domainEntityInfo.setDriver(driver);
        }
    }


    /**
     * 将领域对象加入缓存
     *
     * @param domainEntityInfo
     */
    private void addToDomainCache(DomainEntityInfo domainEntityInfo) {


        Serializable pk = null;

        try {
            pk = domainEntityInfo.getPk();
        } catch (Exception e) {
            log.warn("[FRAMEWORK] getPk failed. msg: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[FRAMEWORK] getPk failed.", e);
            }
        }

        if (pk != null) {

            Map<Serializable, DomainEntityInfo> domainMap = domainCache.get(domainEntityInfo.getDomainClass().getSimpleName());
            if (domainMap == null) {
                domainMap = new HashMap<>();
                domainCache.put(domainEntityInfo.getDomainClass().getSimpleName(), domainMap);
            }
            domainMap.put(domainEntityInfo.getPk(), domainEntityInfo);

        } else {
            List<DomainEntityInfo> domainList = domainCacheWithoutPk.get(domainEntityInfo.getDomainClass().getSimpleName());
            if (domainList == null) {
                domainList = new ArrayList<>();
                domainCacheWithoutPk.put(domainEntityInfo.getDomainClass().getSimpleName(), domainList);
            }
            domainList.add(domainEntityInfo);
        }

    }


    /**
     * 将领域对象加入缓存
     *
     * @param domainEntity
     * @param persistenceDriver
     */
    private <C> void addToDomainCache(Serializable domainEntity, IDriver<? extends C> persistenceDriver) {
        addToDomainCache(buildDomainEntityInfo(domainEntity, persistenceDriver));
    }


    /**
     * 从缓存中获取领域对象
     *
     * @param pk
     * @param domainClass
     * @param persistenceDriver
     * @return
     */
    private <C> DomainEntityInfo getFromDomainCache(Serializable pk, Class domainClass, IDriver<? extends C> persistenceDriver) {
        Map<Serializable, DomainEntityInfo> domainMap = domainCache.get(domainClass.getSimpleName());
        if (domainMap == null) {
            return null;
        }

        DomainEntityInfo domainEntityInfo = domainMap.get(pk);
        if (domainEntityInfo != null) {
            fillDomainEntityInfo(domainEntityInfo, persistenceDriver);
        }

        return domainEntityInfo;
    }

    /**
     * 从缓存中获取领域对象
     *
     * @param domainEntity
     * @param persistenceDriver
     * @return
     */
    private <C> DomainEntityInfo getFromDomainCache(Serializable domainEntity, IDriver<? extends C> persistenceDriver) {
        Map<Serializable, DomainEntityInfo> domainMap = domainCache.get(domainEntity.getClass().getSimpleName());
        if (domainMap == null) {
            return null;
        }
        Serializable pk = getPk(domainEntity);

        DomainEntityInfo domainEntityInfo = domainMap.get(pk);
        if (domainEntityInfo != null) {
            fillDomainEntityInfo(domainEntityInfo, persistenceDriver);
        }
        return domainEntityInfo;
    }


    /**
     * 准备所有需要缓存数据
     *
     * @param domainEntityInfoList
     * @param aroundPersistenceHandler
     * @param driverCache
     * @param writeWrapperCache
     * @param deleteWrapperCache
     * @param deleteByPkWrapperCache
     */
    private void prepareWrapperCache(Collection<DomainEntityInfo> domainEntityInfoList,
                                     AroundPersistenceHandler aroundPersistenceHandler,
                                     Set<IDriver> driverCache,
                                     Map<String, List<DomainEntityPersistenceWrapper>> writeWrapperCache,
                                     Map<String, List<DomainEntityPersistenceWrapper>> deleteWrapperCache,
                                     Map<String, List<DomainEntityPersistenceWrapper>> deleteByPkWrapperCache) {

        for (DomainEntityInfo domainEntityInfo : domainEntityInfoList) {

            Map<String, List<DomainEntityPersistenceWrapper>> wrapperCache = null;
            if (domainEntityInfo.isDeleteMark()) {
                if (domainEntityInfo.getDomainEntity() != null) {
                    wrapperCache = deleteWrapperCache;
                } else {
                    wrapperCache = deleteByPkWrapperCache;
                }
            } else {
                wrapperCache = writeWrapperCache;
            }

            List<DomainEntityPersistenceWrapper> entityWrapperList = wrapperCache
                    .get(domainEntityInfo.getDriver().getClass().getName());
            if (CollectionUtils.isEmpty(entityWrapperList)) {
                entityWrapperList = new ArrayList<>();
                wrapperCache.put(domainEntityInfo.getDriver().getClass().getName(), entityWrapperList);
            }

            driverCache.add(domainEntityInfo.getDriver());

            AroundPersistenceHandler aroundHandlerToTrigger = aroundPersistenceHandler;
            if (aroundHandlerToTrigger == null && domainEntityInfo.getDomainEntity() instanceof IBaseDo) {
                aroundHandlerToTrigger = ((IBaseDo) domainEntityInfo.getDomainEntity())
                        .getAroundPersistenceHandler(this);
            }
            if (domainEntityInfo.getDomainEntity() != null) {
                entityWrapperList.add(new DomainEntityPersistenceWrapper(domainEntityInfo.getDomainEntity(),
                        aroundHandlerToTrigger, this));
            } else {
                entityWrapperList.add(new DomainEntityPersistenceWrapper(domainEntityInfo.getPk(),
                        aroundHandlerToTrigger, this));
            }

        }

    }

    /**
     * 根据对象获取主键
     *
     * @param domainEntity
     * @return
     */
    private Serializable getPk(Serializable domainEntity) {
        Serializable pk = null;
        if (domainEntity instanceof IBaseDo) {
            try {
                pk = ((IBaseDo) domainEntity).getPk();
            } catch (Exception e) {
                log.warn("[FRAMEWORK] getPk failed. msg: {}", e.getMessage());
                if (log.isDebugEnabled()) {
                    log.error("[FRAMEWORK] getPk failed.", e);
                }
            }
        }
        return pk;
    }


    /**
     * 获取对应驱动器
     *
     * @param clazz
     * @return
     */
    private IDriver getDriver(Class clazz) {
        return getDriver(clazz.getSimpleName());
    }


    /**
     * 获取对应驱动器
     *
     * @param className
     * @return
     */
    private IDriver getDriver(String className) {
        return DriverManager.getDriver(className);
    }


    /**
     * 是否支持缓存
     *
     * @param domainEntity
     * @return
     */
    private boolean isSupportCache(IBaseDo domainEntity) {
        if (domainEntity.getPk() != null) {
            return true;
        }
        return false;
    }

}
