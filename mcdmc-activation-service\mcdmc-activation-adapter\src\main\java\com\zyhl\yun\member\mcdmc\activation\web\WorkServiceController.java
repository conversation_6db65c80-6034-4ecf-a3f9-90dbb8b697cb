package com.zyhl.yun.member.mcdmc.activation.web;


import com.zyhl.yun.member.mcdmc.activation.service.WorkOrderService;
import com.zyhl.yun.member.mcdmc.activation.req.SyncOrderReq;
import com.zyhl.yun.member.mcdmc.activation.result.Result;
import com.zyhl.yun.member.mcdmc.activation.result.ResultCode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 服务开通订单入口
 *
 * <AUTHOR>
 * @since 240527
 */
@RestController
@Data
@Slf4j
public class WorkServiceController {
    @Autowired
    private WorkOrderService workOrderService;

    @RequestMapping(value = "/api/syncOrder")
    public Mono<Result<Integer>> syncOrder(@RequestBody SyncOrderReq orderReq) {

        Mono<Integer> appMono = workOrderService.syncOrder(orderReq);
        if (appMono == null) {//上面抛错了
            return Mono.just(Result.error(ResultCode.SYSTEM_ERROR));
        }
        //定义返回值
        Result<Integer> rsp = Result.error(ResultCode.SYSTEM_ERROR);
        return appMono
                .doOnSuccess(r -> {
                    rsp.setSuccess(true);
                    rsp.setResultCode(ResultCode.SUCCESS.getCode());
                    rsp.setMessage(ResultCode.SUCCESS.getMsg());
                    rsp.setData(r);
                })
                .doOnError(ex -> {
                    log.error("syncOrder fail:", ex);
                    rsp.setMessage(ex.getMessage());
                    rsp.setSuccess(false);
                }).flatMap(r -> Mono.just(rsp));
    }

    @PostMapping(value = "/cloudSEE/openApi/**")
    public Mono<String> callback(@RequestHeader Map<String, String> headers,
                                 @RequestBody String body, HttpServletRequest request) {
        return workOrderService.callback(headers, body, request)
                .doOnError(ex -> log.error("callback fail:", ex))
                .onErrorReturn("error");
    }

    /**
     * 刷新流程配置
     */
    @PostMapping(value = "/custom/refreshFlow")
    public Mono<String> refreshFlow() {
        return workOrderService.refreshFlowConfig();
    }
}
