-- MySQL dump 10.13  Distrib 5.5.62, for Win64 (AMD64)
--
-- Host: **********    Database: member_sopen
-- ------------------------------------------------------
-- Server version	8.0.37

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `activation_prod_service_relation`
--

DROP TABLE IF EXISTS `activation_prod_service_relation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activation_prod_service_relation` (
  `relation_id` varchar(128) NOT NULL COMMENT '主键id',
  `product_id` varchar(128) NOT NULL COMMENT '产品id',
  `service_id` varchar(128) NOT NULL COMMENT '服务id',
  `relation_type` varchar(255) NOT NULL COMMENT '业务类型：订购，退订，退费，等等',
  `state` smallint NOT NULL COMMENT '状态',
  `created_time` timestamp NOT NULL COMMENT '创建时间',
  `updated_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `operator` varchar(128) DEFAULT NULL COMMENT '更新人',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户id',
  `version_tag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  PRIMARY KEY (`relation_id`),
  UNIQUE KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='产品-服务关系配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activation_work_order`
--

DROP TABLE IF EXISTS `activation_work_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activation_work_order` (
  `work_id` varchar(128) NOT NULL COMMENT '工单id',
  `order_id` varchar(128) DEFAULT NULL COMMENT '订单id',
  `user_id` varchar(128) DEFAULT NULL COMMENT '用户id',
  `service_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '服务编号',
  `work_attrs` varchar(1024) DEFAULT NULL COMMENT '工单属性',
  `transaction_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '交易流水号id',
  `state` smallint DEFAULT NULL COMMENT '状态',
  `fail_count` int NOT NULL DEFAULT '0' COMMENT '工单失败次数',
  `flow_progress` int DEFAULT '0' COMMENT '流程进度，多个流程时会更新该进度，从0开始',
  `created_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `operator` varchar(128) DEFAULT NULL COMMENT '操作人',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户id',
  `version_tag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  PRIMARY KEY (`work_id`),
  UNIQUE KEY `activation_work_order_transaction_id_IDX` (`transaction_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务工单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activation_work_order_attr`
--

DROP TABLE IF EXISTS `activation_work_order_attr`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activation_work_order_attr` (
  `work_id` varchar(128) NOT NULL COMMENT '工单id',
  `attr_code` varchar(128) NOT NULL COMMENT '属性编码',
  `attr_val` varchar(255) DEFAULT NULL COMMENT '属性值',
  `state` smallint DEFAULT NULL COMMENT '状态',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `operator` varchar(128) DEFAULT NULL COMMENT '操作人',
  `tenant_id` varchar(128) DEFAULT NULL COMMENT '租户id',
  `version_tag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  PRIMARY KEY (`work_id`,`attr_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工单属性表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activation_work_service`
--

DROP TABLE IF EXISTS `activation_work_service`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activation_work_service` (
  `service_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '服务编号',
  `service_name` varchar(255) DEFAULT NULL COMMENT '服务名称',
  `channel_id` int DEFAULT NULL COMMENT '渠道id',
  `prior` int DEFAULT NULL COMMENT '优先级',
  `state` smallint NOT NULL COMMENT '状态',
  `operator` varchar(128) DEFAULT NULL COMMENT '修改人',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户id',
  `created_time` timestamp NOT NULL COMMENT '创建时间',
  `updated_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `version_tag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  PRIMARY KEY (`service_code`),
  UNIQUE KEY `service_id` (`service_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activation_work_service_attr`
--

DROP TABLE IF EXISTS `activation_work_service_attr`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activation_work_service_attr` (
  `service_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '服务编号',
  `attr_code` varchar(128) NOT NULL COMMENT '属性编码',
  `attr_val` varchar(512) DEFAULT NULL COMMENT '属性值',
  `state` smallint NOT NULL COMMENT '状态',
  `created_time` timestamp NOT NULL COMMENT '创建时间',
  `updated_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `operator` varchar(128) DEFAULT NULL COMMENT '更新人',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户id',
  `version_tag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  PRIMARY KEY (`service_code`,`attr_code`),
  UNIQUE KEY `service_id` (`service_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务属性表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activation_work_service_channel`
--

DROP TABLE IF EXISTS `activation_work_service_channel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activation_work_service_channel` (
  `channel_id` varchar(128) NOT NULL COMMENT '渠道ID',
  `name` varchar(255) DEFAULT NULL COMMENT '渠道名称',
  `state` int NOT NULL COMMENT '状态',
  `created_time` timestamp NOT NULL COMMENT '创建时间',
  `updated_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `operator` varchar(128) DEFAULT NULL COMMENT '更新人',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户id',
  `version_tag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  PRIMARY KEY (`channel_id`),
  UNIQUE KEY `channel_id` (`channel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='接入方渠道发放表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activation_work_service_flow`
--

DROP TABLE IF EXISTS `activation_work_service_flow`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activation_work_service_flow` (
  `work_service_flow_id` varchar(128) NOT NULL COMMENT '接口id',
  `service_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '服务编号',
  `service_flow_class` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '业务流程类名',
  `interface_name` varchar(255) DEFAULT NULL COMMENT '接口名称',
  `condition_template` mediumtext COMMENT '执行条件',
  `request_header_template` varchar(2000) DEFAULT NULL COMMENT '发送header',
  `request_body_template` mediumtext COMMENT '发送body ',
  `request_type` int NOT NULL COMMENT '发送类型,0:post,1:get,2:del,3:put',
  `url` varchar(255) NOT NULL COMMENT '发送url ',
  `notify_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '回调地址URI（不包含项目contextPath及之前的地址）',
  `response_condition_template` mediumtext COMMENT '返回条件',
  `callback_condition_template` mediumtext COMMENT '写回调查询',
  `sync_flag` smallint DEFAULT NULL COMMENT '是否并行',
  `prior_sort` int DEFAULT '0' COMMENT '执行优先级，从0开始，数字越小，优先级越高',
  `state` smallint NOT NULL COMMENT '状态',
  `created_time` timestamp NOT NULL COMMENT '创建时间',
  `updated_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `operator` varchar(128) DEFAULT NULL COMMENT '更新人',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户id',
  `retry_count` int DEFAULT NULL COMMENT '重试次数',
  `version_tag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  `retry_policy` int DEFAULT NULL COMMENT '重试策略',
  PRIMARY KEY (`work_service_flow_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='接口表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activation_work_service_flow_callback`
--

DROP TABLE IF EXISTS `activation_work_service_flow_callback`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activation_work_service_flow_callback` (
  `service_callback_id` varchar(128) NOT NULL COMMENT '接口id',
  `service_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '服务编号',
  `interface_name` varchar(255) DEFAULT NULL COMMENT '接口名称',
  `url` varchar(255) DEFAULT NULL COMMENT '调用url',
  `condition_template` mediumtext COMMENT '获取服务id参数表达式',
  `state` smallint NOT NULL COMMENT '状态',
  `created_time` timestamp NOT NULL COMMENT '创建时间',
  `updated_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `operator` varchar(128) DEFAULT NULL COMMENT '更新人',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户id',
  `version_tag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  PRIMARY KEY (`service_callback_id`),
  UNIQUE KEY `service_callback_id` (`service_callback_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='回调接口表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `activation_work_service_flow_log`
--

DROP TABLE IF EXISTS `activation_work_service_flow_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `activation_work_service_flow_log` (
  `log_id` varchar(128) NOT NULL COMMENT '服务id',
  `service_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '服务编号',
  `work_id` varchar(128) DEFAULT NULL COMMENT '工单id',
  `order_id` varchar(128) DEFAULT NULL COMMENT '订单id',
  `work_service_flow_id` varchar(128) DEFAULT NULL COMMENT '接口配置表id',
  `url` varchar(1024) DEFAULT NULL COMMENT '发送url',
  `request_header` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '接口入参_header',
  `request_body` mediumtext COMMENT '接口入参_body',
  `response_header` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '接口出参_header',
  `response_body` mediumtext COMMENT '接口出参_body',
  `callback_url` varchar(255) DEFAULT NULL COMMENT '回调地址',
  `callback_condition` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '回调查询参数',
  `retry_count` int DEFAULT NULL COMMENT '接口重试次数',
  `state` smallint DEFAULT NULL,
  `created_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `operator` varchar(128) DEFAULT NULL COMMENT '修改人',
  `tenant_id` varchar(64) DEFAULT NULL COMMENT '租户id',
  `version_tag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  `ext_params` json DEFAULT NULL COMMENT '扩展字段上下文',
  `response_http_code` int DEFAULT NULL COMMENT '调用接口返回的httpcode',
  PRIMARY KEY (`log_id`),
  KEY `activation_work_service_flow_log_callback_url_IDX` (`callback_url`,`callback_condition`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='接口日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `task_t_inst_work_order`
--

DROP TABLE IF EXISTS `task_t_inst_work_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `task_t_inst_work_order` (
  `wrkder_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'inst_work_order.wrkder_id',
  `source_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知来源表对应的唯一标识，如id',
  `order_id` varchar(128) DEFAULT NULL COMMENT '订单Id',
  `sopen_service_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '服务id',
  `enterprise_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户id',
  `user_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
  `open_time` datetime DEFAULT NULL COMMENT '开通时间',
  `open_is_done` tinyint NOT NULL DEFAULT '0' COMMENT '是否开通完成(0未开通，1开通完成)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1：正常, 2: 在处理中, 3：已删除',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` tinyint NOT NULL DEFAULT '0' COMMENT '版本号',
  `context` json DEFAULT NULL COMMENT '上下文内容信息',
  `ext` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '扩展备注等',
  `sum_fail` int DEFAULT '0',
  `version_tag` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`wrkder_id`),
  UNIQUE KEY `task_t_inst_work_order_unique` (`source_id`),
  KEY `idx_source_id` (`source_id`) USING BTREE,
  KEY `idx_open_is_done` (`open_is_done`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务开通定时任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `task_t_inst_work_order_history`
--

DROP TABLE IF EXISTS `task_t_inst_work_order_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `task_t_inst_work_order_history` (
  `wrkder_his_task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '唯一标识',
  `wrkder_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'inst_work_order.wrkder_id',
  `source_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知来源表对应的唯一标识，如id',
  `enterprise_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户id',
  `user_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
  `open_time` datetime DEFAULT NULL COMMENT '开通时间',
  `open_is_done` tinyint NOT NULL DEFAULT '0' COMMENT '是否开通完成(0未开通，1开通完成)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态  3：通知失败, 4：通知成功, 5：已删',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` tinyint NOT NULL DEFAULT '0' COMMENT '版本号',
  `context` json DEFAULT NULL COMMENT '上下文内容信息',
  `ext` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '扩展备注等',
  `version_tag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  `order_id` varchar(128) DEFAULT NULL COMMENT '订单id',
  `service_id` varchar(128) DEFAULT NULL COMMENT '服务id',
  PRIMARY KEY (`wrkder_his_task_id`),
  KEY `idx_source_id` (`source_id`) USING BTREE,
  KEY `idx_open_is_done` (`open_is_done`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务开通定时任务历史记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `task_t_message_notice`
--

DROP TABLE IF EXISTS `task_t_message_notice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `task_t_message_notice` (
  `notice_task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '唯一标识notice_task_id',
  `source_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知来源表对应的唯一标识，如id',
  `enterprise_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户id',
  `user_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
  `notice_way` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '1' COMMENT '发送通知方式：1站内信、2仅短信、3仅邮件',
  `receiver` varchar(32) DEFAULT '' COMMENT '接收方',
  `notice_item` tinyint DEFAULT '0' COMMENT '通知项(1：续订提醒通知, 2：退订通知)',
  `notice_time` datetime DEFAULT NULL COMMENT '通知时间',
  `notice_is_done` tinyint NOT NULL DEFAULT '0' COMMENT '是否通知完成(0未通知，1通知完成)',
  `goods_ids` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品列表，若有多个用逗号隔开',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1：正常, 2: 在处理中, 5：已删, 9：挂起',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` tinyint NOT NULL DEFAULT '0' COMMENT '版本号',
  `sum_fail` int NOT NULL DEFAULT '0' COMMENT '失败总次数',
  `context` json DEFAULT NULL COMMENT '上下文内容信息',
  `ext` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '扩展备注等',
  `version_tag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  PRIMARY KEY (`notice_task_id`),
  KEY `idx_source_id` (`source_id`) USING BTREE,
  KEY `idx_notice_is_done` (`notice_is_done`) USING BTREE,
  KEY `idx_receiver` (`receiver`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通知表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `task_t_message_notice_history`
--

DROP TABLE IF EXISTS `task_t_message_notice_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `task_t_message_notice_history` (
  `notice_his_task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '唯一标识notice_his_task_id',
  `notice_task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '唯一标识notice_task_id',
  `source_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知来源表对应的唯一标识，如id',
  `enterprise_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户id',
  `user_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
  `notice_way` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '1' COMMENT '发送通知方式：1站内信、2仅短信、3仅邮件',
  `receiver` varchar(32) DEFAULT '' COMMENT '接收方',
  `notice_item` tinyint DEFAULT '0' COMMENT '通知项(1：续订提醒通知, 2：退订通知)',
  `notice_time` datetime DEFAULT NULL COMMENT '通知时间',
  `notice_is_done` tinyint NOT NULL DEFAULT '0' COMMENT '是否通知完成(0未通知，1通知完成)',
  `goods_ids` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '商品列表，若有多个用逗号隔开',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态  3：通知失败, 4：通知成功, 5：已删, 9：挂起',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` tinyint NOT NULL DEFAULT '0' COMMENT '版本号',
  `context` json DEFAULT NULL COMMENT '上下文内容信息',
  `ext` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '扩展备注等',
  `version_tag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  PRIMARY KEY (`notice_his_task_id`),
  KEY `idx_notice_task_id` (`notice_task_id`) USING BTREE,
  KEY `idx_source_id` (`source_id`) USING BTREE,
  KEY `idx_notice_is_done` (`notice_is_done`) USING BTREE,
  KEY `idx_receiver` (`receiver`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通知历史记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `task_t_sub_expired_pause`
--

DROP TABLE IF EXISTS `task_t_sub_expired_pause`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `task_t_sub_expired_pause` (
  `sep_task_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '唯一标识id',
  `source_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知来源表对应的唯一标识，如id',
  `enterprise_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户id',
  `user_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
  `pause_time` datetime DEFAULT NULL COMMENT '暂停时间',
  `pause_is_done` tinyint NOT NULL DEFAULT '0' COMMENT '是否暂停完成(0未完成，1暂停完成)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态 1：正常, 2: 在处理中, 5：已删除, 9：挂起',
  `overlap` tinyint NOT NULL DEFAULT '1' COMMENT '是否可重叠 1：source_id可重叠, 0：不可重叠',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` tinyint NOT NULL DEFAULT '0' COMMENT '版本号',
  `sum_fail` int NOT NULL DEFAULT '0' COMMENT '失败总次数',
  `context` json DEFAULT NULL COMMENT '上下文内容信息',
  `ext` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '扩展备注等',
  `version_tag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  PRIMARY KEY (`sep_task_id`),
  KEY `idx_source_id` (`source_id`) USING BTREE,
  KEY `idx_pause_time` (`pause_time`) USING BTREE,
  KEY `idx_pause_is_done` (`pause_is_done`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='第三方支付到期未续费自动暂停';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `task_t_sub_expired_pause_history`
--

DROP TABLE IF EXISTS `task_t_sub_expired_pause_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `task_t_sub_expired_pause_history` (
  `sep_his_task_id` varchar(64) NOT NULL COMMENT '唯一标识id',
  `sep_task_id` varchar(64) NOT NULL COMMENT 'sub_t_expired_pause_task.唯一标识id',
  `source_id` varchar(128) NOT NULL COMMENT '通知来源表对应的唯一标识，如id',
  `enterprise_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '租户id',
  `user_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '更新人',
  `pause_time` datetime DEFAULT NULL COMMENT '暂停时间',
  `pause_is_done` tinyint NOT NULL DEFAULT '0' COMMENT '是否暂停完成(0未完成，1暂停完成)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态  3：失败, 4：成功, 5：已删',
  `overlap` tinyint NOT NULL DEFAULT '1' COMMENT '是否可重叠 1：source_id可重叠, 0：不可重叠',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `version` tinyint NOT NULL DEFAULT '0' COMMENT '版本号',
  `context` json DEFAULT NULL COMMENT '上下文内容信息',
  `ext` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '扩展备注等',
  `version_tag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  PRIMARY KEY (`sep_his_task_id`),
  KEY `idx_sep_task_id` (`sep_task_id`) USING BTREE,
  KEY `idx_source_id` (`source_id`) USING BTREE,
  KEY `idx_pause_time` (`pause_time`) USING BTREE,
  KEY `idx_pause_is_done` (`pause_is_done`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='执行完的历史表：第三方支付到期未续费自动暂停';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `worker_node`
--

DROP TABLE IF EXISTS `worker_node`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `worker_node` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT 'auto increment id',
  `HOST_NAME` varchar(64) NOT NULL COMMENT 'host name',
  `PORT` varchar(64) NOT NULL COMMENT 'port',
  `TYPE` int NOT NULL COMMENT 'node type: ACTUAL or CONTAINER',
  `LAUNCH_DATE` date NOT NULL COMMENT 'launch date',
  `MODIFIED` timestamp NOT NULL COMMENT 'modified time',
  `CREATED` timestamp NOT NULL COMMENT 'created time',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=918 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='DB WorkerID Assigner for DID Generator';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping routines for database 'member_sopen'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-07-16 14:54:46
