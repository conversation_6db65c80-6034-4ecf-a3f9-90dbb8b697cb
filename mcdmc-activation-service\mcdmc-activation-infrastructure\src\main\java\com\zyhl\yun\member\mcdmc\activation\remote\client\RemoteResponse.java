package com.zyhl.yun.member.mcdmc.activation.remote.client;

import lombok.Data;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 远程调用响应体
 *
 * <AUTHOR>
 * @since 2024/08/05 19:20
 */
@Data
public class RemoteResponse<R> {

    /**
     * http响应码
     */
    private Integer responseHttpCode;
    /**
     * 响应头
     */
    private Map<String, String> responseHeader;
    /**
     * 响应体
     * (同步时取该值)
     */
    private R responseData;
    /**
     * 响应体Mono
     * （异步时取该值）
     */
    private Mono<R> responseMono;

    /**
     * 远程调用异常
     */
    private Exception remoteException;
}
