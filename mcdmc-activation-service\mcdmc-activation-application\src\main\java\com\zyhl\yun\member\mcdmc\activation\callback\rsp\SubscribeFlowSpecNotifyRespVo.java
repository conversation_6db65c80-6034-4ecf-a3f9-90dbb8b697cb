package com.zyhl.yun.member.mcdmc.activation.callback.rsp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zyhl.yun.member.common.constants.NumberConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 10:42
 * @descrition
 */
@ToString
@Data
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.FIELD)
public class SubscribeFlowSpecNotifyRespVo {

    /**
     * 响应返回码
     */
    private String resultCode = NumberConstant.ZERO_STR;
    ;

    /**
     * 响应描述
     */
    @JsonIgnore
    private String resultDesc;

    /**
     * 响应描述
     */
    private SubscribeFlowSpecNotifyResp subscribeFlowSpecNotifyResp;

    /**
     * 构造函数
     *
     * @param subscribeFlowSpecNotifyResp
     */
    public SubscribeFlowSpecNotifyRespVo(SubscribeFlowSpecNotifyResp subscribeFlowSpecNotifyResp) {
        if (!NumberConstant.ZERO_STR.equals(subscribeFlowSpecNotifyResp.getResultCode())) {
            this.resultCode = subscribeFlowSpecNotifyResp.getResultCode();
            this.resultDesc = subscribeFlowSpecNotifyResp.getResultDesc();
            return;
        }
        this.subscribeFlowSpecNotifyResp = subscribeFlowSpecNotifyResp;
    }
}