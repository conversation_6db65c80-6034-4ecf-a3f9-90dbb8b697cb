package com.zyhl.yun.member.mcdmc.activation.remote.send.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/06/19 16:18
 */
@Data
public class OpenFreeFlowSyncReq {
    /**
     * 消息类型，IntergratedSalesOrderSyncReq
     */
    private String msgType;

    /**
     * 消息版本号
     */
    private String version;

    /**
     * 19位唯一渠道号：
     * 配置为1000032022110600003
     */
    private String uniChannelId;

    /**
     * 订单编码,接入方订单编码
     * 邮箱:139mail+唯一订单号
     * 和彩云:clouds+唯一订单号
     * 和彩云主平台:mpclouds+唯一订单号
     * 由order通过序列生成，并拼接mpclouds前缀，对应order.reserve8
     */
    private String orderId;

    /**
     * 订单创建时间
     * yyyyMMddHHmmss（GMT+8北京时间）
     * 填写order的创建时间
     */
    private String createTime;

    /**
     * 买家姓名
     * 填写手机号
     */
    private String buyerNickname;

    /**
     * 买家邮件地址
     */
    private String buyerEmail;

    /**
     * 买家留言
     */
    private String buyerMessage;

    /**
     * 订单备注
     * 当订购商品为合约机时，需填写机型名称。
     */
    private String tradeMemo;

    /**
     * 卖家留言
     */
    private String sellerMemo;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 销售人员id
     */
    private String sellerId;

    /**
     * 支付信息
     */
    private PaymentInfo paymentInfo;

    /**
     * 是否需省公司配送 --- 填写2
     * 1-需要；2-不需要
     */
    private String needDistribution;

    /**
     * 收货人联系信息
     */
    private ConsigneeInfo consigneeInfo;

    /**
     * "是否需要开发票
     * 特指归属移动公司的、单独开发票的业务类型（如流量直充）不含账单发票：1-需要 ；2-不需要
     * 固定写2
     */
    private String needInvoice = "2";

    /**
     * 发票信息
     */
    private String invoiceInfo;

    /**
     * "订单反馈地址，
     * 对应2.1.2 流量包业务订购反馈接口
     * 填写为vsbo开放的回调接口url
     */
    private String feedbackUrl;

    /**
     * "100000: 任我看
     * 100002: 四川业务
     * 100003 139邮箱
     * 100004 和彩云
     * 100005 和飞信
     * 100006和彩云主平台"
     */
    private String channelCode;

    /**
     * 目前仅针对四川业务才存在该字段
     * 订购二次确认信息
     * 不填
     */
    private ConfirmInfor confirmInfor;

    /**
     * 子订单集合
     */
    private List<SubIntegratedSalesOrder> subOrderList;

    /**
     * 支付信息
     */
    @Data
    public static class PaymentInfo {
        /**
         * 扣费类型
         * 0：统一支付扣费；
         * 1：话费扣费;
         * 2：第三方扣费；
         * 3：统一支付话费充值；
         * 4：线下支付；
         * 5：积分支付
         * 6：能力平台支付
         */
        private String chargeType = "2";

        /**
         * 订单金额，单位：分
         * 固定填写0
         */
        private Number totalFee = 0;

        /**
         * 优惠金额，单位：分
         * 固定填写0
         */
        private Number discountedPrice = 0;

        /**
         * 实际支付金额，单位：分
         * 固定填写0
         */
        private Number payment = 0;

        /**
         * "支付单编码
         * 客户在第三方平台支付完毕后，生成的支付单流水。当 chargeType=6时必填
         * 不填
         */
        private String paymentOrderID;

        /**
         * "支付时间
         * yyyyMMddHHmmss（GMT+8北京时间）"
         * 填写订单创建时间
         */
        private String paymentTime;

        /**
         * 支付方式
         * 当chargeType=2时填写，由电商渠道同步过来：
         * 取值详见《中国移动总部一级能力开放-平台接口规范v3.0.25（********）.pdf》附录十二：
         * 支付方式取值说明
         * 不填
         */
        private String paymentType;

        /**
         * "支付机构名称
         * 当合作渠道为中移自有电渠（渠道编码320）时必填
         * 不填
         */
        private String paymentOrganization;

        /**
         * "支付服务费
         * 单位：分；当合作渠道为中移自有电渠（渠道编码320）时必填
         * 不填
         */
        private Number paymentServiceFee;

        /**
         * "退费是否退服务费
         * 1：是；2：否；当合作渠道为中移自有电渠（渠道编码320）时必填
         * 不填
         */
        private String serviceFeeRefundable;
    }

    /**
     * 收货人联系信息
     */
    @Data
    public static class ConsigneeInfo {
        /**
         * 收货人姓名
         */
        private String name;

        /**
         * 收货人所在国家
         * 国家名称
         */
        private String country;

        /**
         * 收货人所在省份，省份名称
         */
        private String province;

        /**
         * 收货人所在市，市名称
         */
        private String city;

        /**
         * 收货人所在地区，地区名称
         */
        private String district;

        /**
         * 收货人的详细地址
         */
        private String address;

        /**
         * 标准地址id
         */
        private String addressId;

        /**
         * 邮编
         */
        private String postcode;

        /**
         * 收货人手机号码
         */
        private String mobilephone;

        /**
         * 收货人固话号码
         */
        private String landline;
    }

    /**
     * 确认信息
     *
     * <AUTHOR>
     * @since 2019-12-13
     */
    @Data
    public static class ConfirmInfor {
        private String confirmWay;

        private String confirmTime;

        private String confirmLog;
    }

    /**
     * 子订单项
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubIntegratedSalesOrder {
        /**
         * 子订单编码，接入方子订单编码，无子订单可填订单编码
         * 填写移动云盘侧orderItemID，长度问题可以通过去掉中间的分隔符处理
         */
        private String subOrderId;

        /**
         * 用户号码信息
         */
        private SubscriberInfo subscriberInfo;

        /**
         * 购买数量
         * 当numberOprType=01时填写，默认为1，单位：个
         * 不填
         */
        private Number quantity;

        /**
         * 号码单价，当numberOprType=01时填写，单位：分
         * 不填
         */
        private Number numberPrice;

        /**
         * SIM卡单价
         * 当numberOprType=01时填写，单位：分
         * 不填
         */
        private String simPrice;

        /**
         * 证件姓名
         * 当numberOprType=01时必填
         * 不填
         */
        private String legalName;

        /**
         * 证件类型
         * 当numberOprType=01时必填，
         * 取值参见《中国移动总部一级能力开放-平台接口规范v3.0.25（********）.pdf》
         * 附录三：证件类型
         * 不填
         */
        private String certificateType;

        /**
         * 证件号码
         * 当numberOprType=01时必填
         * 不填
         */
        private String certificateNo;

        /**
         * 订购商品信息
         */
        private GoodsInfo goodsInfo;

        /**
         * API服务列表
         * 不填
         */
        private String serviceIdList;

        /**
         * 营销活动信息
         * 不填
         */
        private ActivityInfo activityInfo;

        /**
         * 子订单总金额，单位：分
         * 填写0
         */
        private Number subtotalFee = 0;

        /**
         * 手工调整金额，单位：分
         * 填写0
         */
        private Number adjustFee = 0;

        /**
         * 调整原因
         * 不填
         */
        private String adjustReason;

        /**
         * 取值参见《中国移动总部一级能力开放
         * -平台接口规范v3.0.25（********）.pdf》附录四
         */
        private String orderStatus = "00";

        public SubIntegratedSalesOrder(String orderItemId, String account) {
            SubscriberInfo subInfo = new SubscriberInfo();
            subInfo.setNumber(account);
            this.subscriberInfo = subInfo;
            this.subOrderId = orderItemId;
        }

        /**
         * 业务号码信息
         *
         * <AUTHOR>
         * @since 2019-12-13
         */
        @Data
        public static class SubscriberInfo {
            /**
             * 业务号码操作类型
             * 01：新增用户选购号卡（支持同时办理子订单所含合约/套餐）；
             * 02：存量（老）用户办理合约/套餐/流量包等商品；03：（宽带）新装；
             * 04：（宽带）续订；05：（宽带）提速
             * 固定填写02
             */
            private String numberOprType = "02";

            /**
             * "业务号码
             * 手机号或宽带号
             * 填手机号
             */
            private String number;

            /**
             * 业务号码
             * 1-手机号
             * 2-宽带号码
             * 固定填写1
             */
            private String numberType = "1";

            /**
             * 业务号码品牌
             * 1- 全球通； 2- 动感地带；3- 神州行；
             * 4- 其他
             * 不填
             */
            private String numberbrand;
        }

        /**
         * 商品信息
         */
        @Data
        public static class GoodsInfo {
            private String goodsId;

            private String goodsTitle = "0元(1GB)";

            private Number amount;

            private Number price;

            private String goodsProvinceCode;

            private String goodsProvince;

            private String goodsCityCode;

            private String goodsCity;
        }

        /**
         * 活动信息
         */
        @Data
        public static class ActivityInfo {
            private String activityCode;

            private String activityType;

            private String activityName;

            private String activityDesc;
        }
    }
}
