package com.zyhl.yun.member.mcdmc.activation.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用响应码
 *
 * <AUTHOR>
 * @since 2023/05/27 11:48
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    SUCCESS("200", "请求成功"),
    VALIDATION_ERROR("10001", "字段校验异常"),

    //回调接口相关业务异常码
    UNKNOWN_CALLBACK_TYPE("1800000000", "Unknown callback type!"),
    PARAMETER_CHECK_ERROR("1809111400", "Parameter check error!"),
    EMPTY_BODY_ERROR("1809010029", "Empty request body!"),
    EMPTY_RESPONSE_ERROR("1809120016", "Empty response content!"),
    SIGN_CHECK_ERROR("20003", "签名失败"),
    SUCCESS_RESULT_ERROR("0", "ok"),
    /**
     * 订单不存在错误码
     */
    ORDER_NOT_EXISTS("1809020001", "Order is not exists"),
    LOG_NOT_FOUND("1809020001", "Order send Log is not found"),


    //系统异常相关
    LOCK_ERROR("2024062900000", "加锁异常"),

    UNKNOWN_ERROR("400", "未知错误"),

    SYSTEM_ERROR("1809112500", "Service internal error!");

    private final String code;
    private final String msg;

}
