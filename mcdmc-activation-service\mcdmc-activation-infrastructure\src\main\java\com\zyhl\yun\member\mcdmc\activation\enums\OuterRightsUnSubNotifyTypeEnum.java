package com.zyhl.yun.member.mcdmc.activation.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description:
 * @Author: linjia
 * @Date: 2024/11/25
 */
@AllArgsConstructor
@Getter
public enum OuterRightsUnSubNotifyTypeEnum {

    // -1 不发货
    NOTHING("-1", "标准发货"),


    // 0 通知外部权益方
    ONLY_NOTIFY_RIGHTSHOLDER("0", "通知外部权益方"),

    // 1 通知能开MQ
    ONLY_SEND_MQ("1", "通知能开MQ"),

    //2  通知外部权益方 和 能开mq
    NOTIFY_BOTH("2", "通知外部权益方和能开mq");


    private final String notifyCode;
    private final String name;



    /**
     * 根据 notifyCode 获取对应的枚举对象
     *
     * @param notifyCode 通知代码
     * @return 对应的枚举对象，如果找不到返回 null
     */
    public static OuterRightsUnSubNotifyTypeEnum getByNotifyCode(String notifyCode) {
        return Arrays.stream(values())
                .filter(enumValue -> enumValue.notifyCode.equals(notifyCode))
                .findFirst()
                .orElse(null);
    }


}

