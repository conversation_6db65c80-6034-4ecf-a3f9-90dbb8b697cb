package com.zyhl.yun.member.mcdmc.activation.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import javax.net.ssl.SSLException;
import java.time.Duration;
import java.util.Map;

/**
 * <AUTHOR>
 * @see
 * @since 2021/12/15 17:04
 */
@Slf4j
@Configuration
public class WebClientConfig {


    @Bean
    @Qualifier("webClient_mills")
    @Primary
    public WebClient webClient_mills() throws Exception {
        SslContext sslContext = SslContextBuilder
                .forClient()
                .trustManager(InsecureTrustManagerFactory.INSTANCE)
                .build();

        return WebClient.builder()
                .filter(getExchangeFilterFunction())
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(512 * 1024 * 1024))
                .clientConnector(new ReactorClientHttpConnector(reactorMillsHttpClient()
                        .secure(t -> t.sslContext(sslContext))))
//                .clientConnector(new ReactorClientHttpConnector(HttpClient.create().secure(t -> t.sslContext(sslContext) )))
                .build();
    }

    @Bean
    @Qualifier("webClient_second")
    public WebClient webClient_second() throws SSLException {
        SslContext sslContext = SslContextBuilder
                .forClient()
                .trustManager(InsecureTrustManagerFactory.INSTANCE)
                .build();
        return WebClient.builder()
                .filter(getExchangeFilterFunction())
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(512 * 1024 * 1024))
                .clientConnector(new ReactorClientHttpConnector(reactorSecondHttpClient()
                        .secure(t -> t.sslContext(sslContext))))
//                .clientConnector(new ReactorClientHttpConnector(HttpClient.create().secure(t -> t.sslContext(sslContext) )))
                .build();
    }

    @Bean
    @Qualifier("webClient_long_second")
    public WebClient webClient_long_second() throws SSLException {
        SslContext sslContext = SslContextBuilder
                .forClient()
                .trustManager(InsecureTrustManagerFactory.INSTANCE)
                .build();

        return WebClient.builder()
                .filter(getExchangeFilterFunction())
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(512 * 1024 * 1024))
                .clientConnector(new ReactorClientHttpConnector(reactorLongSecondHttpClient()
                        .secure(t -> t.sslContext(sslContext))))
//                .clientConnector(new ReactorClientHttpConnector(HttpClient.create().secure(t -> t.sslContext(sslContext) )))
                .build();
    }


    public HttpClient reactorMillsHttpClient() {
        ConnectionProvider provider = ConnectionProvider.builder("reactorMillsHttpClient")
                .maxConnections(1200)
                .pendingAcquireMaxCount(2200)
                .pendingAcquireTimeout(Duration.ofSeconds(10))
                .maxIdleTime(Duration.ofSeconds(10))
                .maxLifeTime(Duration.ofSeconds(10))
                .lifo()
                .build();

        return HttpClient
                .create(provider)
                .keepAlive(true)
                .responseTimeout(Duration.ofSeconds(10))
                .tcpConfiguration(tcp ->
                        tcp.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000)
                                .option(ChannelOption.TCP_NODELAY, true)
                                .doOnConnected(conn -> {
                                    conn.addHandlerLast(new ReadTimeoutHandler(10));
                                    conn.addHandlerLast(new WriteTimeoutHandler(10));
                                })
                );
    }

    public HttpClient reactorSecondHttpClient() {
        ConnectionProvider provider = ConnectionProvider.builder("reactorSecondHttpClient")
                .maxConnections(1700)
                .pendingAcquireMaxCount(1700)
                .pendingAcquireTimeout(Duration.ofSeconds(11))
                .maxIdleTime(Duration.ofSeconds(11))
                .maxLifeTime(Duration.ofSeconds(11))
                .lifo()
                .build();

        return HttpClient
                .create(provider)
                .keepAlive(true)
                .responseTimeout(Duration.ofSeconds(11))
                .tcpConfiguration(tcp ->
                        tcp.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 11000)
                                .option(ChannelOption.TCP_NODELAY, true)
                                .doOnConnected(conn -> {
                                    conn.addHandlerLast(new ReadTimeoutHandler(11));
                                    conn.addHandlerLast(new WriteTimeoutHandler(11));
                                })
                );
    }

    public HttpClient reactorLongSecondHttpClient() {
        ConnectionProvider provider = ConnectionProvider.builder("reactorLongSecondHttpClient")
                .maxConnections(2500)
                .pendingAcquireMaxCount(2500)
                .pendingAcquireTimeout(Duration.ofSeconds(20))
                .maxIdleTime(Duration.ofSeconds(20))
                .lifo()
                .build();

        return HttpClient
                .create(provider)
                .keepAlive(true)
                .responseTimeout(Duration.ofSeconds(20))
                .tcpConfiguration(tcp ->
                        tcp.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000)
                                .option(ChannelOption.TCP_NODELAY, true)
                                .doOnConnected(conn -> {
                                    conn.addHandlerLast(new ReadTimeoutHandler(20));
                                    conn.addHandlerLast(new WriteTimeoutHandler(10));
                                })
                );
    }


    public static SslContext sslContext() {
        try {
            return SslContextBuilder
                    .forClient()
                    .trustManager(InsecureTrustManagerFactory.INSTANCE)
                    .build();
        } catch (SSLException e) {
            log.error("sslContext error", e);
        }
        return null;
    }


    public  ExchangeFilterFunction getExchangeFilterFunction() {
        ExchangeFilterFunction function = (request, next) -> {
            Map<String, String> map = MDC.getCopyOfContextMap();
            return next.exchange(request)
                    .doOnNext(value -> {
                        if (map != null) {
                            MDC.setContextMap(map);
                        }
                    });
        };
        return function;
    }
}
