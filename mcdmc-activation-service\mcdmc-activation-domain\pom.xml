<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zyhl.yun.member.mcdmc.activation</groupId>
        <artifactId>mcdmc-activation-service</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>mcdmc-activation-domain</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.zyhl.yun.member.mcdmc.activation</groupId>
            <artifactId>mcdmc-activation-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.zyhl.yun.member.mcdmc.member</groupId>
            <artifactId>mcdmc-member-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.zyhl.yun.member.mcdmc.task</groupId>
            <artifactId>mcdmc-task-common</artifactId>
            <version>1.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.zyhl.yun.member.mcdmc.order</groupId>
            <artifactId>mcdmc-order-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.zyhl.yun.member.mcdmc.support</groupId>
            <artifactId>mcdmc-support-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.zyhl.yun.member.mcdmc.mqconsumer</groupId>
            <artifactId>mcdmc-mq-consumer-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${jedis.version}</version> <!-- 请根据需要选择合适的版本 -->
        </dependency>


    </dependencies>
</project>