package com.zyhl.yun.member.common.domain.framework;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 领域调度异常
 *
 * <AUTHOR>
 * @since 2024/07/11 16:05
 */
@Data
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DomainScheduleException extends RuntimeException {

    /**
     * 调度操作类型
     * WRITE：写操作
     * READ：读操作
     */
    private final String operation;
    /**
     * 原始异常信息
     */
    private final Exception originException;

    @Getter
    @AllArgsConstructor
    public enum ScheduleOperation {

        WRITE("WRITE"),

        READ("READ"),
        COUNT("GET_COUNT");

        private final String operation;
    }

    @Override
    public String toString() {
        return "domain operation is " + operation +
                ",detail exception is :" + originException.toString();
    }

}
