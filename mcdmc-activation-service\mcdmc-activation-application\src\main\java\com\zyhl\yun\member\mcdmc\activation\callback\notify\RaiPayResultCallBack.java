package com.zyhl.yun.member.mcdmc.activation.callback.notify;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.yun.member.common.Constant;
import com.zyhl.yun.member.common.ErrorCode;
import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.mono.Objects;
import com.zyhl.yun.member.common.domain.serviceid.SyncAppOrderServiceId;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.receive.domain.dto.QueryGoodsInstanceExtendCondition;
import com.zyhl.yun.member.goodsinstance.enums.GoodsInstanceStateEnum;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.BaseCallback;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.CallbackContext;
import com.zyhl.yun.member.mcdmc.activation.callback.req.RaiPayResultNotifyReq;
import com.zyhl.yun.member.mcdmc.activation.callback.req.RaiPayResultNotifyResp;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.CallbackException;
import com.zyhl.yun.member.mcdmc.activation.result.BaseResult;
import com.zyhl.yun.member.mcdmc.activation.result.ResultCode;
import com.zyhl.yun.member.order.common.constants.SyncOrderConstant;
import com.zyhl.yun.member.order.domain.SyncAppOrderDo;
import com.zyhl.yun.member.product.common.constants.RaiNotifyEquityConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 功能描述。畅享会员退订、续订回调
 * 包名称:  com.zyhl.yun.member.mcdmc.activation.callback.notify
 * 类名称:  RaiPayResultCallBack
 * 类描述:  。
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/10/21 19:47
 */
@Component
@Slf4j
public class RaiPayResultCallBack extends BaseCallback<RaiPayResultNotifyReq, RaiPayResultNotifyResp> {

    @Override
    protected Class<RaiPayResultNotifyReq> getNotifyReqClass() {
        return RaiPayResultNotifyReq.class;
    }

    @Override
    protected void doCheck(CallbackContext<RaiPayResultNotifyReq, RaiPayResultNotifyResp> callbackContext) throws CallbackException {
        RaiPayResultNotifyReq req = callbackContext.getCallbackReq();
        if (CharSequenceUtil.isEmpty(req.getOutOrderId())) {
            log.error(ErrorCode.ILLEGAL_PARAMETER + ":" + ErrorCode.ILLEGAL_PARAMETER_MESSAGE + "outOrderId is null.");
            throw new ServiceException(ErrorCode.ILLEGAL_PARAMETER, ErrorCode.ILLEGAL_PARAMETER_MESSAGE + "outOrderId is null.");
        }

        if (CharSequenceUtil.isEmpty(req.getPayResult())) {
            log.error(ErrorCode.ILLEGAL_PARAMETER + ":" + ErrorCode.ILLEGAL_PARAMETER_MESSAGE + "payResult is null.");
            throw new ServiceException(ErrorCode.ILLEGAL_PARAMETER, ErrorCode.ILLEGAL_PARAMETER_MESSAGE + "payResult is null.");
        }

        // payResult检查下边2个状态：  0: 续订成功   3：退订成功
        if (!req.getPayResult().equals(RaiNotifyEquityConstant.PayResult.PAY_SUCCESS) &&
                !req.getPayResult().equals(RaiNotifyEquityConstant.PayResult.UNSUB_SUCCESS)) {
            log.error(ErrorCode.ILLEGAL_PARAMETER + ":" + ErrorCode.ILLEGAL_PARAMETER_MESSAGE + "payResult must 0 or 3.");
            throw new ServiceException(ErrorCode.ILLEGAL_PARAMETER, ErrorCode.ILLEGAL_PARAMETER_MESSAGE + "payResult must 0 or 3.");

        }
    }

    @Override
    protected void doNotify(CallbackContext<RaiPayResultNotifyReq, RaiPayResultNotifyResp> callbackContext) {
        RaiPayResultNotifyReq callbackReq = callbackContext.getCallbackReq();
        GoodsInstanceDo goodsInstanceExtendDo = checkGoodsInstance(callbackContext);
        if (GoodsInstanceStateEnum.PAUSE != goodsInstanceExtendDo.getStateEnum()
                && GoodsInstanceStateEnum.NORMAL != goodsInstanceExtendDo.getStateEnum()) {
            log.error(ErrorCode.SUBSCRIPTION_STATUS_NOT_RENEW_CODE + ":" + ErrorCode.SUBSCRIPTION_STATUS_NOT_RENEW_DESC);
            throw new ServiceException(ErrorCode.SUBSCRIPTION_STATUS_NOT_RENEW_CODE, ErrorCode.SUBSCRIPTION_STATUS_NOT_RENEW_DESC);
        }

        SyncAppOrderDo syncAppOrderDo = new SyncAppOrderDo();
        syncAppOrderDo.setOutOrderId(callbackReq.getOutOrderId());
        syncAppOrderDo.setReturnStatus(Constant.SUCCESS);

        if (RaiNotifyEquityConstant.PayResult.PAY_SUCCESS.equals(callbackReq.getPayResult())) {
            if (GoodsInstanceStateEnum.NORMAL == goodsInstanceExtendDo.getStateEnum()) {
                syncAppOrderDo.setActionID(SyncOrderConstant.SyncAppOrderActionID_RENEW_SERVICE);
            } else {
                syncAppOrderDo.setActionID(SyncOrderConstant.SyncAppOrderActionID_ACTIATE_SERVICE);
            }
        } else if (RaiNotifyEquityConstant.PayResult.UNSUB_SUCCESS.equals(callbackReq.getPayResult())) {
            syncAppOrderDo.setActionID(SyncOrderConstant.SyncAppOrderActionID_STOP_SERVICE);
        }

        DomainServiceContext domainServiceContext = new DomainServiceContext(SyncAppOrderServiceId.SYNC_APP_ORDER);
        domainServiceContext.putInstance(syncAppOrderDo);
        domainServiceContext.writeAndFlush();
    }

    /**
     * 获取对应实例记录
     *
     * @param callbackContext
     * @return
     */
    private GoodsInstanceDo checkGoodsInstance(CallbackContext<RaiPayResultNotifyReq, RaiPayResultNotifyResp> callbackContext) {
        RaiPayResultNotifyReq callbackReq = callbackContext.getCallbackReq();
        //会员订单号
        String orderId = callbackReq.getOutOrderId() + Constant.RAI_ORDER_SUFFIX;
        //查询对应商品实例
        DomainServiceContext domainServiceContext = new DomainServiceContext(null);
        QueryGoodsInstanceExtendCondition queryGoodsInstanceExtendCondition = new QueryGoodsInstanceExtendCondition();
        // todo 待确认 zzf
        queryGoodsInstanceExtendCondition.setOrderId(orderId);
        GoodsInstanceDo goodsInstanceExtendDo = domainServiceContext.readFirst(queryGoodsInstanceExtendCondition, GoodsInstanceDo.class);
        if (Objects.isNull(goodsInstanceExtendDo)) {
            log.error(ErrorCode.ORDER_NOT_EXISTS + ":" + ErrorCode.ORDER_NOT_EXISTS_MESSAGE);
            throw new ServiceException(ErrorCode.ORDER_NOT_EXISTS, ErrorCode.ORDER_NOT_EXISTS_MESSAGE);
        }
        return goodsInstanceExtendDo;
    }

    @Override
    protected boolean isSuccess(RaiPayResultNotifyReq notifyReq) {
        return true;
    }

    @Override
    protected BaseResult getSuccessResult() {
        return BaseResult.sc(ResultCode.SUCCESS);
    }

    @Override
    protected String getSearchCondition(RaiPayResultNotifyReq notifyReq) {
        return null;
    }

    @Override
    protected RaiPayResultNotifyResp buildNotifyRsp
            (CallbackContext<RaiPayResultNotifyReq, RaiPayResultNotifyResp> callbackContext, BaseResult result) {
        RaiPayResultNotifyResp notifyResp = new RaiPayResultNotifyResp();
        notifyResp.setMsgType(RaiNotifyEquityConstant.PAY_RESULT_NOTIFY_MSGTYPE);
        notifyResp.setMsgVer(RaiNotifyEquityConstant.PAY_RESULT_NOTIFY_MSGVER);
        if (result.isSuccess()) {
            notifyResp.setReturnCode(RaiNotifyEquityConstant.RETURNCODESUCCESS);
        } else {
            notifyResp.setReturnCode(RaiNotifyEquityConstant.RETURNCODEFAIL);
        }
        return notifyResp;
    }

    @Override
    protected SendOperation getSendOperation() {
        return null;
    }

    @Override
    public String execCallback(String uri, Map<String, String> reqHeader, String callbackReqStr) {
        CallbackContext<RaiPayResultNotifyReq, RaiPayResultNotifyResp> callbackContext = CallbackContext.newInstance(
                uri, reqHeader, callbackReqStr, this::convertReq);
        BaseResult handleResult = getSuccessResult();
        try {
            // 校验参数
            this.doCheck(callbackContext);
            // 执行回调逻辑
            this.doNotify(callbackContext);
        } catch (CallbackException e1) {
            if (!ResultCode.SUCCESS.getCode().equals(e1.getErrorCode())) {
                // 只有不成功的情况下才需要改值
                handleResult = BaseResult.fail(e1.getErrorCode(), e1.getMessage());
            }
        } catch (Exception e) {
            log.error("回调接口发生异常，异常信息为：", e);
            handleResult = BaseResult.fail(ResultCode.SYSTEM_ERROR);
        }
        RaiPayResultNotifyResp notifyRsp = buildNotifyRsp(callbackContext, handleResult);
        // 返回响应结果字符串
        String rspStr = convertRsp2Str(notifyRsp);
        callbackContext.updateLogAndWorkDo(handleResult.isSuccess(), rspStr);
        return rspStr;
    }
}
