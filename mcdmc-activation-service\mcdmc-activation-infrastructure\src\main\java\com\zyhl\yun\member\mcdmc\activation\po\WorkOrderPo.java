package com.zyhl.yun.member.mcdmc.activation.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/06/04 14:10
 * 服务工单表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("activation_work_order")
public class WorkOrderPo extends BasePo {

    /**
     * 工单ID
     */
    @TableId
    private String workId;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 服务id
     */
    private String serviceCode;

    /**
     * 工单属性
     */
    private String workAttrs;

    /**
     * 事务流水号id（用于去重）
     *
     * @apiNote 唯一主键
     */
    private String transactionId;

    /**
     * 工单状态,具体状态值见
     * {@link com.zyhl.yun.member.mcdmc.activation.enums.WorkOrderStateEnum WorkOrderStateEnum}
     */
    private Integer state;

    /**
     * 流程进度，多个流程时会更新该进度，从0开始
     */
    private Integer flowProgress;

    /**
     * 工单失败次数
     */
    private Integer failCount = 0;
}
