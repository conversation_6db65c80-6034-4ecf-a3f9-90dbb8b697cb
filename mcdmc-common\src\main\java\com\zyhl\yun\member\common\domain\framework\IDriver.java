package com.zyhl.yun.member.common.domain.framework;

import java.io.Serializable;
import java.util.List;

/**
 * 对象持久化驱动接口
 *
 * <AUTHOR>
 * @date 2024/05/17 10:10
 */
public interface IDriver<T extends Serializable> extends Serializable {


    /**
     * 删除领域对象
     *
     * @param domainEntity
     * @return
     */
    int delete(Serializable domainEntity);


    /**
     * 删除领域对象列表
     *
     * @param domainEntityList
     * @return
     */
    int deleteList(List<? extends Serializable> domainEntityList);

    /**
     * 根据主键删除领域对象
     *
     * @param pk
     * @return
     */
    int deleteByPk(Serializable pk);

    /**
     * 根据主键列表删除领域对象
     *
     * @param pkList
     * @return
     */
    int deleteByPkList(List<? extends Serializable> pkList);

    /**
     * 写入
     *
     * @param domainEntity
     * @return
     */
    int write(Serializable domainEntity);


    /**
     * 写入列表
     *
     * @param domainEntityList
     * @return
     */
    int writeList(List<? extends Serializable> domainEntityList);

    /**
     * 根据主键读取
     *
     * @param pk
     * @return
     */
    T readByPk(Serializable pk);

    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    List<? extends T> readByCondition(Serializable condition);


    /**
     * 根据条件查询总数
     *
     * @param condition
     * @return
     */
    long getCount(Serializable condition);

}
