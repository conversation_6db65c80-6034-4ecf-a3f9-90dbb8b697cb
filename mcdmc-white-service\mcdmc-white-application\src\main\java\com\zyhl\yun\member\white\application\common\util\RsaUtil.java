
package com.zyhl.yun.member.white.application.common.util;

import org.apache.commons.io.IOUtils;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * 提供RSA加解密算法
 *
 * <AUTHOR>
 * @date 2019/5/21
 */
public class RsaUtil {


    private static final String RSA_ALGORITHM = "RSA";
    private static final int DEFAULT_KEY_SIZE = 2048;
    public static final String CIPHER_ALGORITHM = "RSA/ECB/PKCS1Padding";

    public static String getCipherAlgorithm() {
        return CIPHER_ALGORITHM;
    }

    public static String getRsaAlgorithm() {
        return RSA_ALGORITHM;
    }

    private RsaUtil() {
    }

    /**
     * RSA私钥加密
     *
     * @param data
     * @param key  RSA私钥
     * @return
     * @throws Exception
     */
    public static byte[] encryptByPrivateKey(byte[] data, String key) throws Exception {
        byte[] keyBytes = Base64.decode(key);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8KeySpec);
        Cipher cipher = Cipher.getInstance(getCipherAlgorithm());
        cipher.init(1, privateKey);
        return cipher.doFinal(data);
    }


    public static KeyPair createKeyPair() {
        return createKeyPair(DEFAULT_KEY_SIZE);
    }

    public static KeyPair createKeyPair(int keySize) {
        //为RSA算法创建一个KeyPairGenerator对象
        KeyPairGenerator kpg;
        try {
            kpg = KeyPairGenerator.getInstance(getRsaAlgorithm());
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalArgumentException("No such algorithm-->[" + getRsaAlgorithm() + "]");
        }

        //初始化KeyPairGenerator对象,密钥长度
        kpg.initialize(keySize);
        //生成密匙对
        return kpg.generateKeyPair();
    }

    /**
     * 得到Url安全的公钥
     */
    public static String getBase64PublicKey(KeyPair keyPair) {
        // 得到公钥
        Key publicKey = keyPair.getPublic();
        return org.apache.commons.codec.binary.Base64.encodeBase64URLSafeString(publicKey.getEncoded());
    }

    /**
     * 得到Url不安全的公钥， 如公钥不放置url中，建议使用，减少接入端不必要的字符替换
     */
    public static String getBase64UrlUnSafePublicKey(KeyPair keyPair) {
        Key publicKey = keyPair.getPublic();
        return org.apache.commons.codec.binary.Base64.encodeBase64String(publicKey.getEncoded());
    }

    public static String getBase64PrivateKey(KeyPair keyPair) {
        //得到私钥
        Key privateKey = keyPair.getPrivate();
        return org.apache.commons.codec.binary.Base64.encodeBase64URLSafeString(privateKey.getEncoded());
    }

    /**
     * 得到公钥
     *
     * @param publicKey 密钥字符串（经过base64编码）
     */
    public static RSAPublicKey getPublicKey(String publicKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        //通过X509编码的Key指令获得公钥对象
        KeyFactory keyFactory = KeyFactory.getInstance(getRsaAlgorithm());
        X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(org.apache.commons.codec.binary.Base64.decodeBase64(publicKey));
        return (RSAPublicKey) keyFactory.generatePublic(x509KeySpec);
    }

    /**
     * 得到私钥
     *
     * @param privateKey 密钥字符串（经过base64编码）
     */
    public static RSAPrivateKey getPrivateKey(String privateKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        //通过PKCS#8编码的Key指令获得私钥对象
        KeyFactory keyFactory = KeyFactory.getInstance(getRsaAlgorithm());
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(org.apache.commons.codec.binary.Base64.decodeBase64(privateKey));
        return (RSAPrivateKey) keyFactory.generatePrivate(pkcs8KeySpec);
    }

    /**
     * 公钥加密
     *
     * @param data      待加密数据
     * @param publicKey 加密公钥
     */
    public static String publicEncrypt(String data, RSAPublicKey publicKey) {
        try {
            Cipher cipher = Cipher.getInstance(getRsaAlgorithm());
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            return org.apache.commons.codec.binary.Base64.encodeBase64URLSafeString(rsaSplitCodec(cipher, Cipher.ENCRYPT_MODE, data.getBytes(StandardCharsets.UTF_8), publicKey.getModulus().bitLength()));
        } catch (Exception e) {
            throw new RuntimeException("加密字符串[" + data + "]异常", e);
        }
    }

    /**
     * 私钥解密
     *
     * @param data       待解密数据
     * @param privateKey 解密私钥
     */

    public static String privateDecrypt(String data, RSAPrivateKey privateKey) {
        try {
            Cipher cipher = Cipher.getInstance(getRsaAlgorithm());
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            return new String(rsaSplitCodec(cipher, Cipher.DECRYPT_MODE, org.apache.commons.codec.binary.Base64.decodeBase64(data), privateKey.getModulus().bitLength()), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("解密字符串[" + data + "]时异常", e);
        }
    }

    /**
     * 私钥加密
     *
     * @param data       待加密数据
     * @param privateKey 加密私钥
     */
    public static String privateEncrypt(String data, RSAPrivateKey privateKey) {
        try {
            Cipher cipher = Cipher.getInstance(getRsaAlgorithm());
            cipher.init(Cipher.ENCRYPT_MODE, privateKey);
            return org.apache.commons.codec.binary.Base64.encodeBase64URLSafeString(rsaSplitCodec(cipher, Cipher.ENCRYPT_MODE, data.getBytes(StandardCharsets.UTF_8), privateKey.getModulus().bitLength()));
        } catch (Exception e) {
            throw new RuntimeException("加密字符串[" + data + "]时遇到异常", e);
        }
    }

    /**
     * 公钥解密
     *
     * @param data      待解密数据
     * @param publicKey 解密公钥
     */
    public static String publicDecrypt(String data, RSAPublicKey publicKey) {
        try {
            Cipher cipher = Cipher.getInstance(getRsaAlgorithm());
            cipher.init(Cipher.DECRYPT_MODE, publicKey);
            return new String(rsaSplitCodec(cipher, Cipher.DECRYPT_MODE, org.apache.commons.codec.binary.Base64.decodeBase64(data), publicKey.getModulus().bitLength()), StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("解密字符串[" + data + "]时遇到异常", e);
        }
    }

    private static byte[] rsaSplitCodec(Cipher cipher, int opmode, byte[] datas, int keySize) {
        int maxBlock;
        if (opmode == Cipher.DECRYPT_MODE) {
            maxBlock = keySize / 8;
        } else {
            maxBlock = keySize / 8 - 11;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] buff;
        int i = 0;
        try {
            while (datas.length > offSet) {
                if (datas.length - offSet > maxBlock) {
                    buff = cipher.doFinal(datas, offSet, maxBlock);
                } else {
                    buff = cipher.doFinal(datas, offSet, datas.length - offSet);
                }
                out.write(buff, 0, buff.length);
                i++;
                offSet = i * maxBlock;
            }
        } catch (Exception e) {
            throw new RuntimeException("加解密阀值为[" + maxBlock + "]的数据时发生异常", e);
        }
        byte[] resultDatas = out.toByteArray();
        IOUtils.closeQuietly(out);
        return resultDatas;
    }

}
