package com.zyhl.yun.member.mcdmc.activation.remote.send.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 停车退订配置
 *
 * <AUTHOR>
 * @since 2024/06/18 17:49
 * url=/config/vsbo/serviceConfig/parkSync/baseUrl+/config/vsbo/serviceConfig/parkSync/refundUri
 */
@Data
@Configuration
@ConfigurationProperties("platform.park-unsub")
public class ParkUnSubProperties {
    /**
     * 停车方分配渠道号
     * /config/vsbo/serviceConfig/parkSync/channelid
     */
    private String channelId;
    /**
     * 签名加密key
     * /config/vsbo/serviceConfig/parkSync/key
     */
    private String key;
}
