package com.zyhl.yun.member.mcdmc.activation.service;

import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.activation.WorkOrderServiceId;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.DbFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowLogDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/09/21 13:24
 */
@Slf4j
@Service
public class CustomService {
    public String flowGetUserId(String uri, String searchCondition) {
        DomainServiceContext workFlowLogContext = new DomainServiceContext(WorkOrderServiceId.QUERY_ACTIVATION);
        // 查询发货回调日志
        BaseCondition baseCondition = new BaseCondition();
        baseCondition.putCondition(DbFieldConstants.CALLBACK_CONDITION, searchCondition);
        baseCondition.putCondition(DbFieldConstants.CALLBACK_URL, uri);
        WorkServiceFlowLogDo sendFlowLogDo = workFlowLogContext.readLast(baseCondition, WorkServiceFlowLogDo.class);
        if (null == sendFlowLogDo) {
            log.error("根据回调条件={}和回调地址={}未获取到对应的发货接口请求日志", searchCondition, uri);
            return null;
        }
        return sendFlowLogDo.getUserId();
    }
}
