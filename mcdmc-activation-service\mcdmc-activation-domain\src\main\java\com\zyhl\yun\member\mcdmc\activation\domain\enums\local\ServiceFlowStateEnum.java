package com.zyhl.yun.member.mcdmc.activation.domain.enums.local;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务流程状态枚举
 *
 * <AUTHOR>
 * @since 2024/07/22 15:15
 */
@Getter
@AllArgsConstructor
public enum ServiceFlowStateEnum {

    INVALID(0, "未生效"),
    VALID(1, "生效中");
    private final Integer code;
    private final String desc;

    public boolean codeEquals(Integer anotherCode) {
        return this.code.equals(anotherCode);
    }
}
