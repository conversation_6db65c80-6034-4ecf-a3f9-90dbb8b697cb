package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.check;

import com.zyhl.yun.member.mcdmc.activation.domain.enums.rai.CheckActionTypeEnum;
import org.springframework.stereotype.Component;

/**
 * 重推流程
 * com.huawei.jaguar.vsbo.strategy.differencedIgital.RePushHandler#doDigitalInvoke
 *
 * <AUTHOR>
 * @since 2024/10/08 17:29
 */
@Component
public class RaiOrderRePushIFace extends RaiOrderIncreaseIFace {

    @Override
    protected CheckActionTypeEnum getCheckActionTypeEnum() {
        // 重推标识
        return CheckActionTypeEnum.ORDER_REPUSH;
    }
}
