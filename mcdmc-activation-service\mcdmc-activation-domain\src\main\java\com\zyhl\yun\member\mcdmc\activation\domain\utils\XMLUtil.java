package com.zyhl.yun.member.mcdmc.activation.domain.utils;

import com.zyhl.yun.member.mcdmc.activation.exception.ServiceException;
import com.zyhl.yun.member.mcdmc.activation.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.xml.sax.*;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;
import javax.xml.transform.Source;
import javax.xml.transform.sax.SAXSource;
import java.io.*;
import java.util.*;

/**
 * xml工具类
 *
 * <AUTHOR>
 * @version [版本号, 2017-6-29]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Slf4j
public class XMLUtil {

    private static Map<String, JAXBContext> CACHE = new HashMap<>(100);


    /**
     * 将SAXParser作为线程变量
     */
    private static ThreadLocal<SAXParser> SAX_PARSER = ThreadLocal.withInitial(() -> {
        SAXParser saxParser = null;
        try {
            SAXParserFactory sax = SAXParserFactory.newInstance();
            // 禁用dtd -- 防止xml内、外部实体攻击
            sax.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            sax.setNamespaceAware(false);
            saxParser = sax.newSAXParser();
        } catch (ParserConfigurationException e) {
            log.info("ParserConfigurationException", e);
        } catch (SAXNotRecognizedException e) {
            log.info("SAXNotRecognizedException", e);
        } catch (SAXNotSupportedException e) {
            log.info("SAXNotSupportedException", e);
        } catch (SAXException e) {
            log.info("SAXException", e);
        }

        return saxParser;
    });

    /**
     * java对象转换为xml文件
     *
     * @param obj  xml文件路径
     * @param load java对象.Class
     * @return xml文件的String
     */
    public static String beanToXml(Object obj, Class<?> load) {
        try {
            JAXBContext context = CACHE.get(load.getSimpleName());
            if (null == context) {
                context = JAXBContext.newInstance(load);
                CACHE.put(load.getSimpleName(), context);
            }

            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, false);
            marshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8");
            marshaller.setProperty(Marshaller.JAXB_FRAGMENT, true);

            StringWriter writer = new StringWriter();
            marshaller.marshal(obj, writer);
            return writer.toString();
        } catch (JAXBException e) {
            log.error("Failed to parse javaBean to xml String", e);
            throw new ServiceException(ResultCode.SYSTEM_ERROR);
        }

    }

    /**
     * xml文件配置转换为对象
     *
     * @param xmlString xmlString
     * @param load      load
     * @param <T>       a
     * @return <T> 泛型
     * @see [类、类#方法、类#成员]
     */
    public static <T> T xmlToBean(String xmlString, Class<T> load) {
        JAXBContext context = CACHE.get(load.getSimpleName());
        if (null == context) {
            try {
                context = JAXBContext.newInstance(load);
            } catch (JAXBException e) {
                log.error("Failed to parse xml String to javaBean", e);
                throw new ServiceException(ResultCode.SYSTEM_ERROR);
            }
            CACHE.put(load.getSimpleName(), context);
        }

        try {
            StringReader reader = new StringReader(xmlString);
            XMLReader xmlReader = SAX_PARSER.get().getXMLReader();
            Source source = new SAXSource(xmlReader, new InputSource(reader));
            Unmarshaller unmarshaller = context.createUnmarshaller();
            return (T) unmarshaller.unmarshal(source);
        } catch (JAXBException e) {
            log.error("Cannot load xsd,xsd name is " + load.getName() + ".xsd", e);
            throw new ServiceException(ResultCode.SYSTEM_ERROR);
        } catch (SAXException e) {
            log.error("Cannot load xsd,xsd name is " + load.getName() + ".xsd", e);
            throw new ServiceException(ResultCode.SYSTEM_ERROR);
        }
    }

}
