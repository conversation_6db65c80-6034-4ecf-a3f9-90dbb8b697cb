FROM b08ypcg01-53ab9ddf.ecis.guangzhou-2.cmecloud.cn/prod/mcdmc-openresty-service:1.0.0

RUN addgroup --gid 1368 hcaiyun && adduser --uid 1368 --gid 1368 hcaiyun
WORKDIR /home/<USER>

#请修改为应用端口
EXPOSE 80 443

ENV TZ=Asia/Shanghai

#请修改应用名
ENV applicationName=mcdmc-openresty-service-starter
#按照应用实际需求修改

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
RUN sed -i s/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g /etc/apt/sources.list
RUN apt update && apt upgrade -y && apt install curl -y
#RUN apt install procps -y
##RUN apt install vim -y
#RUN apt install util-linux -y
#RUN apt install iputils-ping -y
#RUN apt install net-tools -y
##RUN apt install apache2-utils -y

# 安装必要的工具和依赖
RUN apt-get update && apt-get install -y \
    logrotate \
    && rm -rf /var/lib/apt/lists/*

#创建自定义lua脚本目录

RUN mkdir /usr/local/openresty/lualib/aspire
RUN mkdir /usr/local/openresty/temp
COPY KCS_OPENRESTY/prod/lua /usr/local/openresty/lualib/aspire
COPY KCS_OPENRESTY/prod/conf/nginx.conf /usr/local/openresty/nginx/conf/nginx.conf

COPY KCS_OPENRESTY/prod/conf/entrypoint.sh /entrypoint.sh

COPY KCS_OPENRESTY/prod/ssl/_.caiyun.feixin.10086.cn.pem /usr/local/openresty/ssl/_.caiyun.feixin.10086.cn.pem
COPY KCS_OPENRESTY/prod/ssl/_.caiyun.feixin.10086.cn.key /usr/local/openresty/ssl/_.caiyun.feixin.10086.cn.key

# 创建日志目录
RUN mkdir -p /logs/nginx/web
RUN chown -R hcaiyun:hcaiyun /usr/local/openresty
RUN chown -R hcaiyun:hcaiyun /logs/nginx/web
RUN chown -R hcaiyun:hcaiyun /entrypoint.sh

RUN chmod +x /entrypoint.sh
USER hcaiyun

CMD ["/entrypoint.sh"]
#CMD  /usr/local/openresty/nginx/sbin/nginx  -g  "daemon off;"





