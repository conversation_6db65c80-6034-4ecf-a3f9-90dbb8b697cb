package com.zyhl.yun.member.mcdmc.activation;

import cn.hutool.core.lang.UUID;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.activation.WorkOrderServiceId;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @since 2024/07/11 15:38
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ActivationApplication.class)
@AutoConfigureMockMvc
public class RemoteFlowTest {

    @Test
    public void testBmsuiteFlow() {
//新建当前服务开通领域的上下文
        DomainServiceContext workServiceContext = new DomainServiceContext(WorkOrderDo.class, WorkOrderServiceId.INSERT_WORK_ORDER);
        //设置extParams 只支持json
        String extParamsStr = "{\"goodsInstanceId\":\"12testSub-hhjkjk-sjknc1\"," +
                "\"orderID\":\"************_9eaf5794c98841dd8ae7bcda21cd02e3_CloudSpaceContractSubscribeOrder\"," +
                "\"goodsId\":\"bmsuite空间\",\"msisdn\":\"13501503672\"," +
                "\"userDomainId\":\"12595632568523\",\"userId\":\"************\"," +
                "\"nationCode\":\"86\",\"notifyType\":1," +
                "\"effectiveTime\":\"20240102100540\",\"expireTime\":\"20240208150504\"," +
                "\"proServiceId\":\"500000000000000000000001117\"}";

        //设置工单实例
        WorkOrderDo workOrderDo = workServiceContext.newInstance(WorkOrderDo.class);
        workOrderDo.setOrderId("************_9eaf5794c98841dd8ae7bcda21cd02e3_CloudSpaceContractSubscribeOrder");
        workOrderDo.setUserId("************");
        workOrderDo.setServiceCode("bmsuiteServiceCode");
        workOrderDo.setTransactionId(UUID.randomUUID().toString(true));
        workOrderDo.setWorkAttrs(extParamsStr);
        // 写入工单
        int result = workServiceContext.writeAndFlush();

        Assert.assertEquals(1, result);
    }
}
