package com.zyhl.yun.member.mcdmc.activation.callback.req;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 订购定向流量结果通知请求
 *
 * <AUTHOR>
 * @apiNote 一级能开免流开通回调请求体
 * @since 2019-12-13
 */
@Data
@XmlRootElement(name = "OPFlowPkgSubsFdbkReq")
@XmlAccessorType(XmlAccessType.FIELD)
public class OpenFreeFlowSyncNotifyReq {

    /**
     * 消息类型
     */
    @XmlElement(name = "MsgType", required = true)
    private String msgType;

    /**
     * 接口消息版本号, 如：1.0.0
     */
    @XmlElement(name = "Version", required = true)
    private String version;

    /**
     * 原订单ID
     */
    @XmlElement(required = true)
    private String orderId;

    /**
     * 原子订单ID
     */
    @XmlElement(required = true)
    private String subOrderId;

    /**
     * 业务订单时间
     */
    @XmlElement(required = true)
    private String orderTime;

    /**
     * 订购结果码：
     * 1 订购成功
     * 2 订购失败
     * 3 订购反馈超时关闭
     */
    @XmlElement(required = true)
    private String orderResult;

    /**
     * 备注信息
     */
    @XmlElement(required = false)
    private String memo;

    /**
     * 反馈时间
     */
    @XmlElement(required = true)
    private String respTime;

    @Override
    public String toString() {
        return "OpenFreeFlowSyncNotifyReq [msgType=" +
                msgType +
                ",version=" +
                version +
                ", orderId=" +
                orderId +
                ", subOrderId=" +
                subOrderId +
                ", orderTime=" +
                orderTime +
                ", orderResult=" +
                orderResult +
                ", memo=" +
                memo +
                ", respTime=" +
                respTime +
                "]";
    }
}
