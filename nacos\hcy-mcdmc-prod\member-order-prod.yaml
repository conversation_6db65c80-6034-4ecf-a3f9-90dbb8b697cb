spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    cachePrepStmts: true
    prepStmtCacheSize: 300
    prepStmtCacheSqlLimit: 2048
    useServerPrepStmts: true
    useLocalSessionState: true
    rewriteBatchedStatements: true
    cacheResultSetMetadata: true
    cacheServerConfiguration: true
    elideSetAutoCommits: true
    maintainTimeStats: false
    url: ******************************************************************************************************************************************************************************
    username: vip_refactor_4_user@yun_member_refactor#ypcg_member_prod
    password: ENC(sQM6sJqhdpEMFzVd2mHn6ZvwdYKvmFXAUKJV+AFhS6U=)

  cache:
    type: redis
    redis:
      time-to-live: '3000'
  redis:
    host: ************
    password: ENC(w6V7yZOWZpUY/lwBHTocvrkznf/GAyWbDSDEePmFuvI=)
#    password: 'aB=6RBSXtGg!+g(-@g'
    port: 32066
    database: 0
    max-redirects: 3
    timeout: 3000
    jedis:
      pool:
        # 最大连接，单位：个。当前tomcat配置线程数为200，考虑每秒内有一半线程在操作redis，且每个线程操作不超过100ms，故线程数设置为50
        maxTotal: 200
        #最大空闲连接，单位：个
        maxIdle: 200
        # 最小空闲连接，单位：个
        minIdle: 20
        # 最大获取连接等待时间，单位：毫秒
        maxWaitMillis: 3000
        #空闲连接逐出时间，大于该值的空闲连接一直未被使用则会被释放，单位：毫秒
        minEvictableIdleTimeMillis: 30000
        #空闲连接探测时间间隔，单位：毫秒。 例如系统的空闲连接探测时间配置为30s，则代表每隔30s会对连接进行探测，如果30s内发生异常的连接，
        #经过探测后会进行连接排除。根据连接数的多少进行配置，如果连接数太大，配置时间太短，会造成请求资源浪费。
        timeBetweenEvictionRunsMillis: 30000
        #向资源池借用连接时是否做连接有效性检测（ping），检测到的无效连接将会被移除。对于业务连接极端敏感的，并且性能可以接受的情况下，
        #可以配置为True，一般来说建议配置为False，启用连接空闲检测。
        testOnBorrow: true
        # 是否在空闲资源监测时通过ping命令监测连接有效性，无效连接将被销毁。
        testWhileIdle: true
        # 向资源池归还连接时是否做连接有效性检测（ping），检测到无效连接将会被移除。耗费性能
        testOnReturn: false
        # 连接空闲检测的时间间隔，单位：毫秒
        timeout: 3000
        # 连接建立超时时间，单位：毫秒
        connectTimeout: 3000
        # 空闲连接检测时，每次检测的连接数
        numTestsPerEvictionRun: 3
  http:
    buffer:
      byte-threshold: 409600

mybatis-plus:
  # 启动时是否检查MyBatis XML文件是否存在
  check-config-location: true
  mapper-locations: classpath:mapper/*.xml
  #开启sql日志
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

inner-service:
  list:
    - serviceIp: 127.0.0.1
      serviceName: member-vip
      servicePort: 18085
      servicePath: /member-vip
    - serviceIp: 127.0.0.1
      serviceName: member-order
      servicePort: 18189
      servicePath: /member-order
    - serviceIp: 127.0.0.1
      serviceName: member-product
      servicePort: 18089
      servicePath: /member-product
    - serviceIp: 127.0.0.1
      serviceName: member-payment
      servicePort: 18083
      servicePath: ''
    - serviceIp: 127.0.0.1
      serviceName: member-support
      servicePort: 18078
      servicePath: /member-support
    - serviceIp: 127.0.0.1
      serviceName: member-task
      servicePort: 18081
      servicePath: /member-task
rocketmq:
  producer:
    new:
      nameServerAddr: '************:31563;************:31563'
      nameSpace: 'MQ_INST_1678440605886_rVJEkxlB'
      accessKey: 727f6f51df47489facab1910178b3fe7
      secretKey: 826e4606957a4b2c8ca709d9eac805cc
      producerGroup: producerGroupNew
    old:
      nameServerAddr: '************:9876;************:9876'
      nameSpace: ""
      accessKey: xxx
      secretKey: xxx
      producerGroup: producerGroupOld
  advert:
    adverTopic: adverPlatform
    userActionTag: userActionTag
    oldAdverTopic: adverPlatform
    oldUserActionTag: userActionTag
  task:
    activationTopic: TOPIC_LOCAL_MEMBER-ACTIVATION
    activationTag: 'open'
    topic: TOPIC_LOCAL_MEMBER-BENIFIT_COMMON
    tag: '*'
  schedule:
    topic: TOPIC_LOCAL_MEMBER-BENIFIT_BILL-MONTHLY-DELIVERY
    tag: GID_LOCAL_MEMBER-BENIFIT_BILL-MONTHLY-DELIVERY
  vsbo-independency:
    oldVsboIndependenceTopic: VSBO_INDEPENDENCE
    oldVsboIndependenceTag: '*'
  vsbo:
    oldVsboTopic: vsbo
    oldVsboTag: 'subscribe'
  lockStock:
    lockStockTopic: TOPIC_MEMBER-BENIFIT_GROUP-LOCK-STOCK
    lockStockTag: '*'
    timeout: 3000
    delayLevel: 18
  goodsInstance:
    goodsInstanceTopic: goods-instance-event
    goodsInstanceTag: '*'
    oldGoodsInstanceTopic: goods-instance-event
    oldGoodsInstanceTag: '*'

############################# 订单同步 ####################
sync-app-order:
  reverseUnSubReturnStatus: 319004
  sevenDayUnSubReturnStatus: 319024
  syncAppOrderTopic: TOPIC_LOCAL_MEMBER-BENIFIT_SYNC-APP-ORDER
  syncAppOrderTag: syncAppOrder_tag
  syncAppBillPaySwitch: true

############################# 退费签名密钥 ####################
refund-config:
  billPpAndRefundNotifyPublicKey: 'MIIBtzCCASwGByqGSM44BAEwggEfAoGBAP1/U4EddRIpUt9KnC7s5Of2EbdSPO9EAMMeP4C2USZpRV1AIlH7WT2NWPq/xfW6MPbLm1Vs14E7gB00b/JmYLdrmVClpJ+f6AR7ECLCT7up1/63xhv4O1fnxqimFQ8E+4P208UewwI1VBNaFpEy9nXzrith1yrv8iIDGZ3RSAHHAhUAl2BQjxUjC8yykrmCouuEC/BYHPUCgYEA9+GghdabPd7LvKtcNrhXuXmUr7v6OuqC+VdMCz0HgmdRWVeOutRZT+ZxBxCBgLRJFnEj6EwoFhO3zwkyjMim4TwWeotUfI0o4KOuHiuzpnWRbqN/C/ohNWLx+2J6ASQ7zKTxvqhRkImog9/hWuWfBpKLZl6Ae1UlZAFMO/7PSSoDgYQAAoGARVTkD6s8Z9VrX46eVNtG43hUUb2LNKRRFdGL5n2cxDs+c0TtePwpBI89bFRWIPQtVnQBpto1yAT35gz5gKlYJYJbU67xlQM15wId2qQXA2SIQZGnhCbhVGqlBIYS2x4N91mGDQHKf0JRwbND6pFlcDkrfUWDwu/Criyco3eobHc='
  hcloud: 'MIIBuDCCASwGByqGSM44BAEwggEfAoGBAP1/U4EddRIpUt9KnC7s5Of2EbdSPO9EAMMeP4C2USZpRV1AIlH7WT2NWPq/xfW6MPbLm1Vs14E7gB00b/JmYLdrmVClpJ+f6AR7ECLCT7up1/63xhv4O1fnxqimFQ8E+4P208UewwI1VBNaFpEy9nXzrith1yrv8iIDGZ3RSAHHAhUAl2BQjxUjC8yykrmCouuEC/BYHPUCgYEA9+GghdabPd7LvKtcNrhXuXmUr7v6OuqC+VdMCz0HgmdRWVeOutRZT+ZxBxCBgLRJFnEj6EwoFhO3zwkyjMim4TwWeotUfI0o4KOuHiuzpnWRbqN/C/ohNWLx+2J6ASQ7zKTxvqhRkImog9/hWuWfBpKLZl6Ae1UlZAFMO/7PSSoDgYUAAoGBAKfCPjjIeEV2MJsOKVl/F2FyWu9+zX/dRM2DjAibRDRx5306T6pPidSWJW23b2FfaV28th/+UxZ8l34LI5uRurUl+OvKgApIglUYS68fUXEtk30vNeQOL6zu2bBDhbF1rL1dIcdmGro+R/OnjArR+AKnLyU9IJKPUKxSXC21+phs'
  outSystemId: HCLOUD
  privateKey: MIIBSwIBADCCASwGByqGSM44BAEwggEfAoGBAP1/U4EddRIpUt9KnC7s5Of2EbdSPO9EAMMeP4C2USZpRV1AIlH7WT2NWPq/xfW6MPbLm1Vs14E7gB00b/JmYLdrmVClpJ+f6AR7ECLCT7up1/63xhv4O1fnxqimFQ8E+4P208UewwI1VBNaFpEy9nXzrith1yrv8iIDGZ3RSAHHAhUAl2BQjxUjC8yykrmCouuEC/BYHPUCgYEA9+GghdabPd7LvKtcNrhXuXmUr7v6OuqC+VdMCz0HgmdRWVeOutRZT+ZxBxCBgLRJFnEj6EwoFhO3zwkyjMim4TwWeotUfI0o4KOuHiuzpnWRbqN/C/ohNWLx+2J6ASQ7zKTxvqhRkImog9/hWuWfBpKLZl6Ae1UlZAFMO/7PSSoEFgIUMMJtfle8MwIjv9ecejp51QjG3Rg=
channelStrongCheckList: 1,2,3,4,5,6,7,8
#############################################################
service:
  config:
    channelStrongCheckList: Honour_QGuo_1Month-New|SuperMemberMcs_Times|utcontract945|Space_Jfen_1M_100G|Space_Jfen_1M_200G|Space_Jfen_1M_500G|pdf_1times_3_month|voice_90min_3_month|silver_changx_vip_1month|silver_changx_vip_6month|silver_changx_vip_12month|silver_changx_vip_monthly|gold_changx_vip_1month|gold_changx_vip_6month|gold_changx_vip_12month|gold_changx_vip_monthly|diamond_changx_vip_1month|diamond_changx_vip_monthly|mangguo_vip_1month|mangguo_vip_monthly|meitushow_vip_1month|meitushow_vip_monthly|beautycamera_vip_1month|beautycamera_vip_monthly|youku_vip_1month|youku_vip_monthly|iqiyi_optimization_vip_monthly|iqiyi_platinum_vip_monthly|iqiyi_gold_vip_monthly|iqiyi_optimization_vip_1month|iqiyi_platinum_vip_1month|iqiyi_gold_vip_1month|oneplusone_proprietary_vip_bilibili_1year|oneplusone_proprietary_vip_qq_music_1year|oneplusone_proprietary_vip_iqy_1year|oneplusone_proprietary_vip_tencent_video_1year|oneplusone_proprietary_vip_mangguo_tv_1year|maoyan_vip_monthly|maoyan_vip_1month|changying_4kvip_monthly|changying_4kvip_4_1month|changdu_basic_1month|changdu_basic_monthly|changdu_plus_1month|changdu_plus_monthly|changlive_plus_1month|changlive_plus_monthly|changlive_basic_monthly|changlive_basic_1month|changxing_plus_monthly|changxing_basic_monthly|changxing_plus_1month|changxing_basic_1month|picture_ai_tool_android_1month|picture_ai_tool_ios_1month|text_ai_tools_android_1month|text_ai_tools_ios_1month|activity_01_yuan_200G_1month|silver_vip_3mouth_tk240715|gold_vip_3mouth_TG24071101|diamond_vip_1mouth_TG24071101|yn_cloud_tutoring_member_monthly|liaoning_province_vip_9_dot_9_monthly|liaoning_province_vip_19_dot_9_monthly|liaoning_province_vip_29_dot_9_monthly|liaoning_province_vip_39_dot_9_monthly|douyu_member_monthly
    gotone-present-product: GoTone-HuBei
    accessSourceEnum: 1,2,3,4,5,6,7,8
    integralRangeValue: 0-65536

# 这里是计费回调地址（重构后vsbo地址）
member-order:
  return-url: http://************:30080/cloudSEE/openApi/notifyOrderPayResult
  cancel-url: http://************:30080/cloudSEE/openApi
  return-apple-url: http://mcdmc-vip.yun.139.com/cloudSEE/openApi/notifyTpctPayResult
  cancel-apple-url: http://mcdmc-vip.yun.139.com/cloudSEE/openApi/notifyTpctPayResult


member-product:
  availableAccessSources: 1,2,3,4,5,6,7,8
  availableBuyType: 1,2,3,78,79,86
  availableEvenType: 1,2,3,4
  defaultStartIndex: 0
  querySubScribeDefaultValue: 199


management:
  endpoints:
    jmx:
      exposure:
        include: "*"
    web:
      exposure:
        include: "*"

market-pass:
  deliverTopic: TOPIC_MEMBER-BENIFIT_GROUP_BUY-RESULT-DELIVER
  groupBuySuccessSmsTemplate: Hcy#applepay2024071601
  groupBuyFailedSmsTemplate: Hcy#applepay2024071602

# 计费中心
tpct-url: http://************:8066

third:
  notify:
    jFTelNumberPrivateKey: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKx4bJ26q1kXjaglk7jX/Vz55iRczXtUvppqxkCez0KshzQEoxrwuCirpo0L3Xxqxp6ehpkEeoDxRBAYTHt6e9aj4apiszD6uMhgV4nnYyFMAip4t13Cce+ngZoIi0/eZlbaXaB9zvsqHqgZs10MuDsXQdTjVb+TNRNt6LuHOAvzAgMBAAECgYBvg3WHDlkXw7jdzV4UF+NDqHh7bl2VrpeGtB73TTdpPrA0nALxuk74TPPlIpTUix1dFdalPVYP5O9DENLyy6EheQ9bi0yvPjohQ0Wlmvd6/D4f8qeTym0ojZg2CuHNh4NlwzOvf4X9M9h6PNzAImRd5PAWekQKfKctKwYm0HxiQQJBANa3MYJVsFTMVdzozkc1XQNmTPTRkhG+jYsHpUI4siNhi5QCdLzTmZAu9r/arxw8EwRNdMH6J/Vk/FQQulVbgzECQQDNodK+s3Hdgje72qbjRLl4m4Bsi1cszFpG6Cd63bY272ZOgNpqqjpt+w/wkwovbwt9ejWDCFxesaR4Lc5h5VBjAkEAw122h5/OLKor6jBOGM6+TiyUEyJo5QpcIceYH7QUqkrERK0jaPijvmRtEc46DNfAeea8OQ+Tsh3r1dXOpYH50QJBALqBSHTgqqjev5Api+5b2eDzITLR2tpt6n+fdpdw2iDPV7piEjhmdyJjcRyhML6+9+vx2PEktyrH8kVyPlr6MsMCQDCIM2ll/4b9VExoLaQgSIYv5NZvzz3UCpOgVF0o57HL46rpiqCYqI9zNDOvBHb/T1kzcC3nGNK0hQW6VWILons=
    checkSign: true
    signPublicKey: MIIBuDCCASwGByqGSM44BAEwggEfAoGBAP1/U4EddRIpUt9KnC7s5Of2EbdSPO9EAMMeP4C2USZpRV1AIlH7WT2NWPq/xfW6MPbLm1Vs14E7gB00b/JmYLdrmVClpJ+f6AR7ECLCT7up1/63xhv4O1fnxqimFQ8E+4P208UewwI1VBNaFpEy9nXzrith1yrv8iIDGZ3RSAHHAhUAl2BQjxUjC8yykrmCouuEC/BYHPUCgYEA9+GghdabPd7LvKtcNrhXuXmUr7v6OuqC+VdMCz0HgmdRWVeOutRZT+ZxBxCBgLRJFnEj6EwoFhO3zwkyjMim4TwWeotUfI0o4KOuHiuzpnWRbqN/C/ohNWLx+2J6ASQ7zKTxvqhRkImog9/hWuWfBpKLZl6Ae1UlZAFMO/7PSSoDgYUAAoGBAJPlZtAu1Dm4vpmY5/UXpwNQtu78crV2gYwbpn2XmUyF1B5SE7hq4Y7S9AFo2IYtqwAyuFhUx0hs5V55VdZ8PMtu4QkWbWicNAF1zwQjxFRA1zaPudpXW3nk3mLre/01DkC8Y4bGIciI4YXl8QVRYZtWvlqLvqxw6kBAhPwtQAnM

# VSBO服务地址
vsbo-url: 'http://***************:8080'

server:
  tomcat:
    threads:
      max: 400
      min-spare: 50
    #等待队列长度，当可分配的线程数全部用完之后，后续的请求将进入等待队列等待，等待队列满后则拒绝处理，默认100
    accept-count: 20000
    #最大可被连接数
    max-connections: 10000
    #连接超时时间，该值需要大于nginx的keepalive_timeout，否则nginx会主动断开连接，默认60000
    connection-timeout: 70000

manage:
  neAuth: TWpBeU5UQXhNRFF3TkRFME1qZzFNN0xlTXEwMXdSakJkak9DeWJZSTN4OTlWVnZpVzBiQWhyTDlXNFJtbzZTag==
# 报文压缩
server.compressFlag: true
# http2开启
http2.flag: true

# 全球通配置项
gotone-config:
  # 商品全球通等级
  allGotoneGoodsGrade:
    - '1'
    - '2'
    - '3'

goods-prior:
  priorMap:
    -1: ['liaoning_province_vip_9_dot_9_monthly','liaoning_province_vip_19_dot_9_monthly','liaoning_province_vip_29_dot_9_monthly','liaoning_province_vip_39_dot_9_monthly','iqiyi_optimization_vip_monthly','changying_vip_3_monthly','changying_vip_5_monthly','changying_vip_8_monthly','changying_vip_10_monthly']