package com.zyhl.yun.member.user.impl;

import com.zyhl.yun.member.domain.equity.doamin.PhoneTypeDo;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.vip.utils.UserInfoOperatorUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/03/18 16:06
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class UserServiceTest {

    @Resource
    private UserInfoOperatorUtil userInfoOperatorUtil;

    @Test
    public void testGetUserInfo() {
        UserDo userDo = new UserDo();
        userDo.setMsisdn("13800138000");
        PhoneTypeDo phoneType = userInfoOperatorUtil.getPhoneType(userDo.getMsisdn());
        log.info("PhoneType: {}", phoneType);
    }
}
