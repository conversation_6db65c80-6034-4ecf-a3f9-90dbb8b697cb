package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.free_flow;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.yun.member.common.constants.JiyunConstants;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.JiyunServiceId;
import com.zyhl.yun.member.domain.jiyun.domain.FlowSpecSubscriptionDo;
import com.zyhl.yun.member.dto.QueryJiyunFlowSpecCondition;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.CustomDidGenerator;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.SendTemplate;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.JiyunProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.AddOrderReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.AddOrderResp;
import com.zyhl.yun.member.mcdmc.activation.util.JiyunAuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

/**
 * 集运免流下单addorder接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/3 10:37
 */
@Slf4j
@Component
public class JiyunFlowSpecIFace extends SendTemplate<AddOrderReq, AddOrderResp> {


    @Resource
    private JiyunAuthUtils jiyunAuthUtils;

    @Resource
    private JiyunProperties jiyunProperties;

    @Override
    protected void doCheckParam(InterfaceContext<AddOrderReq, AddOrderResp> interfaceContext) throws FlowTerminationException {
        // 校验参数
        WorkOrderDo workOrderDo = interfaceContext.getWorkOrderDo();
        if (CharSequenceUtil.isBlank(interfaceContext.getComSendReq().getProductId())
                || CharSequenceUtil.isBlank(interfaceContext.getComSendReq().getMsisdn())
                || CharSequenceUtil.isBlank(interfaceContext.getComSendReq().getOrderID())) {
            throw new FlowTerminationException(this.getClass(), workOrderDo, "productId,msisdn、orderId不能为空");
        }
    }

    @Override
    protected Map<String, String> getRequestHeader(InterfaceContext<AddOrderReq, AddOrderResp> interfaceContext) {
        return jiyunAuthUtils.getJiyunRequestHeader(interfaceContext.getInterfaceReqObjStr(), JiyunConstants.JiyunURL.ADDORDER);
    }

    @Override
    protected AddOrderReq getRequestBody(InterfaceContext<AddOrderReq, AddOrderResp> interfaceContext) {
        AddOrderReq addOrderReq = new AddOrderReq();
        String sourceGoodsId = interfaceContext.getComSendReq().getProductId();
        addOrderReq.setActivityId(jiyunProperties.getActivityIdBySkuId(sourceGoodsId));
        addOrderReq.setSourceOrderNo(interfaceContext.getWorkOrderDo().getOrderId());
        addOrderReq.setTelephone(interfaceContext.getComSendReq().getMsisdn());
        addOrderReq.setSourceGoodsId(sourceGoodsId);
        addOrderReq.setSourceApp(jiyunProperties.getJiyunSourceApp());
        return addOrderReq;
    }

    @Override
    protected Class<AddOrderReq> getReqClass() {
        return AddOrderReq.class;
    }

    @Override
    protected Class<AddOrderResp> getRspClass() {
        return AddOrderResp.class;
    }


    @Override
    protected boolean isSuccess(AddOrderResp rsp) {
        return Boolean.TRUE;
    }

    @Override
    protected void doBusiComplete(InterfaceContext<AddOrderReq, AddOrderResp> interfaceContext) {
        //请求成功后处理更新数据库
        buildSubscribeWithRequest(interfaceContext);
        super.doBusiComplete(interfaceContext);
    }

    private void buildSubscribeWithRequest(InterfaceContext<AddOrderReq, AddOrderResp> interfaceContext) {
        DomainServiceContext jiyunServiceContext = new DomainServiceContext(JiyunServiceId.MERGE_JIYUN_FLOW_SPEC);
        QueryJiyunFlowSpecCondition qryCondition = new QueryJiyunFlowSpecCondition();
        String userId = interfaceContext.getComSendReq().getUserId();
        qryCondition.setUserId(userId);
        AddOrderReq addOrderReq = interfaceContext.getInterfaceReqObj();
        qryCondition.setSourceApp(addOrderReq.getSourceApp());
        qryCondition.setSourceGoodsId(interfaceContext.getComSendReq().getProductId());
        qryCondition.setSourceOrderNo(interfaceContext.getComSendReq().getOrderID());
        FlowSpecSubscriptionDo flowSpecSubDo = jiyunServiceContext.readFirst(qryCondition, FlowSpecSubscriptionDo.class);
        if (flowSpecSubDo == null) {
            flowSpecSubDo = new FlowSpecSubscriptionDo();
            flowSpecSubDo.setCreateTime(new Date());
            flowSpecSubDo.setResourceId(CustomDidGenerator.generateId());
            flowSpecSubDo.setUserId(userId);
            flowSpecSubDo.setSourceApp(addOrderReq.getSourceApp());
            flowSpecSubDo.setSourceGoodsId(interfaceContext.getComSendReq().getProductId());
            flowSpecSubDo.setSourceOrderNo(interfaceContext.getComSendReq().getOrderID());
        }
        AddOrderResp addOrderResp = interfaceContext.getInterfaceRspObj();
        flowSpecSubDo.setBizCode(addOrderResp.getCode());
        flowSpecSubDo.setBizDesc(addOrderResp.getMessage());
        flowSpecSubDo.setActivityId(interfaceContext.getInterfaceReqObj().getActivityId());
        flowSpecSubDo.setSkuCode(jiyunProperties.getJiyunSkuCode());
        flowSpecSubDo.setAccount(interfaceContext.getComSendReq().getMsisdn());
        flowSpecSubDo.setGoodsId(jiyunProperties.getJiyunGoodsID());
        flowSpecSubDo.setSubChannelId(interfaceContext.getComSendReq().getSubChannelId());
        flowSpecSubDo.setChannelId(interfaceContext.getComSendReq().getChannelId());
        flowSpecSubDo.setStatus(JiyunConstants.JiyunSubscriptionStatus.CODE.equals(addOrderResp.getCode()) ? JiyunConstants.JiyunSubscriptionStatus.CALLBACK : JiyunConstants.JiyunSubscriptionStatus.FAIL);
        flowSpecSubDo.setUpdateTime(new Date());
        jiyunServiceContext.putInstance(flowSpecSubDo);
        jiyunServiceContext.writeAndFlush();
        jiyunAuthUtils.updateResource(flowSpecSubDo);
    }

    /**
     * 有回调请求时重写该方法，返回回调时对应的订单id
     *
     * @param context 接口上下文
     */
    @Override
    protected String getCallbackCondition(InterfaceContext<AddOrderReq, AddOrderResp> context) {
        return context.getWorkOrderDo().getOrderId();
    }

    @Override
    protected boolean isNeedCacheFlowLog() {
        // 缓存发货流水日志，加快回调处理速度
        return true;
    }
}