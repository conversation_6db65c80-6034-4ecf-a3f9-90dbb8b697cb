package com.zyhl.yun.member.mcdmc.activation.remote.send.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 订单核减配置
 *
 * <AUTHOR>
 * @since 2024/07/28 16:20
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "platform.rai.order-reduction")
public class RaiOrderReductionProperties {
}
