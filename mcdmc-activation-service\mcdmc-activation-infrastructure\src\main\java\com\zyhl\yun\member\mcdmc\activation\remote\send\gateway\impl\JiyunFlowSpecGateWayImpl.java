package com.zyhl.yun.member.mcdmc.activation.remote.send.gateway.impl;

import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.ErrorCode;
import com.zyhl.yun.member.common.JsonUtil;
import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.util.WebClientUtil;
import com.zyhl.yun.member.dto.GivenPrdOrderRelQueryReq;
import com.zyhl.yun.member.dto.GivenPrdOrderRelQueryResp;
import com.zyhl.yun.member.mcdmc.activation.remote.send.gateway.JiyunFlowSpecGateWay;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.JiyunProperties;
import com.zyhl.yun.member.mcdmc.activation.util.JiyunAuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/5 18:22
 * @descrition
 */
@Slf4j
@Service
public class JiyunFlowSpecGateWayImpl implements JiyunFlowSpecGateWay {


    @Resource
    private WebClientUtil webClientUtil;

    @Resource
    private JiyunProperties jiyunProperties;

    @Resource
    private JiyunAuthUtils jiyunAuthUtils;


    @Override
    public GivenPrdOrderRelQueryResp givenPrdOrderRelQuery(GivenPrdOrderRelQueryReq givenPrdOrderRelQueryReq) {
        if (log.isDebugEnabled()) {
            log.debug(">>>>>>>>calling the method[JiyunFlowSpecServices.givenPrdOrderRelQuery], args[givenPrdOrderRelQueryReq]="
                    + givenPrdOrderRelQueryReq);
        }
        GivenPrdOrderRelQueryResp givenPrdOrderRelQueryResp = null;
        String jiyunUrl = jiyunProperties.getJiyunURL().concat(jiyunProperties.getGivenPrdOrderRelQueryURL());

        String requestJson = JSONUtil.toJsonStr(givenPrdOrderRelQueryReq);
        Map<String, String> headers = jiyunAuthUtils.getJiyunRequestHeader(requestJson,
                jiyunProperties.getGivenPrdOrderRelQueryURL());
        try {

            String result = webClientUtil.postRequest(jiyunUrl, requestJson,
                    headers, MediaType.APPLICATION_JSON, String.class).block();
            givenPrdOrderRelQueryResp = JsonUtil.fromJson(result, GivenPrdOrderRelQueryResp.class);
        } catch (Exception e) {
            String description = "call JiyunFlowSpecServices.givenPrdOrderRelQuery failed!";
            log.error(description, e);
            throw new ServiceException(ErrorCode.SERVICE_INTERNAL_ERROR, ErrorCode.SERVICE_INTERNAL_ERROR_MESSAGE);
        }

        if (log.isDebugEnabled()) {
            log.debug("givenPrdOrderRelQueryResp = " + givenPrdOrderRelQueryResp);
        }
        return givenPrdOrderRelQueryResp;
    }

}