package com.zyhl.yun.member.mcdmc.activation.driver;

import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.common.domain.framework.BaseLocalDriver;
import com.zyhl.yun.member.common.domain.framework.DomainEntityPersistenceWrapper;
import com.zyhl.yun.member.mcdmc.activation.convertor.WorkOrderConvertor;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.conditions.QueryWorkOrderCondition;
import com.zyhl.yun.member.mcdmc.activation.mapping.WorkOrderMethodMapping;
import com.zyhl.yun.member.mcdmc.activation.po.WorkOrderPo;
import com.zyhl.yun.member.mcdmc.activation.repository.WorkOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/06/04 15:05
 */
@Slf4j
@Component
public class WorkOrderDriver extends BaseLocalDriver<WorkOrderDo> {

    @Resource
    private transient WorkOrderConvertor workOrderConvertor;
    @Resource
    private transient WorkOrderRepository workOrderRepository;


    @Override
    public int doWrite(DomainEntityPersistenceWrapper domainEntityWrapper) {
        if (log.isDebugEnabled()) {
            log.debug("[INFRA] doWrite start. workOrderDo:{}", domainEntityWrapper.getData());
        }

        String serviceId = domainEntityWrapper.getDomainServiceContext().getServiceId();
        // 类转换
        WorkOrderDo workOrderDo = (WorkOrderDo) domainEntityWrapper.getData();
        WorkOrderPo workOrderPo = workOrderConvertor.toPO(workOrderDo);
        WorkOrderMethodMapping.WRITE writer = WorkOrderMethodMapping.getWriterByServiceId(serviceId);
        if (null == writer) {
            log.error("[INFRA] doWrite error. not supported entity type:{}", domainEntityWrapper.getData().getClass());
            throw new ServiceException("WorkOrderDriver没有对应的writer,serviceId=" + serviceId);
        }
        return writer.exec(workOrderRepository, workOrderPo);
    }

    @Override
    public WorkOrderDo doReadByPk(Serializable pk) {
        log.warn("[INFRA] doReadByPk is not user index,please use doReadByCondition to use index(workId,userId). pk:{}", pk);
        WorkOrderPo workOrderPo = workOrderRepository.getById(pk);
        return workOrderConvertor.toDo(workOrderPo);
    }

    @Override
    public List<? extends WorkOrderDo> doReadByCondition(BaseCondition condition) {
        List<WorkOrderPo> workOrderPoList;
        if (condition instanceof QueryWorkOrderCondition) {
            workOrderPoList = workOrderRepository.searchByCondition((QueryWorkOrderCondition) condition);
        } else {
            workOrderPoList = workOrderRepository.searchByCondition(condition.getConditions());
        }
        return workOrderConvertor.toDoList(workOrderPoList);
    }

    @Override
    public Long doGetCount(BaseCondition condition) {
        return 0L;
    }


    @Override
    protected List<Class> getSupportedClass() {
        return Collections.singletonList(WorkOrderDo.class);
    }
}
