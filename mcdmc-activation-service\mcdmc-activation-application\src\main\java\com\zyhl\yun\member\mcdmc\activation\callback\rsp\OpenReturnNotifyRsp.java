package com.zyhl.yun.member.mcdmc.activation.callback.rsp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 订购定向流量结果通知响应
 *
 * <AUTHOR>
 * @since 2019-12-13
 */
@Data
@XmlRootElement(name = "OPFlowPkgSubsFdbkResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class OpenReturnNotifyRsp {

    /**
     * 消息类型
     */
    @XmlElement(name = "MsgType")
    private String msgType = "OPFlowPkgOrderFdbkResp";

    /**
     * 该接口消息版本号
     */
    @XmlElement(name = "Version")
    private String version;

    /**
     * 返回结果码
     */
    private String hRet;
}