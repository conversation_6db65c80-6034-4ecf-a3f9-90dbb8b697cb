package com.zyhl.yun.member.mcdmc.activation.mapping;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zyhl.yun.member.mcdmc.activation.serviceid.LocalServiceId;
import com.zyhl.yun.member.mcdmc.activation.po.WorkServiceFlowLogPo;
import com.zyhl.yun.member.mcdmc.activation.repository.WorkServiceFlowLogRepository;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DataIntegrityViolationException;

import java.util.Date;

/**
 * 接口日志服务映射
 *
 * <AUTHOR>
 * @since 2024/06/21 15:20
 */
@Slf4j
@Getter
public class FlowLogMethodMapping {
    @Getter
    @AllArgsConstructor
    public enum WRITE {
        INSERT(LocalServiceId.INSERT_OPERATION, "插入接口日志") {
            @Override
            public int exec(WorkServiceFlowLogRepository flowLogRepository, WorkServiceFlowLogPo workServiceFlowLogPo) {
                workServiceFlowLogPo.setCreatedTime(new Date());
                workServiceFlowLogPo.setUpdatedTime(new Date());
                boolean isSave = flowLogRepository.save(workServiceFlowLogPo);
                return isSave ? 1 : 0;
            }
        },

        UPDATE_BY_ID(LocalServiceId.UPDATE_BY_ID_OPERATION, "更新接口日志") {
            @Override
            public int exec(WorkServiceFlowLogRepository flowLogRepository, WorkServiceFlowLogPo workServiceFlowLogPo) {
                workServiceFlowLogPo.setUpdatedTime(null);
                LambdaUpdateWrapper<WorkServiceFlowLogPo> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(WorkServiceFlowLogPo::getLogId, workServiceFlowLogPo.getLogId())
                        .eq(StringUtils.isNotEmpty(workServiceFlowLogPo.getUserId()),
                                WorkServiceFlowLogPo::getUserId, workServiceFlowLogPo.getUserId());
                workServiceFlowLogPo.setLogId(null);
                workServiceFlowLogPo.setUserId(null);
                try {
                    boolean isUpdate = flowLogRepository.update(workServiceFlowLogPo, updateWrapper);
                    return isUpdate ? 1 : 0;
                } catch (Exception e) {
                    log.error("UPDATE_BY_ID exception,workServiceFlowLogPo is {},detail exception is ", workServiceFlowLogPo, e);
                    throw e;
                }
            }
        },
        UPSERT_BY_ID(LocalServiceId.UPSERT_OPERATION, "插入或更新接口日志") {
            @Override
            public int exec(WorkServiceFlowLogRepository flowLogRepository, WorkServiceFlowLogPo workServiceFlowLogPo) {
                workServiceFlowLogPo.setUpdatedTime(null);
                workServiceFlowLogPo.setCreatedTime(null);
                boolean isSave;
                try {
                    isSave = flowLogRepository.save(workServiceFlowLogPo);
                } catch (DataIntegrityViolationException e) {
                    log.debug("DuplicateKeyException:{}", e.getMessage());
                    isSave = false;
                }
                boolean isUpdate = false;
                if (Boolean.FALSE.equals(isSave)) {
                    LambdaUpdateWrapper<WorkServiceFlowLogPo> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(WorkServiceFlowLogPo::getLogId, workServiceFlowLogPo.getLogId())
                            .eq(StringUtils.isNotEmpty(workServiceFlowLogPo.getUserId()),
                                    WorkServiceFlowLogPo::getUserId, workServiceFlowLogPo.getUserId());
                    workServiceFlowLogPo.setUserId(null);
                    isUpdate = flowLogRepository.update(workServiceFlowLogPo, updateWrapper);
                }
                return isSave || isUpdate ? 1 : 0;
            }
        };

        private final String serviceId;
        private final String msg;

        public abstract int exec(WorkServiceFlowLogRepository flowLogRepository,
                                 WorkServiceFlowLogPo workServiceFlowLogPo);
    }

    public static WRITE getWriterByServiceId(String serviceId) {
        for (WRITE write : WRITE.values()) {
            if (write.serviceId.equals(serviceId)) {
                return write;
            }
        }
        return null;
    }
}
