package com.zyhl.yun.member.mcdmc.activation.util;

import lombok.extern.slf4j.Slf4j;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

/**
 * <AUTHOR>
 * @since  2022/07/21 09:36
 */
@Slf4j
public class IpUtil {
    private IpUtil() {
    }

    private static final String EMPTY = "";

    /**
     * 获取本地ip（ipv4）
     *
     * @return 本地ip（ipv4）
     */
    public static String getLocalHostAddress() {
        //定义网络接口枚举类
        Enumeration<NetworkInterface> allNetInterfaces;
        try {//获得网络接口
            allNetInterfaces = NetworkInterface.getNetworkInterfaces();
            //声明一个InetAddress类型ip地址
            //遍历所有的网络接口
            while (allNetInterfaces.hasMoreElements()) {
                NetworkInterface netInterface = allNetInterfaces.nextElement();
                if (!isValidInterface(netInterface)) {
                    continue;
                }
                //同样再定义网络地址枚举类
                Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress candidateAddress = addresses.nextElement();
                    //InetAddress类包括Inet4Address和Inet6Address
                    if ((candidateAddress instanceof Inet4Address) && !candidateAddress.isLoopbackAddress()) {
                        return candidateAddress.toString().substring(1);
                    }
                }
            }
        } catch (SocketException e) {
            log.error("获取服务端ip地址出错，异常信息：", e);
        }
        return EMPTY;
    }

    /**
     * 过滤回环网卡、点对点网卡、非活动网卡、虚拟网卡
     *
     * @param ni 网卡
     * @return 如果满足要求则true，否则false
     */
    private static boolean isValidInterface(NetworkInterface ni) throws SocketException {
        return !ni.isLoopback() && !ni.isPointToPoint() && ni.isUp() && !ni.isVirtual();
    }


    /**
     * 获取本地的ipv4地址
     *
     * @return ipv4地址
     */
    public static InetAddress getLocalhost() {
        InetAddress candidateAddress = null;
        NetworkInterface iface;
        InetAddress inetAddr;
        try {
            for (Enumeration<NetworkInterface> ifaces = NetworkInterface.getNetworkInterfaces(); ifaces.hasMoreElements(); ) {
                iface = ifaces.nextElement();
                for (Enumeration<InetAddress> inetAddrs = iface.getInetAddresses(); inetAddrs.hasMoreElements(); ) {
                    inetAddr = inetAddrs.nextElement();
                    if (!inetAddr.isLoopbackAddress()) {
                        if (inetAddr.isSiteLocalAddress()) {
                            return inetAddr;
                        } else if (null == candidateAddress) {
                            // 非site-local地址做为候选地址返回
                            candidateAddress = inetAddr;
                        }
                    }
                }
            }
        } catch (SocketException e) {
            log.error("获取服务端ip地址出错，异常信息：", e);
        }

        return candidateAddress;
    }
}
