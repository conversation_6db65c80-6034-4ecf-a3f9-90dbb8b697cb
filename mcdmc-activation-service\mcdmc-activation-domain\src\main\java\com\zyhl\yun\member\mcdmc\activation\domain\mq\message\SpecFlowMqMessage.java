package com.zyhl.yun.member.mcdmc.activation.domain.mq.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/3 18:45
 * @descrition 消息体
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class SpecFlowMqMessage {
    /**
     * 取自调用链唯一标识
     */
    private String traceID;

    /**
     * 系统生成的唯一标识
     */
    private String msgID;

    /**
     * 操作时间
     */
    private String eventTime;

    /**
     * 用户标识
     */
    private String userID;

    /**
     * 用户账号
     */
    private String account;

    /**
     * 商品ID
     */
    private String sourceGoodsId;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 来源订单ID
     */
    private String sourceOrderNo;

    /**
     * 来源系统
     */
    private String sourceApp;

    /**
     * 履约ID
     */
    private String orderNo;

    /**
     * 履约次数
     */
    private Integer index;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 期望执行时间
     */
    private String expectedExecuteTime;

    /**
     * 实际执行时间
     */
    private String actualExecuteTime;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 业务码描述
     */
    private String bizDesc;

    /**
     * 订购时间
     */
    private String orderTime;

    /**
     * 生效时间
     */
    private String validDate;

    /**
     * 失效时间
     */
    private String expireDate;

    /**
     * 用户归属环境标识
     */
    private String envID;

    /**
     * 自定义扩展信息
     */
    private String extInfo;

    /**
     * 渠道id
     */
    private String channelId;

    /**
     * 子渠道id
     */
    private String subChannelId;

    private String userDomainId;
}