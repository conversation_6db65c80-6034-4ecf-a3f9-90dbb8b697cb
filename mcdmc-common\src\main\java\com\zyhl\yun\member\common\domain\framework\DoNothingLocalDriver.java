package com.zyhl.yun.member.common.domain.framework;

import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/08/08 14:42
 */
@Slf4j
public abstract class DoNothingLocalDriver<T extends Serializable> extends BaseLocalDriver<T> {

    @Override
    public T doReadByPk(Serializable pk) {
        log.info("[FRAMEWORK] do nothing. doReadByPk: {}", pk);
        return null;
    }

    @Override
    public List<? extends T> doReadByCondition(BaseCondition condition) {
        log.info("[FRAMEWORK] do nothing. doReadByCondition: {}", condition);
        return null;
    }

    @Override
    public Long doGetCount(BaseCondition condition) {
        log.info("[FRAMEWORK] do nothing. doGetCount: {}", condition);
        return null;
    }

}