package com.zyhl.yun.member.mcdmc.activation.convertor;

import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowDo;
import com.zyhl.yun.member.mcdmc.activation.po.WorkServiceFlowPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/06/05 17:13
 */
@Mapper(componentModel = "spring")
public interface WorkServiceFlowConvertor {

    List<WorkServiceFlowDo> toDoList(List<WorkServiceFlowPo> poList);
}
