package com.zyhl.yun.member.mcdmc.activation.domains.conditions;

import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/10 17:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class QueryWorkServiceFlowLogCondition extends BaseCondition {


    private static final long serialVersionUID = 4796510665112265902L;

    private String userId;

    private List<String> serviceCodeList;

    private List<String> workIdList;

}
