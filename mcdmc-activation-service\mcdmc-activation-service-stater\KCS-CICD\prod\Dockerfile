FROM b08ypcg01-53ab9ddf.ecis.guangzhou-2.cmecloud.cn/prod/openjdk8-arthas-skywalking:1.0.0

#请修改为应用端口
EXPOSE 18084

ENV TZ=Asia/Shanghai

#南基-https://www.kdocs.cn/l/ccjjXxdSB9gt【各模块中间件地址】I列
#或者
#凤凰-https://www.kdocs.cn/l/cc7KvTQt4bUL【各模块中间件地址】I列
ENV skywalkingService=b08-yp-p02-skyw-01.skywalking-b08-yp-p02-skyw-01:11800
#请修改应用名
ENV applicationName=mcdmc-activation-service-starter
#调整JVM最大最小堆
ENV jvm_heap_size="-Xmx16g -Xms16g -Xss256K"
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

#请将编译后的jar放置到镜像根目录，建议先本地编译确认具体目录
#编译后jar目录类似yun-neauth-config-web/target/yun-neauth-config-web-1.0-SNAPSHOT.jar，版本可用*代替
#放到/目录可以改jar包名，把版本去掉
ADD mcdmc-activation-service/mcdmc-activation-service-stater/target/mcdmc-activation-service-starter-1.0.jar /mcdmc-activation-service-starter-1.0.jar
CMD timestamp=$(date +%Y%m%d%H%M%S) \
&& mkdir -p logs/${applicationName}/$HOSTNAME/gc \
&& java \
${jvm_heap_size} \
#skywalking-agent
# -javaagent:/skywalking/agent/skywalking-agent.jar \
# -Dskywalking.agent.service_name=${applicationName} \
# -Dskywalking.collector.backend_service=${skywalkingService} \

-Djava.security.egd=file:/dev/./urandom \
-XX:+PrintClassHistogram -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintHeapAtGC \
-Xloggc:logs/${applicationName}/$HOSTNAME/gc/${applicationName}-gc-${timestamp}.log \
#请修改应用名
-jar /mcdmc-activation-service-starter-1.0.jar
