package com.zyhl.yun.member.mcdmc.activation.domain.dos;

import com.zyhl.yun.member.common.domain.framework.BaseDo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 业务流程接口
 *
 * <AUTHOR>
 * @since 2024/08/02 16:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class WorkServiceFlowIFaceDo extends BaseDo implements Serializable {

    /**
     * 业务流程接口ID
     */
    private Long iFaceId;
    /**
     * 业务流程id
     */
    private String workServiceFlowId;
    /**
     * 服务编码
     */
    private String serviceCode;

    /**
     * 主动查询的服务编码
     */
    private String queryServiceCode;
    /**
     * 业务流程接口名称
     */
    private String iFaceName;

    /**
     * 接口类名
     */
    private String iFaceClassName;

    /**
     * 发送类型,0:post,1:get,2:del,3:put
     */
    private String requestType;
    /**
     * 请求方式
     * eg:application/json,application/xml,text/xml
     */
    private String mediaType;
    /**
     * 发送url
     */
    private String url;

    /**
     * 回调地址URI（不包含项目contextPath及之前的地址）
     * eg:/cloudSEE/openApi/salesorderResult
     */
    private String notifyUri;

    /**
     * 执行优先级，从0开始，数字越小，优先级越高
     */
    private int priorSort;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 接口配置
     */
    private String iFaceConfig;
}
