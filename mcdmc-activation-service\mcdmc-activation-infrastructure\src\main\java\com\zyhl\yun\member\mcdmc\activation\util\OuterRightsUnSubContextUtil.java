package com.zyhl.yun.member.mcdmc.activation.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.zyhl.yun.member.common.domain.framework.service.MdcLogInterceptor;
import com.zyhl.yun.member.consumer.common.producer.MqProducer;
import com.zyhl.yun.member.mcdmc.activation.constants.IFaceConfigMapKey;
import com.zyhl.yun.member.mcdmc.activation.domain.mq.properties.OuterRightsOpenCapacityMqProperties;
import com.zyhl.yun.member.mcdmc.activation.enums.ParkAppIdEnum;
import com.zyhl.yun.member.message.domain.OpenCapacityUnSubMqMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 外部权益域相关工具类
 *
 * <AUTHOR>
 * @apiNote 调用外部权益域的公共查询或写入动作
 * @since 2024/11/25 16:32
 */
@Slf4j
@Component
public class OuterRightsUnSubContextUtil {
    @Resource
    protected MqProducer mqProducer;

    @Resource
    protected IFaceConfigUtil iFaceConfigUtil;

    @Resource
    protected OuterRightsOpenCapacityMqProperties outerRightsOpenCapacityMqProperties;


    /**
     * 发送能开退订任务的消息
     *
     */
    public void sendOpenCapacityUnsubscribeMessage(String iFaceConfig, String orderNo) {
        OpenCapacityUnSubMqMessage messageDo = new OpenCapacityUnSubMqMessage();
        messageDo.setTraceId(MdcLogInterceptor.getCurrentTraceId());
        messageDo.setAppId(iFaceConfigUtil.getFaceConfig(iFaceConfig, IFaceConfigMapKey.APP_ID));
        messageDo.setNotifyTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        messageDo.setOrderNo(orderNo);
        messageDo.setRefundTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        mqProducer.sendOuterMessage(outerRightsOpenCapacityMqProperties, messageDo);
    }


    /**
     * 发送能开退订任务的消息
     *
     */
    public void sendOpenCapacityParkUnsubscribeMessage(String iFaceConfig, String orderNo, String goodsId) {
        OpenCapacityUnSubMqMessage messageDo = new OpenCapacityUnSubMqMessage();
        messageDo.setTraceId(MdcLogInterceptor.getCurrentTraceId());
        String appIdStr = iFaceConfigUtil.getFaceConfig(iFaceConfig, IFaceConfigMapKey.APP_ID);
        // pp停车|一点停停车|捷停车
        List<String> appIdList = Arrays.asList(appIdStr.split("\\|"));
        ParkAppIdEnum parkAppIdEnum = ParkAppIdEnum.getByName(goodsId);
        String appId = appIdList.get(parkAppIdEnum.getAppIdIndex());
        messageDo.setAppId(appId);
        messageDo.setNotifyTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        messageDo.setOrderNo(orderNo);
        messageDo.setRefundTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        mqProducer.sendOuterMessage(outerRightsOpenCapacityMqProperties, messageDo);
    }
}
