@startuml
'https://plantuml.com/activity-diagram-beta

|白名单服务|
start
: 解析传入报文;
switch (入参)
case ( account )
  :wait;
case ( userId )
  :查询新用户表得到account;
  :映射关系写入缓存;
case ( userDomainId )
  :查询用户域得到account;
  :映射关系写入缓存;
endSwitch
if (account在__白名单表__?) then (no)
    #red:返回非白名单;
    stop
(yes  )elseif (currentTime<=effectTime(白名单生效时间)?) then (yes)
  : 添加至缓存为__待静默用户__;
  #red: 返回非白名单;
  stop
(no  )elseif (用户存在于__待静默用户__缓存?) then (yes)
  : 重新将该用户缓存过期时长设置为初始值;
  #red: 返回非白名单;
  stop
else (no)
  #cyan: 返回白名单;
  stop
endif
@enduml
