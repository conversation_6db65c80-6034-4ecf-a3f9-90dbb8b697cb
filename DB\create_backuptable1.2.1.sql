CREATE TABLE  member_vip.`member_t_goods_instance_backup` (
  `goods_instance_id` varchar(128) NOT NULL COMMENT '商品实例ID',
  `received_id` varchar(128) DEFAULT NULL COMMENT '主键id',
  `goods_id` varchar(128) NOT NULL COMMENT '商品ID',
  `pay_way` smallint(6) DEFAULT NULL COMMENT '支付渠道 2：话费支付 3：营销活动赠送 66：微信支付 77：支付宝 78：纯积分 79：话费积分混合 86：苹果支付 50：权益中心畅享会员割接',
  `sub_time` datetime NOT NULL COMMENT '订购时间',
  `sub_way` smallint(6) DEFAULT NULL COMMENT '订购方式 1：默认渠道 98：积分渠道  其它渠道：参见VSBO接口文档 7.2.7 销售渠道类型',
  `unsub_time` datetime DEFAULT NULL COMMENT '退订时间',
  `unsub_way` smallint(6) DEFAULT NULL COMMENT '退订方式 18：七天无理由退订 11：话费1小时退订',
  `pause_time` datetime DEFAULT NULL COMMENT '暂停时间',
  `active_time` datetime DEFAULT NULL COMMENT '激活时间',
  `renew_time` datetime DEFAULT NULL COMMENT '下次收租时间(续约时间)',
  `charge_type` varchar(4) DEFAULT NULL COMMENT '计费类型：0点播 1包月',
  `deal_price` int(11) DEFAULT NULL COMMENT '订单金额(分)',
  `time_plan_goods_group_id` varchar(128) DEFAULT NULL COMMENT '时间计划商品分组Id，需要做分组内策略计算时依赖的商品分组id',
  `goods_package_id` varchar(128) DEFAULT NULL COMMENT '商品包Id(权益包)',
  `goods_package_instance_id` varchar(128) DEFAULT NULL,
  `goods_package_rights_type` varchar(128) DEFAULT NULL COMMENT 'sale_type=103 则有权益类型:0：主平台空间权益 2：数字生活平台权益 3:组合产品权益',
  `rights_type` smallint(6) DEFAULT NULL COMMENT 'sale_type=103 则有权益类型:0：主平台空间权益 2：数字生活平台权益 3:组合产品权益',
  `sub_channel_id` varchar(512) DEFAULT NULL COMMENT '子渠道 id',
  `channel_id` varchar(128) DEFAULT NULL COMMENT '渠道 id',
  `cycle_type` smallint(6) DEFAULT NULL COMMENT '计费类型',
  `cycle_count` int(11) DEFAULT NULL COMMENT '计费周期',
  `user_id` varchar(128) NOT NULL COMMENT '用户ID',
  `order_id` varchar(128) DEFAULT NULL COMMENT '订单ID',
  `received_time` datetime NOT NULL COMMENT '领取时间',
  `state` smallint(6) NOT NULL COMMENT '订购合约状态 0：在途 1：正常 8：退订 9：暂停',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `tenant_id` varchar(128) DEFAULT 'default' COMMENT '租户id',
  `version_flag` varchar(1024) DEFAULT '0000000000000000' COMMENT '多版本支持',
  `sale_type` smallint(6) NOT NULL COMMENT '销售(产品)类型 101：会员 102：空间 103:权益 104:免流产品',
  `operator` varchar(128) DEFAULT 'default' COMMENT '操作者',
  `effective_start_time` datetime DEFAULT NULL COMMENT '生效时间',
  `effective_end_time` datetime DEFAULT NULL COMMENT '失效时间',
  `resource_id` varchar(100) DEFAULT NULL COMMENT '资源id',
  `product_id` varchar(100) DEFAULT NULL COMMENT '产品id',
  `right_reduction` varchar(1024) DEFAULT NULL,
  `rights_status` smallint(6) DEFAULT NULL COMMENT '\'权益状态 --RIGHTSSTATUS权益状态:1：发起权益成功 2：发起权益失败 3：权益开通成功 4：权益开通失败 5：权益激活接口调用成功 \r\n--6：权益激活接口调用失败 7：权益激活成功 8：权益激活失败 9：停车券退货成功 10：停车券退货失败 11：停车券退货中\';',
  `ext_info` text DEFAULT NULL COMMENT '订购关系拓展信息',
  PRIMARY KEY (`goods_instance_id`, `user_id`),
  KEY `idx_goodsinst_backup_saleType` (`sale_type`) LOCAL,
  KEY `idx_goodsinst_backup_update_time` (`update_time`, `sale_type`, `user_id`) GLOBAL,
  KEY `idx_goodsinst_backup_instID` (`goods_package_instance_id`) GLOBAL,
  KEY `idx_goodsinst_backup_orderId` (`order_id`)LOCAL,
  KEY `idx_goodsinst_backup_userId` (`user_id`) LOCAL
)COMMENT = '商品实例备份表' PARTITION BY KEY(user_id) PARTITIONS 384;



CREATE TABLE  member_order.`ord_t_order_backup` (
  `order_no` varchar(256) NOT NULL COMMENT '订单编号',
  `old_order_id` varchar(256) DEFAULT NULL COMMENT '旧订单号',
  `type` varchar(64) DEFAULT NULL COMMENT '订单类型',
  `status` smallint(6) NOT NULL COMMENT '订单状态:0,草稿状态;1,已提交状态;2,异步支付,正在等待支付;3,已支付状态;4,取消状态;5,完成状态;6,履约状态,已触发履约事件;7,订单退订中;8,订单已退订',
  `total_amount` bigint(20) DEFAULT NULL COMMENT '订单总价',
  `currency` smallint(6) DEFAULT NULL COMMENT '货币类型',
  `order_user_id` varchar(128) NOT NULL COMMENT '订单用户ID',
  `create_user_id` varchar(128) DEFAULT NULL COMMENT '创建订单用户id',
  `pay_user_id` varchar(128) DEFAULT NULL COMMENT '支付订单用户id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `channel_id` varchar(64) DEFAULT NULL COMMENT '渠道id',
  `pay_way` smallint(6) DEFAULT NULL COMMENT '支付渠道类型 支付方式:55-微信支付;44-支付宝支付;2-话费支付;26-苹果支付;86-苹果支付(计费中心);66-全业务计费平台微信支付;77-全业务计费平台支付宝支付;78-积分支付;79-积分/话费支付;3-营销赠送',
  `channel_type` varchar(4) DEFAULT NULL COMMENT '订单渠道类型',
  `last_update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `charge_type` smallint(6) DEFAULT NULL COMMENT '计费类型 0：按次 1：包月',
  `sale_type` smallint(6) DEFAULT NULL COMMENT '销售类型 101：会员产品  102：空间包',
  `display_to_user` smallint(6) DEFAULT NULL COMMENT '是否展示',
  `send_product_type` smallint(6) DEFAULT NULL COMMENT '商品发货类型',
  `sub_channel_id` varchar(100) DEFAULT NULL COMMENT '计费中心4.4.1接口subplace字段，定购发生地，标示订单正向或反向订购7代表反向，13&29代表正向（包月、一次性包时长、动态月业务有效）',
  `out_order_id` varchar(256) DEFAULT NULL COMMENT '营销外部订单id',
  `tenant_id` varchar(64) DEFAULT 'default' COMMENT '租户id',
  `version_flag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  `goods_id` varchar(100) DEFAULT NULL COMMENT '商品id',
  `json_orderext` text DEFAULT NULL COMMENT '订单属性',
  `json_paytype` text DEFAULT NULL COMMENT '冗余字段，为了数据回流',
  `flow_order_no` varchar(32) DEFAULT NULL COMMENT '免流订单号，冗余字段，为了数据回流',
  `third_platform_trad_id` varchar(128) DEFAULT NULL COMMENT '第三方平台id',
  PRIMARY KEY (`order_no`, `order_user_id`),  
  KEY `idx_order_backup_update_time` (`last_update_time`, `order_user_id`) GLOBAL,
  KEY `idx_order_backup_userId` (`order_user_id`)LOCAL,
  KEY `idx_order_backup_out_order_id` (`out_order_id`)LOCAL,
  KEY `idx_backup_third_platform_trad_id` (`third_platform_trad_id`) LOCAL
) COMMENT = '订单备份表'  
 PARTITION BY KEY(order_user_id) PARTITIONS 384;


CREATE TABLE  member_order.`ord_t_order_detail_backup` (
  `order_id` varchar(128) DEFAULT NULL,
  `order_detail_id` varchar(100) NOT NULL COMMENT 'order明细id',
  `order_user_id` varchar(128) NOT NULL COMMENT '订单用户ID',
  `good_id` varchar(100) DEFAULT NULL COMMENT '商品id',
  `order_detail_price` varchar(100) DEFAULT NULL COMMENT '订单明细价格',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `actual_receipt` int(11) DEFAULT NULL COMMENT '实际收取',
  `should_receipt` int(11) DEFAULT NULL COMMENT '应收',
  `tenant_id` varchar(64) DEFAULT 'default' COMMENT '租户id',
  `version_flag` varchar(1024) DEFAULT NULL COMMENT '多版本支持',
  PRIMARY KEY (`order_detail_id`, `order_user_id`),
  KEY `idx_orderdetail_backup_orderid` (`order_id`)GLOBAL,
  KEY `idx_orderdetail_backup_userId` (`order_user_id`)LOCAL
)COMMENT = '订单明细备份表'   
PARTITION BY KEY(order_user_id) PARTITIONS 384;

