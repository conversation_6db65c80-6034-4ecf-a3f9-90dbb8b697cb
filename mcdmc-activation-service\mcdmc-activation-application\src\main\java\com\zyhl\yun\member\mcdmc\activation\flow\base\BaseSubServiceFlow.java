package com.zyhl.yun.member.mcdmc.activation.flow.base;

import cn.hutool.core.date.StopWatch;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.goodsinstance.enums.GoodsInstanceStateEnum;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import java.util.Date;
import java.util.Objects;

/**
 * 订购统一流程
 *
 * <AUTHOR>
 * @since 2024/12/10 10:45
 */
@Slf4j
public abstract class BaseSubServiceFlow extends BaseDefaultServiceFlow {

    /**
     * 流程前置逻辑
     *
     * @param workOrderDo 工单信息
     * @return boolean
     * @apiNote 如果为true，则直接以成功结束工单流程，不会生成接口日志
     */
    @Override
    protected IFlowResult beforeFlow(WorkOrderDo workOrderDo) {
        // 订购前需要判断是否已经退订了
        ComSendInterfaceReq comSendReq = JSONUtil.toBean(workOrderDo.getWorkAttrs(), ComSendInterfaceReq.class);
        GoodsInstanceDo subGoodsInstanceDo = MemberContextUtil.qryGoodsInstanceDo(workOrderDo.getUserId(), comSendReq.getGoodsInstanceId());
        if (Objects.isNull(subGoodsInstanceDo)) {
            log.info("sub error, goodsInstance is null,workOrder detail is {},goodsInstanceId={}",
                    workOrderDo.toSimpleLogStr(), comSendReq.getGoodsInstanceId());
            // 退费则直接不发起订购（针对延迟发货）
            return IFlowResult.cancel("sub error, goodsInstance is null,workOrder detail is %s,goodsInstanceId=%s",
                    workOrderDo.toSimpleLogStr(), comSendReq.getGoodsInstanceId());
        }
        Date currentDate = new Date();
        //比对时间时加入误差，以避免服务器间时间有误差时误杀
        if (this.isJudgeSubGoodInstanceEffective() &&
                currentDate.before(DateUtils.addMilliseconds(subGoodsInstanceDo.getEffectiveStartTime(), -30000))) {
            // 如果商品实例无效，则直接不发起订购
            log.info("sub delay, goodsInstance is not effective now,real effectiveStartTime is {},workOrder detail is {},goodsInstanceId={}",
                    subGoodsInstanceDo.getEffectiveStartTime(), workOrderDo.toSimpleLogStr(), comSendReq.getGoodsInstanceId());
            return IFlowResult.reset("sub delay, goodsInstance is not effective now,real effectiveStartTime is %s,workOrder detail is %s,goodsInstanceId=%s",
                    subGoodsInstanceDo.getEffectiveStartTime(), workOrderDo.toSimpleLogStr(), comSendReq.getGoodsInstanceId());
        } else if (this.isJudgeSubGoodInstanceEffective() &&
                currentDate.after(DateUtils.addMilliseconds(subGoodsInstanceDo.getEffectiveEndTime(), 30000))) {
            // 如果商品实例无效，则直接不发起订购
            log.info("sub error, goodsInstance is not effective,real effectiveEndTime is {},workOrder detail is {},goodsInstanceId={}",
                    subGoodsInstanceDo.getEffectiveEndTime(), workOrderDo.toSimpleLogStr(), comSendReq.getGoodsInstanceId());
            return IFlowResult.cancel("sub error, goodsInstance is not effective,real effectiveEndTime is %s,workOrder detail is %s,goodsInstanceId=%s",
                    subGoodsInstanceDo.getEffectiveEndTime(), workOrderDo.toSimpleLogStr(), comSendReq.getGoodsInstanceId());
        }
        GoodsInstanceDo parentGoodInstanceDo = MemberContextUtil.qryGoodsInstanceDo(workOrderDo.getUserId(), subGoodsInstanceDo.getGoodsPackageInstanceId());
        if (Objects.isNull(parentGoodInstanceDo)) {
            // 可能存在部分产品没有父产品，直接使用子产品
            parentGoodInstanceDo = subGoodsInstanceDo;
        }
        if (GoodsInstanceStateEnum.REFUND.equals(parentGoodInstanceDo.getStateEnum())) {
            log.info("sub error, parent goodsInstance is refund,workOrder detail is {},parentGoodsInstanceId={}",
                    workOrderDo.toSimpleLogStr(), parentGoodInstanceDo.getGoodsInstanceId());
            // 退费则直接不发起订购（针对延迟发货）
            return IFlowResult.cancel("sub error, parent goodsInstance is refund,parentGoodsInstanceId=%s", parentGoodInstanceDo.getGoodsInstanceId());
        }
        workOrderDo.putExtData(OtherFieldConstants.GOODS_INSTANCE_DO, subGoodsInstanceDo);
        log.debug("start sub,workOrder detail is {},parentGoodsInstanceId={},parent instance state is {}",
                workOrderDo.toSimpleLogStr(), parentGoodInstanceDo.getGoodsInstanceId(), parentGoodInstanceDo.getStateEnum());
        return IFlowResult.next();
    }


    /**
     * 是否需要判断商品实例是否有效
     */
    protected abstract boolean isJudgeSubGoodInstanceEffective();

    /**
     * 流程成功后执行动作
     */
    @Override
    protected void doFlowSuccess(WorkOrderDo workOrderDo, boolean isFinally, IFlowResult lastFaceResult) {
        ComSendInterfaceReq comSendReq = JSONUtil.toBean(workOrderDo.getWorkAttrs(), ComSendInterfaceReq.class);
        String resourceId = lastFaceResult == null ? null : lastFaceResult.getResourceId();
        // 根据是否有回调来判断是否为最终流程
        boolean isRealFinally = Boolean.FALSE.equals(hasCallback());
        //失败更新领取表状态
        MemberContextUtil.updateGoodsInstance(comSendReq, resourceId,
                isFinally, true, SendOperation.SUB);
        // 执行原流程结束动作
        super.doFlowSuccess(workOrderDo, isRealFinally, lastFaceResult);
    }

    @Override
    protected void doFlowFail(WorkOrderDo workOrderDo) {
        ComSendInterfaceReq comSendReq = JSONUtil.toBean(workOrderDo.getWorkAttrs(), ComSendInterfaceReq.class);
        //失败更新领取表状态
        boolean isFinally = Boolean.FALSE.equals(hasCallback());
        MemberContextUtil.updateGoodsInstance(comSendReq, null,
                isFinally, false, SendOperation.SUB);
    }

    /**
     * 判断是否有回调接口
     */
    protected abstract boolean hasCallback();

}
