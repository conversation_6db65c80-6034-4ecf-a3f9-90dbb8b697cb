package com.zyhl.yun.member.mcdmc.activation.domain.exception;

import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * 流程终止异常
 *
 * <AUTHOR>
 * @since 2024/07/12 17:43
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class FlowTerminationException extends RuntimeException {
    /**
     * 流程名字
     */
    private final String flowName;
    /**
     * 工单id
     */
    private final String workId;

    /**
     * 工单参数/属性
     */
    private final String workAttrs;

    /**
     * 终止原因
     */
    private final String reason;

    /**
     * 流程终止后下一步操作
     */
    private final NextHintEnum nextHint;

    public FlowTerminationException(Class<?> flowClass, WorkOrderDo workOrderDo, String causeFormat, Object... args) {
        this(flowClass, workOrderDo, String.format(causeFormat, args));
    }

    public FlowTerminationException(Class<?> flowClass, WorkOrderDo workOrderDo, String errorMsg) {
        this(flowClass, workOrderDo, errorMsg, NextHintEnum.FAIL_FINISH);
    }

    public FlowTerminationException(Class<?> flowClass, WorkOrderDo workOrderDo, NextHintEnum nextHint, String causeFormat, Object... args) {
        this(flowClass, workOrderDo, String.format(causeFormat, args), nextHint);
    }

    public FlowTerminationException(Class<?> flowClass, WorkOrderDo workOrderDo, String errorMsg, NextHintEnum nextHint) {
        super(errorMsg);
        this.flowName = flowClass.getSimpleName();
        this.workId = workOrderDo.getWorkId();
        this.workAttrs = workOrderDo.getWorkAttrs();
        this.reason = errorMsg;
        this.nextHint = nextHint;
    }

    @Override
    public String toString() {
        return "流程终止异常：" + flowName + "，工单id：" + workId +
                "，工单参数为：" + workAttrs + "，终止原因：" + reason;
    }

    public IFlowResult toIFlowResult() {
        IFlowResult flowResult = new IFlowResult();
        flowResult.setNextHintEnum(this.nextHint);
        String message = this.reason;
        StackTraceElement[] stackTrace = this.getStackTrace();
        if (Objects.nonNull(stackTrace) && stackTrace.length > 0) {
            message = message + "\n" + stackTrace[0];
        }
        flowResult.setMessage(message);
        return flowResult;
    }

}
