package com.zyhl.yun.member.common.domain.framework;

import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 驱动器管理器
 *
 * <AUTHOR>
 * @date 2024/05/21 18:40
 */
public class DriverManager {

    /**
     * 驱动器缓存
     */
    private static Map<String, IDriver> driverCache = new HashMap<>();


    /**
     * 默认驱动器
     */
    private static IDriver defaultDriver;

    /**
     * 注册驱动器
     *
     * @param driver
     * @param domain
     */
    public static void register(IDriver driver, Class domain) {
        driverCache.put(domain.getSimpleName(), driver);
    }


    /**
     * 注册驱动器
     *
     * @param driver
     * @param domainList
     */
    public static void register(IDriver driver, List<Class> domainList) {
        if (CollectionUtils.isEmpty(domainList)) {
            return;
        }
        for (Class domain : domainList) {
            driverCache.put(domain.getSimpleName(), driver);
        }

    }


    /**
     * 注册默认驱动器
     *
     * @param driver
     */
    public static void registerDefaultDriver(IDriver driver) {
        defaultDriver = driver;
    }


    /**
     * 获取驱动器
     *
     * @param domainClassName
     * @return
     */
    public static IDriver getDriver(String domainClassName) {
        return driverCache.get(domainClassName) != null ? driverCache.get(domainClassName) : defaultDriver;
    }


    /**
     * 获取驱动器
     *
     * @param domainClass
     * @return
     */
    public static IDriver getDriver(Class domainClass) {
        return getDriver(domainClass.getClass().getSimpleName());
    }


}
