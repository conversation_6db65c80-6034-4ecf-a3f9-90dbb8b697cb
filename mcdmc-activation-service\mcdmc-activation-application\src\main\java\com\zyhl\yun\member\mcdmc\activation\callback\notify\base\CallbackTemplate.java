package com.zyhl.yun.member.mcdmc.activation.callback.notify.base;

import cn.hutool.extra.spring.SpringUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceExtendServiceId;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowLogDo;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.CallbackException;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.redis.RedisComponent;
import com.zyhl.yun.member.mcdmc.activation.result.BaseResult;
import com.zyhl.yun.member.mcdmc.activation.result.ResultCode;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.Map;

/**
 * 回调执行模板
 *
 * <AUTHOR>
 * @since 2024/07/05 10:30
 */
@Slf4j
public abstract class CallbackTemplate<T, R> extends BaseCallback<T, R> {

    protected RedisComponent redisComponent;

    @PostConstruct
    private void init() {
        redisComponent = SpringUtil.getBean(RedisComponent.class);
    }

    @Override
    public String execCallback(String uri, Map<String, String> reqHeader, String callbackReqStr) {
        CallbackContext<T, R> callbackContext = CallbackContext.newInstance(
                uri, reqHeader, callbackReqStr, this::convertReq);
        BaseResult handleResult = getSuccessResult();
        try {
            // 查询根据回调订单号获取发货接口日志
            callbackContext.searchSendFLowLog(this::getSearchCondition, redisComponent, this::initSendFlowLogWhileNotFound);
            // 校验参数
            this.doCheck(callbackContext);
            // 执行回调逻辑
            this.doNotify(callbackContext);
        } catch (CallbackException e1) {
            if (!ResultCode.SUCCESS.getCode().equals(e1.getErrorCode())) {
                // 只有不成功的情况下才需要改值
                handleResult = BaseResult.fail(e1.getErrorCode(), e1.getMessage());
            }
        } catch (Exception e) {
            log.error("回调接口发生异常，异常信息为：", e);
            handleResult = BaseResult.fail(ResultCode.SYSTEM_ERROR);
        }
        R notifyRsp = buildNotifyRsp(callbackContext, handleResult);
        // 返回响应结果字符串
        String rspStr = convertRsp2Str(notifyRsp);
        // 找不到发货日志则不更新日志表记录
        if (!ResultCode.LOG_NOT_FOUND.getMsg().equals(handleResult.getMessage())) {
            callbackContext.updateLogAndWorkDo(handleResult.isSuccess(), rspStr);
        }
        return rspStr;
    }

    protected WorkServiceFlowLogDo initSendFlowLogWhileNotFound(T req) {
        return null;
    }

    @Override
    protected void doNotify(CallbackContext<T, R> callbackContext) {
        GoodsInstanceDo goodsInstanceExtendDo = this.checkGoodsInstance(callbackContext);
        this.doNotifyOtherDomainStatus(callbackContext, goodsInstanceExtendDo);
        this.doAfterNotify(callbackContext, goodsInstanceExtendDo);
    }

    /**
     * 校验是否存在商品实例
     *
     * @param callbackContext 回调上下文
     * @return 对应的商品实例
     */
    protected GoodsInstanceDo checkGoodsInstance(CallbackContext<T, R> callbackContext) {
        DomainServiceContext memberContext = new DomainServiceContext(GoodsInstanceExtendServiceId.QUERY_GOODS_INSTANCE_EXTEND);
        ComSendInterfaceReq comSendReq = callbackContext.getComSendReq();
        // 查询商品实例
        QueryGoodsInstanceCondition queryCondition = new QueryGoodsInstanceCondition();
        queryCondition.setUserId(comSendReq.getUserId());
        queryCondition.setGoodsInstanceIdList(Collections.singletonList(comSendReq.getGoodsInstanceId()));
        GoodsInstanceDo goodsInstanceExtendDo = memberContext.readFirst(queryCondition, GoodsInstanceDo.class);
        if (null == goodsInstanceExtendDo) {
            log.error("根据商品实例ID={}未查询到商品实例", comSendReq.getGoodsInstanceId());
            throw new CallbackException(ResultCode.ORDER_NOT_EXISTS);
        }
        return goodsInstanceExtendDo;
    }

    /**
     * 更新订单和订购关系状态
     *
     * @param callbackContext       回调上下文
     * @param goodsInstanceExtendDo 商品实例
     */
    protected void doNotifyOtherDomainStatus(CallbackContext<T, R> callbackContext,
                                             GoodsInstanceDo goodsInstanceExtendDo) {
        // 需要回调则直接通知订单和订购关系结果
        boolean isSuccess = isSuccess(callbackContext.getCallbackReq());
        ComSendInterfaceReq comSendReq = callbackContext.getComSendReq();
        SendOperation sendOperation = getSendOperation();
        // 通知会员域更新商品实例状态
        String resourceId = callbackContext.getSendServiceFlowLogDo().getCallbackCondition();
        MemberContextUtil.updateGoodsInstance(comSendReq, resourceId, true, isSuccess, sendOperation);
    }

    /**
     * 通知其他域之后执行的动作
     *
     * @param callbackContext 回调上下文
     */
    protected void doAfterNotify(CallbackContext<T, R> callbackContext, GoodsInstanceDo goodsInstanceExtendDo) {
        // 更新订单和订购关系状态之后执行的动作
    }

    /**
     * 校验参数是否为空
     */
    protected void doCheckEmpty(Object obj) throws CallbackException {
        this.doCheckEmpty(obj, ResultCode.PARAMETER_CHECK_ERROR.getMsg());
    }

    protected void doCheckEmpty(Object obj, String errorMsg) throws CallbackException {
        if (null == obj) {
            throw new CallbackException(ResultCode.PARAMETER_CHECK_ERROR, errorMsg);
        }
        if (obj instanceof String && !StringUtils.hasLength((String) obj)) {
            throw new CallbackException(ResultCode.PARAMETER_CHECK_ERROR, errorMsg);
        }
    }

}
