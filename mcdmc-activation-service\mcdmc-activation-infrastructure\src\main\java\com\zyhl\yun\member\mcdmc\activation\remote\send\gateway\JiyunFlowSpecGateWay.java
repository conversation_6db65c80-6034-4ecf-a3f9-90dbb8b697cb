package com.zyhl.yun.member.mcdmc.activation.remote.send.gateway;


import com.zyhl.yun.member.dto.GivenPrdOrderRelQueryReq;
import com.zyhl.yun.member.dto.GivenPrdOrderRelQueryResp;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/3 10:37
 * @descrition 集运0元30G免流
 */
public interface JiyunFlowSpecGateWay {

    /**
     * 查询集运免流接口
     *
     * @param directPaymentVo
     * @return
     */
    GivenPrdOrderRelQueryResp givenPrdOrderRelQuery(GivenPrdOrderRelQueryReq directPaymentVo);
}