package com.zyhl.yun.member.white.common.did;

import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Locale;

/**
 * Guid生成器
 *
 * <AUTHOR>
 */
@Slf4j
public class GuidGenerator {
    private String valueAfterMD5 = "";
    private static String sId;
    private static final String BAR = "-";
    private static final String COLON = ":";

    private static final String MD5 = "MD5";

    public String getMD5() {
        return MD5;
    }

    private GuidGenerator() {
        this.getRandomGUID(false);
    }

    public GuidGenerator(boolean secure) {
        this.getRandomGUID(secure);
    }

    private void getRandomGUID(boolean secure) {
        MessageDigest md5 = null;
        StringBuilder sbValueBeforeMD5 = new StringBuilder();

        try {
            md5 = MessageDigest.getInstance(getMD5());
        } catch (NoSuchAlgorithmException var12) {
            log.error("Error: ", var12);
        }

        if (md5 == null) {
            log.info("md5 is null");
            throw new RuntimeException("md5 is null");
        }

        try {
            long time = System.currentTimeMillis();
            long rand = RandomUtil.getRandom(secure).nextLong();

            sbValueBeforeMD5.append(sId);
            sbValueBeforeMD5.append(COLON);
            sbValueBeforeMD5.append(time);
            sbValueBeforeMD5.append(COLON);
            sbValueBeforeMD5.append(rand);
            String valueBeforeMD5 = sbValueBeforeMD5.toString();
            Assert.notNull(md5, "Get md5 MessageDigest is null");
            md5.update(valueBeforeMD5.getBytes());
            byte[] array = md5.digest();

            StringBuilder sb = new StringBuilder();
            for (byte value : array) {
                int b = value & 255;
                if (b < 16) {
                    sb.append('0');
                }
                sb.append(Integer.toHexString(b));
            }
            this.valueAfterMD5 = sb.toString();
        } catch (Exception var13) {
            log.error("Error occurred while getting MD5 MessageDigest.", var13);
        }

    }

    public String toString() {
        String raw = this.valueAfterMD5.toUpperCase();
        return raw.substring(0, 8) +
                BAR +
                raw.substring(8, 12) +
                BAR +
                raw.substring(12, 16) +
                BAR +
                raw.substring(16, 20) +
                BAR +
                raw.substring(20);
    }

    private static String randomGUID() {
        GuidGenerator guid = new GuidGenerator();
        return guid.toString();
    }

    private static String randomGUID(String splitStr) {
        return randomGUID().replace(BAR, splitStr);
    }

    public static String randomGUID(String splitStr, Locale locale) {
        return randomGUID(splitStr).toUpperCase(locale);
    }

    static {

        try {
            sId = InetAddress.getLocalHost().toString();
        } catch (UnknownHostException var3) {
            log.error("Error occurred while getting MD5 MessageDigest.", var3);
        }

    }
}
