-- 指定白名单列表
local whiteList = {"13590104821_0","14779767754_0","14779767755_0","14779767756_0","14779767757_0","13360302181_0",
"13424062603_0","13632753245_0","19898398810_0","800001477309_1","248900851819_1","800001482111_1","19878342211_0",
"800001477875_1","19878342212_0","312349646378_1","800006498816_1","224582019260_1","19878342213_0","363215608142_1",
"20240816172357552999_2","11180309173530006308_2","344963956374241292_2"}
-- 指定新旧机器配置信息
local newVsboArr = {"127.0.0.1:18088"}
local oldVsboArr = {"127.0.0.1:18089"}
local oldAdArr = {"127.0.0.1:18090"}
local oldPgwArr = {"127.0.0.1:18091"}
-- 旧现网vsbo机器
local oldProdVsboArr = {"127.0.0.1:18092"}
local conf_dict = ngx.shared.conf_dict
-- 新vsbo机器
for i = 1,#newVsboArr do
    local ip, port = newVsboArr[i]:match("^(.+):(.+)$")
    local index =i-1
    local ipKey = "newVsboIp" .. index
    local portKey = "newVsboPort" .. index
    conf_dict:set(ipKey,ip)
    conf_dict:set(portKey,tonumber(port))
end
-- 旧vsbo机器
for i = 1,#oldVsboArr do
    local ip, port = oldVsboArr[i]:match("^(.+):(.+)$")
    local index =i-1
    local ipKey = "oldVsboIp" .. index
    local portKey = "oldVsboPort" .. index
    conf_dict:set(ipKey,ip)
    conf_dict:set(portKey,tonumber(port))
end

-- 旧Ad机器
for i = 1,#oldAdArr do
    local ip, port = oldAdArr[i]:match("^(.+):(.+)$")
    local index = i - 1
    local ipKey = "oldAdIp" .. index
    local portKey = "oldAdPort" .. index
    conf_dict:set(ipKey,ip)
    conf_dict:set(portKey,tonumber(port))
end
-- 旧Pgw机器
for i = 1,#oldPgwArr do
    local ip, port = oldPgwArr[i]:match("^(.+):(.+)$")
    local index = i - 1
    local ipKey = "oldPgwIp" .. index
    local portKey = "oldPgwPort" .. index
    conf_dict:set(ipKey,ip)
    conf_dict:set(portKey,tonumber(port))
end

-- 旧现网vsbo机器
for i = 1,#oldProdVsboArr do
    local ip, port = oldProdVsboArr[i]:match("^(.+):(.+)$")
    local index = i - 1
    local ipKey = "oldProdVsboIp" .. index
    local portKey = "oldProdVsboPort" .. index
    conf_dict:set(ipKey,ip)
    conf_dict:set(portKey,tonumber(port))
end

conf_dict:set("newIndex",0)
conf_dict:set("oldIndex",0)
conf_dict:set("newTotal",#newVsboArr)
conf_dict:set("oldTotal",#oldVsboArr)

-- 设置白名单
local white_dict = ngx.shared.white_dict
for i = 1,#whiteList do
    white_dict:set(whiteList[i],"exit")
end
local json_path_dict = ngx.shared.json_path_dict
-- userId获取规则为：lua脚本远程调用接口获取userId
json_path_dict:set("raiNotifyEquityResult","5_Ad_1_channelNo")
json_path_dict:set("notifyMovieEquityRightsResult","3_orderNo")
-- userId获取规则为：userId_***
json_path_dict:set("notifyRightsSalesReturnResult","1_contractRoot.body.channelNo")

json_path_dict:set("userQryServiceJson1","1_Ad_0_orderId")
-- 5号规则：根据会员订单生成判定分流
json_path_dict:set("userQryServiceJson5","5_Ad_1_orderId")

-- 设置映射路径
local xml_path_dict = ngx.shared.xml_path_dict
-- 0号规则：直接获取字段值
xml_path_dict:set("userQryService","0_ProdVsbo_0_userQryService.userQryServiceReq.userId")
-- 1号规则：userId获取规则为：userId_***
xml_path_dict:set("userQryService1","1_ProdVsbo_0_userQryService.userQryServiceReq.userId")
-- 2号规则：userId获取规则为：***_userId
xml_path_dict:set("userQryService2","2_Ad_0_userQryService.userQryServiceReq.account")
xml_path_dict:set("userQryService2_1","2_Ad_0_userQryService.userQryServiceReq.userId")
-- 3号规则：***_rft分割规则，以_rft结尾则直接设置结果为白名单用户
xml_path_dict:set("userQryService3","3_Vsbo_0_userQryService.userQryServiceReq.userId")
-- 4号规则：直接转发至新vsbo
xml_path_dict:set("userQryService4","4_Vsbo")
-- 5号规则：根据会员订单生成判定分流
xml_path_dict:set("userQryService5","5_Ad_1_userQryService.userQryServiceReq.userId")

-- 6号规则：根据会员订单生成判定分流（uuid去除分隔符）
xml_path_dict:set("userQryService5","5_Ad_1_userQryService.userQryServiceReq.userId")

xml_path_dict:set("/cgw/fsapay/notifyTradeStatus","2_Pgw_1_msgReq.outOrderId")
xml_path_dict:set("notifyPaymentResultOfThirdParty","0_Ad_0_msgReq.featureData")

xml_path_dict:set("manageSubscribeRelation","0_Vsbo_0_manageSubscribeRelationReq.account")
xml_path_dict:set("manageSubscribeRelation_1","0_Vsbo_2_manageSubscribeRelationReq.userDomainId")
json_path_dict:set("notifyRightsSalesReturnResult","7_Ad")

xml_path_dict:set("deleteProduct_0","0_deleteProductReq.testEnv")
xml_path_dict:set("createProduct_0","0_createProductReq.productInfo.testEnv")
ngx.log(ngx.ERR,"init success")

local flow =  require "aspire/flowByService"

