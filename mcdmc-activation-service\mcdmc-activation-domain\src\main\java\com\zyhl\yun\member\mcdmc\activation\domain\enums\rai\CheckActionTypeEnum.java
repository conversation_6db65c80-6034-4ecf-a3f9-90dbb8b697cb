package com.zyhl.yun.member.mcdmc.activation.domain.enums.rai;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/10/08 17:25
 */
@Getter
@AllArgsConstructor
public enum CheckActionTypeEnum {
    ORDER_REDUCTION("1", "核减"),

    ORDER_INCREASE("2", "核增"),

    ORDER_REPUSH("3", "重推"),

    ORDER_RE_UNSUB("4", "补退");
    private final String type;
    private final String desc;
}
