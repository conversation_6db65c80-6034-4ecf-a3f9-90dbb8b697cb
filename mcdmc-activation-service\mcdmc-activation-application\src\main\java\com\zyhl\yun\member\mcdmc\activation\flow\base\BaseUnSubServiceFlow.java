package com.zyhl.yun.member.mcdmc.activation.flow.base;

import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import lombok.extern.slf4j.Slf4j;


/**
 * 退订统一流程
 *
 * <AUTHOR>
 * @since 2024/12/10 20:51
 */
@Slf4j
public abstract class BaseUnSubServiceFlow extends BaseDefaultServiceFlow {

    /**
     * 流程成功后执行动作
     */
    @Override
    protected void doFlowSuccess(WorkOrderDo workOrderDo, boolean isFinally, IFlowResult lastFaceResult) {
        ComSendInterfaceReq comSendReq = JSONUtil.toBean(workOrderDo.getWorkAttrs(), ComSendInterfaceReq.class);
        String resourceId = lastFaceResult == null ? null : lastFaceResult.getResourceId();
        // 退订成功更新商品实例状态
        MemberContextUtil.updateGoodsInstance(comSendReq, resourceId,
                isFinally, true, SendOperation.UN_SUB);
        // 执行原流程结束动作
        super.doFlowSuccess(workOrderDo, isFinally, lastFaceResult);
    }

    @Override
    protected void doFlowFail(WorkOrderDo workOrderDo) {
        ComSendInterfaceReq comSendReq = JSONUtil.toBean(workOrderDo.getWorkAttrs(), ComSendInterfaceReq.class);
        // 退订失败更新商品实例状态
        MemberContextUtil.updateGoodsInstance(comSendReq, null, true, false, SendOperation.UN_SUB);
    }
}
