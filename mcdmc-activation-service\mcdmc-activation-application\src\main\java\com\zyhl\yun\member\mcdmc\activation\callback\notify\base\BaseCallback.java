package com.zyhl.yun.member.mcdmc.activation.callback.notify.base;

import cn.hutool.core.util.JAXBUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.CallbackException;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.XMLUtil;
import com.zyhl.yun.member.mcdmc.activation.result.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;


/**
 * 回调基类
 *
 * @param <T> 回调请求入参
 * @param <R> 回调响应值
 * <AUTHOR>
 * @since 2024/06/21 09:59
 */
@Slf4j
public abstract class BaseCallback<T, R> implements ICallback {
    /**
     * 接口协议
     */
    protected MediaType getMediaType() {
        return MediaType.APPLICATION_JSON;
    }

    /**
     * 将请求入参字符串转化成指定对象
     *
     * @param notifyReqStr 请求入参
     */
    protected T convertReq(String notifyReqStr) {
        if (MediaType.APPLICATION_JSON.equals(getMediaType())) {
            return JSONUtil.toBean(notifyReqStr, getNotifyReqClass());
        } else if (MediaType.APPLICATION_XML.equals(getMediaType()) || MediaType.TEXT_XML.equals(getMediaType())) {
            return XMLUtil.xmlToBean(notifyReqStr, getNotifyReqClass());
        }
        return JSONUtil.toBean(notifyReqStr, getNotifyReqClass());
    }

    /**
     * 将响应对象转化为字符串
     *
     * @param notifyRsp 响应对象
     * @return 响应字符串
     */
    protected String convertRsp2Str(R notifyRsp) {
        if (MediaType.APPLICATION_JSON.equals(getMediaType())) {
            return JSONUtil.toJsonStr(notifyRsp);
        } else if (MediaType.APPLICATION_XML.equals(getMediaType()) || MediaType.TEXT_XML.equals(getMediaType())) {
            return JAXBUtil.beanToXml(notifyRsp);
        }
        return JSONUtil.toJsonStr(notifyRsp);
    }

    protected abstract Class<T> getNotifyReqClass();

    /**
     * 校验请求入参
     *
     * @param callbackContext 回调上下文
     * @throws CallbackException 校验异常返回错误
     */
    protected abstract void doCheck(CallbackContext<T, R> callbackContext) throws CallbackException;

    /**
     * 回调通知
     *
     * @param callbackContext 回调上下文
     */
    protected abstract void doNotify(CallbackContext<T, R> callbackContext);


    /**
     * 判断回调结果是否成功
     *
     * @param notifyReq 回调请求入参
     */
    protected abstract boolean isSuccess(T notifyReq);

    /**
     * 获取回调成功地响应码
     */
    protected abstract BaseResult getSuccessResult();

    protected abstract String getSearchCondition(T notifyReq);

    /**
     * 构建响应对象
     *
     * @param callbackContext 回调上下文
     * @param result          响应结果
     * @return 响应对象
     */

    protected abstract R buildNotifyRsp(CallbackContext<T, R> callbackContext, BaseResult result);

    /**
     * 获取回调对应的操作类型
     *
     * @return {@link SendOperation}
     */
    protected abstract SendOperation getSendOperation();

}
