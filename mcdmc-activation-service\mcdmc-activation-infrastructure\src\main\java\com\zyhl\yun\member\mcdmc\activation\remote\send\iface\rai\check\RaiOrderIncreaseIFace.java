package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.check;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.mono.ProcessByThrowExceptionUtil;
import com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceExtendServiceId;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.facade.OrderServiceFacade;
import com.zyhl.yun.member.mcdmc.activation.constants.SymbolConstant;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.rai.CheckActionTypeEnum;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.sub.RaiSubBaseIFace;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiBaseProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiNormalProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.RaiSubReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.RaiSubRsp;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import com.zyhl.yun.member.order.domain.OrderDo;
import com.zyhl.yun.member.product.domain.channel.enums.OrderSettlementFlagEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Objects;

import static com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceExtendServiceId.UPDATE_GOODS_INSTANCE_EXTEND;

/**
 * 核增流程
 * <p>
 * <p>
 * com.huawei.jaguar.vsbo.strategy.differencedIgital.ComplementRelationHandler#doDigitalInvoke
 *
 * <AUTHOR>
 * @since 2024/09/29 18:24
 */
@Slf4j
@Component
public class RaiOrderIncreaseIFace extends RaiSubBaseIFace {
    @Resource
    private RaiNormalProperties raiProperties;

    @Override
    protected RaiBaseProperties getBaseRaiProperties() {
        return raiProperties;
    }

    @Override
    protected String getOrderChannelCode(String userId, String parentOrderId) {
        OrderDo parentOrder = OrderServiceFacade.getOrder(parentOrderId, userId, false);
        if (Objects.isNull(parentOrder)) {
            log.error("未找到父订单信息，默认使用原价渠道号，userId:{},parentOrderId:{}", userId, parentOrderId);
            return raiProperties.getOrderChannelCode(OrderSettlementFlagEnum.ORIGINAL.getCode());
        }
        String extInfoByKey = (String) parentOrder.getExtInfoByKey(OrderDo.ExtInfoKeys.ORDER_SETTLEMENT_FLAG);
        return raiProperties.getOrderChannelCode(extInfoByKey);
    }

    @Override
    protected RaiSubReq.Body buildRequestBody(ComSendInterfaceReq comReq) {
        RaiSubReq.Body body = super.buildRequestBody(comReq);
        // 核增需要传标记通知权益中心不发货只补订单
        RaiSubReq.ProdInfo prodInfo = body.getProdInfo().get(0);
        String extFieldStr = prodInfo.getExtField();
        HashMap<String, String> extFieldMap = JSONUtil.toBean(extFieldStr, HashMap.class);
        // 若该字段存在，则通知权益中心不发货只补订单
        extFieldMap.put("delivery", "2");
        if (StrUtil.isNotEmpty(comReq.getParentCampaignId())) {
            extFieldMap.put(OrderDo.ExtInfoKeys.CAMPAIGN_ID, comReq.getParentCampaignId());
        }

        prodInfo.setExtField(JSONUtil.toJsonStr(extFieldMap));
        body.setProdInfo(Collections.singletonList(prodInfo));
        return body;
    }


    @Override
    protected void doBusiComplete(InterfaceContext<RaiSubReq, RaiSubRsp> interfaceContext) {
        Object resultCode = ProcessByThrowExceptionUtil.runNotThrowExceptionProcessByReturn(() ->
                interfaceContext.getInterfaceRspObj().getContractRoot().getBody().getResultCode());
        this.updateGoodsInstance(interfaceContext, String.valueOf(resultCode));
    }

    @Override
    protected void doException(InterfaceContext<RaiSubReq, RaiSubRsp> interfaceContext, Exception e) throws Exception {
        this.updateGoodsInstance(interfaceContext, "innerException");
        throw e;
    }

    /**
     * 更新商品实例的核减标识
     *
     * @param interfaceContext 接口上下文
     * @param resultCode       结果码
     */
    private void updateGoodsInstance(InterfaceContext<RaiSubReq, RaiSubRsp> interfaceContext, String resultCode) {
        String userId = interfaceContext.getWorkOrderDo().getUserId();
        String orderId = interfaceContext.getWorkOrderDo().getOrderId();
        String goodsInstanceId = interfaceContext.getComSendReq().getGoodsInstanceId();
        GoodsInstanceDo goodsInstanceDo = MemberContextUtil.qryGoodsInstanceDo(userId, goodsInstanceId);
        if (goodsInstanceDo == null) {
            log.error("根据orderId={}和goodInstanceId={}查询不到商品实例", orderId, goodsInstanceId);
            return;
        }
        // 更新核对标识
        String rightReduction = String.join(SymbolConstant.PIPE, this.getCheckActionTypeEnum().getType(),
                DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        String oriRightReduction = goodsInstanceDo.getRightReduction();
        if (StringUtils.hasText(oriRightReduction)) {
            goodsInstanceDo.setRightReduction(oriRightReduction + "," + rightReduction);
        } else {
            goodsInstanceDo.setRightReduction(rightReduction);
        }
        log.info("更新商品实例核增/重推标识,userId={},orderId={},goodsInstanceId={},oriRightReduction={},rightReduction={}",
                userId, orderId, goodsInstanceId, oriRightReduction, rightReduction);
        DomainServiceContext memberContext = new DomainServiceContext(UPDATE_GOODS_INSTANCE_EXTEND);
        memberContext.putInstance(goodsInstanceDo);
        memberContext.writeAndFlush();
        updateParentInstanceByRightReduction(goodsInstanceDo);

    }


    protected CheckActionTypeEnum getCheckActionTypeEnum() {
        // 核增标识
        return CheckActionTypeEnum.ORDER_INCREASE;
    }

}
