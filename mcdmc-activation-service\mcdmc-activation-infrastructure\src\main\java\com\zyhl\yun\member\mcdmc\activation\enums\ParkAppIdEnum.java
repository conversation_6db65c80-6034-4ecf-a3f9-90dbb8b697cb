package com.zyhl.yun.member.mcdmc.activation.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Description:
 * @Author: linjia
 * @Date: 2024/11/25
 */
@AllArgsConstructor
@Getter
public enum ParkAppIdEnum {

    PP_PARK(0, "pp_park"),
    ONE_PARK(1, "one_park"),
    JIE_PARK(2, "jie_park");

    private final int appIdIndex;
    private final String name;

    /**
     * 根据 productId 部分匹配 name 获取对应的枚举对象
     *
     * @param productId 要匹配的产品id（name的一部分）
     * @return 对应的枚举对象，如果找不到返回 null
     */
    public static ParkAppIdEnum getByName(String productId) {
        return Arrays.stream(values())
                .filter(enumValue -> productId.contains(enumValue.name)) // 部分匹配
                .findFirst()
                .orElse(null); // 如果没有匹配的返回 null
    }
}
