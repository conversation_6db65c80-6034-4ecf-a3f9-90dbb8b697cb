package com.zyhl.yun.member.mcdmc.activation.domain.remote;

import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 接口响应结果
 *
 * <AUTHOR>
 * @since 2024/08/26 18:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IFlowResult {
    /**
     * 下一步执行动作
     */
    private NextHintEnum nextHintEnum;
    /**
     * 回调条件（可选，如果有回调且成功响应）
     */
    private String callbackCondition;

    /**
     * 资产id
     */
    private String resourceId;

    /**
     * 错误信息
     */
    private String message;

    public IFlowResult(NextHintEnum nextHintEnum, String callbackCondition, String resourceId) {
        this.nextHintEnum = nextHintEnum;
        this.callbackCondition = callbackCondition;
        this.resourceId = resourceId;
    }

    public static IFlowResult result(NextHintEnum nextHintEnum, String callbackCondition) {
        return new IFlowResult(nextHintEnum, callbackCondition, callbackCondition);
    }

    public static IFlowResult result(NextHintEnum nextHintEnum, String callbackCondition, String resourceId) {
        return new IFlowResult(nextHintEnum, callbackCondition, resourceId);
    }

    public static IFlowResult finish(String resourceId, String message, Object... args) {
        return new IFlowResult(NextHintEnum.FINISH, resourceId, resourceId, String.format(message, args));
    }

    public static IFlowResult next() {
        return new IFlowResult(NextHintEnum.NEXT, null, null);
    }

    public static IFlowResult cancel(String message) {
        return new IFlowResult(NextHintEnum.CANCEL, null, null, message);
    }

    public static IFlowResult cancel(String message, Object... args) {
        String cancelMsg = String.format(message, args);
        return IFlowResult.cancel(cancelMsg);
    }

    public static IFlowResult reset(String message, Object... args) {
        String resetMsg = String.format(message, args);
        return new IFlowResult(NextHintEnum.RESET, null, null, resetMsg);
    }

    /**
     * 流程是否被提前结束-成功结束
     */
    public boolean isSuccessFinish() {
        return NextHintEnum.FINISH.equals(nextHintEnum);
    }

    /**
     * 流程是否被提前结束-失败结束
     */
    public boolean isFailFinish() {
        return NextHintEnum.FAIL_FINISH.equals(nextHintEnum);
    }

    /**
     * 流程是否需要重试
     */
    public boolean isRestart() {
        return NextHintEnum.RESTART.equals(nextHintEnum);
    }

    public boolean isContinue() {
        return NextHintEnum.NEXT.equals(nextHintEnum);
    }
}
