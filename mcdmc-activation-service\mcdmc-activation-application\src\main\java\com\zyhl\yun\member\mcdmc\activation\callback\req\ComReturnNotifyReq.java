package com.zyhl.yun.member.mcdmc.activation.callback.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


/**
 * 退订回调请求统一请求体
 *
 * <AUTHOR>
 * @since 2024/06/22 11:05
 */
@Data
public class ComReturnNotifyReq {

    @JsonProperty("contractRoot")
    private ContractRootDTO contractRoot;

    @Data
    public static class ContractRootDTO {
        /**
         * 会话控制内容
         */
        @JsonProperty("head")
        private Head head;

        /**
         * 业务内容
         */
        @JsonProperty("body")
        private Body body;

        @Data
        public static class Head {
            @JsonProperty("channelCode")
            private String channelCode;
            @JsonProperty("reqTime")
            private String reqTime;
            @JsonProperty("sign")
            private String sign;
            @JsonProperty("apiId")
            private String apiId;
            @JsonProperty("transactionId")
            private String transactionId;

            @Override
            public String toString() {
                return "Head{" +
                        "channelCode='" + channelCode + '\'' +
                        ", apiId='" + apiId + '\'' +
                        ", reqTime='" + reqTime + '\'' +
                        ", sign=" + sign +
                        ", transactionId=" + transactionId +
                        '}';
            }
        }

        @Data
        public static class Body {
            /**
             * 渠道订单号
             */
            @JsonProperty("channelNo")
            private String channelNo;
            /**
             * 退货状态最新变更时间, 格式YYYYMMDDHH24MISS
             */
            @JsonProperty("returnUpdateTime")
            private String returnUpdateTime;
            /**
             * 退货状态, 1-退货成功，2-退货失败，4-退货中（权益回收中）
             */
            @JsonProperty("returnStatus")
            private Integer returnStatus;

            @Override
            public String toString() {
                return "Body{" +
                        "channelNo='" + channelNo + '\'' +
                        ", returnUpdateTime='" + returnUpdateTime + '\'' +
                        ", returnStatus=" + returnStatus +
                        '}';
            }
        }

        @Override
        public String toString() {
            return "ContractRootDTO{" +
                    "head='" + head + '\'' +
                    ", body='" + body + '\'' +
                    '}';
        }
    }

    /**
     * 回调结果返回成功
     */
    public static final Integer RETURN_SUSS = 1;

    /**
     * 回调结果返回失败
     */
    public static final int RETURN_FAIL = 2;

    /**
     * 回调结果返回权益回收中
     */
    public static final int RETURNING = 4;
}
