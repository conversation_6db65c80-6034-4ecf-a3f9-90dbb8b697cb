package com.zyhl.yun.member.mcdmc.activation.driver;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.common.domain.framework.BaseLocalDriver;
import com.zyhl.yun.member.common.domain.framework.DomainEntityPersistenceWrapper;
import com.zyhl.yun.member.mcdmc.activation.convertor.WorkOrderAttrConvertor;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderAttrDo;
import com.zyhl.yun.member.mcdmc.activation.domains.conditions.QueryWorkAttrCondition;
import com.zyhl.yun.member.mcdmc.activation.mapping.WorkOrderAttrMethodMapping;
import com.zyhl.yun.member.mcdmc.activation.po.WorkOrderAttrPo;
import com.zyhl.yun.member.mcdmc.activation.repository.WorkOrderAttrRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/11 15:15
 */
@Slf4j
@Component
public class WorkOrderAttrDriver extends BaseLocalDriver<WorkOrderAttrDo> {

    @Resource
    private transient WorkOrderAttrConvertor workOrderAttrConvertor;
    @Resource
    private transient WorkOrderAttrRepository workOrderAttrRepository;

    @Override
    public int doWrite(DomainEntityPersistenceWrapper domainEntityWrapper) {
        if (log.isDebugEnabled()) {
            log.debug("[INFRA] doWrite start. workOrderDo:{}", domainEntityWrapper.getData());
        }

        String serviceId = domainEntityWrapper.getDomainServiceContext().getServiceId();
        // 类转换
        WorkOrderAttrDo workOrderAttrDo = (WorkOrderAttrDo) domainEntityWrapper.getData();
        WorkOrderAttrPo workOrderAttrPo = workOrderAttrConvertor.toPO(workOrderAttrDo);
        WorkOrderAttrMethodMapping.WRITE writer = WorkOrderAttrMethodMapping.getWriterByServiceId(serviceId);
        if (null == writer) {
            log.error("[INFRA] doWrite error. not supported entity type:{}", domainEntityWrapper.getData().getClass());
            throw new ServiceException("WorkOrderDriver没有对应的writer,serviceId=" + serviceId);
        }
        return writer.exec(workOrderAttrRepository, workOrderAttrPo);
    }

    @Override
    public WorkOrderAttrDo doReadByPk(Serializable pk) {
        return null;
    }

    @Override
    public List<? extends WorkOrderAttrDo> doReadByCondition(BaseCondition condition) {
        if (condition instanceof QueryWorkAttrCondition) {
            QueryWorkAttrCondition qryCondition = (QueryWorkAttrCondition) condition;
            LambdaQueryWrapper<WorkOrderAttrPo> attrQryWrapper = new LambdaQueryWrapper<>();
            attrQryWrapper.in(WorkOrderAttrPo::getWorkId, qryCondition.getWorkIdList());
            attrQryWrapper.in(CollUtil.isNotEmpty(qryCondition.getAttrKeyList()), WorkOrderAttrPo::getAttrCode, qryCondition.getAttrKeyList());
            attrQryWrapper.eq(StringUtils.isNotEmpty(qryCondition.getUserId()), WorkOrderAttrPo::getUserId, qryCondition.getUserId());
            List<WorkOrderAttrPo> workOrderAttrPoList = workOrderAttrRepository.list(attrQryWrapper);
            return workOrderAttrConvertor.toDoList(workOrderAttrPoList);
        } else {
            log.error("[INFRA] doReadByCondition error. not supported entity type:{}", condition.getClass());
            throw new ServiceException("WorkOrderAttrDriver not supported condition,class=" + condition.getClass());
        }
    }

    @Override
    public Long doGetCount(BaseCondition condition) {
        return 0L;
    }

    @Override
    protected List<Class> getSupportedClass() {
        return Collections.singletonList(WorkOrderAttrDo.class);
    }
}
