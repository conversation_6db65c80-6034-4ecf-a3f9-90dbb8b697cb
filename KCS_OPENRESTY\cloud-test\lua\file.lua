file={}
-- 定义读取配置文件的函数
function file.get_value_by_key(key)
    -- 定义文件路径
    local file_path = "/usr/local/openresty/configmap/openresty.properties"
    -- 打开文件
    local file, err = io.open(file_path, "r")
    if not file then
        return nil, "无法打开文件: " .. err
    end

    -- 逐行读取文件内容
    for line in file:lines() do
        -- 去除行首尾的空白字符
        line = line:match("^%s*(.-)%s*$")

        -- 忽略空行和注释行（以 # 开头的行）
        if line ~= "" and not line:match("^#") then
            -- 解析 key=value
            local current_key, value = line:match("^(.-)=(.*)$")
            if current_key == key then
                file:close()  -- 关闭文件
                return value  -- 返回对应的 value
            end
        end
    end
    -- 关闭文件
    file:close()
    -- 如果没有找到对应的 key，返回 nil
    return nil, "未找到 key: " .. key
end

return file