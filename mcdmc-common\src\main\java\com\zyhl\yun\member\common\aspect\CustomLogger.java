package com.zyhl.yun.member.common.aspect;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 加了该注解的controller方法的请求响应报文会打印到sub文件
 *
 * <AUTHOR>
 * @since 2024/05/01 16:54
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CustomLogger {

    /**
     * 是否打印请求URI
     */
    boolean logUri() default true;

    /**
     * 是否打印类名
     */
    boolean logClassName() default true;

    /**
     * 是否打印总耗时
     */
    boolean logTotalConsume() default true;

    /**
     * 是否打印请求头
     */
    boolean logHeaders() default true;

    /**
     * 是否打印请求参数
     */
    boolean logInput() default true;

    /**
     * 是否打印响应结果
     */
    boolean logOutput() default true;

    /**
     * 是否打印异常
     */
    boolean logException() default true;

}
