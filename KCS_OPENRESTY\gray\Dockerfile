FROM b08ypcg01-53ab9ddf.ecis.guangzhou-2.cmecloud.cn/prod/mcdmc-openresty-service:1.0.0

#请修改为应用端口
EXPOSE 80 443

ENV TZ=Asia/Shanghai

#请修改应用名
ENV applicationName=mcdmc-openresty-service-starter
#按照应用实际需求修改

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
RUN sed -i s/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g /etc/apt/sources.list
#RUN apt update && apt upgrade -y && apt install curl -y
#RUN apt install procps -y
#RUN apt install vim -y
#RUN apt install util-linux -y
#RUN apt install iputils-ping -y


#创建自定义lua脚本目录
RUN mkdir /usr/local/openresty/lualib/aspire
RUN mkdir /usr/local/openresty/temp
COPY KCS_OPENRESTY/gray/lua /usr/local/openresty/lualib/aspire
COPY KCS_OPENRESTY/gray/conf/nginx.conf /usr/local/openresty/nginx/conf/nginx.conf

CMD  /usr/local/openresty/nginx/sbin/nginx  -g  "daemon off;"





