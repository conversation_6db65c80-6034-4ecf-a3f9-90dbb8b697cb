package com.zyhl.yun.member.mcdmc.activation.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RedisLockOperator {


    private static final String DEL_LUA_SCRIPT = "local retn=false\n" +
            "local lockkey=KEYS[1]\n" +
            "local lockId=ARGV[1]\n" +
            "local lockValue=redis.call('get',lockkey)\n" +
            "if lockValue==lockId then\n" +
            "  redis.call('del',lockkey)\n" +
            "  retn=true\n" +
            "end\n" +
            "return retn \n";
    private static final String UPDATE_LUA_SCRIPT = "local retn=false\n" +
            "local lockKey=KEYS[1]\n" +
            "local lockId=ARGV[1]\n" +
            "local ttlSecond=ARGV[2]\n" +
            "local lockValue=redis.call('get',lockKey)\n" +
            "if lockValue==lockId then\n" +
            "  redis.call('set',lockKey,lockId)\n" +
            "  redis.call('expire',lockKey,ttlSecond)\n" +
            "  retn=true\n" +
            "end\n" +
            "return retn \n";
    private static final int WAIT_TIMEOUT = 1000;
    private static final int MAX_TIMEOUT = 10000;

    public interface RLockCallback {
        Object exec(Object in);
    }

    public static final ThreadLocal<String> valFlag = new ThreadLocal<>();

    @Resource
    private RedisTemplate<String, String> redisTemplate;


    public String lock(String lockKey, long ttlSecond) {
        String uuid = UUID.randomUUID().toString();
        Boolean result = redisTemplate.execute((RedisConnection connection) -> {
            Expiration expiration = Expiration.from(ttlSecond, TimeUnit.SECONDS);
            return connection.set(lockKey.getBytes(StandardCharsets.UTF_8),
                    uuid.getBytes(StandardCharsets.UTF_8),
                    expiration, RedisStringCommands.SetOption.ifAbsent());
        });
        log.info("redis lock lockKey={},result={}", lockKey, result);
        return Boolean.TRUE.equals(result) ? uuid : null;
    }

    public boolean continueLock(String lockKey, String lockId, long ttlSecond) {
        //执行脚本
        Boolean result = redisTemplate.execute((RedisConnection connection) ->
                connection.eval(UPDATE_LUA_SCRIPT.getBytes(StandardCharsets.UTF_8), ReturnType.fromJavaType(Boolean.class), 1,
                        lockKey.getBytes(StandardCharsets.UTF_8), lockId.getBytes(StandardCharsets.UTF_8),
                        String.valueOf(ttlSecond).getBytes(StandardCharsets.UTF_8)));
        log.info("redis continueLock lockKey={},result={}", lockKey, result);
        return result != null;
    }

    public boolean unlock(String lockKey, String lockId) {
        //批量get+set

        //执行脚本
        Boolean result = redisTemplate.execute((RedisConnection connection) -> {
            return connection.eval(DEL_LUA_SCRIPT.getBytes(StandardCharsets.UTF_8), ReturnType.fromJavaType(Boolean.class), 1,
                    lockKey.getBytes(StandardCharsets.UTF_8), lockId.getBytes(StandardCharsets.UTF_8));
        });
        log.info("redis unlock lockKey={},result={}", lockKey, result);
        return result != null;
    }

    public Object execute(RLockCallback callback, String lockKey, long ttlSecond, Object in) throws Exception {
        if (callback != null) {
            long start = System.currentTimeMillis();
            long end = start + MAX_TIMEOUT;
            String lockId = null;
            try {
                lockId = lock(lockKey, ttlSecond);
                while (!StringUtils.hasLength(lockId) && System.currentTimeMillis() < end) {
                    Thread.sleep(WAIT_TIMEOUT);//暂停1s
                    lockId = lock(lockKey, ttlSecond);
                }
                if (StringUtils.hasLength(lockId)) {
                    log.info("lock success lockKey={},lockId={}", lockKey, lockId);
                    //执行脚本
                    return callback.exec(in);
                } else {
                    throw new Exception("redis lock fail,lockKey=" + lockKey);
                }
            } finally {
                if (StringUtils.hasLength(lockId)) {
                    Boolean result = unlock(lockKey, lockId);
                    log.info("unlock lockId={},result={},cost={}", lockId, result, (System.currentTimeMillis() - start));
                }
            }
        }
        return null;
    }

    public void setRedisTemplate(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
}
