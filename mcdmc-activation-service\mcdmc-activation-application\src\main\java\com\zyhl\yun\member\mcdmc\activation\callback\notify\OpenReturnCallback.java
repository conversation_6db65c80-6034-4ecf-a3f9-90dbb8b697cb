package com.zyhl.yun.member.mcdmc.activation.callback.notify;

import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.ResourceServiceId;
import com.zyhl.yun.member.common.enums.MemberResourceStateEnum;
import com.zyhl.yun.member.common.enums.ResourceTypeEnum;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.resource.domain.ResourceDo;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.CallbackContext;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.CallbackTemplate;
import com.zyhl.yun.member.mcdmc.activation.callback.req.OpenReturnNotifyReq;
import com.zyhl.yun.member.mcdmc.activation.callback.rsp.OpenReturnNotifyRsp;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.CallbackException;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.result.BaseResult;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

/**
 * 一级能开退订回调
 * <p>
 * com.huawei.jaguar.vsbo.service.action.OrderAction#flowCardOrderResult
 * </p>
 *
 * <AUTHOR>
 * @since 2024/07/02 10:10
 */
@Component
public class OpenReturnCallback extends CallbackTemplate<OpenReturnNotifyReq, OpenReturnNotifyRsp> {
    @Override
    protected void doCheck(CallbackContext<OpenReturnNotifyReq, OpenReturnNotifyRsp> callbackContext) throws CallbackException {
        doCheckEmpty(callbackContext.getCallbackReq());
    }

    @Override
    protected void doAfterNotify(CallbackContext<OpenReturnNotifyReq, OpenReturnNotifyRsp> callbackContext,
                                 GoodsInstanceDo goodsInstanceExtendDo) {
        // 退订更新会员资产为失效
        if (isSuccess(callbackContext.getCallbackReq())) {
            DomainServiceContext memberContext = new DomainServiceContext(ResourceServiceId.UPDATE_OPEN_FREE_RESOURCE_IN_UNSUBSCRIBE);
            ResourceDo resourceDo = memberContext.newInstance(ResourceDo.class);
            resourceDo.setUserId(goodsInstanceExtendDo.getUserId());
            resourceDo.setResourceType(ResourceTypeEnum.OPEN_FREE_FLOW);
            // 这里必须使用父订单id，否则重构前订购的数据，在重构后退订会无法更新用户资产表的状态（由于商品实例104类型resourceId字段割接有误）
            ComSendInterfaceReq comSendReq = JSONUtil.toBean(callbackContext.getSendServiceFlowLogDo().getExtParams(), ComSendInterfaceReq.class);
            resourceDo.setParentOrderId(comSendReq.getParentOrderId());
            resourceDo.setState(MemberResourceStateEnum.INVALID.getState());
            memberContext.writeAndFlush();
        }
    }

    @Override
    protected boolean isSuccess(OpenReturnNotifyReq notifyReq) {
        return "RC".equals(notifyReq.getStatus());
    }

    @Override
    protected BaseResult getSuccessResult() {
        return BaseResult.sc("0", "success");
    }

    @Override
    protected String getSearchCondition(OpenReturnNotifyReq notifyReq) {
        return notifyReq.getReturnId();
    }

    @Override
    protected OpenReturnNotifyRsp buildNotifyRsp(CallbackContext<OpenReturnNotifyReq, OpenReturnNotifyRsp> callbackContext, BaseResult result) {
        OpenReturnNotifyRsp openReturnNotifyRsp = new OpenReturnNotifyRsp();
        openReturnNotifyRsp.setHRet(result.getResultCode());
        openReturnNotifyRsp.setVersion(callbackContext.getCallbackReq().getVersion());
        return openReturnNotifyRsp;
    }

    @Override
    protected MediaType getMediaType() {
        return MediaType.APPLICATION_XML;
    }

    @Override
    protected Class<OpenReturnNotifyReq> getNotifyReqClass() {
        return OpenReturnNotifyReq.class;
    }

    @Override
    protected SendOperation getSendOperation() {
        return SendOperation.UN_SUB;
    }
}
