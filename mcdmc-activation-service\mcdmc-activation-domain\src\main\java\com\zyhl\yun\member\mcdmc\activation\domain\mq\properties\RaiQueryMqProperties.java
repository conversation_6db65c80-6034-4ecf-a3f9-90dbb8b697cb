package com.zyhl.yun.member.mcdmc.activation.domain.mq.properties;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 流程重试mq配置
 *
 * <AUTHOR>
 * @since 2024/07/24 10:40
 */
@Data
@Configuration
@ConfigurationProperties("rocketmq.consumer.rai-query")
public class RaiQueryMqProperties {

    /**
     * 权益主动查询的topic
     */
    private String raiQueryTopic;

    /**
     * 权益主动查询的tag
     */
    private String raiQueryTag;

}
