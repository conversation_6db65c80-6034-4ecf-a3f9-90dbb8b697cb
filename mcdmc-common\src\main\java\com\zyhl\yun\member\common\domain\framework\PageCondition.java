
package com.zyhl.yun.member.common.domain.framework;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 分页信息
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageCondition implements Serializable {


    private static final long serialVersionUID = -244976954092017558L;

    /**
     * 页号
     */
    private Integer pageNo;

    /**
     * 页大小
     */
    private Integer pageSize;
}
