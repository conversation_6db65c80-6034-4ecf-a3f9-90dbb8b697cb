package com.zyhl.yun.member.common.constants;

public class RegexConstant {

    /**
     * 8位数字正则表达式
     */
    public static final String EIGHT_NUMBER_CHAR = "^(\\+?852\\s?)?(4|5|6|7|8|9)\\d{7}$";

    /** 物联卡手机号码正则判断 */
    public static final String IOT_NUMBER_CHAR = "^(10648|10647|1440|1441[0-6]|14419|1442[0-5]|1476[4-6]|1789[2-4]|1849|172[123459]|1727[0-3]|148|154[5-9])(.*?)";

    /**
     * 中国大陆手机号码正则表达式
     */
    public static final String CHINA_PHONE_CHAR = "^(\\+?86\\s?)?1[3-9]\\d{9}$";
}
