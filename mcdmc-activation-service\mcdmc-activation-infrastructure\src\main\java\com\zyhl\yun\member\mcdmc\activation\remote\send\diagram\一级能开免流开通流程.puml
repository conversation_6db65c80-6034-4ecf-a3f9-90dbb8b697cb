@startuml
'https://plantuml.com/sequence-diagram

autonumber
participant 定时任务域 as task
participant 服开域 as open
database 数据库 as db
participant 订单域 as order
participant 会员域 as member
task-> open: 收到一级能开免流通知（写工单请求）
open->open: 根据transactionId加锁
open-->task: 加锁失败，直接异常返回
open -> db: 根据入参查询工单
open->db:若工单存在则不写入数据库，否则生成新的工单
open-->task:若工单为完成则直接结束流程
open->db:查询业务流程
autonumber 7.1
open--> task: 查不到业务流程则直接结束工单\n（工单为已取消）
open-->task: 工单重试次数超过配置次数，\n直接结束流程（工单为已失败）
autonumber 8
open->member: 查询用户所有生效中的商品，并得到最大的失效时间
autonumber 8.1
open-->member: 如果用户有生效中的商品，\n则更新会员资产对应的失效时间为商品实例里的最大失效时间
open-->db: 更新工单状态为已完成
autonumber 9
open-> open: 手机号码去86
open->一级能开: 调用接口号码校验
open->一级能开: 调用接口业务资格校验
alt 当月已退订过
    open->task: 生成隔月重试的定时任务
    open->db: 更新工单状态为已完成
end

 
@enduml