package com.zyhl.yun.member.activity.remote.client;

import com.zyhl.yun.member.activity.remote.config.FeignConfig;
import com.zyhl.yun.member.activity.remote.req.QueryOrCreateUserIdReq;
import com.zyhl.yun.member.activity.remote.resp.QueryOrCreateUserIdResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @date 2024/1/20 11:42
 * @descrition 调用vsbo模块客户端
 */
@FeignClient(name = "vsboClient", url = "${vsbo-url}", configuration = {FeignConfig.class})
public interface VsboClient {
    /**
     * 调用vsbo，通过手机号查询或创建用户信息
     *
     * @param queryOrCreateUserIdReq
     * @return QueryOrCreateUserIdResp
     */
    @PostMapping(value = "/cloudSEE/openApi/queryOrCreateUserID", produces = MediaType.TEXT_XML_VALUE,
        consumes = MediaType.TEXT_XML_VALUE)
    QueryOrCreateUserIdResp queryUserIdByMobile(QueryOrCreateUserIdReq queryOrCreateUserIdReq);
}
