package com.zyhl.yun.member;

import cn.hutool.core.util.JAXBUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.JiyunServiceId;
import com.zyhl.yun.member.domain.goodsinstance.MemberQueryConditionSubScriptionResp;
import com.zyhl.yun.member.domain.goodsinstance.QueryConditionSubScriptionReq;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.domain.invite.MemberManageSubscribeRelationResp;
import com.zyhl.yun.member.domain.jiyun.domain.FlowSpecSubscriptionDo;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.dto.QueryJiyunFlowSpecCondition;
import com.zyhl.yun.member.invite.client.VsboMemberOrderClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceExtendServiceId.QUERY_CONDITION_GOODS_INSTANCE;

/**
 * <AUTHOR>
 * @since 2024/10/11 16:01
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
@ComponentScan(basePackages = {"com.zyhl.yun.member", "com.zyhl.yun.member.common"})
@MapperScan(value = {"com.zyhl.yun"}, annotationClass = Mapper.class)
@TestPropertySource(properties = {
        "nacos.namespace=0f50ffae-87c4-4614-888c-092c9d0c053a",
        "driver.remote.type=ip",
        "spring.profiles.active=dev"
})
public class JiYunTest {

    @Resource
    private VsboMemberOrderClient vsboMemberOrderClient;

    @Test
    public void test() {
//        String xmlReqStr = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?><manageSubscribeRelationReq><who>502</who><subChannelID></subChannelID><account>***********</account><saleType>101</saleType><productID>lxiang05</productID><accessSource>1</accessSource><buyType>1</buyType><eventType>1</eventType><thirdPlatformTradID>1110997350313234433</thirdPlatformTradID><startTime>2024-03-31 09:55:00</startTime><endTime>2099-11-01 09:55:00</endTime></manageSubscribeRelationReq>";
//        MemberManageSubscribeRelationResp obj = vsboMemberOrderClient.manageSubscribeRelation(xmlReqStr);
//        log.info("xmlRspStr:{}", obj);
//        Assert.assertNotNull(obj);
    }

    @Test
    public void testReadJiYun() {
        DomainServiceContext jiyunServiceContext = new DomainServiceContext(JiyunServiceId.UPDATE_JIYUN_FLOW_SPEC);
        QueryJiyunFlowSpecCondition qryCondition = new QueryJiyunFlowSpecCondition();
        qryCondition.setUserId("************");
        qryCondition.setSourceApp("P00000011765");
        qryCondition.setSourceGoodsId("27862");
        qryCondition.setSourceOrderNo("31863b7650134e22a546755a07d0351a");
        FlowSpecSubscriptionDo flowSpecSubDo = jiyunServiceContext.readFirst(qryCondition, FlowSpecSubscriptionDo.class);
        log.info("flowSpecSubDo:{}", flowSpecSubDo);
        Assert.assertNotNull(flowSpecSubDo);

    }


    @Test
    public void testReadGoodsInstanceCon() {
        DomainServiceContext goodInstanceContext = new DomainServiceContext(QUERY_CONDITION_GOODS_INSTANCE);
        // 创建查询条件对象
        QueryGoodsInstanceCondition queryGoodsInstanceCondition = new QueryGoodsInstanceCondition();

        // 设置商品实例ID列表（模拟3个实例ID）
        queryGoodsInstanceCondition.setGoodsInstanceIdList(
                Arrays.asList("1905550212903104512", "RHR016_1905550212903104512", "106d1a84-7bec-4c00-bd9a-b604cac1fe16"));

        // 设置商品ID列表（模拟2个商品ID）
        queryGoodsInstanceCondition.setGoodsIdList(
                Arrays.asList("sliver_vip_family_monthly_call_5", "BENEFIT:sliver_vip_family_monthly_call_5:RHR016", "youku_vip_1month_sub"));

        // 设置订单ID列表（JSON格式）
        queryGoodsInstanceCondition.setOrderIdList(
                Arrays.asList("900000466592_9ae3e164-016a-3145-9029-7182ea88ae72_CloudSpaceContractSubscribeOrder", "900000466592_24bef8ab-d43e-3c0b-b93e-7ffa2f21a2b8_CloudSpaceContractSubscribeOrder", "800006498812_ad16112c-d6e9-47d7-a6a4-daa85998d078_CloudSpaceContractSubscribeOrder"));

        // 设置用户ID（模拟单个用户查询）
        queryGoodsInstanceCondition.setPhoneNumber("***********");

        QueryConditionSubScriptionReq queryConditionSubScriptionReq = new QueryConditionSubScriptionReq();
        queryConditionSubScriptionReq.setAccount(queryGoodsInstanceCondition.getPhoneNumber());
        queryConditionSubScriptionReq.setSubIdList(queryGoodsInstanceCondition.getGoodsInstanceIdList());
        queryConditionSubScriptionReq.setOrderIdList(queryGoodsInstanceCondition.getOrderIdList());
        queryConditionSubScriptionReq.setGoodsIdList(queryGoodsInstanceCondition.getGoodsIdList());

        String xmlReqStr = JAXBUtil.beanToXml(queryConditionSubScriptionReq);
        List<GoodsInstanceDo> list = goodInstanceContext.read(queryGoodsInstanceCondition, GoodsInstanceDo.class);
        System.out.println(xmlReqStr);

        for (GoodsInstanceDo item : list) {
            // 将每个对象序列化为 JSON 字符串
            String jsonString = JSONUtil.toJsonStr(item);
            System.out.println(jsonString);
        }

    }
}
