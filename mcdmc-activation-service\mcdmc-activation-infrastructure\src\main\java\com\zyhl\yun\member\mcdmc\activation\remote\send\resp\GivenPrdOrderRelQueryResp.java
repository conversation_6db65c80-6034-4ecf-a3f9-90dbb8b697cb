package com.zyhl.yun.member.mcdmc.activation.remote.send.resp;

import lombok.Data;
import lombok.ToString;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/3 10:37
 * @descrition 定向免流订购关系响应
 */
@Data
@ToString
public class GivenPrdOrderRelQueryResp {
    /**
     * 响应码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 错误描述
     */
    private String success;

    /**
     * 订购关系详情
     */
    private OrderRelationResp data;


    /**
     * <AUTHOR>
     * @version 1.0
     * @date 2024/7/3 10:37
     * @descrition 集运侧订购关系查询响应体
     */
    @Data
    @ToString
    public class OrderRelationResp {
        /**
         * 业务返回码
         */
        private String bizCode;

        /**
         * 返回码描述
         */
        private String bizDesc;

        /**
         * 操作时间
         */
        private String oprTime;

        /**
         * 详细订购关系
         */
        private List<RespBizInfo> bizInfoList;
    }

    /**
     * <AUTHOR>
     * @version 1.0
     * @date 2024/7/3 10:37
     * @descrition 集运侧订购关系查询响应体
     */
    @Data
    @ToString
    public class RespBizInfo {
        /**
         * 是否订购过校验商品
         */
        private String isOrder;

        /**
         * 商品编码
         */
        private String goodsId;

        /**
         * 商品名称
         */
        private String goodsName;

        /**
         * APP服务列表
         */
        private String serviceIdList;

        /**
         * 订购时间
         */
        private String orderTime;

        /**
         * 生效时间
         */
        private String validDate;

        /**
         * 失效时间
         */
        private String expireDate;
    }
}
