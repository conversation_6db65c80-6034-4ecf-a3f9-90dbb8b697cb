package com.zyhl.yun.member.mcdmc.activation.remote.send.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/06/14 09:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RaiSubReq {

    private ContractRoot contractRoot;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContractRoot {
        /**
         * 请求头
         */
        private Head head;
        /**
         * 请求体
         */
        private Body body;
    }

    @Data
    public static class Head {
        private String apiId;// "65001",
        private String channelCode;// "65041",
        private String transactionId;// "201806281112121200",
        private String reqTime;// "20180628000000",
        private String sign;// "xxxxxxxxx",
        private String version;// "1.0"
    }

    @Data
    public static class Body {
        /**
         * 可选，订单所属渠道ID, 为空则默认HEAD相同的channelCode
         */
        private String channelCode;

        /**
         * 渠道订单号（32位）
         */
        private String channelNo;// ": "2001807130001",

        /**
         * 与权益方统一标识id
         */
        private String orderItemId;

        /**
         * 受理设备号码
         */
        private String phone;// ": "13712341234",

        /**
         * 销售品ID
         */
        private String salesId;// ": "2018001",

        /**
         * 销售品名称
         */
        private String salesName;// ": "权益包",

        /**
         * 交易类型 0-订购,1-退订,2-兑换
         */
        private String dealType;// ": "01",

        /**
         * 受理时间:YYYYMMDDHH24MISS
         */
        private String acceptTime;// ": "20180712",

        /**
         * 客户端IP
         */
        private String clentIp;// ": "***********",

        /**
         * 价格
         */
        private String amount;// ": "0",

        /**
         * 是否需要支付 0 不需要 1 需要
         */
        private String isPay;// ":"0",

        /**
         * 统一认证token 渠道没有在权益中心侧token校验白名单中时必传
         */
        private String token;// ? String V500

        /**
         * 营销活动编码 0表示不参加营销活动
         */
        private String marketingCode;// 1 String V50

        /**
         * 权益中心会员ID 当dealType为2-兑换时必填
         */
        private String vipCardId;// ? String V50

        /**
         * 卡槽 根据订购关系查询与校验接口查得
         */
        private String slot;// ? String V10

        /**
         * 组合产品信息
         */
        private List<ProdInfo> prodInfo;

        /**
         * 支付信息
         */
        private PayInfo payInfo;

    }

    @Data
    public static class PayInfo {
        /**
         * 支付方式<br/>
         * -1 免费<br/>
         * 0 话费<br/>
         * 1 支付宝<br/>
         * 2 微信<br/>
         * 3 银联<br/>
         * 4 和包<br/>
         * 5 京东闪付<br/>
         * 6 apple play<br/>
         * 7 小河马支付<br/>
         * 8 统一支付<br/>
         * 9 话费支付（国漫）<br/>
         */
        private String payType;// ": "1",

        /**
         * 支付通道<br/>
         * WWW 支付宝WWW门户<br/>
         * WAP 支付宝WAP门户<br/>
         * H5 H5支付，目前仅微信支付使用<br/>
         * WX-JSAPI 微信JSAPI（微信公众号）<br/>
         * WX-NATIVE 微信 NATIVE（微信扫码支付）<br/>
         */
        private String payWay;// ": "WWW",

        /**
         * 用户标识<br/>
         * 微信JSAPI支付时，此参数必传，用户在商户appid下的唯一标识.
         */
        private String wxOpenid;// ": "xxxxx",

        /**
         * 数字，订单有效期单位同时构成订单有效期<br/>
         * 默认值 30
         */
        private String period;// ":"30",

        /**
         * 只能取以下枚举值：<br/>
         * 00- 分<br/>
         * 01- 小时<br/>
         * 02- 日<br/>
         * 03- 月<br/>
         * 默认00<br/>
         */
        private String periodUnit;// ":"00",

        /**
         * 商户展示名称
         */
        private String merchantName;// ":"爱奇艺",

        /**
         * 销售描述
         */
        private String salesDesc;// ":"周卡",

        /**
         * 交易返回时原样返回给商家网站，给商户备用
         */
        private String reserved1;// ":"XXXX",

        /**
         * 销售品展示链接地址
         */
        private String salesURL;// ":"XXXXX"

        /**
         * 商户号
         */
        private String appid;// ": "001",

        /**
         * 跳转URL<br/>
         * payType不为7时必选
         */
        private String returnUrl;// ": "http://xxxxx.com",

        /**
         * 支付时间:YYYYMMDDHH24MISS <br/>
         * payType为7时必填
         */
        private String payTime;// ? String F14

        /**
         * 支付金额<br/>
         * payType为7时必填
         */
        private String payAmount;// ？ Integer V10

        /**
         * 支付状态<br/>
         * payType为7时必填，取值参考附录定义参照表<br/>
         * 1 未提交支付<br/>
         * 10 待支付,已提交支付请求<br/>
         * 20 支付成功<br/>
         * 30 支付平台反馈失败<br/>
         * 40 支付单超时<br/>
         * 50 对账补单成功<br/>
         * 60 退款中<br/>
         * 70 退款成功<br/>
         * 80 退款失败<br/>
         * 90 退款补单成功<br/>
         * 15 关闭订单<br/>
         * 5 订单提交失败<br/>
         * 16 关闭订单失败<br/>
         * 17 退款关闭<br/>
         */
        private String payStatus;// ？ Integer V4

        /**
         * 支付流水<br/>
         * payType为7时必填
         */
        private String payNo;// ？ String V20

        /**
         * 支付机构<br/>
         * 取值参考附录定义参照表<br/>
         * ALIPAY 支付宝<br/>
         * WEIXIN 微信支付<br/>
         */
        private String payOrgpayOrg;// ？ String V20

        @Override
        public String toString() {
            return "PayInfo{" +
                    "payType='" + payType + '\'' +
                    ", payWay='" + payWay + '\'' +
                    ", wxOpenid='" + wxOpenid + '\'' +
                    ", period='" + period + '\'' +
                    ", periodUnit='" + periodUnit + '\'' +
                    ", merchantName='" + merchantName + '\'' +
                    ", salesDesc='" + salesDesc + '\'' +
                    ", reserved1='" + reserved1 + '\'' +
                    ", salesURL='" + salesURL + '\'' +
                    ", appid='" + appid + '\'' +
                    ", returnUrl='" + returnUrl + '\'' +
                    ", payTime='" + payTime + '\'' +
                    ", payAmount='" + payAmount + '\'' +
                    ", payStatus='" + payStatus + '\'' +
                    ", payNo='" + payNo + '\'' +
                    ", payOrgpayOrg='" + payOrgpayOrg + '\'' +
                    '}';
        }
    }

    @Data
    public static class ProdInfo {
        /**
         * 组合产品ID
         */
        private Long prodId;// ": "75001",

        /**
         * 组合产品名称
         */
        private String prodName;// ": "7天权益包",

        /**
         * 设备号码
         */
        private String serverNum;// ": "13712341234",

        /**
         * 数量
         */
        private Integer quantity;// ": "1",

        /**
         * 产品扩展字段
         */
        private String extField;// ":"xxxx"

        @Override
        public String toString() {
            return "ProdInfo{" +
                    "prodId='" + prodId + '\'' +
                    ", prodName='" + prodName + '\'' +
                    ", serverNum='" + serverNum + '\'' +
                    ", quantity='" + quantity + '\'' +
                    ", extField='" + extField + '\'' +
                    '}';
        }
    }
}
