package com.zyhl.yun.member.mcdmc.activation.domain.remote;

import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;

import java.util.concurrent.Callable;

/**
 * 业务流程接口
 *
 * <AUTHOR>
 * @since 2024/07/05 17:24
 */
public interface IServiceFlow {

    /**
     * 处理业务流程
     *
     * @param workOrderDo      工单
     * @param continueLockFunc 续锁的函数钩子（多流程时使用）
     */
    void handle(WorkOrderDo workOrderDo, Callable<Boolean> continueLockFunc);
}
