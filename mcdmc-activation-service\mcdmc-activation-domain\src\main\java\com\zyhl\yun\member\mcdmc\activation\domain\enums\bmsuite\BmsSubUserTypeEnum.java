package com.zyhl.yun.member.mcdmc.activation.domain.enums.bmsuite;

import com.zyhl.yun.member.common.enums.NationCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 订购用户类型枚举类
 *
 * <AUTHOR>
 * @since 2024/4/15 17:48
 */

@Getter
@AllArgsConstructor
public enum BmsSubUserTypeEnum {

    /**
     * 云盘产品大陆用户
     */
    YUN_PRODUCT("1", "云盘产品大陆用户", NationCodeEnum.LAND.getNationCode()),

    /**
     * 独立空间大陆用户
     */
    INDEPENDENCE("2", "独立空间大陆用户", NationCodeEnum.LAND.getNationCode()),

    /**
     * 香港用户
     */
    HONG_KONG("3", "香港用户", NationCodeEnum.HK.getNationCode()),

    /**
     * 巴基斯坦用户
     */
    PAKISTANI("4", "巴基斯坦用户", NationCodeEnum.PAKISTAN.getNationCode()),

    /**
     * 巴基斯坦用户
     */
    MOUNT("5", "挂载盘用户", NationCodeEnum.LAND.getNationCode());

    /**
     * 订购用户类型
     */
    private final String subUserType;

    private final String msg;

    private final String nationCode;

    public static BmsSubUserTypeEnum getEnum(String nationCode, String ownerId) {
        // 大陆用户根据ownerId判断
        if (NationCodeEnum.LAND.getNationCode().equals(nationCode)) {
            if (StringUtils.isNotEmpty(ownerId)) {
                return BmsSubUserTypeEnum.INDEPENDENCE;
            }
            return BmsSubUserTypeEnum.YUN_PRODUCT;
        }
        // 其他用户根据国家码判断
        for (BmsSubUserTypeEnum value : BmsSubUserTypeEnum.values()) {
            if (value.getNationCode().equals(nationCode)) {

                return value;
            }
        }
        return BmsSubUserTypeEnum.YUN_PRODUCT;
    }

    public static String getByNationCode(String nationCode) {
        for (BmsSubUserTypeEnum value : BmsSubUserTypeEnum.values()) {
            if (value.getNationCode().equals(nationCode)) {
                return value.getSubUserType();
            }
        }
        return null;
    }
}
