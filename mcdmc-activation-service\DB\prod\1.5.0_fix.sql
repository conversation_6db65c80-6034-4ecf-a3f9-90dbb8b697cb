alter table member_sopen.task_t_message_notice drop INDEX task_t_message_notice_notice_time_IDX;
create index task_t_message_notice_notice_way_IDX USING BTREE ON member_sopen.task_t_message_notice (`notice_way`,`status`,`notice_is_done`,`slice`, `notice_time`,`sum_fail`) local;

create index task_t_inst_work_order_status_IDX USING BTREE ON member_sopen.task_t_inst_work_order (status,open_is_done,sum_fail,open_time,slice) local;
alter table member_sopen.task_t_inst_work_order drop INDEX task_t_inst_work_order_open_time_IDX;

create index task_t_inst_schedule_status_IDX USING BTREE ON member_sopen.task_t_inst_schedule (status,slice,next_trigger_time) local;
alter table member_sopen.task_t_inst_schedule drop INDEX task_t_inst_schedule_next_trigger_time_IDX;