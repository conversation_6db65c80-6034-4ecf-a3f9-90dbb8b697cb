package com.zyhl.yun.member.common.annotation;

import com.zyhl.yun.member.common.NumberValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import javax.validation.constraints.NotNull;
import java.lang.annotation.Documented;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.ElementType.TYPE_USE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 功能描述。
 * 包名称:  com.zyhl.yun.member.common.annotation
 * 类名称:  RawAccount
 * 类描述:  获取原始号码切面注解。
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/9/12 10:22
 */

@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = {NumberValidator.class})
public @interface LegalAccount {
    String message() default "illegal account input";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
