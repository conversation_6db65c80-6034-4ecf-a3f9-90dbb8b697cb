package com.zyhl.yun.member.mcdmc.activation.convertor;

import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.enums.WorkOrderStateEnum;
import com.zyhl.yun.member.mcdmc.activation.po.WorkOrderPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/06/04 19:14
 */
@Mapper(componentModel = "spring", imports = WorkOrderStateEnum.class)
public interface WorkOrderConvertor {

    @Mapping(target = "state", expression = "java(workOrderDo.getState()!=null?workOrderDo.getState().getState():null)")
    WorkOrderPo toPO(WorkOrderDo workOrderDo);

    @Mapping(target = "state", expression = "java(WorkOrderStateEnum.fromState(workOrderPo.getState()))")
    WorkOrderDo toDo(WorkOrderPo workOrderPo);

    List<WorkOrderDo> toDoList(List<WorkOrderPo> poList);

}
