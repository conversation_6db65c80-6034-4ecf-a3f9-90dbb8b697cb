package com.zyhl.yun.member.mcdmc.activation.flow.rai.sub;

import com.zyhl.yun.member.common.enums.RightsStatusEnum;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.flow.base.BaseSubServiceFlow;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/07/03 12:02
 */
@Component
public class RaiSubNormalFlow extends BaseSubServiceFlow {
    @Override
    protected IFlowResult beforeFlow(WorkOrderDo workOrderDo) {
        IFlowResult flowResult = super.beforeFlow(workOrderDo);
        if (!flowResult.isContinue()) {
            return flowResult;
        }
        GoodsInstanceDo goodsInstanceDo = (GoodsInstanceDo) workOrderDo.getExtData(OtherFieldConstants.GOODS_INSTANCE_DO);
        if (RightsStatusEnum.OPEN_SUCCESS.equals(goodsInstanceDo.getRightsStatusEnum())
                || RightsStatusEnum.INITIALIZATION_SUCCESS.equals(goodsInstanceDo.getRightsStatusEnum())) {
            // 如果商品实例状态为开通成功或者初始化成功，则直接取消流程
            return IFlowResult.cancel("goodsInstanceDo rightsStatusEnum is %s,so cancel the flow",
                    goodsInstanceDo.getRightsStatusEnum());
        }
        return IFlowResult.next();
    }

    @Override
    protected boolean isJudgeSubGoodInstanceEffective() {
        // 需要判断对应商品实例是否生效中，如果生效中，则不能走流程
        return true;
    }

    @Override
    protected boolean hasCallback() {
        return true;
    }

}
