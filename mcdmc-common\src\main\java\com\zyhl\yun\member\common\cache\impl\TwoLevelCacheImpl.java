package com.zyhl.yun.member.common.cache.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.zyhl.yun.member.common.cache.ConditionCacheable;
import com.zyhl.yun.member.common.cache.DelegationCache;
import com.zyhl.yun.member.common.cache.wrapper.CollectionWrapper;
import com.zyhl.yun.member.common.cache.wrapper.ConditionCountWrapper;
import com.zyhl.yun.member.common.cache.wrapper.ConditionWrapper;
import com.zyhl.yun.member.common.cache.wrapper.SimpleValueWrapper;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/20 20:46
 */
@Slf4j
public class TwoLevelCacheImpl extends BaseDelegationCacheImpl {

    private DelegationCache levelOneCache;

    private DelegationCache levelTwoCache;

    public TwoLevelCacheImpl(CaffeineCacheImpl levelOneCache, RedisCacheImpl levelTwoCache) {
        log.info("[CACHE] TwoLevelCacheImpl init");
        this.levelOneCache = levelOneCache;
        this.levelTwoCache = levelTwoCache;
    }


    @Override
    public SimpleValueWrapper get(String cacheSpace, Serializable key, Class clazz) {
        log.debug("[CACHE] get. cacheSpace. {}, key: {}, clazz: {}", cacheSpace, key, clazz.getName());
        SimpleValueWrapper simpleValueWrapper = levelOneCache.get(cacheSpace, key, clazz);
        if (simpleValueWrapper == null) {
            simpleValueWrapper = levelTwoCache.get(cacheSpace, key, clazz);
            if (simpleValueWrapper != null) {
                levelOneCache.put(cacheSpace, key, simpleValueWrapper);
            }
        }
        return simpleValueWrapper;
    }

    @Override
    public CollectionWrapper getList(String cacheSpace, List<Serializable> keyList, Class clazz) {
        log.debug("[CACHE] getList. cacheSpace. {}, keyList: {}, clazz: {}", cacheSpace, keyList, clazz.getName());
        CollectionWrapper collectionWrapper = levelOneCache.getList(cacheSpace, keyList, clazz);
        if (collectionWrapper == null) {
            collectionWrapper = levelTwoCache.getList(cacheSpace, keyList, clazz);
            if (collectionWrapper != null) {
                levelOneCache.putList(cacheSpace, collectionWrapper);
            }
        } else {
            List<Serializable> notMacheKeyList = collectionWrapper.getNotMacheKeyList();
            if (!CollectionUtil.isEmpty(notMacheKeyList)) {
                List<Serializable> totalValueList = new ArrayList<>();

                CollectionWrapper twoLevelCollectionWrapper = levelTwoCache.getList(cacheSpace, notMacheKeyList, clazz);
                if (twoLevelCollectionWrapper != null) {
                    totalValueList.addAll(twoLevelCollectionWrapper.getValueList());
                }
                List<Serializable> oldValueList = collectionWrapper.getValueList();
                totalValueList.addAll(oldValueList);

                collectionWrapper = new CollectionWrapper(keyList, totalValueList);

                //放入未缓存的数据
                if (twoLevelCollectionWrapper != null) {
                    CollectionWrapper collectionCacheValueWrapperToLevelTwoCache
                            = new CollectionWrapper(twoLevelCollectionWrapper.getMacheKeyList(), twoLevelCollectionWrapper.getValueList());
                    levelOneCache.putList(cacheSpace, collectionCacheValueWrapperToLevelTwoCache);
                }
            }
        }
        return collectionWrapper;
    }

    @Override
    public ConditionWrapper getKeyListByCondition(String cacheSpace, ConditionCacheable condition) {
        log.debug("[CACHE] getKeyListByCondition. cacheSpace: {}, condition: {}", cacheSpace, condition);
        ConditionWrapper conditionWrapper = levelOneCache.getKeyListByCondition(cacheSpace, condition);
        if (conditionWrapper == null) {
            conditionWrapper = levelTwoCache.getKeyListByCondition(cacheSpace, condition);
            if (conditionWrapper != null) {
                levelOneCache.put(cacheSpace, condition, conditionWrapper);
            }
        }
        return conditionWrapper;
    }

    @Override
    public ConditionCountWrapper getTotalCountByCondition(String cacheSpace, ConditionCacheable condition) {
        log.debug("[CACHE] getTotalCountByCondition. cacheSpace: {}, condition: {}", cacheSpace, condition);
        ConditionCountWrapper conditionCountWrapper = levelOneCache.getTotalCountByCondition(cacheSpace, condition);
        if (conditionCountWrapper == null) {
            conditionCountWrapper = levelTwoCache.getTotalCountByCondition(cacheSpace, condition);
            if (conditionCountWrapper != null) {
                levelOneCache.put(cacheSpace, condition, conditionCountWrapper);
            }
        }
        return conditionCountWrapper;
    }

    @Override
    public void put(String cacheSpace, Serializable key, SimpleValueWrapper value) {
        log.debug("[CACHE] put. cacheSpace: {}, key:{}, value: {}", cacheSpace, key, value);
        levelOneCache.put(cacheSpace, key, value);
        levelTwoCache.put(cacheSpace, key, value);
    }

    @Override
    public void put(String cacheSpace, ConditionCacheable condition, ConditionWrapper conditionWrapper) {
        log.debug("[CACHE] put. cacheSpace: {}, condition:{}, conditionWrapper: {}", cacheSpace, condition, conditionWrapper);
        levelOneCache.put(cacheSpace, condition, conditionWrapper);
        levelTwoCache.put(cacheSpace, condition, conditionWrapper);
    }

    @Override
    public void put(String cacheSpace, ConditionCacheable condition, ConditionCountWrapper conditionCountWrapper) {
        log.debug("[CACHE] put. cacheSpace: {}, condition:{}, conditionCountWrapper: {}", cacheSpace, condition, conditionCountWrapper);
        levelOneCache.put(cacheSpace, condition, conditionCountWrapper);
        levelTwoCache.put(cacheSpace, condition, conditionCountWrapper);
    }

    @Override
    public void putList(String cacheSpace, CollectionWrapper collectionWrapper) {
        log.debug("[CACHE] putList. cacheSpace: {}, collectionWrapper: {}", cacheSpace, collectionWrapper);
        levelOneCache.putList(cacheSpace, collectionWrapper);
        levelTwoCache.putList(cacheSpace, collectionWrapper);
    }

    @Override
    public void deleteByKeyList(String cacheSpace, List<Serializable> keyList) {
        log.debug("[CACHE] deleteByKeyList. cacheSpace: {}, keyList: {}", cacheSpace, keyList);
        levelOneCache.deleteByKeyList(cacheSpace, keyList);
        levelTwoCache.deleteByKeyList(cacheSpace, keyList);
    }

    @Override
    public void clearConditions(String cacheSpace, ConditionCacheable condition) {
        log.debug("[CACHE] clearConditions. cacheSpace: {}, condition: {}", cacheSpace, condition);
        levelOneCache.clearConditions(cacheSpace, condition);
        levelTwoCache.clearConditions(cacheSpace, condition);
    }
}
