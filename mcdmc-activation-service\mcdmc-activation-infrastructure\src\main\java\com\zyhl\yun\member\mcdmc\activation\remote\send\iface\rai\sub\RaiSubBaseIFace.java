package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.sub;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.Constant;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.framework.service.MdcLogInterceptor;
import com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceExtendServiceId;
import com.zyhl.yun.member.consumer.common.message.RaiQueryOrderMessage;
import com.zyhl.yun.member.consumer.common.producer.MqProducer;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.goodsinstance.enums.GoodsInstanceStateEnum;
import com.zyhl.yun.member.mcdmc.activation.constant.CustomHeaders;
import com.zyhl.yun.member.mcdmc.activation.constants.BusinessConstant;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.DbFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.constants.WorkAttrCodeConstant;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.mq.properties.RaiQueryMqProperties;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.ActivationContextUtil;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.Md5Util;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.SendTemplate;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiBaseProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.RaiSubReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.RaiSubRsp;
import com.zyhl.yun.member.product.common.constants.SymbolConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;

import static com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceExtendServiceId.UPDATE_GOODS_INSTANCE_EXTEND;

/**
 * 权益发货流程
 * 包括明威和正常权益发货
 *
 * <AUTHOR>
 * @since 2024/06/13 20:24
 * com.huawei.jaguar.vsbo.service.serviceimpl.NotifySendProductServiceImpl#sendOrderAccept
 */
@Slf4j
public abstract class RaiSubBaseIFace extends SendTemplate<RaiSubReq, RaiSubRsp> {

    private MqProducer mqProducer;
    private RaiQueryMqProperties raiQueryMqProperties;

    @PostConstruct
    private void initProducer() {
        mqProducer = SpringUtil.getBean(MqProducer.class);
        raiQueryMqProperties = SpringUtil.getBean(RaiQueryMqProperties.class);
    }

    protected abstract RaiBaseProperties getBaseRaiProperties();

    /**
     * 请求报文里是否发送payWay扩展字段
     */
    protected boolean isNeedPayWay() {
        return true;
    }

    @Override
    protected void doCheckParam(InterfaceContext<RaiSubReq, RaiSubRsp> interfaceContext) {
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        WorkOrderDo workOrderDo = interfaceContext.getWorkOrderDo();
        if (StringUtils.isEmpty(comSendReq.getProServiceId())) {
            throw new FlowTerminationException(this.getClass(), workOrderDo, "proServiceId为空");
        }
        if (StringUtils.isEmpty(comSendReq.getGoodsInstanceId())) {
            throw new FlowTerminationException(this.getClass(), workOrderDo, "goodsInstanceId为空");
        }
    }

    @Override
    protected RaiSubReq getRequestBody(InterfaceContext<RaiSubReq, RaiSubRsp> interfaceContext) {
        RaiSubReq.Body body = this.buildRequestBody(interfaceContext.getComSendReq());
        RaiSubReq.Head head = this.buildRequestHead(body);
        RaiSubReq.ContractRoot contractRoot = new RaiSubReq.ContractRoot(head, body);
        return new RaiSubReq(contractRoot);
    }

    @Override
    protected Map<String, String> getRequestHeader(InterfaceContext<RaiSubReq, RaiSubRsp> interfaceContext) {
        Map<String, String> headerMap = super.getRequestHeader(interfaceContext);
        headerMap.put(CustomHeaders.CALL_ACCOUNT, interfaceContext.getComSendReq().getMsisdn());
        headerMap.put(CustomHeaders.CALL_SRC_MOD_NAME, CustomHeaders.SRC_MOD_NAME);
        return headerMap;
    }

    @Override
    protected boolean isSuccess(RaiSubRsp rsp) {
        return RaiSubRsp.isSuccess(rsp);
    }

    @Override
    protected void doBusiSuccess(InterfaceContext<RaiSubReq, RaiSubRsp> interfaceContext) {
        String queryServiceCode = interfaceContext.getWorkServiceFlowIFaceDo().getQueryServiceCode();
        if (StringUtils.isBlank(queryServiceCode)) {
            // 没有查询接口，直接结束
            return;
        }
        //构造权益查询接口请求对象结构，写Mq消息（延迟队列5min）对象里面有一个标识查询次数 初始为0，如果是权益中心发货，需要发mq队列
        RaiQueryOrderMessage raiQueryOrderMessage = new RaiQueryOrderMessage();
        raiQueryOrderMessage.setTraceId(MdcLogInterceptor.getCurrentTraceId());
        raiQueryOrderMessage.setMsgId(interfaceContext.getWorkOrderDo().getTransactionId());
        WorkOrderDo workOrderDo = interfaceContext.getWorkOrderDo();
        raiQueryOrderMessage.setUserId(workOrderDo.getUserId());
        raiQueryOrderMessage.setOrderId(workOrderDo.getOrderId());
        // 塞入工单id，方便查询反查
        interfaceContext.getComSendReq().putExtInfo(DbFieldConstants.WORK_ID, interfaceContext.getWorkOrderDo().getWorkId());
        raiQueryOrderMessage.setWorkAttrs(JSONUtil.toJsonStr(interfaceContext.getComSendReq()));
        raiQueryOrderMessage.setServiceCode(queryServiceCode);
        raiQueryOrderMessage.setStartQueryTime(DateUtil.offsetMinute(new Date(), this.getBaseRaiProperties().getDelayQueryMinTime()));
        mqProducer.sendNewMessage(raiQueryMqProperties.getRaiQueryTopic(), raiQueryMqProperties.getRaiQueryTag(),
                raiQueryOrderMessage);
    }

    /**
     * 业务失败响应后执行的动作
     *
     * @param interfaceContext 接口上下文
     */
    @Override
    protected void doBusiFail(InterfaceContext<RaiSubReq, RaiSubRsp> interfaceContext) {
        // 权益订购失败时需要记录失败报文
        String workId = interfaceContext.getWorkOrderDo().getWorkId();
        String userId = interfaceContext.getWorkOrderDo().getUserId();
        String attrVal = JSONUtil.toJsonStr(interfaceContext.getInterfaceReqObj().getContractRoot().getBody());
        ActivationContextUtil.upsertWorkAttr(userId, workId, WorkAttrCodeConstant.RAI_SUB_FAIL_BODY, attrVal);
        interfaceContext.setNextHintEnum(NextHintEnum.FAIL_FINISH);
    }

    protected abstract String getOrderChannelCode(String userId, String parentOrderId);

    /**
     * 构建请求体的body
     */
    protected RaiSubReq.Body buildRequestBody(ComSendInterfaceReq comReq) {
        RaiSubReq.Body body = new RaiSubReq.Body();
        body.setChannelCode(getOrderChannelCode(comReq.getUserId(), comReq.getParentOrderId()));
        // 存入extInfo中供主动查询流程使用
        comReq.putExtInfo(OtherFieldConstants.ORDER_CHANNEL_CODE, body.getChannelCode());
        // TODO-2024/10/28: 需要加上重试次数，否则重试会失败，每次该字段需不唯一
        // 截取req.getOrderID()字符串中Constant.ORDER_SUFFIX之前的内容，并将其作为结果返回
        body.setChannelNo(CharSequenceUtil.subBefore(comReq.getOrderID(), BusinessConstant.ORDER_SUFFIX, true));

        body.setPhone(comReq.getMsisdn());
        body.setAcceptTime(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN));
        // 权益发货的serviceId是json报文
        JSONObject proJsonObj = JSONUtil.parseObj(comReq.getProServiceId());
        body.setSalesId(proJsonObj.getStr(OtherFieldConstants.SALES_ID));
        body.setSalesName(proJsonObj.getStr(OtherFieldConstants.SALES_NAME));
        RaiSubReq.ProdInfo prodInfo = new RaiSubReq.ProdInfo();
        prodInfo.setQuantity(1);
        prodInfo.setServerNum(comReq.getMsisdn());
        prodInfo.setProdId(Long.parseLong(proJsonObj.getStr(OtherFieldConstants.PROD_ID)));
        prodInfo.setProdName(proJsonObj.getStr(OtherFieldConstants.PROD_NAME));
        Map<String, String> extFieldMap = new HashMap<>(4);
        String raiTransparent = comReq.getExtInfo(Constant.RAITRANSPARENT);
        if (StringUtils.isNotEmpty(raiTransparent)) {
            extFieldMap.put(Constant.RAITRANSPARENT, raiTransparent);
        }
        if (this.isNeedPayWay()) {
            // 非明威互动或非t3权益商品时才传支付方式
            extFieldMap.put(OtherFieldConstants.PAY_WAY, comReq.getPayWay());
            if (CharSequenceUtil.isNotEmpty(comReq.getParentCampaignId())) {
                extFieldMap.put(OtherFieldConstants.CAMPAIGN_ID, comReq.getParentCampaignId());
            }
        }
        prodInfo.setExtField(JSONUtil.toJsonStr(extFieldMap));
        body.setProdInfo(Collections.singletonList(prodInfo));
        //是否需要支付, 0 不需要 1 需要
        body.setDealType("0");
        body.setIsPay("0");
        //营销活动编码:0表示不参加营销活动
        body.setMarketingCode("0");
        return body;
    }

    /**
     * 构建请求体的head
     */
    protected RaiSubReq.Head buildRequestHead(RaiSubReq.Body body) {
        RaiSubReq.Head head = new RaiSubReq.Head();
        head.setApiId(getBaseRaiProperties().getEquityOrderApiId());
        String formatCurTime = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        head.setTransactionId(formatCurTime);
        head.setReqTime(formatCurTime);
        head.setVersion(getBaseRaiProperties().getEquityVersion());
        head.setChannelCode(body.getChannelCode());
        // body报文的json串需要按自然顺序
        String authStr = head.getTransactionId() + head.getReqTime() + getBaseRaiProperties().getEquityOrderSecretKey()
                + JSONUtil.toJsonStr(body, JSONConfig.create().setNatureKeyComparator());
        String sign = Md5Util.md5Encode(authStr);
        head.setSign(sign.toUpperCase());
        return head;
    }

    @Override
    protected boolean isNeedCacheFlowLog() {
        // 缓存发货流水日志，加快回调处理速度
        return true;
    }

    @Override
    protected String getCallbackCondition(InterfaceContext<RaiSubReq, RaiSubRsp> context) {
        return context.getInterfaceRspObj().getContractRoot().getBody().getOrderNo();
    }

    @Override
    protected String getResourceId(InterfaceContext<RaiSubReq, RaiSubRsp> context) {
        // 权益订购时，资产id为空，需要等权益回调时才有资产id
        return SymbolConstant.EMPTY;
    }


    @Override
    protected Class<RaiSubReq> getReqClass() {
        return RaiSubReq.class;
    }

    @Override
    protected Class<RaiSubRsp> getRspClass() {
        return RaiSubRsp.class;
    }

    /**
     * 更新
     *
     * @param goodsInstanceDo 商品实例
     */
    public static void updateParentInstanceByRightReduction(GoodsInstanceDo goodsInstanceDo) {
        log.info("UpdateGoodsInstanceHandler.afterHandle get goodsInstanceDo id is {}, rightReduction is {}",
                goodsInstanceDo.getGoodsInstanceId(),
                goodsInstanceDo.getRightReduction());
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(goodsInstanceDo.getGoodsPackageInstanceId()) && org.apache.commons.lang3.StringUtils.isNotEmpty(goodsInstanceDo.getRightReduction())) {
            log.info("UpdateGoodsInstanceHandler.afterHandle get goodsInstanceDo goodsPackageInstanceId is {}, rightReduction is {}",
                    goodsInstanceDo.getGoodsPackageInstanceId(),
                    goodsInstanceDo.getRightReduction());
            // 更新父商品实例的rightReduction
            DomainServiceContext goodsPackageInstanceContext = new DomainServiceContext(GoodsInstanceExtendServiceId.QUERY_GOODS_INSTANCE_EXTEND);
            QueryGoodsInstanceCondition queryCondition = new QueryGoodsInstanceCondition();
            queryCondition.setGoodsInstanceIdList(Collections.singletonList(goodsInstanceDo.getGoodsPackageInstanceId()));
            queryCondition.setUserId(goodsInstanceDo.getUserId());
            queryCondition.setStateList(Arrays.asList(GoodsInstanceStateEnum.NORMAL, GoodsInstanceStateEnum.UN_SUB));
            GoodsInstanceDo parentGoodsInstanceDo = goodsPackageInstanceContext.readFirst(queryCondition, GoodsInstanceDo.class);
            log.info("UpdateGoodsInstanceHandler.afterHandle get parentGoodsInstanceDo id is {}, rightReduction is {}",
                    parentGoodsInstanceDo.getGoodsInstanceId(),
                    parentGoodsInstanceDo.getRightReduction());
            DomainServiceContext context = new DomainServiceContext(UPDATE_GOODS_INSTANCE_EXTEND);
            parentGoodsInstanceDo.setRightReduction(goodsInstanceDo.getRightReduction());
            context.putInstance(parentGoodsInstanceDo);
            context.writeAndFlush();

        }
    }

}
