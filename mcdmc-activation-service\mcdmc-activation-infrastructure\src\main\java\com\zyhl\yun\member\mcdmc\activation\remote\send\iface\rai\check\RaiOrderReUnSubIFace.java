package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.check;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.rai.CheckActionTypeEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.rai.ReturnStatusEnum;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.order.domain.OrderDo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 补退流程
 * com.huawei.jaguar.vsbo.strategy.differencedIgital.ReUnsubscribeHandler#doDigitalInvoke
 *
 * <AUTHOR>
 * @since 2024/10/08 17:29
 */
@Component
public class RaiOrderReUnSubIFace extends RaiOrderReductionIFace {
    @Override
    protected String buildExtField1(ComSendInterfaceReq comSendReq) {
        Map<String, String> map = new HashMap<>(2);
        map.put(OtherFieldConstants.PAY_WAY, comSendReq.getPayWay());
        map.put(OtherFieldConstants.RETURN, ReturnStatusEnum.RE_UNSUBSCRIBE.getReturnStatus());
        if (StrUtil.isNotEmpty(comSendReq.getParentCampaignId())) {
            map.put(OrderDo.ExtInfoKeys.CAMPAIGN_ID, comSendReq.getParentCampaignId());
        }
        return JSONUtil.toJsonStr(map);
    }

    @Override
    protected CheckActionTypeEnum getCheckActionTypeEnum() {
        // 补退标识
        return CheckActionTypeEnum.ORDER_RE_UNSUB;
    }
}
