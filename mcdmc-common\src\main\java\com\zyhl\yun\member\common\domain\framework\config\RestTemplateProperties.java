package com.zyhl.yun.member.common.domain.framework.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2025/01/17 19:42
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "driver.rest-template")
public class RestTemplateProperties {
    /**
     * 最大连接数，默认1000个
     */
    private Integer maxTotal = 1000;
    /**
     * 每个路由（域名）最大连接数，默认500个
     */
    private Integer maxPerRouter = 500;

    /**
     * 确定连接建立之前的超时时间(以毫秒为单位)。
     *
     * @apiNote 也就是客户端发起TCP连接请求的超时时间，一般也就是TCP三次握手的时间，默认5秒
     */
    private Integer connectTimeoutMs = 5000;

    /**
     * 客户端等待服务端返回数据的超时时间
     *
     * @apiNote 默认10s
     */
    private Integer socketTimeoutMs = 10000;

    /**
     * 定时清理过期连接的开关，默认打开
     */
    private Boolean evictExpiredConnections = true;

    /**
     * 是否使用restTemplate客户端，false则用webClient
     */
    private Boolean isRestTemplate = true;
}
