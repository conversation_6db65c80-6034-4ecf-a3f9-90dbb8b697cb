-- member_sopen.activation_work_order definition

drop table IF EXISTS `activation_work_order`;

CREATE TABLE `activation_work_order` (
  `work_id` varchar(128) NOT NULL COMMENT '工单id',
  `order_id` varchar(128) DEFAULT NULL COMMENT '订单id',
  `user_id` varchar(128) DEFAULT NULL COMMENT '用户id',
  `service_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '服务编号',
  `work_attrs` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '工单属性',
  `transaction_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '交易流水号id',
  `state` smallint DEFAULT NULL COMMENT '状态,1:处理中；2:已完成；3:已取消；4:已失败',
  `fail_count` int NOT NULL DEFAULT '0' COMMENT '工单失败次数',
  `flow_progress` int DEFAULT '0' COMMENT '流程进度，多个流程时会更新该进度，从0开始',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `operator` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'default',
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'default' COMMENT '租户id',
  `version_tag` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0000000000000000' COMMENT '多版本支持',
  PRIMARY KEY (`work_id`),
  UNIQUE KEY `idx_transactionId` (`transaction_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='服务工单表'

-- member_sopen.activation_work_order_attr definition

drop table if exists activation_work_order_attr;

create TABLE `activation_work_order_attr` (
  `work_id` varchar(128) NOT NULL comment '工单id',
  `attr_code` varchar(128) NOT NULL comment '属性编码',
  `attr_val` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci comment '属性值',
  `state` smallint DEFAULT NULL comment '状态',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP comment '创建时间',
  `updated_time` datetime DEFAULT NULL comment '更新时间',
  `operator` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'default',
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'default' comment '租户id',
  `version_tag` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0000000000000000' comment '多版本支持',
  PRIMARY KEY (`work_id`,`attr_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci comment='工单属性表';

-- member_sopen.activation_work_service_flow definition
drop table if exists activation_work_service_flow;
CREATE TABLE `activation_work_service_flow` (
  `work_service_flow_id` int NOT NULL AUTO_INCREMENT COMMENT '接口id',
  `service_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '服务编号',
  `flow_desc` varchar(255) DEFAULT NULL COMMENT '流程描述',
  `service_flow_class` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '业务流程类名',
  `sync_flag` smallint DEFAULT NULL COMMENT '是否并行',
  `retry_count` int DEFAULT NULL COMMENT '重试次数',
  `retry_policy` int DEFAULT NULL COMMENT '重试策略，-1.不重试；0.指定时间重试，单位：yyyyMMddHHmmss;1.立即重试;2.偏移时间重试,单位：毫秒;3.偏移时间重试,单位：秒;4.偏移时间重试,单位：分钟；5.偏移时间重试,单位：小时；6.偏移时间重试,单位：天；7.偏移时间重试,单位：周；8.偏移时间重试,单位：月；99.代码自定义重试',
  `retry_time` varchar(128) DEFAULT NULL COMMENT '重试时间，根据retryType决定该值的含义，如果策略是偏移时间，则该值为偏移的时间',
  `state` smallint NOT NULL COMMENT '状态,0:未生效,1:生效中',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `operator` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'default' COMMENT '操作人',
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'default' COMMENT '租户id',
  `version_tag` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0000000000000000' COMMENT '多版本支持',
  PRIMARY KEY (`work_service_flow_id`),
  UNIQUE KEY `uidx_serviceCode` (`service_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='流程配置表'

-- member_sopen.activation_work_service_flow_iface definition
drop table if exists activation_work_service_flow_iface;
CREATE TABLE `activation_work_service_flow_iface` (
  `iface_id` int NOT NULL AUTO_INCREMENT COMMENT '接口流程id',
  `work_service_flow_id` int NOT NULL COMMENT '业务流程id',
  `service_code` varchar(128) NOT NULL COMMENT '服务编号',
  `iface_name` varchar(100) DEFAULT NULL COMMENT '业务流程接口名称',
  `iface_class_name` varchar(100) DEFAULT NULL COMMENT '接口类名',
  `request_type` varchar(100) DEFAULT NULL COMMENT '发送类型,0:post,1:get,2:del,3:put',
  `media_type` varchar(100) DEFAULT NULL COMMENT '请求方式 eg:application/json,application/xml,text/xml',
  `url` varchar(100) DEFAULT NULL COMMENT '发送url',
  `notify_uri` varchar(100) DEFAULT NULL COMMENT '回调地址URI（不包含项目contextPath及之前的地址）',
  `prior_sort` varchar(100) DEFAULT NULL COMMENT '执行优先级，从0开始，数字越小，优先级越高',
  `state` smallint DEFAULT NULL COMMENT '状态 0未生效，1生效中',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `operator` varchar(100) DEFAULT NULL COMMENT '操作人',
  `tenant_id` varchar(100) DEFAULT NULL COMMENT '租户id',
  `version_tag` varchar(1024) DEFAULT '0000000000000000' COMMENT '多版本支持',
  PRIMARY KEY (`iface_id`),
  KEY `idx_serviceCode` (`service_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='流程接口配置表'

-- member_sopen.activation_work_service_flow_log definition
drop table if exists activation_work_service_flow_log;
CREATE TABLE `activation_work_service_flow_log` (
  `log_id` varchar(128) NOT NULL COMMENT '服务id',
  `service_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '服务编号',
  `work_id` varchar(128) DEFAULT NULL COMMENT '工单id',
  `user_id` varchar(128) NOT NULL COMMENT '用户id',
  `order_id` varchar(128) DEFAULT NULL COMMENT '订单id',
  `work_service_flow_id` varchar(128) DEFAULT NULL COMMENT '接口配置表id',
  `iface_id` int DEFAULT NULL COMMENT '接口配置id',
  `url` varchar(1024) DEFAULT NULL COMMENT '发送url',
  `request_header` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '接口入参_header',
  `request_body` mediumtext COMMENT '接口入参_body',
  `response_header` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '接口出参_header',
  `response_body` mediumtext COMMENT '接口出参_body',
  `callback_url` varchar(255) DEFAULT NULL COMMENT '回调地址',
  `callback_condition` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '回调查询参数',
  `retry_count` int DEFAULT NULL COMMENT '接口重试次数',
  `state` smallint DEFAULT NULL COMMENT '状态，1.远程调用异常；2.远程调用成功；3.处理结果异常；4.处理结果失败；5.发货成功；11.收到回调请求；12.回调成功；13.回调失败',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime DEFAULT NULL COMMENT '更新时间',
  `operator` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'default',
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'default' COMMENT '租户id',
  `version_tag` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0000000000000000' COMMENT '多版本支持',
  `ext_params` json DEFAULT NULL COMMENT '扩展字段上下文',
  `response_http_code` int DEFAULT NULL COMMENT '调用接口返回的httpcode',
  PRIMARY KEY (`log_id`),
  KEY `idx_callback_url_2_condition` (`callback_url`,`callback_condition`) USING BTREE,
  KEY `idx_workid` (`work_id`) USING BTREE,
  KEY `idx_serviceCode` (`service_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='接口日志表'