spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    cachePrepStmts: true
    prepStmtCacheSize: 300
    prepStmtCacheSqlLimit: 2048
    useServerPrepStmts: true
    useLocalSessionState: true
    rewriteBatchedStatements: true
    cacheResultSetMetadata: true
    cacheServerConfiguration: true
    elideSetAutoCommits: true
    maintainTimeStats: false
#     灰度
#     url: jdbc:mysql://************:2883/member_vip_gray?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&useAffectedRows=true
#     现网
    url: ****************************************************************************************************************************************************************************
    username: vip_refactor_4_user@yun_member_refactor#ypcg_member_prod
    password: ENC(1ryHEFZxZMSeUOEOz0cxtZoQk+JIG8GU7kexfsS4qoU=)
  redis:
    host: ************
    password: ENC(w6V7yZOWZpUY/lwBHTocvrkznf/GAyWbDSDEePmFuvI=)
    port: 32066
    database: 0
    max-redirects: 3
    timeout: 3000
    jedis:
      pool:
        # 最大连接，单位：个。当前tomcat配置线程数为200，考虑每秒内有一半线程在操作redis，且每个线程操作不超过100ms，故线程数设置为50
        maxTotal: 200
        #最大空闲连接，单位：个
        maxIdle: 200
        # 最小空闲连接，单位：个
        minIdle: 20
        # 最大获取连接等待时间，单位：毫秒
        maxWaitMillis: 3000
        #空闲连接逐出时间，大于该值的空闲连接一直未被使用则会被释放，单位：毫秒
        minEvictableIdleTimeMillis: 30000
        #空闲连接探测时间间隔，单位：毫秒。 例如系统的空闲连接探测时间配置为30s，则代表每隔30s会对连接进行探测，如果30s内发生异常的连接，
        #经过探测后会进行连接排除。根据连接数的多少进行配置，如果连接数太大，配置时间太短，会造成请求资源浪费。
        timeBetweenEvictionRunsMillis: 30000
        #向资源池借用连接时是否做连接有效性检测（ping），检测到的无效连接将会被移除。对于业务连接极端敏感的，并且性能可以接受的情况下，
        #可以配置为True，一般来说建议配置为False，启用连接空闲检测。
        testOnBorrow: true
        # 是否在空闲资源监测时通过ping命令监测连接有效性，无效连接将被销毁。
        testWhileIdle: true
        # 向资源池归还连接时是否做连接有效性检测（ping），检测到无效连接将会被移除。耗费性能
        testOnReturn: false
        # 连接空闲检测的时间间隔，单位：毫秒
        timeout: 3000
        # 连接建立超时时间，单位：毫秒
        connectTimeout: 3000
        # 空闲连接检测时，每次检测的连接数
        numTestsPerEvictionRun: 3
  http:
    buffer:
      byte-threshold: 409600

mybatis-plus:
  # 启动时是否检查MyBatis XML文件是否存在
  check-config-location: true
  mapper-locations: classpath:mapper/*.xml
  #开启sql日志
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


inner-service:
  list:
    - serviceIp: 127.0.0.1
      serviceName: member-vip
      servicePort: 18085
      servicePath: /member-vip
    - serviceIp: 127.0.0.1
      serviceName: member-order
      servicePort: 18189
      servicePath: /member-order
    - serviceIp: 127.0.0.1
      serviceName: member-product
      servicePort: 18089
      servicePath: /member-product
    - serviceIp: 127.0.0.1
      serviceName: member-activation
      servicePort: 18084
      servicePath: /member-activation
    - serviceIp: 127.0.0.1
      serviceName: member-payment
      servicePort: 18083
      servicePath: ''


rocketmq:
  producer:
    new:
      nameServerAddr: '************:31563;************:31563'
      nameSpace: 'MQ_INST_1678440605886_rVJEkxlB'
      accessKey: 727f6f51df47489facab1910178b3fe7
      secretKey: 826e4606957a4b2c8ca709d9eac805cc
      producerGroup: producerGroupNew
    old:
      nameServerAddr: '************:9876;************:9876'
      nameSpace: ""
      accessKey: xxx
      secretKey: xxx
      producerGroup: producerGroupOld
  task:
    topic: TOPIC_LOCAL_MEMBER-BENIFIT_COMMON
    tag: '*'
    activationTopic: TOPIC_LOCAL_MEMBER-ACTIVATION
    activationTag: open
  goodsInstance:
    goodsInstanceTopic: TOPIC_LOCAL_MEMBER-BENIFIT_DELIVER-GOODS_ROLLBACK-NOTIFY
    goodsInstanceTag: '*'
    oldGoodsInstanceTopic: goods-instance-event
    oldGoodsInstanceTag: '*'


user-domain:
  server-url: http://user-njs-internal.yun.139.com:30080
  app-key: 1118462887501971461
  app-secrete: DycLby$PIQH2PiLP
  algorithm-version: 1.0
  app-secrete-id: 1123749863394426881

isbo:
  server-url: http://************:6180/isbo/openApi/
# 灰度
#   server-url: http://**********:6180/isbo/openApi/

# redis key
redis-key:
  # %s -> activityId
  activityInfo: 'memberVip:activityInfo:%s'
  # %s:$s -> activityId:mobile
  activityInstanceInfo: 'memberVip:activityInstance:%s:%s'

## 集运免流配置
jiyun:
  cmccSwitch: 1
  # 免流状态redis key,%s:手机号
  flowSpecRedisKey: FlowSpec30G:%s
  # redis过期随机时间，如填10，则+[0,10)秒随机时间
  randomSeconds: 301
  # 限制请求频率redis key:%s:%s:account:sourceGoodsId
  flowSpec: subscribeFlowSpec:%s:%s
  # 限制请求频率redis失效时间
  flowSpecRedisExpirationTime: 1
  # 限流错误码重试redis key:%s:%s:account:sourceGoodsId
  flowSpecNotify: subscribeFlowSpecNotify:%s:%s
  topic: TOPIC_LOCAL_MEMBER-BENIFIT_COMMON
  tag: '*'
  jiyunURL: https://dev.coc.10086.cn/coc
  givenPrdOrderRelQueryURL: /gateway/api/common/gw/cmosp/givenPrdOrderRelQuery
  jiyunSecretId: 45116612737745183413
  jiyunSecretKey: 0c49c8801df64c6b94819c382448d6c6
  jiyunSourceApp: P00000011765
  jiyunSkuCode: ************
  jiyunGoodsID: ****************
  jiyunActivityId: 1469125619641212928
  skuIdActivityIdMapConfig: 15352:1469125619641212928|35432:1612367593897713664
  activityIdActivityNameMapConfig: 1610463590036152320:30GB移动云盘定向流量0元体验包|1612367593897713664:30GB移动云盘定向流量免费服务
  skuIdSkuNameMapConfig: 15352:30GB移动云盘定向流量0元体验包|35432:30GB移动云盘定向流量免费服务


csbo:
  url: 'http://10.3.4.209:80'
  m4pChannel: '168002'
  QueryFreeFlowStatusExpireTime: 300


service-config:
  overseasVersion:
    - '+852'
    - '+92'
  skuCodeGoodsIDMapConfig:
    ************: '****************'
  skuCodeSkuIDMapConfig:
    ************: '15352'
  skuCodeSkuIDNewMapConfig:
    15352: '************'
    35432: '************'
  skuCodeSkuNameMapConfig:
    testSkuCode: '30GB移动云盘定向流量免费服务'

# 全球通相关配置
go-tone:
  # 查询权益接口的开关控制 1-打开；0-关闭（默认1）
  queryGotoneSwitch: '1'
  # 查询全球通接口是否报0000000007错误码
  isQueryGotoneResponseErrorCode: '1'
  # 是否校验账号状态 0-不校验；1-校验（默认配置1）
  isNumberStateCheck: '0'
  # 是否校验流量包业务资格 0-不校验；1-校验（默认配置1）
  isBizQualification: '0'
  #
  numberStateVersion: '1.0.0'
  monthlyGoodsID: '9991010000002650001'
  # 所有全球通用户等级
  allLevels:
    - '0'
    - '1'
    - '2'
    - '3'
    - '4'
    - '5'
  # 高等级全球通用户等级
  highLevels:
    - '3'
    - '4'
    - '5'

query-rights:
  userRightsExpireTime: 86400
  queryCmccSwitch: 1
  jiyunLimitCode: 203
#一级能开
equity-platform:
  url: https://cust.h5cmpassport.com

#权益中心
equity:
  url: http://**********/cmpp-api/external/queryPhoneAndSalesInfo
  apiId: 355
  channelCode: 69705
  privateKey: D1AA0403A55D5F3E

member-product:
  availableAccessSources: 1,2,3,4,5,6,7,8
  availableBuyType: 1,2,3,78,79,86
  availableEvenType: 1,2,3,4
  defaultStartIndex: 0
  querySubScribeDefaultValue: 199


member-vip:
# 灰度
#   server-url: http://**********:8085/member-vip
  # 现网
  server-url: http://**********:8085/member-vip

marketPass:
  # 营销平台、团购活动服务
  # 灰度
#   service-ip: http://**********:8090/api/
  #现网
  service-ip: http://**********:8090/api/
  appId: ATAPP001
  appKey: "8QhMX-bT0g&sz80@"

#旧vsbo地址，视环境修改
vsbo-url: 'http://***************:8080'
#灰度
# vsbo-url: 'http://**********:8080'

#权益核销回传渠道
work-off-rec:
  privateKey: M6S0XN4J6B0RGY3E
  channelCode: 74732
  apiId: 1234567890123697030

# VSBO服务地址
vsbo-url: 'http://***************:8080'

server:
  tomcat:
    threads:
      max: 400
      min-spare: 50
    #等待队列长度，当可分配的线程数全部用完之后，后续的请求将进入等待队列等待，等待队列满后则拒绝处理，默认100
    accept-count: 20000
    #最大可被连接数
    max-connections: 10000
    #连接超时时间，该值需要大于nginx的keepalive_timeout，否则nginx会主动断开连接，默认60000
    connection-timeout: 70000

did-generator:
  segment:
    enable: true

manage:
  neAuth: TWpBeU5UQXhNRFF3TkRFME1qZzFNN0xlTXEwMXdSakJkak9DeWJZSTN4OTlWVnZpVzBiQWhyTDlXNFJtbzZTag==

###################################全球通配置项###################################################
gotone-config:
  # 全球通用户等级：0
  gotoneUserLevelZero: '0'
  # 全球通用户等级：1
  gotoneUserLevelOne: '1'
  # 全球通用户等级：2
  gotoneUserLevelTwo: '2'
  # 全球通用户等级：3
  gotoneUserLevelThree: '3'
  # 全球通用户等级：4
  gotoneUserLevelFour: '4'
  # 全球通用户等级：5
  gotoneUserLevelFive: '5'
  # 全球通产品：银卡
  gotoneGradeSilver: '1'
  # 全球通产品：金卡
  gotoneGradeGold: '2'
  # 全球通产品：钻石卡
  gotoneGradeDiamond: '3'
  # 全球通用户等级映射产品等级(全球通用户等级:全球通产品)
  levelMap:
    0: '1'
    1: '1'
    2: '2'
    3: '3'
    4: '3'
    5: '3'
    8: '3'
  # 商品全球通等级
  allGotoneGoodsGrade:
    - '1'
    - '2'
    - '3'
  # 全球通用户等级
  allGotoneUserLevel:
    - '1'
    - '2'
    - '3'
    - '4'
    - '5'
  # 高等级全球通
  highGotoneUserLevel:
    - '3'
    - '4'
    - '5'
  # 全球通用户等级
  all-gotone=user-level:
    - '8'
  # 高等级全球通
  high-gotone-user-level:
    - '8'

# 报文压缩
server.compressFlag: true
# http2开启
http2.flag: true

thread-pool:
  cancel-account:
    core-pool-size: 5
    max-pool-size: 10
    queue-capacity: 25
    thread-name-prefix: cancelAccountTaskExecutor
#####圈子配置
circle:
  config:
    url: https://group-kd-njs.yun.139.com
    appKey: 1118462887501971461
    appSecretId: 1195080175589212198
    appSecret: L6&(!Y)jKwO%FaGq
    algorithmVersion: 1.0