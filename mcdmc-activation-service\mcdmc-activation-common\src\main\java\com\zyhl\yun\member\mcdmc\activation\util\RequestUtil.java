
package com.zyhl.yun.member.mcdmc.activation.util;

import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/05/22 18:04
 */


public class RequestUtil {
    private RequestUtil() {
    }

    private static final String EMPTY_STR = "";

    public static HttpServletRequest getRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (null == requestAttributes) {
            return null;
        }
        return ((ServletRequestAttributes) requestAttributes).getRequest();
    }

    public static String getRequestUri() {
        HttpServletRequest request = getRequest();
        if (Objects.isNull(request)) {
            return EMPTY_STR;
        }
        return request.getRequestURI();
    }

    public static String getRequestUrl() {
        HttpServletRequest request = getRequest();
        if (Objects.isNull(request)) {
            return EMPTY_STR;
        }
        return request.getRequestURL().toString();
    }

    /**
     * 获取请求头的值
     *
     * @param key key值
     */
    public static String getHeader(String key) {
        HttpServletRequest request = getRequest();
        if (Objects.isNull(request)) {
            return EMPTY_STR;
        }
        return request.getHeader(key);
    }

    public static <T> T getAttribute(String key) {
        return getAttribute(key, null);
    }

    public static <T> T getAttribute(String key, T defaultValue) {
        HttpServletRequest request = getRequest();
        if (Objects.isNull(request)) {
            return defaultValue;
        }
        T attribute = (T) request.getAttribute(key);
        if (Objects.isNull(attribute)) {
            return defaultValue;
        }
        return attribute;
    }

    private static HttpSession getSession(boolean allowCreate) {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return null;
        }
        return request.getSession(allowCreate);
    }

    static Object getContext(String key) {
        HttpSession session = getSession(false);
        if (session == null) {
            return null;
        }
        return session.getAttribute(key);
    }

//    public static String getRemoteAddress(boolean isNeedDefaultIp) {
//        HttpServletRequest request = getRequest();
//        if (Objects.isNull(request)) {
//            return isNeedDefaultIp ? USER_DEFAULT_IP : EMPTY_STR;
//        }
//        return request.getRemoteAddr();
//    }
}