package com.zyhl.yun.member.common.domain.framework.service;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.zyhl.yun.member.common.ServiceException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 返回实体
 *
 * <AUTHOR>
 * @since 2023/01/06 21:30
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class Result<T> extends BaseResult {

    @JsonIgnore
    private T data;


    @JsonAnyGetter
    public Map<String, Object> getDataProperty() {
        Map<String, Object> map = new HashMap<>();
        if (data != null) {
            JacksonXmlRootElement annotation = data.getClass().getAnnotation(JacksonXmlRootElement.class);
            String rootName = data.getClass().getSimpleName();
            if (Objects.nonNull(annotation) && CharSequenceUtil.isNotEmpty(annotation.localName())) {
                rootName = annotation.localName();
            }
            map.put(StringUtils.uncapitalize(rootName), data);
        }
        return map;
    }

    public Result(boolean isSuccess, String resultCode, String message, T data) {
        super(isSuccess, resultCode, message);
        this.data = data;
    }

    public Result(boolean isSuccess, String resultCode, T data) {
        super(isSuccess, resultCode, null);
        this.data = data;
    }

    public static Result<Object> ok() {
        return new Result<>(true, ResultCode.SUCCESS.getCode(), null);
    }

    public static <T> Result<T> success(T data) {
        return new Result<>(true, ResultCode.SUCCESS.getCode(), data);
    }

    public static Result<Object> error(ServiceException exception) {
        return Result.error(exception.getErrorCode(), exception.getErrorMessage(), null);
    }

    public static Result<Object> error(ResultCode resultCode) {
        return Result.error(resultCode.getCode(), resultCode.getMsg(), null);
    }

    public static <T> Result<T> error(ResultCode resultCode, T data) {
        return Result.error(resultCode.getCode(), resultCode.getMsg(), data);
    }

    public static Result<Object> error(String errorCode, String message) {
        return Result.error(errorCode, message, null);
    }

    public static <T> Result<T> error(String errorCode, String message, T data) {
        return new Result<>(false, errorCode, message, data);
    }

}
