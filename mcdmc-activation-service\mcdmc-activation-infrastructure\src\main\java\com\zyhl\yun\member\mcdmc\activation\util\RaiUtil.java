package com.zyhl.yun.member.mcdmc.activation.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.JsonUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceExtendServiceId;
import com.zyhl.yun.member.common.domain.serviceid.GoodsServiceId;
import com.zyhl.yun.member.common.enums.MessageServiceId;
import com.zyhl.yun.member.consumer.common.producer.MqProducer;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.receive.domain.dto.QueryGoodsInstanceExtendCondition;
import com.zyhl.yun.member.mcdmc.activation.constants.BusinessConstant;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.mq.properties.SmsMqProperties;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.rai.RenewFlagEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.mq.message.ContentSquareMqMessage;
import com.zyhl.yun.member.mcdmc.activation.domain.mq.properties.ContentMqProperties;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.redis.RedisComponent;
import com.zyhl.yun.member.mcdmc.activation.redis.RedisKeyConstant;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiNormalProperties;
import com.zyhl.yun.member.message.domain.MessageDo;
import com.zyhl.yun.member.message.domain.SmsMessageContext;
import com.zyhl.yun.member.product.common.constants.SymbolConstant;
import com.zyhl.yun.member.product.common.enums.ChargeTypeEnum;
import com.zyhl.yun.member.product.common.enums.TimePlanCycleTypeEnum;
import com.zyhl.yun.member.product.domain.goods.GoodsDo;
import com.zyhl.yun.member.product.domain.goods.policy.timeplan.GoodsTimePlanDo;
import com.zyhl.yun.member.task.common.domain.MessageNoticeTaskDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.zyhl.yun.member.product.common.constants.Constant.*;

/**
 * <AUTHOR>
 * @since 2024/07/29 16:40
 */
@Slf4j
@Component
public class RaiUtil {
    /**
     * 领取成功状态码
     */
    public static final String RECEIVE_SUCCESS_RESULT_CODE = "0000";
    /**
     * 权益领取成功订单状态
     */
    public static final String RECEIVE_ORDER_SUCCESS_STATUS = "5";
    @Resource
    private RedisComponent redisComponent;
    @Resource
    private MqProducer mqProducer;
    @Resource
    private ContentMqProperties contentMqProperties;
    @Resource
    private SmsMqProperties smsMqProperties;
    @Resource
    private RaiNormalProperties raiNormalProperties;

    public void sendContentMq(ComSendInterfaceReq comSendReq, GoodsInstanceDo subGoodsInstance) {
        // 发送内容mq
        ContentSquareMqMessage contentMqMessage = new ContentSquareMqMessage();
        contentMqMessage.setTraceID(UUID.fastUUID().toString(true));
        contentMqMessage.setOprTime(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN));
        contentMqMessage.setActionType(1);
        contentMqMessage.setOrderID(comSendReq.getOrderID());
        contentMqMessage.setAccount(comSendReq.getMsisdn());
        contentMqMessage.setOrdUserID(comSendReq.getUserId());
        contentMqMessage.setProductID(comSendReq.getGoodsId());
        contentMqMessage.setProductName(comSendReq.getGoodsName());
        contentMqMessage.setChargeType(ChargeTypeEnum.BY_TIMES.getType());
        contentMqMessage.setPayType(Integer.parseInt(comSendReq.getPayWay()));
        contentMqMessage.setOperator(RenewFlagEnum.FIRST_SUB.getOpType());
        Map<String, Object> extInfoMap = new HashMap<>();
        JSONObject proJsonObj = JSONUtil.parseObj(comSendReq.getProServiceId());

        extInfoMap.put(OtherFieldConstants.RIGHTS_SERVICE_ID, proJsonObj.getStr(OtherFieldConstants.SALES_ID));
        extInfoMap.put(OtherFieldConstants.RIGHTS_SERVICE_SKU_ID, proJsonObj.getStr(OtherFieldConstants.PROD_ID));
        extInfoMap.put(OtherFieldConstants.RIGHTS_START_TIME, DateUtil.format(subGoodsInstance.getEffectiveStartTime(), DatePattern.NORM_DATETIME_PATTERN));
        extInfoMap.put(OtherFieldConstants.RIGHTS_END_TIME, DateUtil.format(subGoodsInstance.getEffectiveEndTime(), DatePattern.NORM_DATETIME_PATTERN));
        extInfoMap.put(OtherFieldConstants.RIGHTS_OUT_PRODUCT_ID, proJsonObj.getStr(OtherFieldConstants.RIGHTS_PROD_ID));
        contentMqMessage.setExtInfo(extInfoMap);
        mqProducer.sendOuterMessage(contentMqProperties, contentMqMessage);
    }

    /**
     * 发送给内容mq消息
     *
     * @param comSendReq       上下文
     * @param outOrderId       外部订单号
     * @param outUpdateTime    外部返回的订单权益更新时间，格式为：yyyy-MM-dd HH:mm:ss
     * @param subGoodsInstance 子订购关系
     */
    public void sendContentMq(ComSendInterfaceReq comSendReq, String outOrderId, String outUpdateTime, GoodsInstanceDo subGoodsInstance, String orderItemId) {
        // 发送内容mq
        ContentSquareMqMessage contentMqMessage = new ContentSquareMqMessage();
        contentMqMessage.setTraceID(UUID.fastUUID().toString(true));
        contentMqMessage.setOprTime(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN));
        contentMqMessage.setActionType(1);
        contentMqMessage.setOrderID(comSendReq.getOrderID());
        contentMqMessage.setAccount(comSendReq.getMsisdn());
        contentMqMessage.setOrdUserID(comSendReq.getUserId());
        contentMqMessage.setProductID(comSendReq.getGoodsId());
        contentMqMessage.setProductName(comSendReq.getGoodsName());
        contentMqMessage.setChargeType(ChargeTypeEnum.BY_TIMES.getType());
        contentMqMessage.setPayType(Integer.parseInt(comSendReq.getPayWay()));
        contentMqMessage.setUserDomainId(comSendReq.getUserDomainId());

        if (isFirstSub(comSendReq, subGoodsInstance)) {
            contentMqMessage.setOperator(RenewFlagEnum.FIRST_SUB.getOpType());
            contentMqMessage.setRenewFlag(RenewFlagEnum.FIRST_SUB.strCode());
        } else {
            //    若是包月产品且子产品只有一条订购关系，则表示为续费
            contentMqMessage.setOperator(RenewFlagEnum.RE_NEW.getOpType());
            contentMqMessage.setRenewFlag(RenewFlagEnum.RE_NEW.strCode());
        }
        Map<String, Object> extInfoMap = new HashMap<>();
        JSONObject proJsonObj = JSONUtil.parseObj(comSendReq.getProServiceId());

        extInfoMap.put(OtherFieldConstants.RIGHTS_SERVICE_ID, proJsonObj.getStr(OtherFieldConstants.SALES_ID));
        extInfoMap.put(OtherFieldConstants.RIGHTS_SERVICE_SKU_ID, proJsonObj.getStr(OtherFieldConstants.PROD_ID));
        extInfoMap.put(OtherFieldConstants.RIGHTS_SERVICE_SUB_ID, outOrderId);
        String rightStartTime = DateUtil.format(subGoodsInstance.getEffectiveStartTime(), DatePattern.NORM_DATETIME_PATTERN);
        if (rightStartTime == null) {
            rightStartTime = outUpdateTime;
        }
        extInfoMap.put(OtherFieldConstants.RIGHTS_START_TIME, rightStartTime);
        extInfoMap.put(OtherFieldConstants.RIGHTS_END_TIME, DateUtil.format(subGoodsInstance.getEffectiveEndTime(), DatePattern.NORM_DATETIME_PATTERN));
        extInfoMap.put(OtherFieldConstants.RIGHTS_OUT_ACCOUNT_ID, orderItemId);
        extInfoMap.put(OtherFieldConstants.RIGHTS_OUT_PRODUCT_ID, proJsonObj.getStr(OtherFieldConstants.RIGHTS_PROD_ID));
        extInfoMap.put(OtherFieldConstants.RENEW_FLAG, contentMqMessage.getRenewFlag());
        contentMqMessage.setExtInfo(extInfoMap);
        mqProducer.sendOuterMessage(contentMqProperties, contentMqMessage);
    }

    /**
     * 判断是否为首次订购
     *
     * @param comSendReq       公共请求入参
     * @param subGoodsInstance 子订购关系
     */
    private boolean isFirstSub(ComSendInterfaceReq comSendReq, GoodsInstanceDo subGoodsInstance) {
        // 查询父商品实例
        GoodsInstanceDo parentGoodsInstance = null;
        if (StringUtils.isNotBlank(subGoodsInstance.getGoodsPackageInstanceId())) {
            DomainServiceContext subContext = new DomainServiceContext(GoodsInstanceExtendServiceId.QUERY_GOODS_INSTANCE_EXTEND);
            // 如果是畅学则父订购关系为空
            QueryGoodsInstanceExtendCondition queryCondition = new QueryGoodsInstanceExtendCondition();
            queryCondition.setUserId(comSendReq.getUserId());
            queryCondition.setGoodsInstanceId(subGoodsInstance.getGoodsPackageInstanceId());
            parentGoodsInstance = subContext.readFirst(queryCondition, GoodsInstanceDo.class);
        }
        // 父商品实例的所有子商品实例数量(默认为1)
        long allSubcount = 1;
        // 默认获取子的计费类型
        String chargeType = subGoodsInstance.getChargeType();
        if (parentGoodsInstance != null) {
            // 存在父商品实例则获取父商品实例的计费类型
            chargeType = parentGoodsInstance.getChargeType();
            // 根据父商品实例查询所有子商品实例
            DomainServiceContext subContext = new DomainServiceContext(GoodsInstanceExtendServiceId.QUERY_GOODS_INSTANCE_EXTEND);
            QueryGoodsInstanceExtendCondition queryCondition = new QueryGoodsInstanceExtendCondition();
            queryCondition.setUserId(comSendReq.getUserId());
            queryCondition.setGoodsPackageInstanceId(subGoodsInstance.getGoodsPackageInstanceId());
            List<GoodsInstanceDo> goodsInstanceList = subContext.read(queryCondition, GoodsInstanceDo.class);
            allSubcount = CollUtil.emptyIfNull(goodsInstanceList).size();
        }
        //若是包月产品且子产品只有一条订购关系，则表示为续费, 其它情况则为首次订购
        return !ChargeTypeEnum.MONTHLY.typeEquals(chargeType) || allSubcount == 1;
    }


    /**
     * 常规权益订购成功之后要发送短信
     *
     * @param comSendReq       公共请求参数
     * @param subGoodsInstance 子商品实例
     * @param logId            接口日志id
     * @apiNote 现在暂时没有用发送短信
     */
    @SuppressWarnings("unused")
    public void sendSms(ComSendInterfaceReq comSendReq, GoodsInstanceDo subGoodsInstance, String logId) {
        DomainServiceContext goodsContext = new DomainServiceContext(GoodsServiceId.QUERY_GOODS_INFO);
        GoodsDo goodsDo = goodsContext.read(subGoodsInstance.getGoodsId(), GoodsDo.class);
        if (goodsDo == null || goodsDo.getGoodsExt() == null) {
            log.error("查询不到商品或商品扩展信息为空，logId={}，商品ID：{},不发送短信", logId, subGoodsInstance.getGoodsPackageId());
            return;
        }
        if (goodsDo.getSmsPolicy() == null) {
            log.error("商品短信策略为空，不发送短信，接口日志id={}，商品id：{}", logId, goodsDo.getGoodsId());
            return;
        }
        GoodsTimePlanDo timePlan = goodsDo.getTimePlan();
        String cycleCount = null;
        if (timePlan != null) {
            cycleCount = queryCycleTypeByUnit(timePlan.getCycleType(), timePlan.getCycleCount());
        }

        // 短信模板占位值
        Map<String, String> argsMap = new HashMap<>();
        argsMap.put("goodsName", goodsDo.getGoodsName());
        argsMap.put("cycleCount", cycleCount);

        String msisdn = comSendReq.getMsisdn();
        String secureMsisdn = String.valueOf(PhoneUtil.hideBetween(msisdn));
        argsMap.put("accountName", secureMsisdn);
        argsMap.put("subTime", DateUtil.format(subGoodsInstance.getSubTime(), BusinessConstant.CHINESE_DATE_PATTERN));

        String totalAmountStr = getTotalAmountStr(subGoodsInstance, comSendReq);
        argsMap.put("totalAmount", totalAmountStr);
        // 通过支付类型获取模板id
        String templateId = getTemplateIdByPayWay(goodsDo, subGoodsInstance.getPayWay());

        MessageDo messageDo = new MessageDo();
        messageDo.setTraceId(goodsDo.getTraceId());
        messageDo.setMsgId(logId);
        messageDo.setServiceId(MessageServiceId.SmsNotice.SERVICE_ID_NOTICE);
        messageDo.setEventType(MessageServiceId.Operator.CREATE);
        messageDo.setOperateTime(new Date());
        messageDo.setUserId(comSendReq.getUserId());
        messageDo.setOrderId(comSendReq.getOrderID());
        SmsMessageContext smsMessageContext = new SmsMessageContext();
        smsMessageContext.setTemplateId(templateId);
        smsMessageContext.setMsisdn(comSendReq.getMsisdn());
        smsMessageContext.setArgsMap(argsMap);
        smsMessageContext.setGoodsId(comSendReq.getGoodsId());
        smsMessageContext.setNoticeWay(MessageNoticeTaskDo.NoticeWay.SMS.getCode());
        messageDo.setContext(JsonUtil.toJson(smsMessageContext));
        // log.info("[ACTIVATION OPERATOR MESSAGE] send smsMessage Message. messageDo:{}",
        //         JsonUtil.toJson(messageDo));
        mqProducer.sendNewMessage(smsMqProperties.getSmsTopic(), smsMqProperties.getSmsTag(), messageDo);
        // 设置redis缓存，防止用户重复发送短信
        String raiSubSmsNotifyExpireKey = RedisKeyConstant.getRaiSubSmsNotifyExpireKey(msisdn, subGoodsInstance.getGoodsInstanceId());
        redisComponent.set(raiSubSmsNotifyExpireKey, SymbolConstant.EMPTY,
                raiNormalProperties.getSmsExpireHourTime(), TimeUnit.HOURS);
    }

    /**
     * 获取支付方式对应的金额短信相关字符串
     *
     * @param subGoodsInstance 子订购关系
     * @param comSendReq       公共请求参数
     */
    private static String getTotalAmountStr(GoodsInstanceDo subGoodsInstance, ComSendInterfaceReq comSendReq) {
        String totalAmountStr;
        if (BOUNDS_POINT_PAY_TYPE.contains(String.valueOf(subGoodsInstance.getPayWay()))) {
            // 积分支付,计算对应的积分
            totalAmountStr = comSendReq.getTotalAmount() + "积分";
        } else {
            // 非积分支付
            DecimalFormat df = new DecimalFormat("0.00");
            String totalAmount = df.format((double) comSendReq.getTotalAmount() / 100);
            if (comSendReq.getActivityPrice() != null && comSendReq.getActivityRenewPrice() != null) {
                String activityPrice = df.format((double) comSendReq.getActivityPrice() / 100);
                if (ChargeTypeEnum.MONTHLY.typeEquals(subGoodsInstance.getChargeType())) {
                    String activityRenewPrice = df.format((double) comSendReq.getActivityRenewPrice() / 100);
                    totalAmount = String.format("首月%s元，次月%s", activityPrice, activityRenewPrice);
                } else {
                    totalAmount = activityPrice;
                }
            }
            totalAmountStr = totalAmount + "元";
        }
        return totalAmountStr;
    }

    /**
     * 根据支付方式获取模板id
     *
     * @param goodsDo 商品信息
     * @param payWay  支付方式
     * @return 模板id
     */
    private String getTemplateIdByPayWay(GoodsDo goodsDo, Integer payWay) {
        // 默认订购模板
        String templateId = null;
        if (INNER_PAY_TYPE.contains(String.valueOf(payWay))) {
            // 端内订购类型模板id
            templateId = goodsDo.getSmsPolicy().getInnerRightsReceivedMessageTemplateId();
        } else if (OUT_PAY_TYPE.contains(String.valueOf(payWay))) {
            // 端外订购类型模板id
            templateId = goodsDo.getSmsPolicy().getOuterRightsReceivedMessageTemplateId();
        }
        if (StringUtils.isBlank(templateId)) {
            templateId = goodsDo.getSmsPolicy().getSubMessageTemplateId();
        }
        return templateId;
    }

    /**
     * 查询周期类型
     *
     * @param cycleTypeEnum 周期类型
     * @param cycleCount    周期数量
     * @return 周期类型
     */
    public String queryCycleTypeByUnit(TimePlanCycleTypeEnum cycleTypeEnum, Integer cycleCount) {
        if (Objects.isNull(cycleTypeEnum) || Objects.isNull(cycleCount)) {
            return null;
        }
        String smsContent = null;
        int cycleType = cycleTypeEnum.getType();
        if (TimePlanCycleTypeEnum.RELATIVE_MONTH.typeEquals(cycleType)
                || TimePlanCycleTypeEnum.FIXED_MONTH.typeEquals(cycleType)) {
            smsContent = "个月";
        } else if (TimePlanCycleTypeEnum.FIXED_DAY.typeEquals(cycleType)
                || TimePlanCycleTypeEnum.RELATIVE_DAY.typeEquals(cycleType)) {
            smsContent = "天";
        }
        return cycleCount + smsContent;
    }

}
