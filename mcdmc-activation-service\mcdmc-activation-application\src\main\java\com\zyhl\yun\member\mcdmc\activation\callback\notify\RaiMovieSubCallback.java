package com.zyhl.yun.member.mcdmc.activation.callback.notify;

import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiMovieProperties;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 影视权益订购回调通知
 * com.huawei.jaguar.vsbo.service.action.RaiEquityNotifyResultAction#notifyMovieEquityRightsResult
 *
 * <AUTHOR>
 * @since 2024/07/09 14:02
 */
@Component
public class RaiMovieSubCallback extends RaiComSubCallback {

    @Resource
    private RaiMovieProperties raiMovieProperties;

    @Override
    protected String getDefaultEquityVersion() {
        return raiMovieProperties.getEquityVersion();
    }

}
