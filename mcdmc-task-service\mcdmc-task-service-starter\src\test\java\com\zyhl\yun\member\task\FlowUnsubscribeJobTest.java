package com.zyhl.yun.member.task;

import com.xxl.job.core.context.XxlJobHelper;
import com.zyhl.yun.member.common.enums.SubscribeTypeEnum;
import com.zyhl.yun.member.common.util.MDCUtils;
import com.zyhl.yun.member.domain.resource.domain.ResourceDo;
import com.zyhl.yun.member.product.domain.goods.BenefitGoodsDo;
import com.zyhl.yun.member.product.domain.goods.facade.ProductServiceFacade;
import com.zyhl.yun.member.product.domain.product.ProductDo;
import com.zyhl.yun.member.product.domain.product.ProductServiceDo;
import com.zyhl.yun.member.task.application.job.FreeFlowUnsubscribeJob;
import com.zyhl.yun.member.task.application.job.impl.TaskInstFreeFlowUnsubscribeExecImpl;
import com.zyhl.yun.member.task.common.properties.JobPageProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

/**
 * 包名称:  com.zyhl.yun.member.task
 * 类名称:  FlowUnsubscribeJobTest
 * 类描述:  。
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2025/7/14 14:33
 */
@ExtendWith(MockitoExtension.class)
public class FlowUnsubscribeJobTest {

    @InjectMocks
    private FreeFlowUnsubscribeJob freeFlowUnsubscribeJob;

    @Mock
    private TaskInstFreeFlowUnsubscribeExecImpl taskInstFreeFlowUnsubscribeExec;

    @Mock
    private JobPageProperties jobPageProperties;

    @Mock
    private ProductDo productDo;

    @Mock
    private ProductServiceDo productServiceDo;

    @BeforeEach
    void setUp() throws NoSuchFieldException, IllegalAccessException {
        Field field = FreeFlowUnsubscribeJob.class.getDeclaredField("totalPartitions");
        field.setAccessible(true);
        field.set(freeFlowUnsubscribeJob, 384);
    }

    @Test
    void testCalculatePartitionRange_ValidParams() {
        int[] partitionRange = ReflectionTestUtils.invokeMethod(freeFlowUnsubscribeJob, "calculatePartitionRange", 0, 2);


        // 测试正常参数下的分区计算
        assertEquals(0, partitionRange[0]);
        assertEquals(192, partitionRange[1]);

        partitionRange = ReflectionTestUtils.invokeMethod(freeFlowUnsubscribeJob, "calculatePartitionRange", 1, 2);
        assertEquals(192, partitionRange[0]);
        assertEquals(384, partitionRange[1]);
    }

    @Test
    void testCalculatePartitionRange_InvalidShardIndex() {
        // 使用ReflectionTestUtils调用私有方法
        int[] result = (int[]) ReflectionTestUtils.invokeMethod(freeFlowUnsubscribeJob, "calculatePartitionRange", 3, 2);
        assertEquals(384, result[0]);
        assertEquals(-1, result[1]);
    }

    @Test
    void testFreeFlowUnsubscribeJob_ProductNull() {
        // 测试产品为null的情况
        try (MockedStatic<XxlJobHelper> xxlJobHelperMock = mockStatic(XxlJobHelper.class);
             MockedStatic<ProductServiceFacade> productFacadeMock = mockStatic(ProductServiceFacade.class);
             MockedStatic<MDCUtils> mdcUtilsMock = mockStatic(MDCUtils.class)) {

            mdcUtilsMock.when(() -> MDCUtils.initMdc(anyString())).thenAnswer(invocation -> null);
            xxlJobHelperMock.when(XxlJobHelper::getShardIndex).thenReturn(0);
            xxlJobHelperMock.when(XxlJobHelper::getShardTotal).thenReturn(1);
            productFacadeMock.when(() -> ProductServiceFacade.getProduct(BenefitGoodsDo.BenefitNo.BENEFIT_NO_FREE_FLOW_PKG)).thenReturn(null);

            freeFlowUnsubscribeJob.freeFlowUnsubscribeJob();

            productFacadeMock.verify(() -> ProductServiceFacade.getProduct(BenefitGoodsDo.BenefitNo.BENEFIT_NO_FREE_FLOW_PKG), times(1));
            verify(taskInstFreeFlowUnsubscribeExec, never()).queryTaskList(anyInt(), anyInt());
        }
    }

    @Test
    void testFreeFlowUnsubscribeJob_ProductServiceNull() {
        // 测试产品服务为null的情况
        try (MockedStatic<XxlJobHelper> xxlJobHelperMock = mockStatic(XxlJobHelper.class);
             MockedStatic<ProductServiceFacade> productFacadeMock = mockStatic(ProductServiceFacade.class);
             MockedStatic<MDCUtils> mdcUtilsMock = mockStatic(MDCUtils.class)) {

            mdcUtilsMock.when(() -> MDCUtils.initMdc(anyString())).thenAnswer(invocation -> null);
            xxlJobHelperMock.when(XxlJobHelper::getShardIndex).thenReturn(0);
            xxlJobHelperMock.when(XxlJobHelper::getShardTotal).thenReturn(1);
            productFacadeMock.when(() -> ProductServiceFacade.getProduct(BenefitGoodsDo.BenefitNo.BENEFIT_NO_FREE_FLOW_PKG)).thenReturn(productDo);
            when(productDo.getProductService(SubscribeTypeEnum.UN_SUBSCRIBE_TYPE_ENUM)).thenReturn(null);

            freeFlowUnsubscribeJob.freeFlowUnsubscribeJob();

            verify(productDo).getProductService(SubscribeTypeEnum.UN_SUBSCRIBE_TYPE_ENUM);
            verify(taskInstFreeFlowUnsubscribeExec, never()).queryTaskList(anyInt(), anyInt());
        }
    }

    @Test
    void testFreeFlowUnsubscribeJob_NoPartitionAssigned() {
        // 测试没有分配到分区的情况
        try (MockedStatic<XxlJobHelper> xxlJobHelperMock = mockStatic(XxlJobHelper.class);
             MockedStatic<MDCUtils> mdcUtilsMock = mockStatic(MDCUtils.class)) {

            mdcUtilsMock.when(() -> MDCUtils.initMdc(anyString())).thenAnswer(invocation -> null);
            xxlJobHelperMock.when(XxlJobHelper::getShardIndex).thenReturn(1);
            xxlJobHelperMock.when(XxlJobHelper::getShardTotal).thenReturn(1);

            freeFlowUnsubscribeJob.freeFlowUnsubscribeJob();

            verify(taskInstFreeFlowUnsubscribeExec, never()).queryTaskList(anyInt(), anyInt());
        }
    }

    @Test
    void testFreeFlowUnsubscribeJob_NormalCase() {
        // 测试正常处理流程
        ResourceDo resourceDo = new ResourceDo();
        resourceDo.setResourceId("testResourceId");
        List<ResourceDo> resourceDoList = Arrays.asList(resourceDo);

        try (MockedStatic<XxlJobHelper> xxlJobHelperMock = mockStatic(XxlJobHelper.class);
             MockedStatic<ProductServiceFacade> productFacadeMock = mockStatic(ProductServiceFacade.class);
             MockedStatic<MDCUtils> mdcUtilsMock = mockStatic(MDCUtils.class)) {

            mdcUtilsMock.when(() -> MDCUtils.initMdc(anyString())).thenAnswer(invocation -> null);
            xxlJobHelperMock.when(XxlJobHelper::getShardIndex).thenReturn(0);
            xxlJobHelperMock.when(XxlJobHelper::getShardTotal).thenReturn(2);
            productFacadeMock.when(() -> ProductServiceFacade.getProduct(BenefitGoodsDo.BenefitNo.BENEFIT_NO_FREE_FLOW_PKG)).thenReturn(productDo);
            when(productDo.getProductService(SubscribeTypeEnum.UN_SUBSCRIBE_TYPE_ENUM)).thenReturn(productServiceDo);
            when(productServiceDo.getSoServiceCode()).thenReturn("testServiceCode");
            when(taskInstFreeFlowUnsubscribeExec.queryTaskList(anyInt(), anyInt())).thenReturn(resourceDoList);

            freeFlowUnsubscribeJob.freeFlowUnsubscribeJob();

            verify(taskInstFreeFlowUnsubscribeExec).setSOpenServiceId("testServiceCode");
            verify(taskInstFreeFlowUnsubscribeExec, times(192)).queryTaskList(anyInt(), anyInt());
            verify(taskInstFreeFlowUnsubscribeExec, times(192)).exec(eq(resourceDo), eq(true));
        }
    }
}
