package com.zyhl.yun.member.mcdmc.activation.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.framework.service.MdcLogInterceptor;
import com.zyhl.yun.member.common.domain.serviceid.*;
import com.zyhl.yun.member.common.enums.*;
import com.zyhl.yun.member.consumer.common.producer.MqProducer;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.resource.domain.ResourceDo;
import com.zyhl.yun.member.facade.ResourceServiceFacade;
import com.zyhl.yun.member.goodsinstance.enums.GoodsInstanceStateEnum;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domain.mq.properties.FlowRetryMqProperties;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.FlowStaticConfig;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.CustomDidGenerator;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.message.domain.ActivationMqMessage;
import com.zyhl.yun.member.task.common.domain.InstWorkOrderTaskDo;
import com.zyhl.yun.member.task.common.enums.WorkOrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 会员域相关工具类
 *
 * <AUTHOR>
 * @apiNote 调用会员域的公共查询或写入动作
 * @since 2024/07/28 16:32
 */
@Slf4j
@Component
public class MemberContextUtil {
    @Resource
    protected MqProducer mqProducer;
    @Resource
    protected FlowRetryMqProperties flowRetryMqProperties;

    /**
     * 根据入参查询会员域商品实例（不管是否过期）
     *
     * @param userId          用户ID
     * @param goodsInstanceId 商品实例ID
     * @return 商品实例
     */
    public static GoodsInstanceDo qryGoodsInstanceDo(String userId, String goodsInstanceId) {
        DomainServiceContext memberContext = new DomainServiceContext(GoodsInstanceExtendServiceId.QUERY_GOODS_INSTANCE_EXTEND);
        // 查询商品实例
        QueryGoodsInstanceCondition queryCondition = new QueryGoodsInstanceCondition();
        queryCondition.setUserId(userId);
        queryCondition.setGoodsInstanceIdList(Collections.singletonList(goodsInstanceId));
        return memberContext.readFirst(queryCondition, GoodsInstanceDo.class);
    }

    /**
     * 发送退订定时任务的消息
     *
     * @param userId        用户id
     * @param orderId       订单id
     * @param comSendReqStr 公共请求入参
     * @param unSubTime     退订时间
     */
    public void sendUnsubscribeMessage(String userId, String orderId, String comSendReqStr, Date unSubTime) {
        // ActivationMqMessage messageDo = new ActivationMqMessage();
        // messageDo.setTraceId(MdcLogInterceptor.getCurrentTraceId());
        // messageDo.setMsgId(CustomDidGenerator.generateId());
        // messageDo.setContext(comSendReqStr);
        // messageDo.setOperatorType(MessageServiceId.Operator.CREATE);
        // messageDo.setOperateTime(unSubTime);
        // messageDo.setUserId(userId);
        // messageDo.setOrderId(orderId);
        // // 获取免流退订的serviceCode
        // messageDo.setSopenServiceId(FlowStaticConfig.getFirstServiceCodeByIFlow("openReturnFlow"));
        // mqProducer.sendNewMessage(flowRetryMqProperties.getRetryTopic(), flowRetryMqProperties.getRetryTag(), messageDo);
    }

    /**
     * 查询生效商品实例中最大的失效时间，为null则表示没有生效中的商品实例
     *
     * @param userId    用户id
     * @param productId 产品id
     * @return 生效商品实例中最大的失效时间
     */
    public static GoodsInstanceDo getMaxEndTimeGoodsInstance(String userId, String productId) {
        // 查询所有生效中的商品实例
        DomainServiceContext memberContext = new DomainServiceContext(GoodsInstanceExtendServiceId.QUERY_GOODS_INSTANCE_EXTEND);
        QueryGoodsInstanceCondition queryCondition = new QueryGoodsInstanceCondition();
        queryCondition.setUserId(userId);
        queryCondition.setProductIdList(Collections.singletonList(productId));
        queryCondition.setStateList(Arrays.asList(GoodsInstanceStateEnum.NORMAL, GoodsInstanceStateEnum.UN_SUB));
        List<GoodsInstanceDo> allGoodsInstanceList = memberContext.read(queryCondition, GoodsInstanceDo.class);
        if (CollectionUtils.isEmpty(allGoodsInstanceList)) {
            // 生效中的商品实例为空
            return null;
        }
        // 获取最大的失效时间
        GoodsInstanceDo maxGoodsInstanceDo = null;
        for (GoodsInstanceDo goodsInstanceDo : allGoodsInstanceList) {
            if (null == goodsInstanceDo.getEffectiveEndTime()) {
                continue;
            }
            if (null == maxGoodsInstanceDo || DateUtil.compare(maxGoodsInstanceDo.getEffectiveEndTime(), goodsInstanceDo.getEffectiveEndTime()) < 0) {
                maxGoodsInstanceDo = goodsInstanceDo;
            }
        }
        if (null != maxGoodsInstanceDo && DateUtil.compare(maxGoodsInstanceDo.getEffectiveEndTime(), new Date()) > 0) {
            // 商品实例必须是生效中的
            return maxGoodsInstanceDo;
        }
        return null;
    }

    /**
     * 更新资产表为最大的失效时间
     *
     * @param workOrderDo 工单
     * @return 当前用户的资产信息
     * @apiNote 如果用户资产失效时间变小，则会发起退订的定时任务
     */
    public ResourceDo updateResourceEndTime(WorkOrderDo workOrderDo, boolean isOpen) {
        // 查询当前用户正在生效的资产
        ResourceDo lastValidResource = ResourceServiceFacade.getValidResource(workOrderDo.getUserId(), ResourceTypeEnum.OPEN_FREE_FLOW);
        if (lastValidResource == null) {
            // 不存在生效中的资源,返回空
            return null;
        }
        String userId = workOrderDo.getUserId();
        ComSendInterfaceReq comSendReq = JSONUtil.toBean(workOrderDo.getWorkAttrs(), ComSendInterfaceReq.class);
        // 获取最大失效时间的商品实例
        GoodsInstanceDo maxEndTimeGoodsInstance = getMaxEndTimeGoodsInstance(userId, comSendReq.getProductId());
        if (null == maxEndTimeGoodsInstance) {
            // 开通免流找不到商品实例则表示已经被退了
            log.error("updateResourceEndTime error while open ,userId={} and productId={} can not find goodsInstance，" +
                    "maybe it is already unSub or refund，isOpen ={}", userId, comSendReq.getProductId(), isOpen);
            DomainServiceContext memberContext = new DomainServiceContext(ResourceServiceId.UPDATE_RESOURCE_WITHOUT_STATUS);
            lastValidResource.setEffectiveEndTime(new Date());
            memberContext.putInstance(lastValidResource);
            memberContext.writeAndFlush();
            return lastValidResource;
        }
        Date maxEffectEndTime = maxEndTimeGoodsInstance.getEffectiveEndTime();
        Date resourceEndTime = lastValidResource.getEffectiveEndTime();
        // 时间不一致时更新会员资产失效时间
        if (DateUtil.compare(maxEffectEndTime, resourceEndTime) != 0) {
            // 如果资产表现有失效时间与当前最大失效时间不一致，则更新资产表
            DomainServiceContext memberContext = new DomainServiceContext(ResourceServiceId.UPDATE_RESOURCE_WITHOUT_STATUS);
            lastValidResource.setEffectiveEndTime(maxEffectEndTime);
            memberContext.putInstance(lastValidResource);
            log.info("userId={} update resource,resourceId={},current effectEndTime is {}", userId, lastValidResource.getResourceId(), maxEffectEndTime);
            memberContext.writeAndFlush();
        }

        if (isOpen && DateUtil.compare(maxEffectEndTime, resourceEndTime) > 0) {
            // 订购且失效时间变大，需要发送最大的退订定时任务
            this.sendUnsubscribeMessage(userId, workOrderDo.getOrderId(), workOrderDo.getWorkAttrs(), maxEffectEndTime);
        } else if (!isOpen && DateUtil.compare(maxEffectEndTime, resourceEndTime) < 0) {
            // 非订购且失效时间变小，需要发送最小的退订定时任务
            this.sendUnsubscribeMessage(userId, workOrderDo.getOrderId(), workOrderDo.getWorkAttrs(), maxEffectEndTime);
            // 需要去除掉该用户其它无用的退订定时任务
            DomainServiceContext taskContext = new DomainServiceContext(TaskServiceId.DELETE);
            InstWorkOrderTaskDo taskDo = new InstWorkOrderTaskDo();
            taskDo.setUserId(workOrderDo.getUserId());
            taskDo.setSopenServiceId(workOrderDo.getServiceCode());
            taskDo.setStatus(WorkOrderStatusEnum.NORMAL);
            taskContext.writeAndFlush();
        }
        return lastValidResource;
    }


    /**
     * 更新商品实例表的权益状态和resourceId
     *
     * @param comSendReq    公共请求参数
     * @param resourceId    资源id
     * @param isFinally     是否最终状态
     * @param isSuccess     是否成功
     * @param sendOperation 操作类型
     */
    public static void updateGoodsInstance(ComSendInterfaceReq comSendReq,
                                           String resourceId, boolean isFinally, boolean isSuccess,
                                           SendOperation sendOperation) {
        // 订购流程、最终状态、或成功都要更新领取记录状态
        if (SendOperation.SUB.equals(sendOperation) || Boolean.TRUE.equals(isFinally) || Boolean.TRUE.equals(isSuccess)) {
            log.debug("updateReceiveStatus, resourceId={}, isFinally={}, isSuccess={}, sendOperation={}",
                    resourceId, isFinally, isSuccess, sendOperation);
            DomainServiceContext memberContext = new DomainServiceContext(GoodsInstanceExtendServiceId.UPDATE_GOODS_INSTANCE_EXTEND);
            GoodsInstanceDo goodsInstanceDo = memberContext.newInstance(GoodsInstanceDo.class);
            goodsInstanceDo.setUserId(comSendReq.getUserId());
            goodsInstanceDo.setGoodsInstanceId(comSendReq.getGoodsInstanceId());
            goodsInstanceDo.setOrderId(comSendReq.getOrderID());
            if (SendOperation.SUB.equals(sendOperation)) {
                // 只有订购才更新resourceId
                goodsInstanceDo.setResourceId(resourceId);
            }
            // 更新rightsStatus
            goodsInstanceDo.setRightsStatusEnum(MemberContextUtil.getRightsStatusEnum(isSuccess, isFinally, sendOperation));
            memberContext.writeAndFlush();
        }
    }

    /**
     * 获取权益状态
     *
     * @param isSuccess     是否有回调
     * @param sendOperation 操作类型
     * @return 权益状态
     * @apiNote 权益状态只有订购和退费流程
     */
    private static RightsStatusEnum getRightsStatusEnum(boolean isSuccess, boolean isFinally, SendOperation sendOperation) {
        switch (sendOperation) {
            case UN_SUB:
                if (isFinally) {
                    return isSuccess ? RightsStatusEnum.UNSUBSCRIBING_SUCCESS : RightsStatusEnum.UNSUBSCRIBING_FAIL;
                } else {
                    return isSuccess ? RightsStatusEnum.UNSUBSCRIBING : RightsStatusEnum.UNSUBSCRIBING_FAIL;
                }
            case REFUND:
                if (isFinally) {
                    return isSuccess ? RightsStatusEnum.RETURN_SUCCESS : RightsStatusEnum.RETURN_FAIL;
                } else {
                    return RightsStatusEnum.RETURN_ING;
                }
            case SUB:
            default:
                if (isFinally) {
                    return isSuccess ? RightsStatusEnum.OPEN_SUCCESS : RightsStatusEnum.OPEN_FAIL;
                } else {
                    return isSuccess ? RightsStatusEnum.INITIALIZATION_SUCCESS : RightsStatusEnum.INITIALIZATION_FAIL;
                }

        }
    }
}
