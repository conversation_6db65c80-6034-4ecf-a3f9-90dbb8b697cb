package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.free_flow;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.zyhl.yun.member.common.constants.HeaderConstant;
import com.zyhl.yun.member.common.domain.framework.service.MdcLogInterceptor;
import com.zyhl.yun.member.common.enums.ResourceTypeEnum;
import com.zyhl.yun.member.domain.resource.domain.ResourceDo;
import com.zyhl.yun.member.facade.ResourceServiceFacade;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.CustomDidGenerator;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.SendTemplate;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.OpenCommonProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.OpenReturnProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.OpenReturnReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.OpenReturnRsp;
import com.zyhl.yun.member.mcdmc.activation.util.OrderContextUtil;
import com.zyhl.yun.member.order.domain.OrderDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 一级能开-免流退订接口流程
 * <p>
 * com.huawei.jaguar.vsbo.pub.thread.DealFreeFlowSubListThread#run
 * </p>
 * 配置url地址：/config/vsbo/goToneConfig/OPReturnSyncForAllUrl
 *
 * <AUTHOR>
 * @apiNote 有回调 <hr/>OpenReturnCallback<hr/>
 * @since 2024/06/25 09:09
 */
@Slf4j
@Component
public class OpenReturnIFace extends SendTemplate<OpenReturnReq, OpenReturnRsp> {

    @Resource
    private OpenReturnProperties openReturnProperties;
    @Resource
    private OpenCommonProperties openCommonProperties;

    @Override
    protected Map<String, String> getRequestHeader(InterfaceContext<OpenReturnReq, OpenReturnRsp> interfaceContext) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.CONTENT_TYPE, interfaceContext.getMediaType().toString());
        headerMap.put(HeaderConstant.SRC_MOD_NAME, HeaderConstant.SRC_MOD_NAME_VALUE);
        headerMap.put(HeaderConstant.MCS_FLOW_ID, MdcLogInterceptor.getCurrentTraceId());
        return headerMap;
    }

    @Override
    protected OpenReturnReq getRequestBody(InterfaceContext<OpenReturnReq, OpenReturnRsp> interfaceContext) {
        OpenReturnReq openReturnReq = new OpenReturnReq();
        openReturnReq.setReturnType(openReturnProperties.getOpReturnSyncReturnType());
        openReturnReq.setVersion(openReturnProperties.getOpReturnSyncVersion());
        openReturnReq.setChannelCode(openReturnProperties.getOpReturnSyncChannelCode());
        openReturnReq.setExtReturnId(CustomDidGenerator.generateId() + OpenFreeFlowSyncIFace.FLOW_ORDER_SUFFIX);
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        //  通过调会员域获取对应的免流订单号
        ResourceDo resourceDo = (ResourceDo) interfaceContext.getWorkOrderDo().getExtData(OtherFieldConstants.RESOURCE_DO);
        if (null == resourceDo) {
            // 获取状态正常的资产信息（不能获取生效的，退费的已经被更新过期时间了）
            resourceDo = ResourceServiceFacade.getNormalResource(comSendReq.getUserId(), ResourceTypeEnum.OPEN_FREE_FLOW);
        }
        if (null == resourceDo || null == resourceDo.getParentOrderId()) {
            // 查不到订单号需要返回报错
            throw new FlowTerminationException(this.getClass(), interfaceContext.getWorkOrderDo(), "resourceDo is null or orderId of resource is null");
        }
        openReturnReq.setOrderId(resourceDo.getResourceId());
        OrderDo orderDo = OrderContextUtil.queryOrderDo(resourceDo.getParentOrderId(), comSendReq.getUserId());
        if (null == orderDo || CollectionUtils.isEmpty(orderDo.getOrderDetailDos())) {
            // 查不到订购时的原订单号需要返回报错
            throw new FlowTerminationException(this.getClass(), interfaceContext.getWorkOrderDo(), "orderDo is null");
        }
        String orderItemId = orderDo.getOrderDetailDos().get(0).getOrderDetailId();
        orderItemId = orderItemId.replace("-", "");
        openReturnReq.setSubOrderId(orderItemId);
        String curTime = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        openReturnReq.setExtChannelReturnTime(curTime);
        openReturnReq.setBuyerNickname(comSendReq.getMsisdn());
        openReturnReq.setServiceNo(comSendReq.getMsisdn());
        openReturnReq.setServiceNoType("1");
        openReturnReq.setShopCode(openCommonProperties.getShopCode());
        openReturnReq.setShopName(openCommonProperties.getShopName());
        openReturnReq.setBuyerReturnTime(curTime);
        openReturnReq.setContactPhone(comSendReq.getMsisdn());
        openReturnReq.setTotalFee(0);
        openReturnReq.setPayment(0);
        openReturnReq.setFeedbackUrl(openReturnProperties.getOpReturnSyncFeedbackUrl());
        // 退货商品信息
        OpenReturnReq.ReturnGoodsInfo returnGoodsInfo = new OpenReturnReq.ReturnGoodsInfo();
        returnGoodsInfo.setPrice(0);
        returnGoodsInfo.setGoodsId(openCommonProperties.getMonthlyGoodsID());
        returnGoodsInfo.setGoodsTitle(openCommonProperties.getGoodsTitle());
        returnGoodsInfo.setReturnQuantity(1);
        openReturnReq.setReturnGoodsInfo(Collections.singletonList(returnGoodsInfo));
        return openReturnReq;
    }

    @Override
    protected Class<OpenReturnRsp> getRspClass() {
        return OpenReturnRsp.class;
    }

    @Override
    protected boolean isSuccess(OpenReturnRsp rsp) {
        return OpenReturnRsp.isSuccess(rsp);
    }

    @Override
    protected boolean isNeedCacheFlowLog() {
        // 缓存发货流水日志，加快回调处理速度
        return true;
    }

    @Override
    protected String getCallbackCondition(InterfaceContext<OpenReturnReq, OpenReturnRsp> context) {
        return context.getInterfaceReqObj().getExtReturnId();
    }

    @Override
    protected Class<OpenReturnReq> getReqClass() {
        return OpenReturnReq.class;
    }

}
