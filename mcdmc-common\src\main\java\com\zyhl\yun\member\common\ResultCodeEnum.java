package com.zyhl.yun.member.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/07/10 15:58
 */
@Getter
@AllArgsConstructor
public enum ResultCodeEnum {

    /**
     * 返回码&&返回描述配置
     */
    SUCCESS("0", "SUCCESS"),

    INTERNAL_SERVER_ERROR("1909011999", "服务器内部错误"),
    PARAM_VALIDATE_FAILED("1909011500", "参数校验错误"),

    /**
     * 订购校验类错误
     */
    ORDER_CHANNEL_LIMIT("1809120001", "订购渠道受限"),
    PHONE_NUMBER_LIMIT("1809010017", "订购省份受限"),
    ACCESS_SOURCE_LIMIT("1809111400", "访问来源受限"),
    GOTONE_CHECK_FAILED("1809112500", "全球通校验失败"),

    EXCLUDE_IN_BENEFIT_GROUP_CHECK_FAILED("1809013400", "同权益组订购互斥校验失败"),
    CHILD_GOODS_ACTIVE_CHECK_FAILED("1809013400", "子商品领取校验失败"),
    OTHER_OPERATOR_NOT_SUPPORT("181812102", "不支持异网用户订购"),
    FP_NOT_SUPPORT("1809013406", "fp超出限制"),
    IOT_NUMBER_NOT_SUPPORT("1809013408", "不支持物联网号码订购"),
    NATION_SALE_NOT_SUPPORT("1809013407", "不支持该区域号码订购"),
    ACCUMULATIVE_SUB_TIMES_EXCEED("1809013401", "累计订购次数超出限制"),
    DAILY_SUB_TIMES_EXCEED("1809013403", "当天订购次数超出限制"),
    MONTHLY_SUB_TIMES_EXCEED("1809013404", "当月订购次数超出限制"),
    DAYS_SUB_TIMES_EXCEED("1809013405", "限定日期内订购次数超出限制"),
    ACTIVE_SUB_TIMES_EXCEED("1809013402", "生效中订购次数超出限制"),

    //数据库类错误
    DATA_ABNORMAL("5000000001", "数据异常"),
    DATA_UPDATE_ERROR("5000000002", "数据修改发生错误"),
    DATA_QUERY_ERROR("5000000003", "数据查询发生错误"),
    DATA_UPDATE_EXISTS("5000000004", "数据更新已存在"),

    //服务内部错误类
    ILLEGAL_TELEPHONE("5000001001", "不合法的手机号码"),
    GOODS_ALREADY_EXISTS("5000002000", "此商品已经存在"),
    GOODS_NOT_FOUND("1809010017", "此商品不存在"),
    CHILD_GOODS_NOT_FOUND("5000002002", "此子商品不存在"),
    GOODS_POLICY_CHECK_FAILED("5000002003", "商品策略校验失败"),
    GOODS_POLICY_CHECK_FAILED_PHONE_PROV_CODE("5000002004", "商品手机号分省策略校验失败"),
    GOODS_POLICY_CHECK_FAILED_PHONE_ACCESS_SOURCE("5000002005", "商品接入来源策略校验失败"),
    GOODS_POLICY_CHECK_FAILED_GOTONE_LEVEL_NOT_MATCH("5000002006", "全球通用户等级校验失败"),
    GOODS_POLICY_CHECK_FAILED_OTHER_OPERATOR_NOT_SUPPORT("5000002007", "不支持异网用户订购"),
    GOODS_POLICY_CHECK_FAILED_PAY_WAY_NOT_SUPPORT("5000002008", "不支持的支付方式"),
    GOODS_POLICY_CHECK_FAILED_PAY_CODE_NOT_SUPPORT("5000002009", "计费点校验失败"),
    GOODS_POLICY_CHECK_FAILED_CHANNEL("5000002010", "商品渠道策略校验失败"),
    GOODS_STATE_CHECK_FAILED("5000002050", "商品状态校验失败"),
    GOODS_RELATE_CHECK_FAILED("5000002100", "商品关系校验失败"),
    GOODS_RELATE_CHECK_FAILED_RELATE_UNSUPPORTED("5000002101", "不支持的商品关系"),
    GOODS_RELATE_CHECK_FAILED_RELATE_NOT_CONFIG("5000002102", "商品关系参数未配置"),
    TIME_PLAN_TYPE_NOT_SUPPORT("5000002150", "不支持的时间计划类型"),
    TIME_PLAN_CYCLE_TYPE_NOT_SUPPORT("5000002151", "不支持的周期类型"),
    UN_SUB_EXPIRATION_POLICY_NOT_SUPPORT("5000002200", "不支持的退订过期策略"),
    GOODS_INSTANCE_NOT_FOUND("5000002250", "商品实例不存在"),
    GOODS_INSTANCE_ALREADY_EXISTS("5000002251", "此商品实例已经存在"),
    GOODS_INSTANCE_STATE_ILLEGAL("5000002252", "非法的商品实例状态"),
    GOODS_INSTANCE_ACTION_NOT_ALLOW("5000002253", "该商品实例操作不允许"),
    SERVICE_OPEN_NO_NOT_SUPPORT("5000002301", "服务编号不支持"),
    SERVICE_OPEN_OPERATOR_NOT_SUPPORT("5000002302", "服务操作不支持"),
    TASK_CHECK_STATUS_ILLEGAL("5000003000", "任务状态检查非法"),
    /**
     * 用户不存在错误码
     *
     * @apiNote 由于渗透测试安全需要，错误信息不能暴露给用户，需模糊化处理
     */
    USER_NOT_FOUND("1919011999", "pleasel check user information"),
    USER_NOT_CMCC_USER("1809120012", "not a cmcc user."),
    REFUND_NOT_FOUND("1809013501", "no refund order"),
    TOO_MANY_REFUND("1809013502", "too many refund order"),

    // 业务处理返回码
    SERVICE_PROCESSING("5000003001", "服务处理中"),


    //    这里是产品的返回码
    ProductDO_STATE_CHECK_FAILED("", "商品状态校验失败"),

    // 原vsbo返回码

    /**
     * Unauthorized/Unauthentication: 1、IP鉴权失败 2、用户账号鉴权失败
     */
    UNAUTHORIZED("1909011501", "Authorization Failed!"),

    // 重复赠送
    REPEAT_GIFT("1809013419", "repeat gift"),

    // 可以绑定
    CAN_BIND("0", "can bind"),
    //圈子未创建
    GROUP_NOT_CREATE("1809013421", "group not create"),

    // 主套餐不能绑定自己
    MASTER_CAN_NOT_BIND_SELF("1809013422", "master can not bind self"),

    //家庭套餐数量已达上限
    FAMILY_PACKAGE_NUM_LIMIT("1809013423", "family package num limit"),

    //此用户套餐为被赠送套餐
    USER_GIFT_PACKAGE("1809013424", "this user package is gift package"),

    // 不存在可赠送的家庭云套餐
    NO_GIFT_PACKAGE("1809013425", "no gift family package"),

    // 家庭云套餐绑定号码为空
    BOUNDED_MSISDN_NULL("1809013426", "bouned msisdn is null"),

    // 未找到绑定关系
    BOUNDED_RELATION_NULL("1809013427", "bounded relation is null"),

    // HISTORY_CREATE_FAILED
    HISTORY_CREATE_FAILED("1809013428", "history create failed"),

    // 用户已被其他人员绑定或者查询不对用户信息
    USER_BINDED_OR_QUERY_ERROR("1809013429", "user binded or query error"),

    // 点播不允许解绑
    TIME_CAN_NOT_UNBIND("1809013430", "by time goods  can not unbind"),

    // 当月不可解绑
    MONTH_CAN_NOT_UNBIND("1809013431", "by month goods  can not unbind"),

    //用户域圈子未创建
    USER_DOMAIN_GROUP_NOT_CREATE("1809013432", "user domain group not create"),
    //需要创建圈子或选定已创建的圈子
    GROUP_NOT_CREATE_OR_SELECT("1809013433", "group not create or select"),
    //解绑失败
    UNBINDING_FAILED("1809013434", "unbind failed"),

    //第三方服务调用失败
    THIRD_PARTY_SERVICE_CALL_FAILED("1809013435", "Third party service call failed"),

    // 在绑定的时候，报家庭云会员绑定上限
    FAMILY_PACKAGE_BIND_LIMIT("1809013436", "family package bind limit"),
    /**
     * 渠道商品上架限制
     */
    CHANNEL_GOODS_SHELF_LIMIT("1809013437", "channel goods shelf limit"),
    /**
     * 渠道商品支付方式限制
     */
    CHANNEL_GOODS_PAY_WAY_LIMIT("1809013438", "channel goods pay way limit"),
    /**
     * 渠道未配置
     */
    CHANNEL_NOT_CONFIGURED("1809013439", "channel not configured"),

    GOODS_ORIGINAL_PRICE_NOT_CONFIGURED("1809013440", "goods original price not configured"),

    /**
     * 渠道上架信息不匹配
     */
    CHANNELS_SHELF_INFO_NOT_MATCH("1809013441", "channel shelf info not match"),

    PARENT_SUBMIT_TIME_EXCEEDED("1809120018", "Parent submit time exceeded is less than 1 hour");
    /**
     * 返回码
     */
    private String resultCode;

    /**
     * 返回描述
     */
    private String resultDesc;

    ResultCodeEnum() {

    }
}
