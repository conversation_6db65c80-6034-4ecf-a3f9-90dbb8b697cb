<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!--<parent>-->
    <!--    <groupId>com.aspire.scaffoldbas</groupId>-->
    <!--    <artifactId>scaffoldbas-parent</artifactId>-->
    <!--    <version>1.0.0-SNAPSHOT</version>-->
    <!--</parent>-->

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zyhl.yun.member.mcdmc</groupId>
    <artifactId>mcdmc</artifactId>
    <packaging>pom</packaging>

    <version>1.0-SNAPSHOT</version>

    <modules>
        <module>mcdmc-common</module>
        <module>mcdmc-inner-gateway</module>
        <module>mcdmc-order-service</module>
        <module>mcdmc-product-service</module>
        <module>mcdmc-member-service</module>
        <module>mcdmc-task-service</module>
        <module>mcdmc-mq-consumer-service</module>
        <module>mcdmc-activation-service</module>
        <module>mcdmc-support-service</module>
        <module>mcdmc-payment-service</module>
        <module>mcdmc-tools</module>
        <module>mcdmc-white-service</module>
        <module>mcdmc-compare-service</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <spring-cloud-starter-bootstrap.version>3.1.3</spring-cloud-starter-bootstrap.version>
        <mapstruct.version>1.4.1.Final</mapstruct.version>
        <slf4j-api.version>1.7.6</slf4j-api.version>
        <redisson-spring-boot-starter.version>3.17.1</redisson-spring-boot-starter.version>
        <spring-cloud.version>2021.0.4</spring-cloud.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
        <mybatis-plus.version>3.5.3.2</mybatis-plus.version>
        <!--        <mysql.version>8.0.32</mysql.version>-->
        <mysql.version>8.2.0</mysql.version>
        <oceanbase.client.version>2.4.11</oceanbase.client.version>
        <!--<redisson-spring-boot-starter.version>3.19.0</redisson-spring-boot-starter.version>-->
        <jedis.version>4.3.1</jedis.version>
        <junit.version>4.13.2</junit.version>
        <lombok.version>1.18.24</lombok.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <hutool-all.version>5.8.28</hutool-all.version>
        <jackson-bom.version>2.13.5</jackson-bom.version>
        <commons-io.version>2.11.0</commons-io.version>
        <commons-pool2.version>2.11.0</commons-pool2.version>
        <commons-text.version>1.10.0</commons-text.version>
        <log4j.version>2.19.0</log4j.version>
        <docker.plugin.version>1.0.0</docker.plugin.version>
        <druid-spring-boot-starter.version>1.2.15</druid-spring-boot-starter.version>
        <pagehelper-spring-boot-starter.version>1.4.6</pagehelper-spring-boot-starter.version>
        <jasypt-spring-boot-starter.version>3.0.5</jasypt-spring-boot-starter.version>

        <snakeyaml.version>2.0</snakeyaml.version>
        <spring-boot-starter-webflux.version>2.7.18</spring-boot-starter-webflux.version>
        <hcy-plugins-starter-logger.version>2.1.7</hcy-plugins-starter-logger.version>
        <hcy-platform-commons.version>2.0.3</hcy-platform-commons.version>
        <hcy-plugin-neauth.version>2.0.0-M</hcy-plugin-neauth.version>
        <!-- 日志组件依赖-版本配置管理 -->
        <aspectj.version>1.9.7</aspectj.version>
        <feign-core.version>10.7.4</feign-core.version>
        <fastjson.version>1.2.83</fastjson.version>
        <nacos.version>2.2.0</nacos.version>

        <rocketmq-spring-boot-starter.version>2.2.3</rocketmq-spring-boot-starter.version>
        <guava.version>33.3.1-jre</guava.version>
        <commons-io.version>2.14.0</commons-io.version>
        <xxl-job-core.version>2.4.2</xxl-job-core.version>
        <commons-fileupload.version>1.5</commons-fileupload.version>
        <grpc-protobuf.version>1.68.1</grpc-protobuf.version>
        <bcprov-jdk18on.version>1.78.1</bcprov-jdk18on.version>
        <spring.boot.maven.plugin.version>2.6.2</spring.boot.maven.plugin.version>
        <!--        <protobuf-java.version>4.29.0</protobuf-java.version>-->
        <netty-all.version>4.1.115.Final</netty-all.version>
        <jedis.version>5.2.0</jedis.version>
        <logback.version>1.2.13</logback.version>
        <rocketmq.version>5.0.0</rocketmq.version>
        <aspire.cbb.did.version>2.3.3</aspire.cbb.did.version>
        <tomcat-embed-core.version>9.0.104</tomcat-embed-core.version>
        <mockito.version>4.5.1</mockito.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <!-- Jackson BOM for version management -->
            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <version>${jackson-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 明确指定Jackson XML依赖版本，确保与BOM一致 -->
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>${jackson-bom.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat-embed-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat-embed-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat-embed-core.version}</version>
            </dependency>
            <!--中移互联日志插件包-->
            <dependency>
                <groupId>com.zyhl.hcy</groupId>
                <artifactId>hcy-plugins-starter-logger</artifactId>
                <version>${hcy-plugins-starter-logger.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zyhl.hcy</groupId>
                <artifactId>yun-neauth-starter</artifactId>
                <version>${hcy-plugin-neauth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zyhl.hcy</groupId>
                <artifactId>hcy-platform-commons</artifactId>
                <version>${hcy-platform-commons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aspire.cbb</groupId>
                <artifactId>did-spring-boot-starter</artifactId>
                <version>${aspire.cbb.did.version}</version>
            </dependency>
            <dependency>
                <groupId>com.oceanbase</groupId>
                <artifactId>oceanbase-client</artifactId>
                <version>${oceanbase.client.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.slf4j/slf4j-api -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.google.guava/guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/ch.qos.logback/logback-classic -->
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.google.protobuf/protobuf-java -->
            <!--            <dependency>-->
            <!--                <groupId>com.google.protobuf</groupId>-->
            <!--                <artifactId>protobuf-java</artifactId>-->
            <!--                <version>${protobuf-java.version}</version>-->
            <!--            </dependency>-->
            <!-- https://mvnrepository.com/artifact/io.grpc/grpc-protobuf -->
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-protobuf</artifactId>
                <version>${grpc-protobuf.version}</version>
            </dependency>


            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-acl</artifactId>
                <version>${rocketmq.version}</version>
                <!--                <exclusions>-->
                <!--                    <exclusion>-->
                <!--                        <groupId>com.google.protobuf</groupId>-->
                <!--                        <artifactId>protobuf-java</artifactId>-->
                <!--                    </exclusion>-->
                <!--                </exclusions>-->
            </dependency>

            <!-- Apache Commons FileUpload - 强制版本覆盖传递依赖 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons-fileupload.version}</version>
            </dependency>

            <!-- SnakeYAML - 强制版本覆盖传递依赖，解决与Jackson的兼容性问题 -->
<!--            <dependency>-->
<!--                <groupId>org.yaml</groupId>-->
<!--                <artifactId>snakeyaml</artifactId>-->
<!--                <version>${snakeyaml.version}</version>-->
<!--            </dependency>-->
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <propertyFile>archetype.yaml</propertyFile>
                    <encoding>UTF-8</encoding>
                    <archetypeFilteredExtentions>java,xml,yaml,yml,properties,txt</archetypeFilteredExtentions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.21.0</version>
                <configuration>
                    <skipTests>true</skipTests>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
        </plugins>

    </build>


    <profiles>
        <!-- 开发 -->
        <profile>
            <id>dev</id>
            <activation>
                <!--默认激活配置-->
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <!--环境名称，与研发云参数相对应-->
                <profile.name>dev</profile.name>
                <!--harbor地址-->
                <harbor.registry>*********:8080</harbor.registry>
                <!--harbor命名空间-->
                <harbor.namespace>mcdmc</harbor.namespace>
                <!--harbor push-->
                <harbor.pushUrl>*********:8080</harbor.pushUrl>
                <nacos.namespace>0f50ffae-87c4-4614-888c-092c9d0c053a</nacos.namespace>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <activation>
                <!--默认激活配置-->
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <!--环境名称，与研发云参数相对应-->
                <profile.name>test</profile.name>
                <!--harbor地址-->
                <harbor.registry>*********:8080</harbor.registry>
                <!--harbor命名空间-->
                <harbor.namespace>mcdmc</harbor.namespace>
                <!--harbor push-->
                <harbor.pushUrl>*********:8080</harbor.pushUrl>
                <nacos.namespace>1fb6d427-0db5-4c0a-be46-0e56af100458</nacos.namespace>
            </properties>
        </profile>
    </profiles>

</project>
