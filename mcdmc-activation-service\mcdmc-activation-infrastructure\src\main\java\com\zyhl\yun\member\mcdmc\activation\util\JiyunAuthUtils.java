package com.zyhl.yun.member.mcdmc.activation.util;

import com.zyhl.yun.member.common.constants.HTTPConstants;
import com.zyhl.yun.member.common.constants.JiyunConstants;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.framework.service.MdcLogInterceptor;
import com.zyhl.yun.member.common.domain.serviceid.ResourceServiceId;
import com.zyhl.yun.member.common.enums.ResourceTypeEnum;
import com.zyhl.yun.member.domain.jiyun.domain.FlowSpecSubscriptionDo;
import com.zyhl.yun.member.domain.resource.domain.ResourceDo;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.JiyunProperties;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 集运签名生成工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/3 10:37
 */
@Slf4j
@Data
@RequiredArgsConstructor
@Component
public class JiyunAuthUtils {

    @Resource
    private JiyunProperties jiyunProperties;

    private static final char[] CHAR_ARRAY =
            {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l',
                    'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G',
                    'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};
    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    private String secretId;

    /**
     * paramMap 参数map
     */
    private Map<String, String[]> paramMap;

    /**
     * 集运接口地址
     */
    private String uri;

    /**
     * 13位时间戳
     */
    private long timestamp;

    /**
     * 4位随机数
     */
    private String nonce;

    /**
     * 签名信息
     */
    private String signature;

    /**
     * 更新资产表数据
     */
    public void updateResource(FlowSpecSubscriptionDo flowSpecSubscriptionDo) {
        ResourceDo resourceDo = new ResourceDo();
        DomainServiceContext resourceContext = new DomainServiceContext(ResourceServiceId.UPDATE_RESOURCE_INFO);
        resourceDo.setResourceType(ResourceTypeEnum.SPEC_FREE_FLOW);
        resourceDo.setUserId(flowSpecSubscriptionDo.getUserId());
        resourceDo.setState(Integer.parseInt(flowSpecSubscriptionDo.getStatus()));
        resourceDo.setResourceId(flowSpecSubscriptionDo.getResourceId());

        resourceDo.setEffectiveStartTime(flowSpecSubscriptionDo.getValidDate());
        resourceDo.setEffectiveEndTime(flowSpecSubscriptionDo.getExpireDate());
        resourceDo.setUpdateTime(new Date());
        resourceContext.putInstance(resourceDo);
        resourceContext.writeAndFlush();
    }


    /**
     * 2023-2-8
     * 获取当前时间距离月底的毫秒数作为subscribeFlowSpecNotify的redis key缓存时间
     *
     * @return Long
     */
    public Long getEndMonthExpirationTime() {
        log.info("method getEndMonthExpirationTime begin!");
        long curMilliSeconds = System.currentTimeMillis();
        log.info("getEndMonthExpirationTime  System  currentTimeMillis  is : " + curMilliSeconds);
        Calendar cale = Calendar.getInstance();
        cale.add(Calendar.MONTH, 1);
        cale.set(Calendar.DAY_OF_MONTH, 0);
        long endMonthTime = cale.getTime().getTime();
        long endMonthExpirationTime = endMonthTime - curMilliSeconds;
        log.info("getEndMonthExpirationTime  endMonthExpirationTime is : " + endMonthExpirationTime);
        log.info("method getEndMonthExpirationTime end!");
        return endMonthExpirationTime;
    }

    public Map<String, String> getJiyunRequestHeader(String body, String uri) {
        log.info("method getJiyunRequestHeader begin!");
        Map<String, String> headerMap = new HashMap<>(7);
        headerMap.put(HTTPConstants.CONTENT_TYPE, HTTPConstants.MIME_OF_JSON);
        headerMap.put(JiyunConstants.ACCOUNT, StringUtils.EMPTY);
        headerMap.put(JiyunConstants.HeaderConstant.X_MCS_FLOWID, MdcLogInterceptor.getCurrentTraceId());
        headerMap.put(JiyunConstants.HeaderConstant.X_SRCMODNAME, JiyunConstants.MOD_NAME);
        headerMap.put(JiyunConstants.HeaderConstant.X_HMAC_AUTH_SECRET_ID, jiyunProperties.getJiyunSecretId());
        long timestamp = System.currentTimeMillis();
        headerMap.put(JiyunConstants.HeaderConstant.X_HMAC_AUTH_TIMESTAMP, String.valueOf(timestamp));

        String nonce = timestamp + JiyunAuthUtils.getNonce();
        headerMap.put(JiyunConstants.HeaderConstant.X_HMAC_AUTH_NONCE, nonce);
        headerMap.put(JiyunConstants.HeaderConstant.X_HMAC_AUTH_SIGNATURE,
                getSignature(jiyunProperties.getJiyunSecretId(), body, uri, timestamp, nonce));
        log.info("Map<String, String> headerMap is ,: {}", headerMap);
        log.info("method getJiyunRequestHeader end!");
        return headerMap;
    }

    /**
     * 生成4位随机数，用于集运请求时签名
     *
     * @return 4位随机数
     */
    public static String getNonce() {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < 4; i++) {
            builder.append(CHAR_ARRAY[SECURE_RANDOM.nextInt(CHAR_ARRAY.length)]);
        }

        return builder.toString();
    }

    /**
     * 生成集运签名
     *
     * @param secretID
     * @param body
     * @param uri
     * @param timestamp
     * @param nonce
     * @return
     */
    public String getSignature(String secretID, String body, String uri, long timestamp, String nonce) {
        JiyunAuthUtils jiyunAuthUtils = new JiyunAuthUtils(secretID, body, uri, timestamp, nonce);
        try {
            return jiyunAuthUtils.getSignature(jiyunProperties.getJiyunSecretKey());
        } catch (Exception e) {
            log.error("get signature failed.", e);
        }
        return null;
    }

    /**
     * @param secretId  集运平台分配的账号
     * @param httpBody  请求体，json格式
     * @param uri       集运接口地址
     * @param timestamp 时间戳
     * @param nonce     随机数
     */
    public JiyunAuthUtils(String secretId, String httpBody, String uri, long timestamp, String nonce) {
        Map<String, String[]> map = new HashMap<>();
        map.put(httpBody, new String[]{null});
        this.secretId = secretId;
        this.paramMap = map;
        this.uri = uriTransfer(uri);
        this.timestamp = timestamp;
        this.nonce = nonce;
    }

    /**
     * 计算签名，先使用hmac算法获取摘要，然后转化为16进制字符串
     *
     * @param secretKey 集运平台分配
     * @return 签名
     */
    public String getSignature(String secretKey) {
        return new String(Hex.encodeHex(hmacSha256(secretKey.getBytes(StandardCharsets.UTF_8), getValueToDigest().getBytes(StandardCharsets.UTF_8)), false));
    }

    private byte[] hmacSha256(byte[] key, byte[] valueToDigest) {
        Mac mac = getInitializedMac(key);

        return mac.doFinal(valueToDigest);
    }

    private Mac getInitializedMac(byte[] key) {
        if (key == null) {
            throw new IllegalArgumentException("Null key");
        } else {
            try {
                SecretKeySpec keySpec = new SecretKeySpec(key, "HmacSHA256");
                Mac mac = Mac.getInstance("HmacSHA256");
                mac.init(keySpec);
                return mac;
            } catch (NoSuchAlgorithmException | InvalidKeyException var4) {
                throw new IllegalArgumentException(var4);
            }
        }
    }

    /**
     * 获取签名计算的内容源
     *
     * @return 签名计算的内容源
     */
    public String getValueToDigest() {
        StringBuilder valueToDigest = new StringBuilder();

        valueToDigest.append(timestamp);
        valueToDigest.append('\n');
        valueToDigest.append(nonce);
        valueToDigest.append('\n');
        valueToDigest.append(uri);
        valueToDigest.append('\n');

        // 升序排序后,拼到一起
        if (this.getParamMap() != null && !this.getParamMap().keySet().isEmpty()) {
            Set<String> set = this.getParamMap().keySet();
            String[] keyArray = set.toArray(new String[0]);
            sort(keyArray);
            for (int i = 0; i < keyArray.length; i++) {
                String[] values = this.getParamMap().get(keyArray[i]);
                sort(values);
                for (String value : values) {
                    // 参数的key不能为null
                    if (StringUtils.isEmpty(keyArray[i])) {
                        log.error("1809112500 系统内部错误 param key can't be empty");
                    }

                    valueToDigest.append(keyArray[i]);
                    /**
                     * 当参数未传入时，拿到的为null http://example.com/context/servlet?x=foo
                     *
                     * string x = request.getParameter("x"); // "foo" string y = request.getParameter("y"); // null
                     *
                     * 当参数传入但是未传入值时，拿到的为空串 http://example.com/context/servlet?x=foo&y
                     *
                     * string x = request.getParameter("x"); // "foo" string y = request.getParameter("y"); // ""
                     *
                     * 为了避免签名不一致，忽略参数列表中的null和空串
                     */
                    if (StringUtils.isNotEmpty(value)) {
                        valueToDigest.append("=").append(value);
                    }

                    valueToDigest.append("&");
                }
            }

            if (keyArray.length > 0) {
                valueToDigest.deleteCharAt(valueToDigest.length() - 1);
            }
        }

        return valueToDigest.toString();
    }

    /**
     * Arrays.sort增强版，兼容null
     *
     * @param stringArray 待排序消息头参数
     */
    private void sort(String[] stringArray) {
        Arrays.sort(stringArray, new StringArrayComparator());
    }

    /**
     * it平台目前暴露给外部的url
     *
     * @param uri 集运接口地址
     * @return 集运接口地址
     */
    private String uriTransfer(String uri) {
        return uri;
    }

    /**
     * string数组比较器
     */
    private static class StringArrayComparator implements Comparator<String>, Serializable {
        private static final long serialVersionUID = 7940863860998110761L;

        @Override
        public int compare(String o1, String o2) {
            if (o1 == null && o2 == null) {
                return 0;
            }

            if (o1 == null) {
                return 1;
            }

            if (o2 == null) {
                return -1;
            }

            return o1.compareTo(o2);
        }
    }
}