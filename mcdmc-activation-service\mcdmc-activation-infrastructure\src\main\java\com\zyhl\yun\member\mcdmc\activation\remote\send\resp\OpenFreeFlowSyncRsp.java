package com.zyhl.yun.member.mcdmc.activation.remote.send.resp;

import lombok.Data;

import java.util.Objects;

/**
 * 免流/一级能开-业务办理同步接口响应体
 *
 * <AUTHOR>
 * @since 2024/06/19 16:37
 */
@Data
public class OpenFreeFlowSyncRsp {
    /**
     * 消息类型，IntergratedSalesOrderSyncResp
     */
    private String msgType;

    /**
     * 该接口消息的版本号，本次所有的接口消息的版本都为“1.0.0”
     */
    private String version;

    /**
     * 开放平台返回值，参考附录3.1定义
     * 0 成功
     * 1 其他错误,未知错误
     * 2 网络异常（调用一级能力开放平台失败）
     * 3 网络超时（一级能力开放平台应答超时）
     * 4 平台级错误，返回报文中只会返回respCode和respDesc这两个字段定义见 3.2平台级错误CODE
     * 101 请求参数错误
     */
    private Integer hRet;

    /**
     * 结果码，hRet返回0时有效。
     * 1-成功；2-订单已存在，无法重复订购；3-失败，商品不存在，无法订购；
     * 4-接入方未获取售卖该省此商品权限
     */
    private Integer bizCode;

    /**
     * 结果描述
     */
    private String bizDesc;

    public static boolean isSuccess(OpenFreeFlowSyncRsp rsp) {
        return rsp != null && Objects.equals(0, rsp.getHRet()) && Objects.equals(1, rsp.getBizCode());
    }
}
