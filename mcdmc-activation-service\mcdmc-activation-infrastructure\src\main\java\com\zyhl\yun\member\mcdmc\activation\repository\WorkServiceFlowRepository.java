package com.zyhl.yun.member.mcdmc.activation.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.yun.member.mcdmc.activation.mapper.WorkServiceFlowMapper;
import com.zyhl.yun.member.mcdmc.activation.po.WorkServiceFlowPo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/06/05 09:48
 */
@Repository
public class WorkServiceFlowRepository extends ServiceImpl<WorkServiceFlowMapper, WorkServiceFlowPo> {


    public List<WorkServiceFlowPo> qryByServiceId(String serviceId) {
        LambdaQueryWrapper<WorkServiceFlowPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkServiceFlowPo::getServiceCode, serviceId);
        return super.list(queryWrapper);
    }

    public List<WorkServiceFlowPo> qryAll() {
        return super.list();
    }
}
