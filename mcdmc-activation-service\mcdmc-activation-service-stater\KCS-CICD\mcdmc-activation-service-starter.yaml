---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名
    app.name: mcdmc-activation-service-starter-test
  # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名
  name: mcdmc-activation-service-starter-test
  # 联调（研发）环境对应develop，测试环境对应test
  namespace: test
spec:
  progressDeadlineSeconds: 600
  # 如没有特殊需求，实例副本数为1即可
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名
      app.name: mcdmc-activation-service-starter-test
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名
        app.name: mcdmc-activation-service-starter-test
    spec:
      containers:
        # 按需修改环境变量，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的BC列
        - env:
            - name: spring.profiles.active
              value: ydy-test
    - name: jasypt.encryptor.password
        value: Vip2024_zwDev
        # 修改镜像，仓库组固定使用***********/k8s/；后面的模块名对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名；镜像版本以流水线执行后的镜像版本为准，是代码分支名+时间戳
        image: >-
          ***********/k8s/mcdmc-activation-service-starter-test:1.0
        imagePullPolicy: Always
        # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名
        name: mcdmc-activation-service-starter-test
        resources:
          # 修改资源限制
          limits:
            cpu: '1'
            memory: 10Gi
          # 修改资源需求
          requests:
            cpu: '0.1'
            memory: 1Gi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
          - mountPath: /logs
            name: logs
          - mountPath: /etc/localtime
            name: time-localtime
            readOnly: true
          ########### 有共享存储需求的模块需修改，具体修改内容请沟通确认，如已知不需要可移除
          ########### mountPath为挂载到容器中的目录；name为存储卷名称；subPath为文件存储中的目录，如果是两个不同应用需共享相同存储卷，要求两个模块的subPath配置的值需要一样，如果没有此需求的可以移除此行
          # - mountPath: /data/xxx/yyy
          #   name: vol-pvc-example
          #   subPath: xxx/yyy
          ###########
          ###########
        dnsPolicy: ClusterFirst
        # 联调（研发）环境、测试环境的应用需要指定主机标签normal: "true"，AI的应用指定主机标签ai: "true"
        nodeSelector:
          normal: "true"
        imagePullSecrets:
          - name: paas-***********-harbor-secret
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        terminationGracePeriodSeconds: 60
        volumes:
          - hostPath:
              # 注意：联调（研发）环境的path值为/var/lib/docker/dev-logs，测试环境的path值为/var/lib/docker/logs，请注意修改
              path: /var/lib/docker/logs
              type: DirectoryOrCreate
            name: logs
          - hostPath:
              path: /etc/localtime
              type: ''
            name: time-localtime
          ########### 有共享存储需求的模块需修改，具体修改内容请沟通确认，如已知不需要可移除
          ########### name为存储卷名称；persistentVolumeClaim为pvc的名字，请沟通确认
          # - name: vol-pvc-example
          #   persistentVolumeClaim:
          #     claimName: pvc-nas-gz3-ypcg-p00X-XXX
          ###########
          ###########
        dnsConfig:
          options:
            - name: single-request-reopen
            - name: timeout
              value: '1'
            - name: attempts
              value: '3'
            - name: ndots
              value: '2'

# 以下为service配置，如应用需要配置公网入口则需要写。若应用不需要被其他模块访问，如MQ消费模块、定时任务模块，可以移除以下内容
---
apiVersion: v1
kind: Service
metadata:
  annotations: {}
  labels:
    # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名
    app.name: mcdmc-activation-service-starter-test
  # 修改为【应用名-svc】,若跟随节点部署应用名后要加上节点标识
  name: mcdmc-activation-service-starter-test-svc
  # 联调（研发）环境对应develop，测试环境对应test
  namespace: test
spec:
  ipFamilies:
    - IPv6
  ports:
    # 修改为应用名-port
    - name: mcdmc-activation-service-starter-test-port
      # 修改为应用端口，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的BB列
      port: 18084
      protocol: TCP
      # 修改为应用端口，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的BB列
      targetPort: 18084
  selector:
    # 修改应用名,若跟随节点部署要加上节点标识，对应https://docs.qq.com/sheet/DUkFpQ1FJUXBNSWF1的D列或H列，应用模块的英文名
    app.name: mcdmc-activation-service-starter-test
  sessionAffinity: None
  type: ClusterIP



