package com.zyhl.yun.member.mcdmc.activation;

import com.zyhl.yun.member.mcdmc.activation.callback.notify.RaiNormalSubCallback;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.ICallback;
import com.zyhl.yun.member.mcdmc.activation.callback.req.RaiComNotifyReq;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowLogDo;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.FlowLogStateEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/03/10 11:32
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ActivationApplication.class)
@AutoConfigureMockMvc
public class CallbackTest {

    @Resource
    private Map<String, ICallback> notifyMap;

    @Test
    public void testFlowCardOrder() {
        String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<OPFlowCardOrderFdbkReq xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\n" +
                "    <MsgType>OPFlowCardOrderFdbkReq</MsgType>\n" +
                "    <Version>1.0.0</Version>\n" +
                "    <orderId>mpclouds634861312413137027_rft</orderId>\n" +
                "    <subOrderId>cef24fb0cb374007ac33c278edac1863</subOrderId>\n" +
                "    <orderTime>20211202173105</orderTime>\n" +
                "    <status>RC</status>\n" +
                "    <statusDesc>退订成功</statusDesc>\n" +
                "    <shipmentNo></shipmentNo>\n" +
                "    <shipmentCompanyCode></shipmentCompanyCode>\n" +
                "    <shipmentCompany></shipmentCompany>\n" +
                "    <updateTime>20250310100356</updateTime>\n" +
                "    <returnId>634861312413137032_rft</returnId>\n" +
                "    <opContactName></opContactName>\n" +
                "    <opContactNo></opContactNo>\n" +
                "    <opContactPhone></opContactPhone>\n" +
                "    <opComments></opComments>\n" +
                "    <opContactOrganization></opContactOrganization>\n" +
                "    <isResourceBundle></isResourceBundle>\n" +
                "    <resourceCode></resourceCode>\n" +
                "    <receiveTime></receiveTime>\n" +
                "    <effTime></effTime>\n" +
                "    <expTime></expTime>\n" +
                "</OPFlowCardOrderFdbkReq>";
        String url = "/cloudSEE/openApi/flowCardOrderResult";
        ICallback iCallback = notifyMap.get("openReturnCallback");
        String rsp = iCallback.execCallback(url, Collections.emptyMap(), xml);
        Assert.assertNotNull(rsp);
    }

    @Resource
    private RaiNormalSubCallback raiNormalSubCallback;

    @Test
    public void testInitSendFlowLogWhileNotFound() {
        // 构造测试请求
        RaiComNotifyReq req = new RaiComNotifyReq();
        // userId_orderId格式
        req.setChannelNo("998592833742_bc69beef-29f4-4e97-8d1c-e0fa16b4e20f");

        // 执行测试方法
        WorkServiceFlowLogDo result = raiNormalSubCallback.initSendFlowLogWhileNotFound(req);

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getLogId());
        Assert.assertNotNull(result.getWorkId());
        Assert.assertEquals("998592833742", result.getUserId());
        Assert.assertEquals("998592833742_bc69beef-29f4-4e97-8d1c-e0fa16b4e20f_CloudSpaceContractSubscribeOrder", result.getOrderId());
        Assert.assertEquals(FlowLogStateEnum.SHIPMENT_SUCCESS, result.getState());
    }

    @Test
    public void testInitSendFlowLogWhileNotFoundWithInvalidChannelNo() {
        // 构造无效的请求
        RaiComNotifyReq req = new RaiComNotifyReq();
        req.setChannelNo("invalid");
        // 执行测试方法
        WorkServiceFlowLogDo result = raiNormalSubCallback.initSendFlowLogWhileNotFound(req);
        // 验证结果
        Assert.assertNull(result);
    }
}
