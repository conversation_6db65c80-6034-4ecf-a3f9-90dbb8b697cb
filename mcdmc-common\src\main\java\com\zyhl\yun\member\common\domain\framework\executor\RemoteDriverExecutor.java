package com.zyhl.yun.member.common.domain.framework.executor;

/**
 * <AUTHOR>
 * @date 2024/05/29 16:00
 */

import com.zyhl.yun.member.common.domain.framework.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

@Getter
@AllArgsConstructor
@Slf4j
public class RemoteDriverExecutor implements DriverExecutor {


    private static final long serialVersionUID = 9069927093442822832L;


    private DriverExecutor executor;


    @Override
    public Serializable exec(DomainEntityPersistenceWrapper wrapper) {
        try {
            IDriver driver = DriverManager.getDriver(wrapper.getDomainClassName());
            if (driver == null) {
                log.error("[FRAMEWORK] exec error. driver not found. domainName:{}", wrapper.getDomainName());
                throw new DomainException("driver not found");
            }

            Serializable result = ((BaseDriver) driver).exec(executor, wrapper);
            return new ResultWrapper(result);
        } catch (Exception e) {
            log.error("[FRAMEWORK] exec error. error msg:{}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[FRAMEWORK] exec error.", e);
            }
            return new ResultWrapper(e);
        }
    }
}
