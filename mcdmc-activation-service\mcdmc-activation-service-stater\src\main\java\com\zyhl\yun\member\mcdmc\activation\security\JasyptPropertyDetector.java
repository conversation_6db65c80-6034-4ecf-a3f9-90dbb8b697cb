package com.zyhl.yun.member.mcdmc.activation.security;

import com.ulisesbocchio.jasyptspringboot.EncryptablePropertyDetector;
import org.springframework.stereotype.Component;

/**
 * jasypt自定义加密
 *
 * <AUTHOR>
 * @since 2022/2/14 17:47
 */
@Component("myJasyptPropertyDetector")
public class JasyptPropertyDetector implements EncryptablePropertyDetector {

    private String prefix = "ENC(";
    private String suffix = ")";

    @Override
    public boolean isEncrypted(String property) {
        if (property == null) {
            return false;
        }
        final String trimmedValue = property.trim();
        return (trimmedValue.startsWith(prefix) &&
                trimmedValue.endsWith(suffix));
    }

    @Override
    public String unwrapEncryptedValue(String property) {
        return property.substring(
                prefix.length(),
                (property.length() - suffix.length()));
    }
}
