package com.zyhl.yun.member.application.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
// import cn.hutool.core.date.StopWatch;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.huawei.jaguar.order.api.domain.pay.PayChannel;
import com.zyhl.hcy.commons.exception.BusinessException;
import com.zyhl.yun.member.application.order.convertor.VsboOrderRespConvertor;
import com.zyhl.yun.member.application.order.dto.OrderReductionReq;
import com.zyhl.yun.member.application.order.enums.PayRCOpTypeEnum;
import com.zyhl.yun.member.application.order.enums.PayResultCodeEnum;
import com.zyhl.yun.member.application.order.service.OrderApplicationService;
import com.zyhl.yun.member.application.order.vo.OrderReductionResp;
import com.zyhl.yun.member.application.order.vo.QueryOrderResp;
import com.zyhl.yun.member.businessAccount.factory.DifferenceDigitalHandlerFactory;
import com.zyhl.yun.member.common.JsonUtil;
import com.zyhl.yun.member.common.NamedParameter;
import com.zyhl.yun.member.common.PerformOrderConverter;
import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.constants.ErrorCode;
import com.zyhl.yun.member.common.constants.NumberConstant;
import com.zyhl.yun.member.common.constants.OrderServiceTypeConstant;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.framework.PageCondition;
import com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceServiceId;
import com.zyhl.yun.member.common.domain.serviceid.OrderGroupBuyServiceId;
import com.zyhl.yun.member.common.domain.serviceid.OrderServiceId;
import com.zyhl.yun.member.common.domain.serviceid.UserServiceId;
import com.zyhl.yun.member.common.enums.ExpireEnum;
import com.zyhl.yun.member.common.enums.PriorSortEnum;
import com.zyhl.yun.member.common.enums.SceneTypeEnum;
import com.zyhl.yun.member.common.enums.SubscribeTypeEnum;
import com.zyhl.yun.member.common.util.*;
import com.zyhl.yun.member.config.MemberOrderProperty;
import com.zyhl.yun.member.config.RedisLockService;
import com.zyhl.yun.member.config.ServiceConfigProperty;
import com.zyhl.yun.member.context.MemberContextUtil;
import com.zyhl.yun.member.direct.manage.ManageOperatorManager;
import com.zyhl.yun.member.direct.manage.enums.EventTypeEnum;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.domain.invite.domain.InviteRelationDo;
import com.zyhl.yun.member.domain.invite.dto.QueryInviteCondition;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.receive.domain.dto.QueryGoodsInstanceExtendCondition;
import com.zyhl.yun.member.domain.user.domain.QueryUserCondition;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.dto.BindFamilyNumberReq;
import com.zyhl.yun.member.dto.MemberProduct;
import com.zyhl.yun.member.facade.GoodsInstanceServiceFacade;
import com.zyhl.yun.member.goodInstance.GoodsInstanceDomainService;
import com.zyhl.yun.member.goods.doamin.service.GoodsDomainService;
import com.zyhl.yun.member.goodsinstance.enums.GoodsInstanceStateEnum;
import com.zyhl.yun.member.invite.InviteRelationDomainService;
import com.zyhl.yun.member.notify.domain.PerformOrderDo;
import com.zyhl.yun.member.order.OrderItem;
import com.zyhl.yun.member.order.ProductOffering;
import com.zyhl.yun.member.order.check.SubscribeCheckValid;
import com.zyhl.yun.member.order.common.constants.ActivityConstant;
import com.zyhl.yun.member.order.common.constants.CommonConstant;
import com.zyhl.yun.member.order.common.constants.MarketPassConstant;
import com.zyhl.yun.member.order.common.dto.CreateOrderReq;
import com.zyhl.yun.member.order.common.dto.ManageSubscribeDto;
import com.zyhl.yun.member.order.common.dto.QueryOrderReq;
import com.zyhl.yun.member.order.common.util.PortalType;
import com.zyhl.yun.member.order.domain.OrderDo;
import com.zyhl.yun.member.order.domain.OrderDomainDo;
import com.zyhl.yun.member.order.domain.OrderGroupBuyDo;
import com.zyhl.yun.member.order.domain.dto.QueryOrderCondition;
import com.zyhl.yun.member.order.domain.dto.QueryOrderGroupBuyCondition;
import com.zyhl.yun.member.order.domainservice.OrderDomainService;
import com.zyhl.yun.member.order.dto.*;
import com.zyhl.yun.member.order.gateway.OrderGateway;
import com.zyhl.yun.member.order.service.LockPerformService;
import com.zyhl.yun.member.order.vo.*;
import com.zyhl.yun.member.payment.common.dto.NotifyOrderPayResultReq;
import com.zyhl.yun.member.payment.common.enums.WeiXinPayTypeEnum;
import com.zyhl.yun.member.product.common.constants.Constant;
import com.zyhl.yun.member.product.common.constants.Constant.AliPayWay;
import com.zyhl.yun.member.product.common.enums.*;
import com.zyhl.yun.member.product.common.util.ServiceUtil;
import com.zyhl.yun.member.product.domain.channel.facade.ChannelServiceFacade;
import com.zyhl.yun.member.product.domain.goods.BenefitGoodsDo;
import com.zyhl.yun.member.product.domain.goods.GoodsDo;
import com.zyhl.yun.member.product.domain.goods.dto.QueryGoodsCondition;
import com.zyhl.yun.member.product.domain.goods.facade.GoodsServiceFacade;
import com.zyhl.yun.member.product.domain.goods.pack.GoodsPackageDo;
import com.zyhl.yun.member.product.domain.goods.policy.timeplan.TimePlanCalculator;
import com.zyhl.yun.member.subscribe.enums.EventType;
import com.zyhl.yun.member.vip.service.UserDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zyhl.yun.member.common.ResultCodeEnum.*;
import static com.zyhl.yun.member.common.constants.ErrorCode.BINGING_RELATUINSHIP_ALREADY_EXISTS;
import static com.zyhl.yun.member.common.constants.ErrorCode.BINGING_RELATUINSHIP_ALREADY_EXISTS_INVITED;
import static com.zyhl.yun.member.common.constants.RedisConstant.CREATE_SUBSCRIBE_REDIS_KEY_PREFIX;
import static com.zyhl.yun.member.common.constants.RedisKeyConstant.*;
import static com.zyhl.yun.member.common.domain.serviceid.GoodsServiceId.QUERY_GOODS_INFO;
import static com.zyhl.yun.member.common.domain.serviceid.InviteRelationServiceId.INSERT_FAMILY_NUMBER_INFO;
import static com.zyhl.yun.member.common.domain.serviceid.InviteRelationServiceId.QUERY_FAMILY_NUMBER_INFO;
import static com.zyhl.yun.member.common.domain.serviceid.PerformOrderServiceId.PERFORM_ORDER;
import static com.zyhl.yun.member.common.domain.serviceid.PerformOrderServiceId.PERFORM_ORDER_CUSTOM_TIME;
import static com.zyhl.yun.member.common.domain.serviceid.UserServiceId.*;
import static com.zyhl.yun.member.common.enums.InviteRelationStatusEnum.*;
import static com.zyhl.yun.member.domain.invite.domain.InviteRelationDo.isExistInviteRelation;
import static com.zyhl.yun.member.domain.invite.domain.InviteRelationDo.isExistMonthInviteRelation;
import static com.zyhl.yun.member.order.domain.OrderDo.DEFAULT_CURRENCY;
import static com.zyhl.yun.member.order.domain.OrderDo.ExtInfoKeys.SUB_CHANNEL_ID;
import static com.zyhl.yun.member.order.vo.OrderVo.IsFreeFlowValue.IS_FREE_FLOW;
import static com.zyhl.yun.member.order.vo.OrderVo.IsFreeFlowValue.IS_NOT_FREE_FLOW;
import static com.zyhl.yun.member.order.vo.OrderVo.NamedParameterKeys.SUBSCRIPTION_ID;

/**
 * <AUTHOR>
 * @description 针对表【ord_t_contract_subscribe_order(订单表)】的数据库操作Service实现
 * @createDate 2023-07-27 17:47:06
 */
@Service
@Slf4j
public class OrderApplicationServiceImpl implements OrderApplicationService {

    @Resource
    private UserDomainService userDomainService;

    @Resource
    private GoodsInstanceDomainService goodsInstanceDomainService;

    @Resource
    private OrderDomainService orderDomainService;

    @Resource
    private MemberOrderProperty orderProperty;

    @Resource
    private ServiceConfigProperty serviceConfigProperty;

    @Resource
    private RedisLockService redisLockService;

    @Resource
    private GoodsDomainService goodsDomainService;

    @Resource
    private InviteRelationDomainService inviteRelationService;

    @Resource
    private PerformOrderConverter performOrderDoConverter;

    @Resource
    private DifferenceDigitalHandlerFactory differenceDigitalHandlerFactory;

    @Resource
    private OrderGateway orderGateway;

    @Resource
    private MemberOrderProperty memberOrderProperty;

    @Resource
    private VsboOrderRespConvertor vsboOrderRespConvertor;

    @Resource
    private LockPerformService lockPerformService;

    /**
     * PC扫码支付缓存key
     */
    private static final String PC_SCAN_CACHE_KEY = "vsbo.payorder:pc:%s";

    /**
     * 查询用户订单列表
     *
     * @param queryOrderReq：查询订单列表查询体
     * @return Mono<QueryOrderResp>
     */
    @Override
    public Mono<QueryOrderResp> queryOrders(QueryOrderReq queryOrderReq) {
        if (log.isDebugEnabled()) {
            log.info(String.format("[app] the queryOrders method of the product is beginning,the req is %s",
                    JsonUtil.toJson(queryOrderReq)));
        }
        // StopWatch stopWatch = new StopWatch();
        // stopWatch.start("queryUser");
        // 查询用户信息，若不存在，报错
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setAccount(queryOrderReq.getRawAccount());
        queryUserCondition.setUserDomainId(queryOrderReq.getOrderQueryCond().getUserDomainId());
        UserDo userDo = userDomainService.getUserDoByMsisdn(queryUserCondition, QUERY_USER_INFO);
        log.info("query order ,user detail msg is {}", userDo);
        // stopWatch.stop();
        // stopWatch.start("queryOrder");
        // 查询订单信息，设置参数，然后本地查询
        DomainServiceContext orderContext = new DomainServiceContext("queryOrderServiceIdByPage");
        QueryOrderCondition queryOrderCondition = buildQueryOrderCondition(queryOrderReq, userDo);
        List<OrderDo> orderDos = orderContext.read(queryOrderCondition, OrderDo.class);
        log.info("query order ,order size is{}", orderDos.size());
        // stopWatch.stop();
        // stopWatch.start("queryOrderCount");
        Long readCount = null;
        if (!orderDos.isEmpty()) {
            // 获取订单总数
            DomainServiceContext orderContextCount = new DomainServiceContext(OrderDomainDo.class,
                    "queryOrderServiceIdByCount");
            readCount = orderContextCount.getCount(queryOrderCondition, OrderDo.class);
            if (null == readCount) {
                throw new ServiceException("query order is null");
            }
        }
        // stopWatch.stop();
        // stopWatch.start("buildQueryOrderResp");
        QueryOrderResp queryOrderResp = new QueryOrderResp(buildQueryOrderResp(orderDos,
                memberOrderProperty.getReturnUrl(), memberOrderProperty.getCancelUrl(), userDo), readCount);
        // stopWatch.stop();
        // log.info("query order cost time is {}ms,detail is {}", stopWatch.getTotalTimeMillis(),
        //         stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        return Mono.just(queryOrderResp);
    }

    /**
     * 构建查询订单返回值
     *
     * @param orderDos 订单列表
     * @param userDo
     * @return 订单列表
     */
    private List<QueryOrderVo> buildQueryOrderResp(List<OrderDo> orderDos, String returnUrl, String cancelUrl,
                                                   UserDo userDo) {
        if (CollectionUtils.isEmpty(orderDos)) {
            return Collections.emptyList();
        }
        // StopWatch stopWatch = new StopWatch();
        // stopWatch.start("queryGoods");
        List<String> goodsIdList = orderDos.stream().map(OrderDo::getGoodsId).distinct().collect(Collectors.toList());
        Map<String, GoodsDo> goodsDoMap = GoodsServiceFacade.buildGoodsCacheMap(goodsIdList, false);
        // stopWatch.stop();
        // stopWatch.start("getOrderId2IndependentGoodsInstanceMap");
        // 查询独立空间的相关商品实例
        Map<String, GoodsInstanceDo> orderId2IndependentGoodsInstanceDoMap = this
                .getOrderId2IndependentGoodsInstanceMap(userDo.getUserId(), orderDos, goodsDoMap);
        // stopWatch.stop();
        // stopWatch.start("getMarketPassOrderGroupBuyDoMap");
        // 查询营销活动平台相关的订单列表
        Map<String, OrderGroupBuyDo> orderId2MarketPassOrderGroupBuyDoMap = this
                .getMarketPassOrderGroupBuyDoMap(userDo.getUserId(), orderDos);
        // stopWatch.stop();
        // stopWatch.start("buildQueryOrderVo");
        List<QueryOrderVo> orderVos = new ArrayList<>();
        for (OrderDo orderDo : orderDos) {
            QueryOrderVo orderVo = new QueryOrderVo();
            ProductOffering productOffering = new ProductOffering();
            productOffering.setProductOfferingID(orderDo.getGoodsId());
            // 获取商品
            GoodsDo goodsDo = goodsDoMap.get(orderDo.getGoodsId());
            if (goodsDo == null) {
                log.info("[APP] goodsDo not found, goodsId: {}", orderDo.getGoodsId());
            } else {
                productOffering.setProductName(goodsDo.getGoodsName());
            }
            productOffering.setProductOfferingID(orderDo.getGoodsId());
            productOffering.setMerchantAccount(userDo.getMsisdn());
            productOffering.setProductType(orderDo.getSaleType());
            productOffering.setOrderItemID(CollUtil.isNotEmpty(orderDo.getOrderDetailDos()) ? orderDo.getOrderDetailDos().get(0).getOrderDetailId() : null);
            productOffering.setStartTime(DateUtil.format(orderDo.getCreateTime(), DatePattern.NORM_DATETIME_MS_PATTERN));
            productOffering.setEndTime(DateUtil.format(orderDo.getExpireTime(), DatePattern.NORM_DATETIME_MS_PATTERN));
            OrderItem orderItem = new OrderItem();
            orderItem.setProductOffering(productOffering);
            orderItem.setUnitPrice(orderDo.getTotalAmount() != null ? orderDo.getTotalAmount() : 0);
            orderItem.setQuantity(orderDo.getChargeType());
            orderItem.setAmount(orderDo.getTotalAmount() != null ? orderDo.getTotalAmount() : 0);
            orderItem.setCurrency(100);
            orderItem.setCurrencyType(orderDo.getSaleType());
            orderItem.setReserve2(String.valueOf(orderDo.getChargeType()));
            orderItem.setReserve4(orderDo.getThirdPlatformTradId());
            orderItem.setReserve5(orderDo.getDisplayToUser());
            orderVo.setOrderIs((Collections.singletonList(orderItem)));
            orderVo.setOrderID(orderDo.getOrderNo());
            // orderVo.setObjectID(goodsDo.getTimePlanGoodsGroupId());
            orderVo.setStatus(String.valueOf(orderDo.getStatus()));
            orderVo.setOrderUserID(orderDo.getOrderUserId());
            orderVo.setPayUserID(orderDo.getPayUserId());
            orderVo.setTotalAmount(String.valueOf(orderDo.getTotalAmount() != null ? orderDo.getTotalAmount() : 0));
            orderVo.setCurrency("100");
            orderVo.setChannelID(orderDo.getChannelId());
            orderVo.setCreateTime(orderDo.getCreateTime());
            orderVo.setNamedParameters(new HashMap<>());
            List<PayChannel> payChannels = new ArrayList<>();
            PayTypeVo payType = new PayTypeVo();
            PayChannel payChannel = new PayChannel();
            payChannel.setChannelType(orderDo.getPayWayEnum() == null ? null : orderDo.getPayWayEnum().getCode());
            payChannel.setPayAmount(Long.valueOf(orderVo.getTotalAmount()));
            payChannel.setCurrency(100);
            payChannels.add(payChannel);
            payType.setPayChannels(payChannels);
            payType.setReturnURL(returnUrl);
            payType.setCancelURL(cancelUrl);
            payType.setOrderID(orderDo.getOrderNo());
            orderVo.setPayType(payType);
            // 针对独立空间的订单（ownerId不为空）进行改造：
            // 1、queyOrders响应参数，扩展字段增加订购关系id
            // 2、queyOrders响应参数，startTime需要赋值订购关系中的生效时间
            GoodsInstanceDo independenceGoodsInstance = orderId2IndependentGoodsInstanceDoMap.get(orderDo.getOrderNo());
            if (Objects.nonNull(independenceGoodsInstance)) {

                NamedParameter namedParameter = new NamedParameter();
                namedParameter.setKey("subscriptionID");
                namedParameter.setValue(independenceGoodsInstance.getGoodsInstanceId());
                orderVo.getNamedParameters().add(namedParameter);

                productOffering.setStartTime(DateUtil.format(independenceGoodsInstance.getEffectiveStartTime(),
                        DatePattern.NORM_DATETIME_MS_PATTERN));
                productOffering.setEndTime(DateUtil.format(independenceGoodsInstance.getEffectiveEndTime(),
                        DatePattern.NORM_DATETIME_MS_PATTERN));
            }
            // 通过order.getExtInfo()中获取活动id和
            if (orderId2MarketPassOrderGroupBuyDoMap.containsKey(orderDo.getOrderNo())) {
                OrderGroupBuyDo orderGroupBuyDo = orderId2MarketPassOrderGroupBuyDoMap.get(orderDo.getOrderNo());
                orderVo.getNamedParameters()
                        .add(new NamedParameter(ActivityConstant.GROUP_ID, orderGroupBuyDo.getGroupId()));
                orderVo.getNamedParameters().add(
                        new NamedParameter(ActivityConstant.GROUP_STATUS, String.valueOf(orderGroupBuyDo.getStatus())));
                orderVo.getNamedParameters().add(new NamedParameter(ActivityConstant.GROUP_TYPE,
                        String.valueOf(orderGroupBuyDo.getGroupType())));
            }
            orderVos.add(orderVo);
        }
        // stopWatch.stop();
        // log.info("queryOrders cost:{},detail is {}", stopWatch.getTotalTimeMillis(),
        //         stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        return orderVos;
    }

    /**
     * 获取订单id和独立空间的商品实例
     *
     * @param userId      用户id
     * @param orderDoList 订单列表
     * @param goodsDoMap  商品实例映射
     */
    private Map<String, GoodsInstanceDo> getOrderId2IndependentGoodsInstanceMap(String userId,
                                                                                List<OrderDo> orderDoList, Map<String, GoodsDo> goodsDoMap) {
        if (CollectionUtils.isEmpty(orderDoList)) {
            return Collections.emptyMap();
        }
        List<String> orderIdList = new ArrayList<>(orderDoList.size());
        for (OrderDo orderDo : orderDoList) {
            GoodsDo goodsDo = goodsDoMap.get(orderDo.getGoodsId());
            if (goodsDo != null && goodsDo.getGoodsExt() != null
                    && CharSequenceUtil.isNotBlank(goodsDo.getGoodsExt().getOwnerId())) {
                orderIdList.add(orderDo.getOrderNo());
            }
        }
        if (CollectionUtils.isEmpty(orderIdList)) {
            return Collections.emptyMap();
        }
        List<GoodsInstanceDo> goodsInstanceDoList = MemberContextUtil.getGoodsInstanceDoList(userId, orderIdList);
        return goodsInstanceDoList.stream()
                .collect(Collectors.toMap(GoodsInstanceDo::getOrderId, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 获取营销平台订单id和对应团购订单的映射关系
     *
     * @param userId      用户id
     * @param orderDoList 订单列表
     */
    private Map<String, OrderGroupBuyDo> getMarketPassOrderGroupBuyDoMap(String userId, List<OrderDo> orderDoList) {
        if (CollectionUtils.isEmpty(orderDoList)) {
            return Collections.emptyMap();
        }
        // 营销活动相关订单id列表
        List<String> marketpassOrderIdList = orderDoList.stream()
                .filter(order -> {
                    if (CollectionUtils.isEmpty(order.getExtInfo()) ||
                            Objects.isNull(order.getExtInfo().get(ActivityConstant.ACTIVITY_SOURCE))) {
                        return false;
                    }
                    return MarketPassConstant.MARKETPASS_FLAG
                            .equals(order.getExtInfo().get(ActivityConstant.ACTIVITY_SOURCE));
                }).map(OrderDo::getOrderNo)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(marketpassOrderIdList)) {
            return Collections.emptyMap();
        }
        DomainServiceContext orderGroupBuyContext = new DomainServiceContext(
                OrderGroupBuyServiceId.QUERY_ORDER_GROUP_BUY_INFO);
        QueryOrderGroupBuyCondition queryOrderGroupBuyCondition = new QueryOrderGroupBuyCondition();
        queryOrderGroupBuyCondition.setOrderIdList(marketpassOrderIdList);
        queryOrderGroupBuyCondition.setUserId(userId);
        List<OrderGroupBuyDo> orderGroupBuyDos = orderGroupBuyContext.read(queryOrderGroupBuyCondition,
                OrderGroupBuyDo.class);
        return CollUtil.emptyIfNull(orderGroupBuyDos).stream()
                .collect(Collectors.toMap(OrderGroupBuyDo::getOrderId, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 查询用户订单列表(大音平台)
     *
     * @param queryOrderReq
     * @return
     */
    @Override
    public QueryOrderResp queryOrdersForDY(QueryOrderReq queryOrderReq) {

        // 查询用户信息，若不存在，报错
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setAccount(queryOrderReq.getRawAccount());
        queryUserCondition.setUserDomainId(queryOrderReq.getOrderQueryCond().getUserDomainId());
        UserDo userDo = userDomainService.getUserDoByMsisdn(queryUserCondition,
                UserServiceId.QUERY_USER_INFO);

        if (null == userDo) {
            throw new ServiceException(ErrorCode.USER_DOMAIN_ID_NOT_EXISTS_CODE,
                    ErrorCode.USER_DOMAIN_ID_NOT_EXISTS_MESSAGE);
        }
        // 查询订单信息
        DomainServiceContext orderContext = new DomainServiceContext(OrderDomainDo.class, OrderServiceId.QUERY_ORDER);
        QueryOrderCondition queryOrderCondition = buildQueryOrderCondition(queryOrderReq, userDo);
        List<OrderDo> orderDoList = orderContext.read(queryOrderCondition, OrderDo.class);

        DomainServiceContext orderContextCount = new DomainServiceContext(OrderDomainDo.class,
                OrderServiceId.QUERY_ORDER);
        Long readCount = orderContextCount.getCount(queryOrderCondition, OrderDo.class);

        return new QueryOrderResp(convertOrderDoListToOrderVoList(orderDoList, userDo), readCount);
    }

    /**
     * 将订单信息转换为订单视图
     *
     * @param orderDoList
     * @return
     */
    private List<QueryOrderVo> convertOrderDoListToOrderVoList(List<OrderDo> orderDoList, UserDo userDo) {
        if (CollectionUtils.isEmpty(orderDoList)) {
            return Collections.emptyList();
        }

        List<QueryOrderVo> orderVos = new ArrayList<>();

        Map<String, GoodsDo> goodsCache = GoodsServiceFacade.buildGoodsCacheMap(orderDoList.stream()
                .map(order -> order.getGoodsId()).collect(Collectors.toList()));

        Map<String, GoodsInstanceDo> goodsInstanceCache = GoodsInstanceServiceFacade
                .buildGoodsInstanceByOrderIdCacheMap(orderDoList.stream()
                                .map(order -> order.getOrderNo()).collect(Collectors.toList()),
                        Arrays.asList(userDo.getUserId()));

        for (OrderDo orderDo : orderDoList) {

            QueryOrderVo orderVo = new QueryOrderVo();

            OrderItem orderItem = new OrderItem();
            orderItem.setUnitPrice(orderDo.getTotalAmount() != null ? orderDo.getTotalAmount() : 0);
            orderItem.setQuantity(orderDo.getChargeType());
            orderItem.setAmount(orderDo.getTotalAmount() != null ? orderDo.getTotalAmount() : 0);
            orderItem.setCurrency(DEFAULT_CURRENCY);
            orderItem.setCurrencyType(orderDo.getSaleType());
            orderItem.setReserve2(String.valueOf(orderDo.getChargeType()));
            if (Constant.CONTRACT_MONTHLY_SUBSCRIBE.equals(String.valueOf(orderDo.getChargeType()))) {
                orderItem.setChargeType("包月订购");
            } else {
                orderItem.setChargeType("按次订购");
            }
            orderItem.setReserve5(orderDo.getDisplayToUser());

            ProductOffering productOffering = new ProductOffering();
            orderItem.setProductOffering(productOffering);
            productOffering.setMerchantAccount(userDo.getMsisdn());
            productOffering.setProductOfferingID(orderDo.getGoodsId());
            productOffering.setProductType(orderDo.getSaleType());
            productOffering.setStartTime(Optional.ofNullable(orderDo.getCreateTime())
                    .map(createTime -> DateUtil.format(createTime, DatePattern.NORM_DATETIME_MS_PATTERN)).orElse(null));

            GoodsDo goodsDo = goodsCache.get(orderDo.getGoodsId());
            if (goodsDo == null) {
                log.info("[APP] goodsDo not found, goodsId: {}", orderDo.getGoodsId());
            } else {
                productOffering.setProductName(goodsDo.getGoodsName());
                productOffering.setProductDesc(goodsDo.getGoodsDesc());
                orderVo.setObjectID(goodsDo.getTimePlanGoodsGroupId());

                orderVo.setFreeTraffic(isFreeFlow(goodsDo) ? IS_FREE_FLOW : IS_NOT_FREE_FLOW);
                orderItem.setReserve3(goodsDo.getGoodsSalesType() != null ? String.valueOf(goodsDo.getGoodsSalesType().getType()) : null);
            }
            if (!CollectionUtils.isEmpty(orderDo.getOrderDetailDos())) {
                productOffering.setOrderItemID(orderDo.getOrderDetailDos().get(0).getOrderDetailId());
            }
            GoodsInstanceDo goodsInstanceDo = goodsInstanceCache.get(orderDo.getOrderNo());
            if (goodsInstanceDo == null) {
                log.info("[APP] goodsInstanceDo not found, orderNo: {}", orderDo.getOrderNo());
            } else {
                productOffering.setStartTime(Optional.ofNullable(goodsInstanceDo.getEffectiveStartTime())
                        .map(createTime -> DateUtil.format(createTime, DatePattern.NORM_DATETIME_MS_PATTERN))
                        .orElse(null));
                productOffering.setEndTime(Optional.ofNullable(goodsInstanceDo.getEffectiveEndTime())
                        .map(expireTime -> DateUtil.format(expireTime, DatePattern.NORM_DATETIME_MS_PATTERN))
                        .orElse(null));
                orderVo.setExpireTime(Optional.ofNullable(goodsInstanceDo.getEffectiveEndTime()).orElse(null));
                NamedParameter namedParameter = new NamedParameter();
                namedParameter.setKey(SUBSCRIPTION_ID);
                namedParameter.setValue(goodsInstanceDo.getGoodsInstanceId());
                if (CollUtil.isEmpty(orderVo.getNamedParameters())) {
                    orderVo.setNamedParameters(new HashMap<>());
                }
                orderVo.getNamedParameters().add(namedParameter);
                orderVo.setUnSubTime(goodsInstanceDo.getUnsubTime());
            }

            Map<String, Object> orderExtInfo = orderDo.getExtInfo();
            if (!CollectionUtils.isEmpty(orderExtInfo)) {
                if (orderExtInfo.get(OrderDo.ExtInfoKeys.CAPACITY) != null) {
                    productOffering
                            .setCapacity(Integer.parseInt((String) orderExtInfo.get(OrderDo.ExtInfoKeys.CAPACITY)));
                }
                if (orderExtInfo.get(OrderDo.ExtInfoKeys.PRODUCT_TYPE) != null) {
                    productOffering.setProductType(
                            Integer.parseInt((String) orderExtInfo.get(OrderDo.ExtInfoKeys.PRODUCT_TYPE)));
                }
                if (orderExtInfo.get(OrderDo.ExtInfoKeys.DISCOUNT) != null) {
                    productOffering
                            .setDiscount(Integer.parseInt((String) orderExtInfo.get(OrderDo.ExtInfoKeys.DISCOUNT)));
                }
                if (orderExtInfo.get(OrderDo.ExtInfoKeys.PRODUCT_ORIGIN_PRICE) != null) {
                    productOffering.setFee(
                            Integer.parseInt((String) orderExtInfo.get(OrderDo.ExtInfoKeys.PRODUCT_ORIGIN_PRICE)));
                }
            }
            List<PayChannel> payChannels = new ArrayList<>();
            PayTypeVo payType = new PayTypeVo();
            PayChannel payChannel = new PayChannel();
            payChannel.setChannelType(orderDo.getPayWayEnum() == null ? null : orderDo.getPayWayEnum().getCode());
            payChannel.setPayAmount(orderDo.getTotalAmount() != null ? Long.valueOf(orderDo.getTotalAmount()) : null);
            payChannel.setCurrency(100);
            payChannels.add(payChannel);
            payType.setPayChannels(payChannels);
            payType.setOrderID(orderDo.getOrderNo());
            orderVo.setPayType(payType);
            orderVo.setOrderIs((Collections.singletonList(orderItem)));
            orderVo.setOrderID(orderDo.getOrderNo());
            orderVo.setChannelID(orderDo.getChannelId());
            orderVo.setChannelType(String.valueOf(orderDo.getChargeType()));
            orderVo.setStatus(String.valueOf(orderDo.getStatus()));
            orderVo.setOrderUserID(orderDo.getOrderUserId());
            orderVo.setTotalAmount(String.valueOf(orderDo.getTotalAmount() != null ? orderDo.getTotalAmount() : 0));
            orderVo.setCurrency(String.valueOf(DEFAULT_CURRENCY));
            orderVo.setCreateTime(orderDo.getCreateTime());
            orderVo.setSubChannel(orderDo.getSubChannelId());
            orderVo.setLastUpdateTime(orderDo.getLastUpdateTime());
            orderVos.add(orderVo);
        }

        return orderVos;
    }

    /**
     * 是否是免流
     *
     * @param goodsDo
     * @return
     */
    private boolean isFreeFlow(GoodsDo goodsDo) {
        // 商品是否存在免流产品
        if (goodsDo instanceof GoodsPackageDo) {
            return ((GoodsPackageDo) goodsDo)
                    .getChildGoodsByProductId(BenefitGoodsDo.BenefitNo.BENEFIT_NO_FREE_FLOW_PKG) != null;
        }
        return false;
    }

    /**
     * 构建查询条件
     *
     * @param queryOrderReq
     * @param userDo
     * @return
     */
    private QueryOrderCondition buildQueryOrderCondition(QueryOrderReq queryOrderReq, UserDo userDo) {
        QueryOrderCondition queryOrderCondition = new QueryOrderCondition();
        queryOrderCondition.setGoodsSalesTypeList(GoodsSalesTypeEnum.GOODS_STALES_TYPE_DESPLAY_TO_USER_LIST);
        queryOrderCondition.setOrderNo(queryOrderReq.getOrderQueryCond().getOrderID());
        queryOrderCondition.setUserId(userDo.getUserId());
        queryOrderCondition.setThirdPlatformTradId(queryOrderReq.getOrderQueryCond().getThirdPlatformId());
        if (StringUtil.isEmpty(queryOrderReq.getIsDisplayToUser())) {
            queryOrderCondition.setIsDisplayToUser(QueryOrderReq.UN_DISPLAY);
        } else if (!QueryOrderReq.ALL_DISPLAY.equals(queryOrderReq.getIsDisplayToUser())) {
            queryOrderCondition.setIsDisplayToUser(queryOrderReq.getIsDisplayToUser());
        }
        queryOrderCondition
                .setOrderType(queryOrderReq.getOrderType() == null ? "" : queryOrderReq.getOrderType().toString());
        queryOrderCondition.setOrderNo(queryOrderReq.getOrderQueryCond().getOrderID());
        queryOrderCondition.setWho(queryOrderReq.getWho() == null ? "" : queryOrderReq.getWho().toString());
        queryOrderCondition
                .setObjectType(queryOrderReq.getObjectType() == null ? "" : queryOrderReq.getObjectType().toString());
        queryOrderCondition.setStatusList(queryOrderReq.getOrderQueryCond().getStatus() == null ? null
                : Arrays.asList(queryOrderReq.getOrderQueryCond().getStatus().split("\\|")));
        PageCondition pageCondition = new PageCondition();
        if (queryOrderReq.getPageNum() == null) {
            pageCondition.setPageNo(1);
        } else {
            pageCondition.setPageNo(queryOrderReq.getPageNum());
        }
        if (queryOrderReq.getPageSize() == null) {
            pageCondition.setPageSize(10);
        } else {
            pageCondition.setPageSize(queryOrderReq.getPageSize());
        }
        queryOrderCondition.setPageCondition(pageCondition);
        return queryOrderCondition;
    }

    /**
     * 订单核减
     *
     * @param orderReductionReq 请求体
     * @return 响应体
     */
    @Override
    public Mono<OrderReductionResp> orderReduction(OrderReductionReq orderReductionReq) {
        return Mono.defer(() -> Mono.just(orderReductionReq))
                .flatMap(req -> {
                    // 查询订单记录
                    DomainServiceContext orderContext = new DomainServiceContext(OrderDomainDo.class,
                            "queryOrderServiceIdByPage");
                    QueryOrderCondition queryOrderCondition = new QueryOrderCondition();
                    String[] split = orderReductionReq.getOrderId().split("_");
                    if (split.length >= 2) {
                        queryOrderCondition.setUserId(split[0]);
                    }
                    queryOrderCondition.setOrderNo(orderReductionReq.getOrderId());
                    OrderDo orderDo = orderContext.readFirst(queryOrderCondition, OrderDo.class);
                    if (null == orderDo) {
                        return Mono.error(new ServiceException("query order is null"));
                    }
                    QueryGoodsInstanceExtendCondition queryGoodsInstanceExtendConditionChild = new QueryGoodsInstanceExtendCondition();
                    queryGoodsInstanceExtendConditionChild.setOrderId(orderReductionReq.getOrderId());
                    queryGoodsInstanceExtendConditionChild.setIsExpired(ExpireEnum.ALL.getState());
                    GoodsInstanceDo goodsInstanceExtendDoChild = goodsInstanceDomainService
                            .queryGoodsInstance(queryGoodsInstanceExtendConditionChild);
                    if (goodsInstanceExtendDoChild == null) {
                        return Mono.error(new ServiceException("sub is not exist"));
                    }
                    Integer payway;
                    if (goodsInstanceExtendDoChild.getSaleType().equals(GoodsSalesTypeEnum.RIGHTS.getType())
                            && goodsInstanceExtendDoChild.getGoodsPackageInstanceId() == null) {
                        payway = goodsInstanceExtendDoChild.getPayWay();
                    } else if (goodsInstanceExtendDoChild.getSaleType().equals(GoodsSalesTypeEnum.RIGHTS.getType())
                            && goodsInstanceExtendDoChild.getGoodsPackageInstanceId() != null) {
                        QueryGoodsInstanceExtendCondition queryGoodsInstanceExtendConditionParent = new QueryGoodsInstanceExtendCondition();
                        queryGoodsInstanceExtendConditionParent
                                .setGoodsInstanceId(goodsInstanceExtendDoChild.getGoodsPackageInstanceId());
                        queryGoodsInstanceExtendConditionParent.setIsExpired(ExpireEnum.ALL.getState());
                        GoodsInstanceDo goodsInstanceExtendDoParent = goodsInstanceDomainService
                                .queryGoodsInstance(queryGoodsInstanceExtendConditionParent);
                        payway = goodsInstanceExtendDoParent.getPayWay();
                    } else {
                        log.error("the data is exception");
                        return Mono.error(new ServiceException("the data is exception"));
                    }
                    // 查询商品实例记录
                    PerformOrderDo performOrderDo = performOrderDoConverter
                            .goodsInstanceDo2PerformOrderDo(goodsInstanceExtendDoChild);
                    // BeanUtils.copyProperties(goodsInstanceExtendDoChild, performOrderDo);
                    performOrderDo.setOpType(SubscribeTypeEnum.REDUCE);
                    DomainServiceContext context = new DomainServiceContext(PerformOrderDo.class,
                            PERFORM_ORDER_CUSTOM_TIME);
                    performOrderDo.setPayWay(payway);
                    context.putInstance(performOrderDo);
                    context.writeAndFlush();
                    return Mono.just(new OrderReductionResp(orderDo.getOrderNo(), "success"));
                });
    }

    /**
     * 创建订单接口
     *
     * @param createOrderReq 请求体
     * @return 响应
     */
    @Override
    public Mono<CreateOrderResp> createOrder(CreateOrderReq createOrderReq) {
        return Mono.defer(() -> Mono.just(createOrderReq))
                .flatMap(req -> {
                    CreateOrderDto order = orderDomainService.createOrder(createOrderReq);
                    if (order == null) {
                        return Mono.error(new ServiceException(""));
                    }
                    CreateOrderResp data = new CreateOrderResp(order, createOrderReq);
                    // log.info(String.format("create order success, order is %s", order));
                    return Mono.just(data);
                });
    }

    /**
     * 支付订单
     *
     * @param payOrderReq 请求体
     * @return Mono<PayOrderResp>
     */
    @Override
    public Mono<PayOrderResp> payOrder(PayOrderAppReq payOrderReq) {
        return Mono.defer(() -> Mono.just(payOrderReq))
                .flatMap(req -> {

                    // 如果支付方式为微信扫码支付或支付宝扫码支付，则从缓存中获取支付结果
                    if (WeiXinPayTypeEnum.QRCODE.getCode().equals(payOrderReq.getWeixinPayType()) ||
                            AliPayWay.ALI_SCAN
                                    .equals(ServiceUtil.getParameter(payOrderReq.getNamedParameters(), AliPayWay.ALI_PAY_TYPE))) {
                        String cacheKey = String.format(PC_SCAN_CACHE_KEY, payOrderReq.getOrderID());
                        String cacheValue = redisLockService.get(cacheKey);
                        if (StringUtils.isNotEmpty(cacheValue)) {
                            PayOrderResp payOrderResp = JsonUtil.fromJson(cacheValue, PayOrderResp.class);
                            if (Objects.nonNull(payOrderResp)) {
                                return Mono.just(payOrderResp);
                            }
                            log.warn("pay order result is null, cache key is {}", cacheKey);
                        }
                    }

                    // 根据支付方式获取对应的url
                    String cancelUrl;
                    String returnUrl;
                    returnUrl = ServiceUtil.getParameter(payOrderReq.getNamedParameters(), "returnURL");
                    cancelUrl = ServiceUtil.getParameter(req.getNamedParameters(), "cancelURL");
                    PayWayEnum payWayEnum = PayWayEnum.fromCode(payOrderReq.getPayChannelID());
                    if (PayWayEnum.APPLE_PAY_V2.equals(payWayEnum) || PayWayEnum.HUAWEI_PAY.equals(payWayEnum)) {
                        if (StringUtils.isEmpty(cancelUrl)) {
                            cancelUrl = orderProperty.getCancelAppleUrl();
                        }
                        if (StringUtils.isEmpty(returnUrl)) {
                            returnUrl = orderProperty.getReturnAppleUrl();
                        }
                    } else {
                        if (StringUtils.isEmpty(cancelUrl)) {
                            cancelUrl = orderProperty.getCancelUrl();
                        }
                        if (StringUtils.isEmpty(returnUrl)) {
                            returnUrl = orderProperty.getReturnUrl();
                        }
                    }

                    PayOrderResult payOrderResult = orderDomainService.payOrder(payOrderReq, cancelUrl, returnUrl);
                    PayOrderResp payOrderResp = new PayOrderResp(payOrderResult, cancelUrl, returnUrl);

                    // 如果支付方式为微信扫码支付或支付宝扫码支付，则将支付结果缓存到redis中
                    if (WeiXinPayTypeEnum.QRCODE.getCode().equals(payOrderReq.getWeixinPayType()) ||
                            AliPayWay.ALI_SCAN
                                    .equals(ServiceUtil.getParameter(payOrderReq.getNamedParameters(), AliPayWay.ALI_PAY_TYPE))) {
                        String cacheKey = String.format(PC_SCAN_CACHE_KEY, payOrderReq.getOrderID());
                        redisLockService.set(cacheKey, JsonUtil.toJson(payOrderResp), 6899, TimeUnit.SECONDS);
                    }
                    return Mono.just(payOrderResp);
                });
    }

    /**
     * 第三方支付结果通知
     *
     * @param payResult
     * @return Mono<NotifyOrderResultAppResp>
     */
    @Override
    public Mono<NotifyOrderResultResp> notifyOrderPayResult(NotifyOrderPayResultAppReq payResult) {
        return Mono.defer(() -> Mono.just(payResult))
                .flatMap(req -> {
                    // 查询用户信息
                    UserDo userDo = userDomainService.getUserDoByUserId(QUERY_AND_CREATE_USER_INFO,
                            req.getOrderID().split("\\_")[0]);
                    OrderDo orderDo = orderDomainService.getOrderDo(req.getOrderID(), userDo.getUserId(),
                            OrderServiceTypeConstant.ORDER_SERVICE_TYPE_GET_ORDER);
                    orderDo.setNotifyOrderPayResultReq(XmlUtil.toXml(req));

                    // 根据产品id查询产品详情
                    // 查询商品详情信息
                    DomainServiceContext domainServiceContext = new DomainServiceContext(GoodsDo.class,
                            QUERY_GOODS_INFO);
                    GoodsDo goodsDo = domainServiceContext.read(orderDo.getGoodsId(), GoodsDo.class);
                    if (OrderStatusEnum.PAYING.getStatus().equals(orderDo.getStatus())
                            && !PayResultCodeEnum.PAY_FAIL.getPayResultCode().equals(req.getPayResultCode())) {
                        log.error(
                                ErrorCode.PERFORM_CONTRACT_FAILED + " : " + ErrorCode.PERFORM_CONTRACT_FAILED_MESSAGE);
                        return Mono.error(new ServiceException(ErrorCode.PERFORM_CONTRACT_FAILED,
                                ErrorCode.PERFORM_CONTRACT_FAILED_MESSAGE));
                    }

                    PayResultCodeEnum payResultCodeEnum = PayResultCodeEnum.fromCode(req.getPayResultCode());
                    if (payResultCodeEnum == null || payResultCodeEnum.equals(PayResultCodeEnum.PAY_RENEW_FAIL)) {
                        log.error(ErrorCode.PARAMETER_CHECK_ERROR, Constant.MandatoryFieldError + "payResultCode!");
                        return Mono.error(new ServiceException(ErrorCode.PARAMETER_CHECK_ERROR,
                                Constant.MandatoryFieldError + "payResultCode!"));
                    }
                    // 续订标识，true为续订，false表示其它
                    boolean reNewFlag = false;
                    if (Integer.valueOf(ThidPayEnum.RENEW.getPayActionId()).equals(req.getPayResultCode())) {
                        reNewFlag = true;
                    }

                    // 指纹数据校验
                    checkOrderFingerPrint(req);

                    // 重复订购直接返回数据库的订单详情,如果是续订不走下面的逻辑
                    if (!reNewFlag && (judgeOrderStatusOrPayResultCode(orderDo, payResultCodeEnum))) {
                        orderDo.setOrderUserId(userDo.getMsisdn());
                        orderDo.setPayUserId(userDo.getMsisdn());
                        orderDo.setCurrency(Long.parseLong(String.valueOf(Constant.CURRENCY)));
                        return Mono.just(
                                new NotifyOrderResultResp(goodsDo, orderDo, userDo.getUserId(), userDo.getMsisdn()));
                    }

                    // 调用营销平台拼团购需要判断是否拼团完成发货，接受到计费过来支付请求要拦截发货，接受到自身的支付请求则正常发货请求
                    String activitySource = (String) orderDo.getExtInfo()
                            .get(CommonConstant.OrderExtParams.ACTIVITY_SOURCE);
                    if (StringUtils.isNotEmpty(activitySource)
                            && MarketPassConstant.MARKETPASS_FLAG
                            .equals(orderDo.getExtInfo().get(Constant.ACTIVITY_SOURCE))) {
                        // 支付成功后，判断是否需要进行库存扣减，并且不是vsbo内部发送过来的支付通知，更新团购支付成功相关状态
                        if (Constant.notifyOrderPayResultSupportPayResultCode.PAY_SUCCESS
                                .equals(String.valueOf(req.getPayResultCode()))
                                && !NotifyOrderPayResultReq.VSBO_INNER_REQ.equals(req.getReqSource())) {
                            req.setPayResultCode(ThidPayEnum.MARKET_PASS_FIRST_SUB.getActionId());
                        }
                    }

                    // ===========并发校验锁================
                    if (Constant.notifyOrderPayResultSupportPayResultCode.PAY_SUCCESS
                            .equals(String.valueOf(req.getPayResultCode()))
                            && lockPerformService.lockAndCheckOrder(req, goodsDo, userDo)) {
                        return Mono.error(new ServiceException(ErrorCode.CONTRACT_EXISTED,
                                Constant.MULTI_PAYMENT_PROMOTIONS_FAILED_MESSAGE));
                    }

                    PayRCOpTypeEnum payRCOpTypeEnum = PayRCOpTypeEnum.fromCode(req.getPayResultCode());
                    DomainServiceContext context = new DomainServiceContext(PERFORM_ORDER);
                    PerformOrderDo performOrderDo = context.newInstance(PerformOrderDo.class);
                    Assert.notNull(payRCOpTypeEnum, "payRCOpTypeEnum is null");
                    performOrderDo.setOpType(payRCOpTypeEnum.getSubscribeTypeEnum());
                    // 用户触发行为，直接插入任务表
                    performOrderDo.setPriorSort(PriorSortEnum.IMMEDIATE);
                    performOrderDo.setOrderDo(orderDo);
                    performOrderDo.setUserDo(userDo);
                    performOrderDo.setUserId(userDo.getUserId());
                    performOrderDo.setGoodsId(goodsDo.getGoodsId());
                    performOrderDo.setOrderId(orderDo.getOrderNo());
                    performOrderDo.setTimePlanGoodsGroupId(goodsDo.getTimePlanGoodsGroupId());
                    // 获取计费的订单号
                    performOrderDo.setPaymentOrderId(StringUtil.isNotEmpty(payResult.getPayID()) ? payResult.getPayID() : null);
                    performOrderDo.setIsUserManualUnsubscribe(true);
                    context.putInstance(performOrderDo);
                    context.writeAndFlush();
                    return Mono
                            .just(new NotifyOrderResultResp(goodsDo, orderDo, userDo.getUserId(), userDo.getMsisdn()));
                });
    }

    private static boolean judgeOrderStatusOrPayResultCode(OrderDo orderDo, PayResultCodeEnum payResultCodeEnum) {
        return (OrderStatusEnum.SUCCESS.getStatus().equals(orderDo.getStatus())
                && PayResultCodeEnum.PAY_SUCCESS.equals(payResultCodeEnum))
                || (OrderStatusEnum.UNSUBSCRIBE.getStatus().equals(orderDo.getStatus())
                && PayResultCodeEnum.PAY_UNSUB_SUCCESS.equals(payResultCodeEnum))
                || (OrderStatusEnum.SUSPEND.getStatus().equals(orderDo.getStatus())
                && PayResultCodeEnum.PAY_PAUSE.equals(payResultCodeEnum))
                || (OrderStatusEnum.SUCCESS.getStatus().equals(orderDo.getStatus())
                && PayResultCodeEnum.PAY_ACTIVE.equals(payResultCodeEnum))
                || PayResultCodeEnum.PAY_FAIL.equals(payResultCodeEnum)
                || PayResultCodeEnum.PAY_CANCEL.equals(payResultCodeEnum);
    }

    /**
     * 支付请求体
     *
     * @param payResult 支付结果
     */
    private void checkOrderFingerPrint(NotifyOrderPayResultAppReq payResult) {
        try {
            String fingerPrint = redisLockService.get(OrderRedisBuildUtil.buildFpLimitKey(payResult.getOrderID()));
            if (StringUtils.isNotEmpty(fingerPrint)
                    && Integer.valueOf(Constant.notifyOrderPayResultSupportPayResultCode.PAY_SUCCESS)
                    .equals(payResult.getPayResultCode())) {
                fingerPrint = fingerPrint.replace("\"", "");
                String value = redisLockService.get(fingerPrint);
                if (ObjectUtil.isNotEmpty(value)) {
                    // 次数加一
                    int fpLimitValue = Integer.parseInt(value);
                    fpLimitValue++;
                    redisLockService.set(fingerPrint, String.valueOf(fpLimitValue),
                            TimeUtil.getDayEndSecond(), TimeUnit.SECONDS);
                } else {
                    redisLockService.set(fingerPrint, NumberConstant.ONE_STR,
                            TimeUtil.getDayEndSecond(), TimeUnit.SECONDS);
                }
            }
        } catch (Exception e) {
            log.warn("checkOrderFingerPrint set redis value error", e);
        }
    }

    /**
     * 直接订购或退订接口
     *
     * @param manageSubscribeRelationReq 请求体
     * @return Mono<ManageSubscribeRelationResp>
     */
    @Override
    public Mono<ManageSubscribeRelationResp> manageSubscribeRelation(
            ManageSubscribeRelationReq manageSubscribeRelationReq) {

        QueryUserCondition queryUserCondition = new QueryUserCondition();
        if (manageSubscribeRelationReq.getAccount() != null) {
            queryUserCondition.setAccount(manageSubscribeRelationReq.getAccount());
        } else if (manageSubscribeRelationReq.getUserDomainId() != null) {
            queryUserCondition.setUserDomainId(manageSubscribeRelationReq.getUserDomainId());
        }
        UserDo userDo = userDomainService.getUserDoByMsisdn(queryUserCondition, QUERY_AND_CREATE_USER_INFO);

        // 如果是非订购类型
        if (!SubscribeTypeEnum.SUBSCRIBE_TYPE_ENUM.getCode().equals(manageSubscribeRelationReq.getEventType())) {
            // 查询当前数据是否在重构数据库
            DomainServiceContext domainServiceContext = new DomainServiceContext(
                    GoodsInstanceServiceId.QUERY_GOODS_INSTANCE);
            QueryGoodsInstanceExtendCondition queryGoodsInstanceExtendCondition = new QueryGoodsInstanceExtendCondition();
            queryGoodsInstanceExtendCondition.setGoodsInstanceId(manageSubscribeRelationReq.getSubscriptionID());
            queryGoodsInstanceExtendCondition.setUserId(userDo.getUserId());
            queryGoodsInstanceExtendCondition.setIsExpired(ExpireEnum.ALL.getState());
            GoodsInstanceDo goodsInstanceExtendDo = domainServiceContext.readFirst(queryGoodsInstanceExtendCondition,
                    GoodsInstanceDo.class);
            if (null == goodsInstanceExtendDo) {
                log.error("user = {} not found goodsInstance record,goodsInstanceId is {},so call manageSubscribeRelation of oldVsbo",
                        userDo.getUserId(), manageSubscribeRelationReq.getSubscriptionID());
                // 转发至旧的
                ManageSubscribeRelationResp manageSubscribeRelationResp = orderGateway
                        .manageSubscribeRelation(manageSubscribeRelationReq);
                return Mono.just(manageSubscribeRelationResp);
            }
        }
        GoodsDo goodsDo = GoodsServiceFacade.getGoods(manageSubscribeRelationReq.getProductID());

        //******** 增加商品状态验证
        if(ObjectUtil.isNotNull(goodsDo)
                && SubscribeTypeEnum.SUBSCRIBE_TYPE_ENUM.getCode() == manageSubscribeRelationReq.getEventType()
                && StateEnum.INVALID.equals(goodsDo.getState())){
            return Mono.error(new ServiceException(GOODS_STATE_CHECK_FAILED));
        }

        final String[] goodsInstanceId = {null};
        return Mono.defer(() -> Mono.just(manageSubscribeRelationReq))
                .flatMap(req -> Mono.just(SubscribeCheckValid.check(manageSubscribeRelationReq,
                                serviceConfigProperty.getChannelStrongCheckList(),
                                serviceConfigProperty.getAccessSourceEnum(), serviceConfigProperty.getIntegralRangeValue()))
                        .flatMap(flag -> {
                            if (Boolean.TRUE.equals(flag)) {
                                Boolean locked;
                                String uuid = UUID.randomUUID().toString();
                                try {
                                    if (StringUtils.isNotEmpty(manageSubscribeRelationReq.getAccount())) {
                                        locked = redisLockService
                                                .acquireLock(String.format(CREATE_SUBSCRIBE_REDIS_KEY_PREFIX,
                                                        manageSubscribeRelationReq.getProductID(),
                                                        manageSubscribeRelationReq.getAccount()), uuid, 20);
                                    } else {
                                        locked = redisLockService.lock(String.format(CREATE_SUBSCRIBE_REDIS_KEY_PREFIX,
                                                manageSubscribeRelationReq.getProductID(),
                                                manageSubscribeRelationReq.getUserDomainId()), 20);
                                    }
                                    if (Boolean.TRUE.equals(locked)) {
                                        try {
                                            ManageSubscribeDto serializable = vsboOrderRespConvertor
                                                    .toManageSubscribeDto(manageSubscribeRelationReq);
                                            serializable.setUserDo(userDo);
                                            serializable.setGoodsDo(goodsDo);
                                            goodsInstanceId[0] = Objects.requireNonNull(ManageOperatorManager.getDeliveryService(
                                                            String.valueOf(manageSubscribeRelationReq.getEventType())))
                                                    .process(serializable);
                                            log.info("[manageSubscribeRelation] Generated goodsInstanceId: {}", goodsInstanceId[0]);
                                        } catch (Exception ex) {
                                            return Mono.error(ex);
                                        }
                                        try {
                                            return Mono.just(buildManageSubscribeRelationResp(
                                                    manageSubscribeRelationReq, userDo, goodsDo, goodsInstanceId[0]));
                                        } catch (Exception e) {
                                            log.error("manageSubscribeRelation error", e);
                                            return Mono.error(new ServiceException(INTERNAL_SERVER_ERROR));
                                        }
                                    }
                                } catch (Exception e) {
                                    log.error("manageSubscribeRelation error", e);
                                    return Mono.error(new ServiceException(INTERNAL_SERVER_ERROR));
                                } finally {
                                    if (StringUtils.isNotEmpty(manageSubscribeRelationReq.getAccount())) {
                                        redisLockService.releaseLock(String.format(CREATE_SUBSCRIBE_REDIS_KEY_PREFIX,
                                                manageSubscribeRelationReq.getProductID(),
                                                manageSubscribeRelationReq.getAccount()), uuid);
                                    } else {
                                        redisLockService.releaseLock(String.format(CREATE_SUBSCRIBE_REDIS_KEY_PREFIX,
                                                manageSubscribeRelationReq.getProductID(),
                                                manageSubscribeRelationReq.getUserDomainId()), uuid);
                                    }
                                }
                            }
                            return Mono.error(
                                    new ServiceException(ErrorCode.MUTUALLY_LIMIT_CODE, ErrorCode.MUTUALLY_LIMIT_DESC));
                        }));

    }

    /**
     * 直接订购或退订接口
     *
     * @param manageSubscribeRelationReq 请求体
     * @return Mono<ManageSubscribeRelationResp>
     */
    @Override
    public Mono<ManageSubscribeRelationResp> manageSubscribeRelationV2(
            ManageSubscribeRelationReq manageSubscribeRelationReq) {

        QueryUserCondition queryUserCondition = new QueryUserCondition();
        if (manageSubscribeRelationReq.getAccount() != null) {
            queryUserCondition.setAccount(manageSubscribeRelationReq.getAccount());
        } else if (manageSubscribeRelationReq.getUserDomainId() != null) {
            queryUserCondition.setUserDomainId(manageSubscribeRelationReq.getUserDomainId());
        }
        UserDo userDo = userDomainService.getUserDoByMsisdn(queryUserCondition, QUERY_AND_CREATE_USER_INFO);

        // 如果是非订购类型
        if (!SubscribeTypeEnum.SUBSCRIBE_TYPE_ENUM.getCode().equals(manageSubscribeRelationReq.getEventType())) {
            // 查询当前数据是否在重构数据库
            DomainServiceContext domainServiceContext = new DomainServiceContext(
                    GoodsInstanceServiceId.QUERY_GOODS_INSTANCE);
            QueryGoodsInstanceExtendCondition queryGoodsInstanceExtendCondition = new QueryGoodsInstanceExtendCondition();
            queryGoodsInstanceExtendCondition.setGoodsInstanceId(manageSubscribeRelationReq.getSubscriptionID());
            queryGoodsInstanceExtendCondition.setUserId(userDo.getUserId());
            queryGoodsInstanceExtendCondition.setIsExpired(ExpireEnum.ALL.getState());
            GoodsInstanceDo goodsInstanceExtendDo = domainServiceContext.readFirst(queryGoodsInstanceExtendCondition,
                    GoodsInstanceDo.class);
            if (null == goodsInstanceExtendDo) {
                log.error("user = {} not found goodsInstance record,goodsInstanceId is {},so call manageSubscribeRelationV2 of oldVsbo",
                        userDo.getUserId(), manageSubscribeRelationReq.getSubscriptionID());
                // 转发至旧的
                ManageSubscribeRelationResp manageSubscribeRelationResp = orderGateway
                        .manageSubscribeRelation(manageSubscribeRelationReq);
                return Mono.just(manageSubscribeRelationResp);
            }
        }
        GoodsDo goodsDo = GoodsServiceFacade.getGoods(manageSubscribeRelationReq.getProductID());
        final String[] goodsInstanceId = new String[1];
        return Mono.defer(() -> Mono.just(manageSubscribeRelationReq))
                .flatMap(req -> Mono.just(SubscribeCheckValid.check(manageSubscribeRelationReq,
                                serviceConfigProperty.getChannelStrongCheckList(),
                                serviceConfigProperty.getAccessSourceEnum(), serviceConfigProperty.getIntegralRangeValue()))
                        .flatMap(flag -> {
                            if (Boolean.TRUE.equals(flag)) {
                                Boolean locked;
                                String uuid = UUID.randomUUID().toString();
                                try {
                                    if (StringUtils.isNotEmpty(manageSubscribeRelationReq.getAccount())) {
                                        locked = redisLockService
                                                .acquireLock(String.format(CREATE_SUBSCRIBE_REDIS_KEY_PREFIX,
                                                        manageSubscribeRelationReq.getProductID(),
                                                        manageSubscribeRelationReq.getAccount()), uuid, 20);
                                    } else {
                                        locked = redisLockService.lock(String.format(CREATE_SUBSCRIBE_REDIS_KEY_PREFIX,
                                                manageSubscribeRelationReq.getProductID(),
                                                manageSubscribeRelationReq.getUserDomainId()), 20);
                                    }
                                    if (Boolean.TRUE.equals(locked)) {
                                        try {
                                            ManageSubscribeDto serializable = vsboOrderRespConvertor
                                                    .toManageSubscribeDto(manageSubscribeRelationReq);
                                            serializable.setUserDo(userDo);
                                            serializable.setGoodsDo(goodsDo);
                                            serializable.setVersion(
                                                    com.zyhl.yun.member.common.Constant.MANAGE_SUBSCRIBE_RELATION_V2);
                                            goodsInstanceId[0] = Objects.requireNonNull(ManageOperatorManager.getDeliveryService(
                                                            String.valueOf(manageSubscribeRelationReq.getEventType())))
                                                    .process(serializable);
                                            log.info("[manageSubscribeRelationV2] Generated goodsInstanceId: {}", goodsInstanceId[0]);
                                        } catch (Exception ex) {
                                            return Mono.error(ex);
                                        }
                                        try {
                                            return Mono.just(buildManageSubscribeRelationResp(
                                                    manageSubscribeRelationReq, userDo, goodsDo, goodsInstanceId[0]));
                                        } catch (Exception e) {
                                            log.error("manageSubscribeRelation error", e);
                                            return Mono.error(new ServiceException(INTERNAL_SERVER_ERROR));
                                        }
                                    }
                                } catch (Exception e) {
                                    log.error("manageSubscribeRelation error", e);
                                    return Mono.error(new ServiceException(INTERNAL_SERVER_ERROR));
                                } finally {
                                    if (StringUtils.isNotEmpty(manageSubscribeRelationReq.getAccount())) {
                                        redisLockService.releaseLock(String.format(CREATE_SUBSCRIBE_REDIS_KEY_PREFIX,
                                                manageSubscribeRelationReq.getProductID(),
                                                manageSubscribeRelationReq.getAccount()), uuid);
                                    } else {
                                        redisLockService.releaseLock(String.format(CREATE_SUBSCRIBE_REDIS_KEY_PREFIX,
                                                manageSubscribeRelationReq.getProductID(),
                                                manageSubscribeRelationReq.getUserDomainId()), uuid);
                                    }
                                }
                            }
                            return Mono.error(
                                    new ServiceException(ErrorCode.MUTUALLY_LIMIT_CODE, ErrorCode.MUTUALLY_LIMIT_DESC));
                        }));

    }

    /**
     * 直接订购或退订接口
     *
     * @param createSubscribeRelationReq 请求体
     * @return Mono<ManageSubscribeRelationResp>
     */
    @Override
    public Mono<CreateSubscribeRelationResp> createSubscribeRelation(
            CreateSubscribeRelationReq createSubscribeRelationReq) {
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        if (createSubscribeRelationReq.getUserID() != null) {
            queryUserCondition.setAccount(createSubscribeRelationReq.getUserID());
        } else if (createSubscribeRelationReq.getUserDomainId() != null) {
            queryUserCondition.setUserDomainId(createSubscribeRelationReq.getUserDomainId());
        }
        UserDo userDo = userDomainService.getUserDoByMsisdn(queryUserCondition, QUERY_AND_CREATE_USER_INFO);
        return Mono.defer(() -> Mono.just(createSubscribeRelationReq))
                .flatMap(req -> {
                    doPreCreateSubscribeRelationCheck(req);
                    GoodsDo goodsDo = GoodsServiceFacade.getGoods(createSubscribeRelationReq.getProductOfferingID());
                    if (Objects.isNull(goodsDo)) {
                        return Mono.error(new ServiceException(GOODS_NOT_FOUND));
                    }
                    //******** 订购增加商品状态验证
                    if(EventTypeEnum.ESTABLISH.getCodeInt() == createSubscribeRelationReq.getEventType()
                            && StateEnum.INVALID.equals(goodsDo.getState())){
                        return Mono.error(new ServiceException(GOODS_STATE_CHECK_FAILED));
                    }

                    if (StringUtils.isNotEmpty(goodsDo.getGotoneGrade())
                            && Arrays.asList(
                                    com.zyhl.yun.member.common.Constant.ProductInfoCheck.ALL_GOTONE_PRODUCT_LEVEL
                                            .split("\\,"))
                            .contains(goodsDo.getGotoneGrade())
                            && ChargeTypeEnum.MONTHLY.equals(goodsDo.getChargeType())
                            && !Arrays.asList(serviceConfigProperty.getGotonePresentProduct().split("\\|"))
                            .contains(createSubscribeRelationReq.getProductOfferingID())) {
                        return Mono.just(new CreateSubscribeRelationResp());
                    }
                    // 校验开始时间和结束时间
                    if ((createSubscribeRelationReq.getEndTime() != null
                            && !DateUtil.parseDate(createSubscribeRelationReq.getEndTime()).after(new Date()))
                            || DateUtil.parseDate(createSubscribeRelationReq.getStartTime())
                            .after(DateUtil.parseDate(createSubscribeRelationReq.getEndTime()))) {
                        log.error("startTime or endTime is illegal.");
                        return Mono.error(new ServiceException(ErrorCode.ILLEGAL_PARAMETER,
                                "startTime or endTime is illegal."));
                    }

                    ManageSubscribeRelationReq serializable = buildManageReq(createSubscribeRelationReq);
                    // 如果是包月，赋值2099年12月31日
                    if (goodsDo.getChargeType().equals(ChargeTypeEnum.MONTHLY)) {
                        serializable.setEndTime(
                                DateUtil.format(TimePlanCalculator.getMaxEndTime(), DatePattern.NORM_DATETIME_PATTERN));
                        List<NamedParameter> namedParameters = new ArrayList<>();
                        namedParameters.add(new NamedParameter("serviceId", PERFORM_ORDER_CUSTOM_TIME));
                        namedParameters.add(new NamedParameter("createSubscribe", "1"));
                        serializable.setNamedParameters(namedParameters);
                    }

                    ManageSubscribeDto manageSubscribeDto = vsboOrderRespConvertor.toManageSubscribeDto(serializable);
                    manageSubscribeDto.setUserDo(userDo);
                    manageSubscribeDto.setGoodsDo(goodsDo);
                    String goodsInstanceId = Objects.requireNonNull(ManageOperatorManager
                                    .getDeliveryService(String.valueOf(createSubscribeRelationReq.getEventType())))
                            .process(manageSubscribeDto);
                    log.info("[createSubscribeRelation] Generated goodsInstanceId: {}", goodsInstanceId);
                    try {
                        return Mono.just(buildCreateSubscribeRelationResp(serializable, userDo, goodsDo, goodsInstanceId));
                    } catch (Exception e) {
                        log.error("createSubscribeRelation error", e);
                        return Mono.error(new ServiceException(INTERNAL_SERVER_ERROR));
                    }
                });

    }

    /**
     * createSubscribeRelationReq 前置校验
     *
     * @param createSubscribeRelationReq createSubscribeRelationReq
     * @return Mono<CreateSubscribeRelationResp>
     */
    private Mono<Void> doPreCreateSubscribeRelationCheck(CreateSubscribeRelationReq createSubscribeRelationReq) {
        // 接口加锁限制
        Boolean locked = redisLockService.lock(String.format(CREATE_SUBSCRIBE_REDIS_KEY_PREFIX,
                createSubscribeRelationReq.getProductOfferingID(), createSubscribeRelationReq.getUserID()), 5);
        if (Boolean.FALSE.equals(locked)) {
            log.error("method subscribeServiceImpl.createSubscribeRelation fail,locked is false!");
            throw new ServiceException(ErrorCode.MUTUALLY_LIMIT_CODE, ErrorCode.MUTUALLY_LIMIT_DESC);
        }
        // 渠道强检验
        // 如果是购买的全球通包月产品，则直接返回成功
        // 特殊赠送全球通包月和尊享会员除外
        List<String> channelList = Arrays.asList(serviceConfigProperty.getChannelStrongCheckList().split("\\|"));
        if (Constant.ALL_CHANNELS == createSubscribeRelationReq.getWho()
                && channelList.contains(createSubscribeRelationReq.getProductOfferingID())) {
            log.error(ErrorCode.CHANNEL_ID_IS_NULL + ":" + createSubscribeRelationReq.getProductOfferingID());
            throw new ServiceException(ErrorCode.CHANNEL_ID_IS_NULL, ErrorCode.CHANNEL_ID_IS_NULL_DESC);
        }
        if ((createSubscribeRelationReq.getEndTime() != null
                && !DateUtil.parseDate(createSubscribeRelationReq.getEndTime()).after(new Date())) ||
                DateUtil.parseDate(createSubscribeRelationReq.getStartTime())
                        .after(DateUtil.parseDate(createSubscribeRelationReq.getEndTime()))) {
            log.error("startTime or endTime is illegal.");
            throw (new ServiceException(ErrorCode.ILLEGAL_PARAMETER, "startTime or endTime is illegal."));
        }
        return Mono.empty();
    }

    /**
     * 激活接口
     *
     * @param activeGotoneRightsReq 请求体
     * @return Mono<ActiveGotoneRightsRes>
     */
    @Override
    public Mono<ActiveGotoneRightsRes> activeGotoneRightsRelation(ActiveGotoneRightsReq activeGotoneRightsReq) {

        return Mono.defer(() -> Mono.just(activeGotoneRightsReq))
                .flatMap(req -> {
                    if (StringUtils.isNotEmpty(req.getAccount())) {
                        QueryUserCondition queryUserCondition = new QueryUserCondition();
                        queryUserCondition.setAccount(req.getAccount());
                        queryUserCondition.setUserDomainId(req.getUserDomainId());
                        UserDo userDo = userDomainService.getUserDoByMsisdn(queryUserCondition,
                                QUERY_AND_CREATE_USER_INFO);
                        if (userDo == null) {
                            log.error("activeGotoneRightsRelation error: user not exist");
                            return Mono.error(new ServiceException(USER_NOT_FOUND));
                        }
                        QueryGoodsCondition queryGoodsCondition = new QueryGoodsCondition();
                        queryGoodsCondition.setGoodsIdList(Collections.singletonList(req.getProductID()));
                        GoodsDo goodsDo = goodsDomainService.getGoodsByCondition(queryGoodsCondition).get(0);
                        if (!GoodsSalesTypeEnum.MEMBER.equals(goodsDo.getGoodsSalesType())) {
                            log.error(ErrorCode.SERVICE_INTERNAL_ERROR.concat(ErrorCode.SUB_PRODUCT_INVALID));
                            return Mono.error(new ServiceException(
                                    ErrorCode.SERVICE_INTERNAL_ERROR, ErrorCode.SUB_PRODUCT_INVALID));
                        }
                        // verifyGotoneProductAndUserLevel(goodsDo, userDo,
                        // com.zyhl.yun.member.common.Constant.EquityPlatformConstant.CHECKLOWUSERLEVEL);
                        // 订购
                        ManageSubscribeRelationReq serializable = new ManageSubscribeRelationReq();
                        serializable.setAccount(req.getAccount());
                        serializable.setEventType(EventType.ESTABLISH.getType());
                        serializable.setProductID(req.getProductID());
                        serializable.setWho(req.getWho());
                        serializable.setStartTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
                        serializable.setEndTime(
                                DateUtil.format(TimePlanCalculator.getMaxEndTime(), DatePattern.NORM_DATETIME_PATTERN));
                        ManageSubscribeDto manageSubscribeDto = vsboOrderRespConvertor
                                .toManageSubscribeDto(serializable);
                        manageSubscribeDto.setGotoneLevelCheckOption(GotoneLevelCheckOptionEnum.CHECK_HIGH_USER_LEVEL);
                        manageSubscribeDto.setUserDo(userDo);
                        manageSubscribeDto.setGoodsDo(goodsDo);
                        String goodsInstanceId = Objects.requireNonNull(
                                        ManageOperatorManager.getDeliveryService(String.valueOf(serializable.getEventType())))
                                .process(manageSubscribeDto);
                        log.info("[activeGotoneRightsRelation] Generated goodsInstanceId: {}", goodsInstanceId);
                        try {
                            return Mono.just(buildActiveGotoneRightsRes(activeGotoneRightsReq, userDo, goodsDo));
                        } catch (Exception e) {
                            log.error("activeGotoneRightsRelation error", e);
                            return Mono.error(new ServiceException(INTERNAL_SERVER_ERROR));
                        }
                    }
                    return Mono.error(new ServiceException(INTERNAL_SERVER_ERROR));
                });
    }

    /**
     * 构造响应体
     *
     * @param activeGotoneRightsReq 请求体
     * @param userDo
     * @param goodsDo
     * @return ActiveGotoneRightsRes
     * @throws Exception
     */
    private ActiveGotoneRightsRes buildActiveGotoneRightsRes(ActiveGotoneRightsReq activeGotoneRightsReq, UserDo userDo,
                                                             GoodsDo goodsDo) throws Exception {
        ActiveGotoneRightsRes activeGotoneRightsRes = new ActiveGotoneRightsRes();
        ActiveGotoneRights activeGotoneRights = new ActiveGotoneRights();
        QueryGoodsInstanceCondition queryGoodsInstanceExtendCondition = new QueryGoodsInstanceCondition();
        queryGoodsInstanceExtendCondition
                .setGoodsIdList(Collections.singletonList(activeGotoneRightsReq.getProductID()));
        queryGoodsInstanceExtendCondition.setUserId(userDo.getUserId());
        queryGoodsInstanceExtendCondition.setEffectiveEndTimeStart(new Date());
        GoodsInstanceDo goodsInsExtDos = goodsInstanceDomainService
                .queryGoodsInstance(queryGoodsInstanceExtendCondition);
        if (ObjectUtils.isNotEmpty(goodsInsExtDos)) {
            activeGotoneRights.setSubscriptionID(goodsInsExtDos.getGoodsInstanceId());
            activeGotoneRights.setAccount(userDo.getMsisdn());
            activeGotoneRights.setStartTime(
                    DateUtil.format(goodsInsExtDos.getEffectiveStartTime(), DatePattern.NORM_DATETIME_PATTERN));
            activeGotoneRights.setEndTime(
                    DateUtil.format(goodsInsExtDos.getEffectiveEndTime(), DatePattern.NORM_DATETIME_PATTERN));
            activeGotoneRights.setProductID(goodsInsExtDos.getGoodsId());
            activeGotoneRights.setProductName(goodsDo.getGoodsName());
            activeGotoneRights.setOrderID(goodsInsExtDos.getOrderId());
            activeGotoneRights.setStatus(goodsInsExtDos.getStateEnum().getState());
            activeGotoneRights.setSubscriptionID(goodsInsExtDos.getGoodsInstanceId());
            activeGotoneRights.setOrderID(goodsInsExtDos.getOrderId());
            activeGotoneRightsRes.setBizCode(Constant.EquityPlatformConstant.ALL_SUCCESS);
            activeGotoneRightsRes.setActiveGotoneRights(activeGotoneRights);
        }
        return activeGotoneRightsRes;
    }

    /**
     * 激活接口
     *
     * @param hySubscribeReq 请求体
     * @return Mono<HySubscribeResp>
     */
    @Override
    public Mono<HySubscribeResp> hySubscribe(HySubscribeReq hySubscribeReq) {
        return Mono.defer(() -> Mono.just(hySubscribeReq))
                .flatMap(req -> {
                    // 校验时间
                    if (hySubscribeReq.getEndTime() == null) {
                        hySubscribeReq.setEndTime((DateUtil.format(TimePlanCalculator.getMaxEndTime(),
                                DatePattern.NORM_DATETIME_PATTERN)));
                    }
                    if (hySubscribeReq.getStartTime().compareTo(hySubscribeReq.getEndTime()) >= 0) {
                        log.error(ErrorCode.ILLEGAL_PARAMETER + ":" + ErrorCode.ILLEGAL_PARAMETER_MESSAGE
                                + ErrorCode.ILLEGAL_ENDTIME_MESSAGE);
                        return Mono.error(new ServiceException(ErrorCode.ILLEGAL_PARAMETER,
                                ErrorCode.ILLEGAL_PARAMETER_MESSAGE + ErrorCode.ILLEGAL_ENDTIME_MESSAGE));
                    }
                    QueryUserCondition queryUserCondition = new QueryUserCondition();
                    queryUserCondition.setAccount(hySubscribeReq.getAccount());
                    queryUserCondition.setUserDomainId(hySubscribeReq.getUserDomainId());
                    UserDo user = userDomainService.getUserDoByMsisdn(queryUserCondition, QUERY_AND_CREATE_USER_INFO);
                    // 1 增加对按次产品的订购判断，按次产品可以订购多次，但是相同起止时间的只能订购一次
                    // 2 包月产品如果处在有效状态只能订购一次
                    QueryGoodsCondition queryGoodsCondition = new QueryGoodsCondition();
                    queryGoodsCondition.setGoodsIdList(Collections.singletonList(hySubscribeReq.getProductID()));
                    List<GoodsDo> goods = goodsDomainService.getGoodsByCondition(queryGoodsCondition);
                    if (goods.isEmpty()) {
                        log.error(ErrorCode.ILLEGAL_PARAMETER + ":" + ErrorCode.ILLEGAL_PARAMETER_MESSAGE);
                        return Mono.error(new ServiceException(ErrorCode.CONTRACT_NOT_EXISTS,
                                ErrorCode.ILLEGAL_PARAMETER_MESSAGE));
                    }
                    hySubCheck(hySubscribeReq, goods.get(0), user);
                    ManageSubscribeRelationReq serializable = buildManageReq(hySubscribeReq);
                    ManageSubscribeDto manageSubscribeDto = vsboOrderRespConvertor.toManageSubscribeDto(serializable);
                    manageSubscribeDto.setUserDo(user);
                    manageSubscribeDto.setGoodsDo(goods.get(0));
                    String goodsInstanceId = Objects.requireNonNull(
                                    ManageOperatorManager.getDeliveryService(String.valueOf(EventType.ESTABLISH.getType())))
                            .process(manageSubscribeDto);
                    log.info("[hySubscribe] Generated goodsInstanceId: {}", goodsInstanceId);
                    try {
                        return Mono.just(buildHySubscribeResp(serializable, user));
                    } catch (Exception e) {
                        log.error("hySubscribe error", e);
                        return Mono.error(new ServiceException(INTERNAL_SERVER_ERROR));
                    }
                });
    }

    /**
     * 订单前置校验
     *
     * @param createOrderReq 请求体
     * @return
     */
    @Override
    public Mono<Void> preOrderCheck(CreateOrderReq createOrderReq) {
        return Mono.defer(() -> Mono.just(createOrderReq))
                .flatMap(req -> {
                    // 渠道商品上架校验
                    String channelId = String.valueOf(createOrderReq.getWho());
                    String subChannelId = ServiceUtil.getParameter(createOrderReq.getNamedParameters(), SUB_CHANNEL_ID);
                    String goodsId = createOrderReq.getOrderItems().getProductOffering().getProductOfferingID();
                    ChannelServiceFacade.validChannelShelfLimit(channelId, subChannelId, goodsId, true);

                    DomainServiceContext orderContext = new DomainServiceContext(OrderDo.class,
                            OrderServiceTypeConstant.PRE_ORDER_CHECK);
                    PreOrderCheckDto preOrderCheckDto = PreOrderCheckDto.builder()
                            .rawAccount(createOrderReq.getRawAccount())
                            .productOfferingId(goodsId)
                            .fp(ServiceUtil.getParameter(createOrderReq.getNamedParameters(),
                                    CommonConstant.OrderExtParams.ORDER_EXT_FP))
                            .activityId(ServiceUtil.getParameter(createOrderReq.getNamedParameters(),
                                    CommonConstant.OrderExtParams.ACTIVITY_ID))
                            .activitySource(ServiceUtil.getParameter(createOrderReq.getNamedParameters(),
                                    CommonConstant.OrderExtParams.ACTIVITY_SOURCE))
                            .payWay(ServiceUtil.getParameter(createOrderReq.getNamedParameters(),
                                    CommonConstant.OrderExtParams.PAY_WAY))
                            .build();
                    orderDomainService.doPreOrderCheck(orderContext, preOrderCheckDto, null);
                    return Mono.empty();
                });
    }

    /**
     * 补订
     *
     * @param backOrderedReq 请求体
     * @return Mono<BackOrderedResp>
     */
    @Override
    public Mono<BackOrderedResp> backOrdered(BackOrderedReq backOrderedReq) {
        return Mono.defer(() -> Mono.just(backOrderedReq))
                .flatMap(req -> {
                    QueryOrderCondition queryOrderCondition = new QueryOrderCondition();
                    queryOrderCondition.setOrderNo(backOrderedReq.getOrderId());
                    OrderDo orderDo = orderDomainService.getOrderDo(queryOrderCondition, "backOrdered");
                    if (orderDo == null) {
                        log.error(
                                "order is not exist! orderId=" + queryOrderCondition);
                        throw new ServiceException(ErrorCode.ORDER_NOT_EXISTS, ErrorCode.ORDER_NOT_EXISTS_MESSAGE);
                    }
                    QueryUserCondition queryUserCondition = new QueryUserCondition();
                    queryOrderCondition.setUserId(orderDo.getOrderUserId());
                    UserDo userDo = userDomainService.getUserDoByMsisdn(queryUserCondition, QUERY_AND_CREATE_USER_INFO);
                    QueryGoodsCondition queryGoodsCondition = new QueryGoodsCondition();
                    queryGoodsCondition.setGoodsIdList(Collections.singletonList(orderDo.getGoodsId()));
                    List<GoodsDo> goods = goodsDomainService.getGoodsByCondition(queryGoodsCondition);
                    if (goods.isEmpty()) {
                        log.error(ErrorCode.ILLEGAL_PARAMETER + ":" + ErrorCode.ILLEGAL_PARAMETER_MESSAGE);
                        return Mono.error(new ServiceException(ErrorCode.CONTRACT_NOT_EXISTS,
                                ErrorCode.ILLEGAL_PARAMETER_MESSAGE));
                    }
                    GoodsDo goodsDo = goods.get(0);
                    DomainServiceContext context = new DomainServiceContext(PerformOrderDo.class, PERFORM_ORDER);
                    PerformOrderDo performOrderDo = context.newInstance(PerformOrderDo.class);
                    performOrderDo.setOpType(SubscribeTypeEnum.BACK_ORDERED);
                    performOrderDo.setOrderDo(orderDo);
                    performOrderDo.setUserDo(userDo);
                    performOrderDo.setUserId(userDo.getUserId());
                    performOrderDo.setGoodsId(goodsDo.getGoodsId());
                    performOrderDo.setOrderId(orderDo.getOrderNo());
                    performOrderDo.setTimePlanGoodsGroupId(goodsDo.getTimePlanGoodsGroupId());
                    performOrderDo.setGoodsPackageInstanceId(backOrderedReq.getGoodsPackageInstanceId());
                    context.putInstance(performOrderDo);
                    context.writeAndFlush();
                    return Mono.just(new BackOrderedResp());
                });
    }

    /**
     * 绑定家庭号
     *
     * @param bindFamilyNumberReq 请求体
     * @return Mono<String> bindFamilyNumber
     */
    @Override
    public Mono<Void> bindFamilyNumber(BindFamilyNumberReq bindFamilyNumberReq) {
        return Mono.defer(() -> Mono.just(bindFamilyNumberReq))
                .flatMap(req -> {
                    try {
                        familyNumberLock(bindFamilyNumberReq);
                        QueryUserCondition queryUserCondition = new QueryUserCondition();
                        queryUserCondition.setAccount(bindFamilyNumberReq.getAccount().getAccountName());
                        queryUserCondition.setUserDomainId(bindFamilyNumberReq.getAccount().getUserDomainId());
                        UserDo invitingUserDo = userDomainService.getUserDoByMsisdn(queryUserCondition,
                                QUERY_USER_INFO);
                        req.getAccount().setAccountName(invitingUserDo.getMsisdn());
                        MemberProduct memberProduct = goodsInstanceDomainService
                                .queryUserGiftedBenefits(invitingUserDo);
                        if (memberProduct.getGoodsInstanceExtendDo() == null) {
                            log.error("search no subscription,the account=".concat(req.getAccount().getAccountName()));
                            return Mono.error(new ServiceException(ErrorCode.ACCOUNT_NO_ORDER_GOTONE,
                                    ErrorCode.ACCOUNT_NO_ORDER_GOTONE_DESC));
                        }
                        String invitingUserId = invitingUserDo.getUserId();
                        QueryUserCondition condition = new QueryUserCondition();
                        condition.setAccount(bindFamilyNumberReq.getFamilyAccount().getAccountName());
                        condition.setUserDomainId(bindFamilyNumberReq.getFamilyAccount().getUserDomainId());
                        condition.setUserSyncByUserDomainFlag(true);
                        UserDo invitedUserDo = userDomainService.getUserAndCheckOpen(condition, USER_LSB_SERVICE);
                        checkBindFamilyNumber(req, memberProduct);
                        String invitedUserId = invitedUserDo.getUserId();
                        InviteRelationDo inviteRelationDo = InviteRelationDo.builder()
                                .inviteId(SnowflakeUtil.getNextString())
                                .invitedAccount(req.getFamilyAccount().getAccountName())
                                .invitingAccount(req.getAccount().getAccountName())
                                .goodsId(req.getProductID())
                                .state(WAIT_GIVE)
                                .invitedUserId(invitedUserId)
                                .invitingUserId(invitingUserId)
                                .invitingOrderId(req.getOrderID())
                                .invitationTime(new Date()).build();
                        inviteRelationService.addRelation(inviteRelationDo, INSERT_FAMILY_NUMBER_INFO);
                        inviteRelationService.inviteRelationBind(
                                memberProduct.getGoodsDo().getGoodsExt().getGotoneGiftGoodsId(), inviteRelationDo,
                                invitingUserDo);
                    } finally {
                        // 解锁
                        familyNumberUnlock(bindFamilyNumberReq);
                    }
                    return Mono.empty();
                });
    }

    /**
     * 解锁
     *
     * @param bindFamilyNumberReq 请求体
     */
    private void familyNumberUnlock(BindFamilyNumberReq bindFamilyNumberReq) {
        if (bindFamilyNumberReq.getAccount().getAccountName() != null) {
            orderGateway
                    .unLock(String.format(FAMILY_NUMBER_PHONE_KEY, bindFamilyNumberReq.getAccount().getAccountName()));
        } else if (bindFamilyNumberReq.getAccount().getUserDomainId() != null) {
            orderGateway.unLock(String.format(FAMILY_NUMBER_USER_DOMAIN_ID_KEY,
                    bindFamilyNumberReq.getAccount().getUserDomainId()));
        }
        if (bindFamilyNumberReq.getFamilyAccount().getAccountName() != null) {
            orderGateway.unLock(String.format(FAMILY_NUMBER_BIND_PHONE_KEY,
                    bindFamilyNumberReq.getFamilyAccount().getAccountName()));
        } else if (bindFamilyNumberReq.getFamilyAccount().getUserDomainId() != null) {
            orderGateway.unLock(String.format(FAMILY_NUMBER_BIND_USER_DOMAIN_ID_KEY,
                    bindFamilyNumberReq.getFamilyAccount().getUserDomainId()));
        }
    }

    private void checkBindFamilyNumber(BindFamilyNumberReq req, MemberProduct memberProduct) {
        // 校验赠送的产品id和订单id
        checkFamilyProduct(req, memberProduct);
        checkBindIsExist(req);
    }

    private void checkBindIsExist(BindFamilyNumberReq req) {
        if (req.getAccount() != null && req.getAccount().getAccountName() != null) {
            QueryInviteCondition queryInviteConditionMonth = new QueryInviteCondition();
            queryInviteConditionMonth.setInvitingAccount(req.getAccount().getAccountName());
            queryInviteConditionMonth.setOperateTime(new Date());
            queryInviteConditionMonth.setStatusList(Arrays.asList(WAIT_GIVE, GIVEN, WAIT_UNSUBSCRIBE, UNSUBSCRIBED));
            List<InviteRelationDo> invitingReadMonth = inviteRelationService.queryInviteByConditions(
                    queryInviteConditionMonth,
                    QUERY_FAMILY_NUMBER_INFO);
            isExistMonthInviteRelation(invitingReadMonth);
            QueryInviteCondition queryInviteCondition = new QueryInviteCondition();
            queryInviteCondition.setInvitingAccount(req.getAccount().getAccountName());
            queryInviteCondition.setStatusList(Arrays.asList(WAIT_GIVE, GIVEN, WAIT_UNSUBSCRIBE));
            List<InviteRelationDo> invitingRead = inviteRelationService.queryInviteByConditions(queryInviteCondition,
                    QUERY_FAMILY_NUMBER_INFO);
            isExistInviteRelation(BINGING_RELATUINSHIP_ALREADY_EXISTS, invitingRead);
        }
        if (req.getFamilyAccount() != null && req.getFamilyAccount().getAccountName() != null) {
            QueryInviteCondition queryInvitingCondition = new QueryInviteCondition();
            queryInvitingCondition.setInvitedAccount(req.getFamilyAccount().getAccountName());
            queryInvitingCondition.setStatusList(Arrays.asList(WAIT_GIVE, GIVEN, WAIT_UNSUBSCRIBE));
            List<InviteRelationDo> invitedRead = inviteRelationService.queryInviteByConditions(queryInvitingCondition,
                    QUERY_FAMILY_NUMBER_INFO);
            invitedRead = invitedRead.stream()
                    .filter(inviteRelationDo -> !inviteRelationDo.getState().equals(GIVEN_FAIL))
                    .collect(Collectors.toList());
            isExistInviteRelation(BINGING_RELATUINSHIP_ALREADY_EXISTS_INVITED, invitedRead);
        }
    }

    /**
     * 绑定关系锁
     *
     * @param bindFamilyNumberReq 绑定关系请求参数
     */
    private void familyNumberLock(BindFamilyNumberReq bindFamilyNumberReq) {
        if (bindFamilyNumberReq.getAccount().getAccountName() != null) {
            orderGateway.setIfAbsent(
                    String.format(FAMILY_NUMBER_PHONE_KEY, bindFamilyNumberReq.getAccount().getAccountName()),
                    bindFamilyNumberReq.getAccount().getAccountName(), 30, TimeUnit.SECONDS);
        } else if (bindFamilyNumberReq.getAccount().getUserDomainId() != null) {
            orderGateway.setIfAbsent(
                    String.format(FAMILY_NUMBER_USER_DOMAIN_ID_KEY, bindFamilyNumberReq.getAccount().getUserDomainId()),
                    bindFamilyNumberReq.getAccount().getUserDomainId(), 30, TimeUnit.SECONDS);
        }
        if (bindFamilyNumberReq.getFamilyAccount().getAccountName() != null) {
            orderGateway.setIfAbsent(
                    String.format(FAMILY_NUMBER_BIND_PHONE_KEY,
                            bindFamilyNumberReq.getFamilyAccount().getAccountName()),
                    bindFamilyNumberReq.getFamilyAccount().getAccountName(), 30, TimeUnit.SECONDS);
        } else if (bindFamilyNumberReq.getFamilyAccount().getUserDomainId() != null) {
            orderGateway.setIfAbsent(
                    String.format(FAMILY_NUMBER_BIND_USER_DOMAIN_ID_KEY,
                            bindFamilyNumberReq.getFamilyAccount().getUserDomainId()),
                    bindFamilyNumberReq.getFamilyAccount().getUserDomainId(), 30, TimeUnit.SECONDS);
        }
    }

    /**
     * 订单核算
     *
     * @param req 请求体
     * @return Mono<DifferenceDigitalReconcileResp> 响应体
     */
    @Override
    public Mono<DifferenceDigitalReconcileResp> differenceDigitalReconcile(DifferenceDigitalReconcileReq req) {
        return Mono.defer(() -> Mono.just(req))
                .flatMap(differenceDigitalReconcileReq -> {
                    DifferenceDigitalReconcileResp differenceDigitalReconcileResp = new DifferenceDigitalReconcileResp();
                    try {
                        checkDigitalReq(differenceDigitalReconcileReq);
                        differenceDigitalHandlerFactory.getProcessHandler(differenceDigitalReconcileReq.getSceneType())
                                .digitalInvoke(differenceDigitalReconcileReq);
                    } catch (Exception e) {
                        differenceDigitalReconcileResp.setMessage(e.getMessage());
                        differenceDigitalReconcileResp.setCode("-1");
                    }
                    return Mono.just(differenceDigitalReconcileResp);
                });
    }

    /**
     * 预校验家庭号
     *
     * @param bindFamilyNumberReq 请求体
     * @return Mono<Void>
     */
    @Override
    public Mono<Void> preCheckFamilyNumber(BindFamilyNumberReq bindFamilyNumberReq) {
        return Mono.defer(() -> Mono.just(bindFamilyNumberReq))
                .flatMap(req -> {
                    checkBindIsExist(req);
                    return Mono.empty();
                });
    }

    /**
     * 参数校验
     *
     * @param differenceDigitalReconcileReq 请求体
     * @throws ServiceException 异常
     */
    private void checkDigitalReq(DifferenceDigitalReconcileReq differenceDigitalReconcileReq) throws ServiceException {
        SceneTypeEnum sceneTypeEnum = SceneTypeEnum.fromType(differenceDigitalReconcileReq.getSceneType());
        if (null == sceneTypeEnum) {
            throw new ServiceException(ErrorCode.SCENE_CHECK_ERROR, "illegal scene type");
        }
        if (StringUtils.isEmpty(differenceDigitalReconcileReq.getAccount()) ||
                StringUtils.isEmpty(differenceDigitalReconcileReq.getSceneType())) {
            throw new ServiceException(ErrorCode.PARAMS_CHECK_ERROR, "illegal param, account is null");
        }
        switch (sceneTypeEnum) {
            case DIFFERENCE_ONE:
                if (StringUtils.isEmpty(differenceDigitalReconcileReq.getProductID())) {
                    throw new ServiceException(ErrorCode.PARAMS_CHECK_ERROR, "illegal param, productID is null");
                }
                break;
            case DIFFERENCE_TWO:
            case DIFFERENCE_FOUR:
                if (StringUtils.isEmpty(differenceDigitalReconcileReq.getOrderId())) {
                    throw new ServiceException(ErrorCode.PARAMS_CHECK_ERROR, "illegal param, orderId is null");
                }
                break;
            default:
                throw new ServiceException(ErrorCode.SCENE_CHECK_ERROR, "illegal scene type");
        }
    }

    /**
     * 校验产品id和订单id
     *
     * @param req           绑定接口入参
     * @param memberProduct 全球通订购关系
     * @throws ServiceException 异常
     */
    private void checkFamilyProduct(BindFamilyNumberReq req, MemberProduct memberProduct) throws ServiceException {

        if (req.getAccount() != null && req.getFamilyAccount() != null) {
            if (StringUtils.isNotEmpty(req.getAccount().getAccountName())
                    && StringUtils.isNotEmpty(req.getFamilyAccount().getAccountName())
                    && req.getAccount().getAccountName().equals(req.getFamilyAccount().getAccountName())) {
                throw new ServiceException(ErrorCode.PARAMS_CHECK_ERROR, "illegal param, account is same");
            }
            if (StringUtils.isNotEmpty(req.getAccount().getUserDomainId())
                    && StringUtils.isNotEmpty(req.getFamilyAccount().getUserDomainId())
                    && req.getAccount().getUserDomainId().equals(req.getFamilyAccount().getUserDomainId())) {
                throw new ServiceException(ErrorCode.PARAMS_CHECK_ERROR, "illegal param, account is same");
            }
        }

        GoodsInstanceDo goodsInstanceExtendDo = memberProduct.getGoodsInstanceExtendDo();
        if (goodsInstanceExtendDo != null && !goodsInstanceExtendDo.getOrderId().equals(req.getOrderID())) {
            log.warn("orderID is not correct.");
            req.setOrderID(goodsInstanceExtendDo.getOrderId());
        }

        // 产品领域上下文
        // 请求本地产品领域
        GoodsDo goodsDo = memberProduct.getGoodsDo();

        // 获取产品基本信息
        String presentProductId = null;
        if (goodsDo == null) {
            log.error("goodsDo is null.");
            throw new ServiceException(ErrorCode.FAILED, "goodsDo is null.");
        }
        if (goodsDo.getGoodsExt() != null) {
            presentProductId = goodsDo.getGoodsExt().getGotoneGiftGoodsId();
        }

        // presentProductId = "GoTone-Golden-Year-New";
        // 查不到对应赠送产品
        if (StringUtils.isEmpty(presentProductId)) {
            log.error("reserve7 in contract is not exist!");
            throw new ServiceException(ErrorCode.FAILED, "reserve7 in contract not exist!");
        }

        if (!req.getProductID().equals(presentProductId)) {
            req.setProductID(presentProductId);
            log.warn("productID is not correct.");
        }
    }

    private HySubscribeResp buildHySubscribeResp(ManageSubscribeRelationReq manageSubscribeRelationReq, UserDo user)
            throws Exception {
        HySubscribeResp hySubscribeResp = new HySubscribeResp();
        QueryGoodsInstanceCondition queryGoodsInstanceExtendCondition = new QueryGoodsInstanceCondition();
        queryGoodsInstanceExtendCondition
                .setGoodsIdList(Collections.singletonList(manageSubscribeRelationReq.getProductID()));
        queryGoodsInstanceExtendCondition.setUserId(user.getUserId());
        queryGoodsInstanceExtendCondition.setEffectiveEndTimeStart(new Date());
        GoodsInstanceDo goodsInsExtDos = goodsInstanceDomainService
                .queryGoodsInstance(queryGoodsInstanceExtendCondition);
        if (ObjectUtils.isNotEmpty(goodsInsExtDos)) {
            hySubscribeResp.setSubscriptionID(goodsInsExtDos.getGoodsInstanceId());
            hySubscribeResp.setOrderID(goodsInsExtDos.getOrderId());
        }
        return hySubscribeResp;
    }

    /**
     * 构造请求体
     *
     * @param hySubscribeReq 请求体
     * @return ManageSubscribeRelationReq
     */
    private static ManageSubscribeRelationReq buildManageReq(HySubscribeReq hySubscribeReq) {
        ManageSubscribeRelationReq serializable = new ManageSubscribeRelationReq();
        serializable.setWho(PortalType.ZHONGYIZIYAN);
        serializable.setAccount(hySubscribeReq.getAccount());
        serializable.setEventType(EventType.ESTABLISH.getType());
        serializable.setProductID(hySubscribeReq.getProductID());
        serializable.setStartTime(
                DateUtil.format(DateUtil.parse(hySubscribeReq.getStartTime()), DatePattern.NORM_DATETIME_PATTERN));
        serializable.setEndTime(
                DateUtil.format(DateUtil.parse(hySubscribeReq.getEndTime()), DatePattern.NORM_DATETIME_PATTERN));
        serializable.setThirdPlatformTradID(hySubscribeReq.getHyOrderID());
        List<NamedParameter> namedParameters = new ArrayList<>();
        namedParameters.add(new NamedParameter("createSubscribe", "1"));
        serializable.setNamedParameters(namedParameters);
        return serializable;
    }

    private void hySubCheck(HySubscribeReq hySubscribeReq, GoodsDo goodsDo, UserDo user) {
        QueryGoodsInstanceExtendCondition queryGoodsInstanceExtendCondition = new QueryGoodsInstanceExtendCondition();
        queryGoodsInstanceExtendCondition.setGoodsId(Collections.singletonList(hySubscribeReq.getProductID()));
        queryGoodsInstanceExtendCondition.setSaleType(goodsDo.getGoodsSalesType().getType());
        queryGoodsInstanceExtendCondition.setUserId(user.getUserId());
        GoodsInstanceDo goodsInsExtDos = goodsInstanceDomainService
                .queryGoodsInstance(queryGoodsInstanceExtendCondition);
        if (goodsDo.getChargeType().equals(ChargeTypeEnum.MONTHLY) && goodsInsExtDos != null
                && (GoodsInstanceStateEnum.NORMAL.equals(goodsInsExtDos.getStateEnum()))) {
            throw new ServiceException(ErrorCode.CREATE_SUBSCRIBRE_FAILED,
                    ErrorCode.CREATE_HYSUBSCRIBREM_MESSAGE);
        } else if (goodsDo.getChargeType().equals(ChargeTypeEnum.BY_TIMES) && goodsInsExtDos != null
                && goodsInsExtDos.getEffectiveEndTime().getTime() == DateUtil.parseDate(hySubscribeReq.getEndTime())
                .getTime()) {
            throw new ServiceException(ErrorCode.CREATE_SUBSCRIBRE_FAILED,
                    ErrorCode.CREATE_HYSUBSCRIBREN_MESSAGE);
        }

    }

    /**
     * 构造请求体
     *
     * @param createSubscribeRelationReq 请求体
     * @return ManageSubscribeRelationReq
     */
    private static ManageSubscribeRelationReq buildManageReq(CreateSubscribeRelationReq createSubscribeRelationReq) {
        ManageSubscribeRelationReq serializable = new ManageSubscribeRelationReq();
        serializable.setAccount(createSubscribeRelationReq.getUserID());
        serializable.setEventType(createSubscribeRelationReq.getEventType());
        serializable.setProductID(createSubscribeRelationReq.getProductOfferingID());
        serializable.setSaleType(createSubscribeRelationReq.getSaleType());
        serializable.setWho(createSubscribeRelationReq.getWho());
        serializable.setStartTime(createSubscribeRelationReq.getStartTime());
        serializable.setEndTime(createSubscribeRelationReq.getEndTime());
        List<NamedParameter> namedParameters = new ArrayList<>();
        namedParameters.add(new NamedParameter("createSubscribe", "1"));
        serializable.setNamedParameters(namedParameters);
        return serializable;
    }

    /**
     * 构造直接订购响应体
     *
     * @param manageSubscribeRelationReq 请求体
     * @param userDo                     用户信息
     * @param goodsDo                    商品信息
     * @param goodsInstanceId            商品实例ID
     * @return CreateSubscribeRelationResp
     */
    private CreateSubscribeRelationResp buildCreateSubscribeRelationResp(
            ManageSubscribeRelationReq manageSubscribeRelationReq, UserDo userDo, GoodsDo goodsDo, String goodsInstanceId) {

        CreateSubscribeRelationResp response = new CreateSubscribeRelationResp();

        // 设置基础信息
        response.setUserID(userDo.getMsisdn());
        response.setProductName(goodsDo.getGoodsName());
        response.setProductOfferingID(goodsDo.getGoodsId());

        // 如果没有商品实例ID，使用请求中的时间信息作为默认值
        if (StringUtils.isEmpty(goodsInstanceId)) {
            log.warn("[buildCreateSubscribeRelationResp] goodsInstanceId is empty, using request time info");
            response.setStartTime(manageSubscribeRelationReq.getStartTime());
            response.setEndTime(manageSubscribeRelationReq.getEndTime());
            response.setStatus(1); // 默认已订购状态
            return response;
        }

        try {
            // 根据时间计划策略处理商品实例信息
            SubTimePlanPolicyEnum subTimePlanPolicy = goodsDo.getTimePlan().getSubTimePlanPolicy();

            switch (subTimePlanPolicy) {
                case INDEPENDENCE:
                    handleIndependencePolicy(response, userDo.getUserId(), goodsInstanceId);
                    break;
                case PUT_OFF:
                    handlePutOffPolicy(response, userDo.getUserId(), goodsDo, manageSubscribeRelationReq);
                    break;
                default:
                    // 其他策略按独立计算处理
                    handleIndependencePolicy(response, userDo.getUserId(), goodsInstanceId);
                    break;
            }
        } catch (Exception e) {
            log.error("[buildCreateSubscribeRelationResp] Failed to query goods instance, using fallback values. goodsInstanceId: {}, error: {}",
                    goodsInstanceId, e.getMessage());
            // 异常情况下使用请求中的时间信息
            response.setStartTime(manageSubscribeRelationReq.getStartTime());
            response.setEndTime(manageSubscribeRelationReq.getEndTime());
            response.setStatus(1); // 默认已订购状态
        }

        return response;
    }

    /**
     * 处理独立计算策略
     *
     * @param response        响应对象
     * @param userId          用户ID
     * @param goodsInstanceId 商品实例ID
     */
    private void handleIndependencePolicy(CreateSubscribeRelationResp response, String userId, String goodsInstanceId) {
        QueryGoodsInstanceCondition condition = new QueryGoodsInstanceCondition();
        condition.setUserId(userId);
        condition.setGoodsInstanceIdList(Collections.singletonList(goodsInstanceId));

        GoodsInstanceDo goodsInstanceDo = GoodsInstanceServiceFacade.queryGoodsInstanceByCondition(condition);
        if (goodsInstanceDo != null) {
            response.setStartTime(DateUtil.format(goodsInstanceDo.getEffectiveStartTime(), DatePattern.NORM_DATETIME_PATTERN));
            response.setEndTime(DateUtil.format(goodsInstanceDo.getEffectiveEndTime(), DatePattern.NORM_DATETIME_PATTERN));
            response.setStatus(goodsInstanceDo.getStateEnum().getState());
        } else {
            log.warn("[handleIndependencePolicy] GoodsInstance not found for id: {}", goodsInstanceId);
            response.setStatus(1); // 默认已订购状态
        }
    }

    /**
     * 处理顺延策略
     *
     * @param response                   响应对象
     * @param userId                     用户ID
     * @param goodsDo                    商品信息
     * @param manageSubscribeRelationReq 请求信息
     */
    private void handlePutOffPolicy(CreateSubscribeRelationResp response, String userId, GoodsDo goodsDo,
                                    ManageSubscribeRelationReq manageSubscribeRelationReq) {
        QueryGoodsInstanceCondition condition = new QueryGoodsInstanceCondition();
        condition.setUserId(userId);
        condition.setTimePlanGoodsGroupId(goodsDo.getTimePlanGoodsGroupId());

        // 如果是创建订购关系，查询有效的商品实例
        if (manageSubscribeRelationReq.getEventType() == EventTypeEnum.ESTABLISH.getCodeInt()) {
            condition.setEffectiveEndTimeStart(new Date());
        }

        List<GoodsInstanceDo> goodsInstanceDos = goodsInstanceDomainService.queryGoodsInstances(condition);

        if (CollUtil.isNotEmpty(goodsInstanceDos)) {
            // 取最早开始时间和最晚结束时间
            GoodsInstanceDo minStartInstance = goodsInstanceDos.stream()
                    .min(Comparator.comparing(GoodsInstanceDo::getEffectiveStartTime))
                    .orElse(null);
            GoodsInstanceDo maxEndInstance = goodsInstanceDos.stream()
                    .max(Comparator.comparing(GoodsInstanceDo::getEffectiveEndTime))
                    .orElse(null);

            if (minStartInstance != null && maxEndInstance != null) {
                response.setStartTime(DateUtil.format(minStartInstance.getEffectiveStartTime(), DatePattern.NORM_DATETIME_PATTERN));
                response.setEndTime(DateUtil.format(maxEndInstance.getEffectiveEndTime(), DatePattern.NORM_DATETIME_PATTERN));
                response.setStatus(maxEndInstance.getStateEnum().getState());
            } else {
                log.warn("[handlePutOffPolicy] Failed to find min/max instances for user: {}", userId);
                response.setStatus(1); // 默认已订购状态
            }
        } else {
            log.warn("[handlePutOffPolicy] No goods instances found for user: {} and groupId: {}",
                    userId, goodsDo.getTimePlanGoodsGroupId());
            response.setStatus(1); // 默认已订购状态
        }
    }

    /**
     * 构造订购关系管理响应体
     *
     * @param manageSubscribeRelationReq 请求体
     * @param userDo                     用户信息
     * @param goodsDo                    商品信息
     * @param goodsInstanceId            商品实例ID
     * @return ManageSubscribeRelationResp
     */
    private ManageSubscribeRelationResp buildManageSubscribeRelationResp(
            ManageSubscribeRelationReq manageSubscribeRelationReq, UserDo userDo, GoodsDo goodsDo, String goodsInstanceId) {

        ManageSubscribeRelationResp response = new ManageSubscribeRelationResp();

        // 设置基础信息
        response.setAccount(userDo.getMsisdn());
        response.setProductID(goodsDo.getGoodsId());
        response.setProductName(goodsDo.getGoodsName());
        response.setSubscriptionID(goodsInstanceId);

        // 如果没有商品实例ID，使用请求中的时间信息作为默认值
        if (StringUtils.isEmpty(goodsInstanceId)) {
            log.warn("[buildManageSubscribeRelationResp] goodsInstanceId is empty, using request time info");
            response.setStartTime(manageSubscribeRelationReq.getStartTime());
            response.setEndTime(manageSubscribeRelationReq.getEndTime());
            response.setStatus(1); // 默认已订购状态
            return response;
        }

        // 根据时间计划策略处理商品实例信息
        SubTimePlanPolicyEnum subTimePlanPolicy = goodsDo.getTimePlan().getSubTimePlanPolicy();

        switch (subTimePlanPolicy) {
            case INDEPENDENCE:
                handleIndependencePolicyForManage(response, userDo.getUserId(), goodsInstanceId);
                break;
            case PUT_OFF:
                handlePutOffPolicyForManage(response, userDo.getUserId(), goodsDo, manageSubscribeRelationReq);
                break;
            default:
                // 其他策略按独立计算处理
                handleIndependencePolicyForManage(response, userDo.getUserId(), goodsInstanceId);
                break;
        }

        return response;
    }

    /**
     * 处理独立计算策略 - 管理订购关系响应
     *
     * @param response        响应对象
     * @param userId          用户ID
     * @param goodsInstanceId 商品实例ID
     */
    private void handleIndependencePolicyForManage(ManageSubscribeRelationResp response, String userId, String goodsInstanceId) {
        QueryGoodsInstanceCondition condition = new QueryGoodsInstanceCondition();
        condition.setUserId(userId);
        condition.setGoodsInstanceIdList(Collections.singletonList(goodsInstanceId));

        GoodsInstanceDo goodsInstanceDo = GoodsInstanceServiceFacade.queryGoodsInstanceByCondition(condition);
        if (goodsInstanceDo != null) {
            response.setStartTime(DateUtil.format(goodsInstanceDo.getEffectiveStartTime(), DatePattern.NORM_DATETIME_PATTERN));
            response.setEndTime(DateUtil.format(goodsInstanceDo.getEffectiveEndTime(), DatePattern.NORM_DATETIME_PATTERN));
            response.setStatus(goodsInstanceDo.getStateEnum().getState());
            response.setOrderID(goodsInstanceDo.getOrderId());
        } else {
            log.warn("[handleIndependencePolicyForManage] GoodsInstance not found for id: {}", goodsInstanceId);
            response.setStatus(1); // 默认已订购状态
        }
    }

    /**
     * 处理顺延策略 - 管理订购关系响应
     *
     * @param response                   响应对象
     * @param userId                     用户ID
     * @param goodsDo                    商品信息
     * @param manageSubscribeRelationReq 请求信息
     */
    private void handlePutOffPolicyForManage(ManageSubscribeRelationResp response, String userId, GoodsDo goodsDo,
                                             ManageSubscribeRelationReq manageSubscribeRelationReq) {
        QueryGoodsInstanceCondition condition = new QueryGoodsInstanceCondition();
        condition.setUserId(userId);
        condition.setTimePlanGoodsGroupId(goodsDo.getTimePlanGoodsGroupId());

        // 如果是创建订购关系，查询有效的商品实例
        if (manageSubscribeRelationReq.getEventType() == EventTypeEnum.ESTABLISH.getCodeInt()) {
            condition.setEffectiveEndTimeStart(new Date());
        }

        List<GoodsInstanceDo> goodsInstanceDos = goodsInstanceDomainService.queryGoodsInstances(condition);

        if (CollUtil.isNotEmpty(goodsInstanceDos)) {
            // 取最早开始时间和最晚结束时间
            GoodsInstanceDo minStartInstance = goodsInstanceDos.stream()
                    .min(Comparator.comparing(GoodsInstanceDo::getEffectiveStartTime))
                    .orElse(null);
            GoodsInstanceDo maxEndInstance = goodsInstanceDos.stream()
                    .max(Comparator.comparing(GoodsInstanceDo::getEffectiveEndTime))
                    .orElse(null);

            if (minStartInstance != null && maxEndInstance != null) {
                response.setStartTime(DateUtil.format(minStartInstance.getEffectiveStartTime(), DatePattern.NORM_DATETIME_PATTERN));
                response.setEndTime(DateUtil.format(maxEndInstance.getEffectiveEndTime(), DatePattern.NORM_DATETIME_PATTERN));
                response.setStatus(maxEndInstance.getStateEnum().getState());
                response.setOrderID(maxEndInstance.getOrderId());
            } else {
                log.warn("[handlePutOffPolicyForManage] Failed to find min/max instances for user: {}", userId);
                response.setStatus(1); // 默认已订购状态
            }
        } else {
            log.warn("[handlePutOffPolicyForManage] No goods instances found for user: {} and groupId: {}",
                    userId, goodsDo.getTimePlanGoodsGroupId());
            response.setStatus(1); // 默认已订购状态
        }
    }

}
