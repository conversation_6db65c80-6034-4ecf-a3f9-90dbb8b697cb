package com.zyhl.yun.member.mcdmc.activation.result;

import lombok.Data;

import java.io.Serializable;

/**
 * 基础返回实体
 *
 * <AUTHOR>
 */
@Data
public class BaseResult implements Serializable {
    /**
     * 是否成功
     */
    private boolean success = true;
    /**
     * 响应码
     */
    private String resultCode;

    /**
     * 响应信息
     */
    private String message;


    public BaseResult() {
    }

    public BaseResult(String resultCode, String message) {
        this.resultCode = resultCode;
        this.message = message;
    }

    public BaseResult(boolean success, String resultCode, String message) {
        this.success = success;
        this.resultCode = resultCode;
        this.message = message;
    }


    public static BaseResult of(boolean success, ResultCode resultCode) {
        return of(success, resultCode.getCode(), resultCode.getMsg());
    }

    public static BaseResult of(boolean success, String code, String msg) {
        return new BaseResult(success, code, msg);
    }


    public static BaseResult sc(ResultCode resultCode) {
        return sc(resultCode.getCode(), resultCode.getMsg());
    }

    public static BaseResult sc(String code, String msg) {
        return new BaseResult(true, code, msg);
    }

    public static BaseResult fail(ResultCode resultCode) {
        return fail(resultCode.getCode(), resultCode.getMsg());
    }

    public static BaseResult fail(String errorCode, String errorMsg) {
        return new BaseResult(false, errorCode, errorMsg);
    }
}
