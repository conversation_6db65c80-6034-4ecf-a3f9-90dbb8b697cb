package com.zyhl.yun.member.mcdmc.activation.callback.notify.base;

import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.framework.service.MdcLogInterceptor;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.DbFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.CustomDidGenerator;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.enums.WorkOrderStateEnum;
import com.zyhl.yun.member.mcdmc.activation.redis.RedisComponent;
import com.zyhl.yun.member.mcdmc.activation.redis.RedisKeyConstant;
import com.zyhl.yun.member.mcdmc.activation.serviceid.LocalServiceId;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowLogDo;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.FlowLogStateEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.CallbackException;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.result.ResultCode;
import com.zyhl.yun.member.mcdmc.activation.util.RequestUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * 回调上下文
 *
 * <AUTHOR>
 * @since 2024/07/05 10:33
 */
@Slf4j
@Data
public class CallbackContext<T, R> {
    /**
     * 回调请求地址的uri
     */
    private String callbackUri;
    /**
     * 回调请求头
     */
    private Map<String, String> requestHeaders;
    /**
     * 回调请求体字符串
     */
    private String callbackReqStr;
    /**
     * 回调请求体
     */
    private T callbackReq;
    /**
     * 回调响应体
     */
    private R callbackRsp;
    /**
     *
     */
    private String serviceCode;

    /**
     * 当前回调的接口日志
     */
    private WorkServiceFlowLogDo callbackFlowLogDo;

    /**
     * 发货接口日志
     */
    private WorkServiceFlowLogDo sendServiceFlowLogDo;
    /**
     * 发货接口的公共请求
     */
    private ComSendInterfaceReq comSendReq;

    /**
     * 回调订单对应的商品实例
     */
    private GoodsInstanceDo goodsInstanceExtendDo;


    private CallbackContext() {
    }

    public static <T, R> CallbackContext<T, R> newInstance(String uri, Map<String, String> headerMap,
                                                           String notifyReqStr, Function<String, T> reqConvertor) {
        CallbackContext<T, R> callbackContext = new CallbackContext<>();
        callbackContext.setCallbackUri(uri);
        callbackContext.setRequestHeaders(headerMap);
        callbackContext.setCallbackReqStr(notifyReqStr);
        // 记录回调日志
        WorkServiceFlowLogDo notifyFlowLogDo = new WorkServiceFlowLogDo();
        notifyFlowLogDo.setLogId(CustomDidGenerator.generateId());
        notifyFlowLogDo.setUrl(RequestUtil.getRequestUrl());
        notifyFlowLogDo.setRequestBody(notifyReqStr);
        notifyFlowLogDo.setRequestHeader(JSONUtil.toJsonStr(headerMap));
        notifyFlowLogDo.setState(FlowLogStateEnum.RECEIVED_CALLBACK);
        callbackContext.callbackFlowLogDo = notifyFlowLogDo;
        // 转换请求体
        callbackContext.setCallbackReq(reqConvertor.apply(notifyReqStr));

        return callbackContext;
    }

    public void searchSendFLowLog(Function<T, String> searchConditionGetter, RedisComponent redisComponent,
                                  Function<T, WorkServiceFlowLogDo> initSendFlowLogWhileNotFound) {
        DomainServiceContext context = new DomainServiceContext(LocalServiceId.INSERT_OPERATION);
        String searchCondition = searchConditionGetter.apply(callbackReq);
        WorkServiceFlowLogDo sendFlowLogDo = redisComponent.getJsonBean(RedisKeyConstant.getSendFlowLogKey(callbackUri, searchCondition), WorkServiceFlowLogDo.class);
        if (Objects.isNull(sendFlowLogDo)) {
            //redis查询不到再去数据库查询发货回调日志
            BaseCondition baseCondition = new BaseCondition();
            baseCondition.putCondition(DbFieldConstants.CALLBACK_CONDITION, searchCondition);
            baseCondition.putCondition(DbFieldConstants.CALLBACK_URL, callbackUri);
            sendFlowLogDo = context.readLast(baseCondition, WorkServiceFlowLogDo.class);
        }
        if (null == sendFlowLogDo) {
            sendFlowLogDo = initSendFlowLogWhileNotFound.apply(this.callbackReq);
        }
        if (null == sendFlowLogDo) {
            log.error("根据回调条件={}和回调地址={}未获取到对应的发货接口请求日志", searchCondition, callbackUri);
            throw new CallbackException(ResultCode.LOG_NOT_FOUND);
        }
        this.sendServiceFlowLogDo = sendFlowLogDo;
        this.comSendReq = JSONUtil.toBean(sendFlowLogDo.getExtParams(), ComSendInterfaceReq.class);
        this.serviceCode = sendFlowLogDo.getServiceCode();
        // 记录回调日志相关信息
        callbackFlowLogDo.setWorkId(sendFlowLogDo.getWorkId());
        callbackFlowLogDo.setUserId(sendFlowLogDo.getUserId());
        callbackFlowLogDo.setOrderId(sendFlowLogDo.getOrderId());
        callbackFlowLogDo.setWorkServiceFlowId(sendFlowLogDo.getWorkServiceFlowId());
        callbackFlowLogDo.setServiceCode(sendFlowLogDo.getServiceCode());
        callbackFlowLogDo.setIFaceId(sendFlowLogDo.getIFaceId());
        this.comSendReq.putExtInfo("sendLogId", sendFlowLogDo.getLogId());
        this.comSendReq.putExtInfo(LogConstants.TRACE_ID, MdcLogInterceptor.getCurrentTraceId());
        callbackFlowLogDo.setExtParams(JSONUtil.toJsonStr(this.comSendReq));
        // 写入回调日志
        context.putInstance(callbackFlowLogDo);
        context.writeAndFlush();
    }

    /**
     * 更新回调接口日志结果
     *
     * @param isSuccess 回调成功与否
     * @param rspStr    回调响应体
     */
    public void updateLogAndWorkDo(boolean isSuccess, String rspStr) {
        if (null == this.sendServiceFlowLogDo) {
            log.error("未获取到发货接口请求日志，无法更新回调接口日志,请求报文为：{}", this.callbackReqStr);
            return;
        }
        DomainServiceContext context = new DomainServiceContext(LocalServiceId.UPDATE_BY_ID_OPERATION);
        this.callbackFlowLogDo.setResponseBody(rspStr);
        FlowLogStateEnum callbackStateEnum = isSuccess ? FlowLogStateEnum.CALLBACK_SUCCESS : FlowLogStateEnum.CALLBACK_FAILED;
        this.callbackFlowLogDo.setState(callbackStateEnum);
        this.callbackFlowLogDo.setResponseHttpCode(HttpStatus.OK.value());
        context.putInstance(this.callbackFlowLogDo);
        context.writeAndFlush();
        // 更新工单状态
        DomainServiceContext workContext = new DomainServiceContext(LocalServiceId.UPDATE_BY_ID_OPERATION);
        WorkOrderDo workOrderDo = workContext.newInstance(WorkOrderDo.class);
        workOrderDo.setWorkId(this.sendServiceFlowLogDo.getWorkId());
        workOrderDo.setUserId(this.sendServiceFlowLogDo.getUserId());
        if (Boolean.TRUE.equals(isSuccess)) {
            workOrderDo.setState(WorkOrderStateEnum.CALLBACK_SC);
        } else {
            workOrderDo.setState(WorkOrderStateEnum.CALLBACK_FAIL);
        }
        workContext.putInstance(workOrderDo);
        workContext.writeAndFlush();
    }

}
