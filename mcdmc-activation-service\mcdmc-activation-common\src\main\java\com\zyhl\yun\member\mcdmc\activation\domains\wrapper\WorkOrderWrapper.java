package com.zyhl.yun.member.mcdmc.activation.domains.wrapper;

import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderAttrDo;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/01/06 21:00
 */
@Data
public class WorkOrderWrapper {

    /**
     * 工单详情
     */
    private WorkOrderDo workOrderDo;
    /**
     * 工单属性列表
     */
    private List<WorkOrderAttrDo> workOrderAttrDoList;
}
