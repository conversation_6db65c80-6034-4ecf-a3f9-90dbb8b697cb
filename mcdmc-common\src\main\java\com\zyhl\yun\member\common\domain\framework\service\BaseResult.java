package com.zyhl.yun.member.common.domain.framework.service;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;

import java.io.Serializable;

/**
 * 基础返回实体
 *
 * <AUTHOR>
 */
@Data
@JacksonXmlRootElement(localName = "response")
public class BaseResult implements Serializable {
    /**
     * 是否成功
     */
    @JsonIgnore
    private boolean isSuccess;
    /**
     * 响应码
     */
    private String resultCode;

    /**
     * 相应信息
     */
//    @JsonIgnore
    private String resultDesc;


    public BaseResult() {
    }

    public BaseResult(boolean isSuccess, String resultDesc) {
        this.isSuccess = isSuccess;
        this.resultDesc = resultDesc;
    }

    public BaseResult(boolean isSuccess, String resultCode, String resultDesc) {
        this.isSuccess = isSuccess;
        this.resultCode = resultCode;
        this.resultDesc = resultDesc;
    }

    public static BaseResult of(boolean isSuccess, ResultCode resultCode) {
        return new BaseResult(isSuccess, resultCode.getCode(), resultCode.getMsg());
    }

    public static BaseResult of(boolean isSuccess, String code, String msg) {
        return new BaseResult(isSuccess, code, msg);
    }

    public static BaseResult fail(ResultCode resultCode) {
        return new BaseResult(false, resultCode.getCode(), resultCode.getMsg());
    }

}
