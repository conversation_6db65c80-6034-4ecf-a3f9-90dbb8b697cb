package com.zyhl.yun.member.mcdmc.activation.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 工单状态
 *
 * <AUTHOR>
 * @since 2024/06/26 17:17
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum WorkOrderStateEnum {
    /**
     * 待处理
     */
    PENDING(0, "未持久化待处理"),
    /**
     * 处理中
     */
    PROCESSING(1, "已持久化处理中"),
    /**
     * 已完成
     */
    COMPLETED(2, "已完成"),
    /**
     * 已取消，如找不到发货流程
     */
    CANCELED(3, "已取消"),
    /**
     * 失败
     */
    FAILED(4, "已失败"),
    /**
     * 待回调
     */
    BE_CALLBACK(5, "待回调"),
    /**
     * 回调成功
     */
    CALLBACK_SC(6, "回调成功"),
    /**
     * 回调失败
     */
    CALLBACK_FAIL(7, "回调失败"),
    ;
    private final Integer state;
    private final String desc;

    public boolean codeEquals(Integer anotherCode) {
        return this.state.equals(anotherCode);
    }

    /**
     * 判断工单状态是否结束
     */
    public static boolean isDone(WorkOrderStateEnum state) {
        return !PENDING.equals(state) && !PROCESSING.equals(state);
    }

    public static WorkOrderStateEnum fromState(Integer state) {
        for (WorkOrderStateEnum value : WorkOrderStateEnum.values()) {
            if (value.getState().equals(state)) {
                return value;
            }
        }
        log.error("!!!!!!!!!!!!!!!!!未找到状态为{}的枚举!!!!!!!!!!!!!!!!!", state);
        return null;
    }
}
