package com.zyhl.yun.member.goodsInstance.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.*;
import com.zyhl.yun.member.common.constants.ErrorCode;
import com.zyhl.yun.member.common.constants.SymbolConstant;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.framework.PageCondition;
import com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceServiceId;
import com.zyhl.yun.member.common.domain.serviceid.GoodsServiceId;
import com.zyhl.yun.member.common.domain.serviceid.UserServiceId;
import com.zyhl.yun.member.common.domain.serviceid.order.OrderServiceId;
import com.zyhl.yun.member.common.enums.SortEnum;
import com.zyhl.yun.member.common.enums.UserStatusEnum;
import com.zyhl.yun.member.common.util.DateUtils;
import com.zyhl.yun.member.common.util.ParameterUtil;
import com.zyhl.yun.member.common.util.TimeUtil;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.domain.goodsinstance.service.dto.QueryGoodsInstanceReq;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.user.domain.QueryUserCondition;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.dto.UnSubscribeReq;
import com.zyhl.yun.member.dto.UnSubscribeResp;
import com.zyhl.yun.member.goodsinstance.dto.AllContractSubscription;
import com.zyhl.yun.member.goodsinstance.dto.ChildProductActiveInfo;
import com.zyhl.yun.member.goodsinstance.dto.MemberSubscription;
import com.zyhl.yun.member.goodsinstance.dto.VipMemberContractSubVO;
import com.zyhl.yun.member.goodsinstance.enums.GoodsInstanceStateEnum;
import com.zyhl.yun.member.mcdmc.activation.constants.WorkAttrCodeConstant;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderAttrDo;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.facade.ActivationServiceFacade;
import com.zyhl.yun.member.order.common.constants.SyncOrderConstant;
import com.zyhl.yun.member.order.domain.OrderDo;
import com.zyhl.yun.member.order.domain.dto.QueryOrderCondition;
import com.zyhl.yun.member.product.common.enums.ChargeTypeEnum;
import com.zyhl.yun.member.product.common.enums.GoodsSalesTypeEnum;
import com.zyhl.yun.member.product.common.enums.SubTimePlanPolicyEnum;
import com.zyhl.yun.member.product.common.enums.ValidateTriggerTimmingEnum;
import com.zyhl.yun.member.product.common.util.ServiceUtil;
import com.zyhl.yun.member.product.domain.goods.BenefitGoodsDo;
import com.zyhl.yun.member.product.domain.goods.GoodsDo;
import com.zyhl.yun.member.product.domain.goods.GoodsExtVo;
import com.zyhl.yun.member.product.domain.goods.dto.QueryGoodsCondition;
import com.zyhl.yun.member.product.domain.goods.pack.ChildGoodsGroupDo;
import com.zyhl.yun.member.product.domain.goods.pack.GoodsPackageDo;
import com.zyhl.yun.member.product.domain.goods.policy.timeplan.GoodsTimePlanDo;
import com.zyhl.yun.member.product.domain.product.ProductDo;
import com.zyhl.yun.member.product.domain.validaterule.SubRuleValidateResultDo;
import com.zyhl.yun.member.product.domain.validaterule.dto.SubRuleValidateCondition;
import com.zyhl.yun.member.receive.service.SubscribeDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zyhl.yun.member.common.Constant.RIGHTS_STATUS;
import static com.zyhl.yun.member.common.constants.NamedParameterConstant.*;
import static com.zyhl.yun.member.common.domain.serviceid.ValidateRuleServiceId.QUERY_SUB_VALIDATE_DETAIL_INFO;
import static com.zyhl.yun.member.goodsinstance.dto.MemberSubscription.IS_FREE_FLOW;
import static com.zyhl.yun.member.goodsinstance.dto.MemberSubscription.IS_NOT_FREE_FLOW;
import static com.zyhl.yun.member.product.common.constants.MapKey.CHILD_PRODUCTS;
import static com.zyhl.yun.member.product.common.enums.GoodsActiveStrategyEnum.N_CHOOSE_1_STRATEGY;
import static com.zyhl.yun.member.product.common.enums.GoodsActiveTypeEnum.MAN_ACTIVE;

/**
 * <AUTHOR>
 * @date 2024/07/11 14:46
 */
@Service
@Slf4j
public class GoodsInstanceDomainService {


    private static final String GOOD_INSTANCE_GROUP_KEY_PREFIX_MERGE = "merge:";

    private static final String GOOD_INSTANCE_GROUP_KEY_PREFIX_NOT_MERGE = "notMerge:";

    @Resource
    private SubscribeDomainService subscribeDomainService;


    /**
     * 查询包月订购
     *
     * @param queryGoodsInstanceReq
     * @return
     */
    public List<AllContractSubscription> queryMonthSubscribe(QueryGoodsInstanceReq queryGoodsInstanceReq) {

        UserDo userDo = getAndCheckUser(queryGoodsInstanceReq.getPhoneNumber(), queryGoodsInstanceReq.getUserDomainId());

        Date now = new Date();
        QueryGoodsInstanceCondition queryGoodsInstanceCondition = new QueryGoodsInstanceCondition();
        queryGoodsInstanceCondition.setChargeType(ChargeTypeEnum.MONTHLY);
        queryGoodsInstanceCondition.setUserId(userDo.getUserId());
        queryGoodsInstanceCondition.setGoodsSalesTypeList(Arrays.asList(GoodsSalesTypeEnum.MEMBER, GoodsSalesTypeEnum.SPACE));
        queryGoodsInstanceCondition.setStateList(Arrays.asList(GoodsInstanceStateEnum.NORMAL));
        queryGoodsInstanceCondition.setEffectiveEndTimeStart(now);
        queryGoodsInstanceCondition.setEffectiveStartTimeEnd(now);
        queryGoodsInstanceCondition.setExcludeAccountingAddition(true);


        DomainServiceContext userServiceContext =
                new DomainServiceContext(GoodsInstanceServiceId.QUERY_GOODS_INSTANCE);
        List<GoodsInstanceDo> goodsInstanceList =
                userServiceContext.read(queryGoodsInstanceCondition, GoodsInstanceDo.class);

        return buildAllContractSubscriptionList(goodsInstanceList, userDo);
    }

    /**
     * 查询所有包月订购
     *
     * @return 返回订购列表
     */
    public List<GoodsInstanceDo> queryAllMonthSubscribe(UserDo userDo) {
        Date now = new Date();
        QueryGoodsInstanceCondition queryGoodsInstanceCondition = new QueryGoodsInstanceCondition();
        queryGoodsInstanceCondition.setChargeType(ChargeTypeEnum.MONTHLY);
        queryGoodsInstanceCondition.setUserId(userDo.getUserId());
        queryGoodsInstanceCondition.setGoodsSalesTypeList(Arrays.asList(GoodsSalesTypeEnum.MEMBER,
                GoodsSalesTypeEnum.SPACE, GoodsSalesTypeEnum.RIGHTS));
        queryGoodsInstanceCondition.setStateList(Arrays.asList(GoodsInstanceStateEnum.NORMAL));
        queryGoodsInstanceCondition.setEffectiveEndTimeStart(now);
        queryGoodsInstanceCondition.setEffectiveStartTimeEnd(now);
        queryGoodsInstanceCondition.setExcludeAccountingAddition(true);


        DomainServiceContext userServiceContext =
                new DomainServiceContext(GoodsInstanceServiceId.QUERY_GOODS_INSTANCE);
        return userServiceContext.read(queryGoodsInstanceCondition, GoodsInstanceDo.class);
    }

    /**
     * 判断用户是否存在生效中的包月订购
     *
     * @param userId 用户id
     */
    public boolean isContainEffectiveMonthSubscribe(String userId) {
        Date now = new Date();
        QueryGoodsInstanceCondition queryGoodsInstanceCondition = new QueryGoodsInstanceCondition();
        queryGoodsInstanceCondition.setChargeType(ChargeTypeEnum.MONTHLY);
        queryGoodsInstanceCondition.setUserId(userId);
        queryGoodsInstanceCondition.setGoodsSalesTypeList(Arrays.asList(GoodsSalesTypeEnum.MEMBER,
                GoodsSalesTypeEnum.SPACE, GoodsSalesTypeEnum.RIGHTS));
        queryGoodsInstanceCondition.setStateList(Arrays.asList(GoodsInstanceStateEnum.NORMAL, GoodsInstanceStateEnum.PAUSE));
        queryGoodsInstanceCondition.setEffectiveEndTimeStart(now);
        queryGoodsInstanceCondition.setEffectiveStartTimeEnd(now);
        queryGoodsInstanceCondition.setExcludeAccountingAddition(true);

        DomainServiceContext userServiceContext =
                new DomainServiceContext(GoodsInstanceServiceId.QUERY_GOODS_INSTANCE);
        GoodsInstanceDo goodsInstanceDo = userServiceContext.readFirst(queryGoodsInstanceCondition, GoodsInstanceDo.class);
        return Objects.nonNull(goodsInstanceDo);
    }

    /**
     * 查询用户的订购列表
     *
     * @param queryGoodsInstanceReq
     * @return
     */
    public List<MemberSubscription> queryContractListSubscription(QueryGoodsInstanceReq queryGoodsInstanceReq, UserDo userDo,
                                                                  List<QueryGoodsInstanceCondition> queryGoodsInstanceConditionList) {
        // log.info("queryContractListSubscription receive queryGoodsInstanceReq {}", queryGoodsInstanceReq);
        // long start = System.currentTimeMillis();
        // StopWatch stopWatch = new StopWatch();
        // stopWatch.start("queryGoodsInstanceList");

        DomainServiceContext userServiceContext =
                new DomainServiceContext(GoodsInstanceServiceId.QUERY_GOODS_INSTANCE);

        List<GoodsInstanceDo> goodsInstanceList = new ArrayList<>();
        for (QueryGoodsInstanceCondition queryGoodsInstanceCondition : queryGoodsInstanceConditionList) {
            List<GoodsInstanceDo> goodsInstanceExtendDos = userServiceContext.read(queryGoodsInstanceCondition, GoodsInstanceDo.class);
            goodsInstanceList.addAll(goodsInstanceExtendDos);
        }
        // stopWatch.stop();

        List<MemberSubscription> memberSubscriptionList = buildMemberSubscriptionList(goodsInstanceList, userDo,
                queryGoodsInstanceReq);
        // log.info("queryContractListSubscription 耗时情况:总耗时={}ms,查询耗时=[{}]", System.currentTimeMillis() - start, stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        return memberSubscriptionList;
    }


    /**
     * 查询用户的订购列表
     *
     * @param queryGoodsInstanceReq
     * @return
     */
    public List<MemberSubscription> queryContractListSubscriptionV2(QueryGoodsInstanceReq queryGoodsInstanceReq, UserDo userDo,
                                                                    List<QueryGoodsInstanceCondition> queryGoodsInstanceConditionList) {
        // log.info("queryContractListSubscription receive queryGoodsInstanceReq {}", queryGoodsInstanceReq);
        // long start = System.currentTimeMillis();
        // StopWatch stopWatch = new StopWatch();
        // stopWatch.start("queryGoodsInstanceList");

        DomainServiceContext userServiceContext =
                new DomainServiceContext(GoodsInstanceServiceId.QUERY_GOODS_INSTANCE);

        List<GoodsInstanceDo> goodsInstanceList = new ArrayList<>();
        for (QueryGoodsInstanceCondition queryGoodsInstanceCondition : queryGoodsInstanceConditionList) {
            List<GoodsInstanceDo> goodsInstanceExtendDos = userServiceContext.read(queryGoodsInstanceCondition, GoodsInstanceDo.class);
            goodsInstanceList.addAll(goodsInstanceExtendDos);
        }
        // stopWatch.stop();
        goodsInstanceList.sort(Comparator.comparing(GoodsInstanceDo::getSubTime, Comparator.reverseOrder()));


        List<MemberSubscription> memberSubscriptionList = buildMemberSubscriptionListV2(goodsInstanceList, userDo,
                queryGoodsInstanceReq);

        // log.info("queryContractListSubscription 耗时情况:总耗时={}ms,查询耗时=[{}]", System.currentTimeMillis() - start, stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        return memberSubscriptionList;
    }


    /**
     * 查询用户的订购列表
     * 通过orderID和userId查询101和103的订购列表
     *
     * @param queryGoodsInstanceReq
     * @return
     */
    public List<VipMemberContractSubVO> queryVipMemberContractSubVO(QueryGoodsInstanceReq queryGoodsInstanceReq) {

        UserDo userDo = getAndCheckUser(queryGoodsInstanceReq.getPhoneNumber(), queryGoodsInstanceReq.getUserDomainId());
        QueryGoodsInstanceCondition queryGoodsInstanceCondition =
                buildQueryGoodsInstanceCondition(userDo, queryGoodsInstanceReq, queryGoodsInstanceReq.getGoodsIdList());

        DomainServiceContext userServiceContext =
                new DomainServiceContext(GoodsInstanceServiceId.QUERY_GOODS_INSTANCE);


        List<GoodsInstanceDo> goodsInstanceList =
                userServiceContext.read(queryGoodsInstanceCondition, GoodsInstanceDo.class);


        return buildVipMemberContractSubVOList(goodsInstanceList);
    }


    /**
     * 查询独立空间订购
     *
     * @param queryGoodsInstanceReq
     * @return
     */
    public List<MemberSubscription> queryIndependentSubscription(QueryGoodsInstanceReq queryGoodsInstanceReq) {

        UserDo userDo = getAndCheckUser(queryGoodsInstanceReq.getPhoneNumber(), queryGoodsInstanceReq.getUserDomainId());

        QueryGoodsInstanceCondition queryGoodsInstanceCondition =
                buildQueryGoodsInstanceCondition(userDo, queryGoodsInstanceReq, queryGoodsInstanceReq.getGoodsIdList());

        DomainServiceContext userServiceContext =
                new DomainServiceContext(UserDo.class, GoodsInstanceServiceId.QUERY_GOODS_INSTANCE);


        List<GoodsInstanceDo> goodsInstanceList =
                userServiceContext.read(queryGoodsInstanceCondition, GoodsInstanceDo.class);

        if (CollectionUtils.isEmpty(goodsInstanceList)) {
            return Collections.emptyList();
        }

        // 每个商品只获取最大有效期的实例
        Map<String, List<GoodsInstanceDo>> goodsInstanceMap = goodsInstanceList
                .stream().collect(Collectors.groupingBy(goodsInstance -> goodsInstance.getGoodsId()));
        List<GoodsInstanceDo> resultGoodsInstanceList = goodsInstanceMap.values().stream()
                .map(goodsInstanceGroup -> goodsInstanceGroup.stream()
                        .max(Comparator.comparing(goodsInstance -> goodsInstance.getEffectiveEndTime()))
                        .orElseGet(null))
                .collect(Collectors.toList());

        return buildMemberSubscriptionList(resultGoodsInstanceList, userDo, queryGoodsInstanceReq);
    }


    /**
     * 查询用户的订购列表数量
     *
     * @param queryGoodsInstanceReq
     * @return
     */
    public Long queryContractListSubscriptionCount(QueryGoodsInstanceReq queryGoodsInstanceReq, UserDo userDo,
                                                   List<QueryGoodsInstanceCondition> queryGoodsInstanceConditionList) {
        // UserDo userDo = getAndCheckUser(queryGoodsInstanceReq.getPhoneNumber(), queryGoodsInstanceReq.getUserDomainId());
        // List<QueryGoodsInstanceCondition> queryGoodsInstanceConditionList = buildQueryGoodsInstanceConditionList(userDo, queryGoodsInstanceReq);

        DomainServiceContext userServiceContext =
                new DomainServiceContext(GoodsInstanceServiceId.QUERY_GOODS_INSTANCE);
        Long count = Long.valueOf(0L);
        for (QueryGoodsInstanceCondition queryGoodsInstanceCondition : queryGoodsInstanceConditionList) {
            count += userServiceContext.getCount(queryGoodsInstanceCondition, GoodsInstanceDo.class);
        }
        return count;
    }


    /**
     * 构建响应
     *
     * @param goodsInstanceList
     */
    private List<MemberSubscription> buildMemberSubscriptionList(List<GoodsInstanceDo> goodsInstanceList,
                                                                 UserDo userDo,
                                                                 QueryGoodsInstanceReq req) {

        List<String> goodsIdList = new ArrayList<>();

        // 准备补充字段信息
        for (GoodsInstanceDo goodsInstanceDo : goodsInstanceList) {
            goodsIdList.add(goodsInstanceDo.getGoodsId());

            if (StringUtils.hasLength(goodsInstanceDo.getGoodsPackageId())) {
                goodsIdList.add(goodsInstanceDo.getGoodsPackageId());
            }
        }
        // stopWatch.start("buildOrdersCacheMap");
        List<String> orderIds = goodsInstanceList.stream().map(GoodsInstanceDo::getOrderId).collect(Collectors.toList());

        Map<String, OrderDo> orderCacheMap = buildOrdersCacheMapV2(orderIds, userDo);
        List<GoodsInstanceDo> monthlyGoodsParentGoodsInstanceList = getMonthlyGoodsParentGoodsInstanceList(goodsInstanceList, userDo);
        Map<String, GoodsInstanceDo> packageGoodsInstanceMap = getPackageGoodsInstanceMap(monthlyGoodsParentGoodsInstanceList);
        Map<String, OrderDo> packageGoodsOrdersMap = getPackageGoodsOrdersMap(monthlyGoodsParentGoodsInstanceList, userDo);

        // stopWatch.stop();
        // stopWatch.start("buildGoodsCacheMap");
        //补充商品信息
        // goodId -> goodsDo
        Map<String, GoodsDo> goodsCacheMap = buildGoodsCacheMap(goodsIdList);
        // stopWatch.stop();
        // stopWatch.start("buildRaiSubFailBodyMap");
        // 获取权益订购失败属性
        Map<String, String> orderId2RaiSubFailBodyMap = buildRaiSubFailBodyMap(userDo.getUserId(), orderIds);
        // stopWatch.stop();
        List<MemberSubscription> subscriptions = new ArrayList<>();
        // 按时间分组Id合并
        if (req.isMergeByTimePlanGoodsGroupId()) {
            // stopWatch.start("mergeByTimePlanGoodsGroupId");
            List<List<GoodsInstanceDo>> goodsInstanceDoGroupList = groupByTimePlanGoodsGroupId(goodsInstanceList, goodsCacheMap);
            for (List<GoodsInstanceDo> goodsInstanceDoGroup : goodsInstanceDoGroupList) {
                subscriptions.add(mergeByTimePlanGoodsGroupId(goodsInstanceDoGroup, goodsCacheMap, orderCacheMap, packageGoodsOrdersMap, packageGoodsInstanceMap, orderId2RaiSubFailBodyMap, userDo));
            }
            // stopWatch.stop();
        } else {
            // stopWatch.start("buildMemberSubscription");
            for (GoodsInstanceDo goodsInstanceDo : goodsInstanceList) {
                subscriptions.add(buildMemberSubscription(goodsInstanceDo, goodsCacheMap, orderCacheMap, packageGoodsOrdersMap, packageGoodsInstanceMap, orderId2RaiSubFailBodyMap, userDo));
            }
            // stopWatch.stop();
        }

        if (req.getOrderBySubTime() != null && req.getOrderBySubTime()) {
            subscriptions.sort(Comparator.comparing(MemberSubscription::getSubTime));
        } else {
            subscriptions.sort(Comparator.comparing(MemberSubscription::getSubTime, Comparator.reverseOrder()));
        }


        // 多个条件查询结果截取分页
        if (req.getPageCondition() != null
                && req.getPageCondition().getPageNo() != null
                && req.getPageCondition().getPageSize() != null) {
            PageCondition pageCondition = req.getPageCondition();
            subscriptions = subscriptions.stream()
                    .skip((pageCondition.getPageNo() - 1) * (long) pageCondition.getPageSize())
                    .limit(pageCondition.getPageSize())
                    .collect(Collectors.toList());
        }

        if (req.isQueryChildGoodsActiveInfo()) {
            fillChildGoodInfo(subscriptions, goodsInstanceList, userDo, goodsCacheMap);
        }

        return subscriptions;
    }


    /**
     * 构建响应
     *
     * @param goodsInstanceList
     */
    private List<MemberSubscription> buildMemberSubscriptionListV2(List<GoodsInstanceDo> goodsInstanceList,
                                                                   UserDo userDo,
                                                                   QueryGoodsInstanceReq req) {

        List<String> goodsIdList = new ArrayList<>();

        // 准备补充字段信息
        for (GoodsInstanceDo goodsInstanceDo : goodsInstanceList) {
            goodsIdList.add(goodsInstanceDo.getGoodsId());

            if (StringUtils.hasLength(goodsInstanceDo.getGoodsPackageId())) {
                goodsIdList.add(goodsInstanceDo.getGoodsPackageId());
            }
        }

        // stopWatch.start("buildGoodsCacheMap");
        //补充商品信息
        // goodId -> goodsDo
        Map<String, GoodsDo> goodsCacheMap = buildGoodsCacheMap(goodsIdList);
        // stopWatch.stop();

        List<MemberSubscription> subscriptions = new ArrayList<>();

        // stopWatch.start("buildMemberSubscription");
        for (GoodsInstanceDo goodsInstanceDo : goodsInstanceList) {
            subscriptions.add(buildMemberSubscriptionV2(goodsInstanceDo, goodsCacheMap, userDo));
        }
        // stopWatch.stop();


        subscriptions.sort(Comparator.comparing(MemberSubscription::getSubTime));


        return subscriptions;
    }


    /**
     * 构建响应
     *
     * @param goodsInstanceList
     */
    private List<VipMemberContractSubVO> buildVipMemberContractSubVOList(List<GoodsInstanceDo> goodsInstanceList) {

        List<VipMemberContractSubVO> subscriptions = new ArrayList<>();

        // 准备补充字段信息
        for (GoodsInstanceDo goodsInstanceDo : goodsInstanceList) {
            VipMemberContractSubVO vipMemberContractSubVO = new VipMemberContractSubVO();
            vipMemberContractSubVO.setSubscriptionId(goodsInstanceDo.getGoodsInstanceId());
            vipMemberContractSubVO.setContractId(goodsInstanceDo.getGoodsId());
            vipMemberContractSubVO.setProductType(String.valueOf(goodsInstanceDo.getSaleType()));
            // extension1 --> resourceId
            vipMemberContractSubVO.setExtension1(goodsInstanceDo.getResourceId());
            // beginTime
            vipMemberContractSubVO.setStartTime(goodsInstanceDo.getEffectiveStartTime());
            // 结束时间
            vipMemberContractSubVO.setEndTime(goodsInstanceDo.getEffectiveEndTime());
            vipMemberContractSubVO.setUserId(goodsInstanceDo.getUserId());
            subscriptions.add(vipMemberContractSubVO);
        }

        return subscriptions;
    }


    /**
     * 分组
     *
     * @param goodsInstanceList
     * @param goodsCacheMap
     * @return
     */
    private List<List<GoodsInstanceDo>> groupByTimePlanGoodsGroupId(List<GoodsInstanceDo> goodsInstanceList,
                                                                    Map<String, GoodsDo> goodsCacheMap) {

        if (CollectionUtils.isEmpty(goodsInstanceList)) {
            return Collections.emptyList();
        }

        HashMap<String, List<GoodsInstanceDo>> goodsInstanceGroupCacheMap = new HashMap<>();

        for (GoodsInstanceDo goodsInstanceDo : goodsInstanceList) {
            if (canMergeByTimePlanGoodsGroupId(goodsCacheMap.get(goodsInstanceDo.getGoodsId()))) {
                List<GoodsInstanceDo> goodsInstanceDoGroup =
                        goodsInstanceGroupCacheMap.computeIfAbsent(GOOD_INSTANCE_GROUP_KEY_PREFIX_MERGE + goodsCacheMap
                                .get(goodsInstanceDo.getGoodsId()).getTimePlanGoodsGroupId(), k -> new ArrayList<>());
                goodsInstanceDoGroup.add(goodsInstanceDo);

            } else {
                List<GoodsInstanceDo> goodsInstanceDoGroup = new ArrayList<>();
                goodsInstanceDoGroup.add(goodsInstanceDo);
                goodsInstanceGroupCacheMap.put(GOOD_INSTANCE_GROUP_KEY_PREFIX_NOT_MERGE + goodsInstanceDo.getGoodsInstanceId(),
                        goodsInstanceDoGroup);
            }
        }
        return new ArrayList<>(goodsInstanceGroupCacheMap.values());
    }


    /**
     * 同个权益组合并
     *
     * @param goodsInstanceList
     * @param goodsCacheMap
     * @return
     */
    private MemberSubscription mergeByTimePlanGoodsGroupId(List<GoodsInstanceDo> goodsInstanceList,
                                                           Map<String, GoodsDo> goodsCacheMap,
                                                           Map<String, OrderDo> orderCacheMap,
                                                           Map<String, OrderDo> packageGoodsInstanceOrderMap,
                                                           Map<String, GoodsInstanceDo> packageGoodsInstanceMap,
                                                           Map<String, String> orderId2RaiSubFailBodyMap,
                                                           UserDo userDo) {

        if (CollectionUtils.isEmpty(goodsInstanceList)) {
            return null;
        }

        GoodsInstanceDo firstGoodsInstanceDo = goodsInstanceList.stream()
                .min(Comparator.comparing(goodsInstanceDo -> goodsInstanceDo.getEffectiveStartTime()))
                .get();

        GoodsInstanceDo lastGoodsInstanceDo = goodsInstanceList.stream()
                .max(Comparator.comparing(goodsInstanceDo -> goodsInstanceDo.getEffectiveEndTime()))
                .get();

        MemberSubscription memberSubscription = buildMemberSubscription(firstGoodsInstanceDo, goodsCacheMap, orderCacheMap,
                packageGoodsInstanceOrderMap, packageGoodsInstanceMap, orderId2RaiSubFailBodyMap, userDo);
        memberSubscription.setEndTime(lastGoodsInstanceDo.getEffectiveEndTime());

        return memberSubscription;
    }


    /**
     * 构建返回订购列表
     *
     * @param goodsInstanceList
     * @param userDo
     * @return
     */
    private List<AllContractSubscription> buildAllContractSubscriptionList(List<GoodsInstanceDo> goodsInstanceList, UserDo userDo) {
        List<AllContractSubscription> allContractSubscriptionList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(goodsInstanceList)) {
            Map<String, GoodsDo> goodsCacheMap = buildGoodsCacheMap(goodsInstanceList.stream()
                    .map(goodsInstance -> goodsInstance.getGoodsId()).collect(Collectors.toList()));
            for (GoodsInstanceDo goodsInstanceExtendDo : goodsInstanceList) {
                AllContractSubscription allContractSubscription = new AllContractSubscription();
                GoodsDo goodsDo = goodsCacheMap.get(goodsInstanceExtendDo.getGoodsId());
                if (goodsDo != null) {
                    if (!goodsDo.isSupportUnSubscribe()) {
                        //不支持退订的商品，过滤
                        continue;
                    }
                    allContractSubscription.setProductName(goodsDo.getGoodsName());
                    allContractSubscription.setIsUnSubscribe(goodsDo.getGoodsExt() != null ?
                            goodsDo.getGoodsExt().getIsUnSubscribe() : null);
                }
                // System.out.println("12345test:" + goodsInstanceExtendDo.getEffectiveEndTime());
                // TimeZone timeZone = TimeZone.getDefault();
                // System.out.println("12345timezone:" + timeZone.getID());

                allContractSubscription.setAccount(userDo.getMsisdn());
                allContractSubscription.setChargingType(goodsInstanceExtendDo.getChargeType());
                allContractSubscription.setContractId(goodsInstanceExtendDo.getGoodsId());
                allContractSubscription.setEndTime(goodsInstanceExtendDo.getEffectiveEndTime());
                allContractSubscription.setStartTime(goodsInstanceExtendDo.getEffectiveStartTime());
                allContractSubscription.setSubTime(goodsInstanceExtendDo.getSubTime());
                allContractSubscription.setStatus(goodsInstanceExtendDo.getStateEnum() != null ?
                        String.valueOf(goodsInstanceExtendDo.getStateEnum().getState()) : null);
                allContractSubscription.setProductType(goodsInstanceExtendDo.getSaleType() != null ?
                        String.valueOf(goodsInstanceExtendDo.getSaleType()) : null);
                allContractSubscriptionList.add(allContractSubscription);

            }

        }
        return allContractSubscriptionList;
    }


    /**
     * 构建返回订购结果
     *
     * @param goodsInstanceDo
     * @param goodsCacheMap
     * @param orderCacheMap
     * @param packageGoodsInstanceOrderMap
     * @param packageGoodsInstanceMap
     * @param user
     * @return
     */
    private MemberSubscription buildMemberSubscription(GoodsInstanceDo goodsInstanceDo,
                                                       Map<String, GoodsDo> goodsCacheMap,
                                                       Map<String, OrderDo> orderCacheMap,
                                                       Map<String, OrderDo> packageGoodsInstanceOrderMap,
                                                       Map<String, GoodsInstanceDo> packageGoodsInstanceMap,
                                                       Map<String, String> orderId2RaiSubFailBodyMap,
                                                       UserDo user) {
        MemberSubscription memberSubscription = new MemberSubscription();
        List<NamedParameter> params = new ArrayList<>();

        memberSubscription.setContractID(goodsInstanceDo.getGoodsId());
        memberSubscription.setSubscriptionId(goodsInstanceDo.getGoodsInstanceId());
        memberSubscription.setStatus(goodsInstanceDo.getStateEnum() != null ? String.valueOf(goodsInstanceDo.getStateEnum().getState()) : null);
        memberSubscription.setAccount(user.getMsisdn());
        memberSubscription.setSubTime(goodsInstanceDo.getSubTime());
        memberSubscription.setStartTime(goodsInstanceDo.getEffectiveStartTime());
        memberSubscription.setEndTime(goodsInstanceDo.getEffectiveEndTime());
        memberSubscription.setProductType(goodsInstanceDo.getSaleType() != null ? String.valueOf(goodsInstanceDo.getSaleType()) : null);
        memberSubscription.setPayWay(goodsInstanceDo.getPayWay());
        memberSubscription.setOrderID(goodsInstanceDo.getOrderId());
        memberSubscription.setPrice(Optional.ofNullable(goodsInstanceDo.getDealPrice())
                .map(dealPrice -> String.valueOf(dealPrice.intValue())).orElse(null));
        memberSubscription.setParentSubscriptionId(goodsInstanceDo.getGoodsPackageInstanceId());
        memberSubscription.setParentProductId(goodsInstanceDo.getGoodsPackageId());

        ParameterUtil.addParameter(params, CYCLE_TYPE, String.valueOf(goodsInstanceDo.getCycleType()));
        ParameterUtil.addParameter(params, CYCLE_COUNT, String.valueOf(goodsInstanceDo.getCycleCount()));
        ParameterUtil.addParameter(params, CHARGING_TYPE, String.valueOf(goodsInstanceDo.getChargeType()));
        ParameterUtil.addParameter(params, PARENT_SUBSCRIPTION_ID, goodsInstanceDo.getGoodsPackageInstanceId());
        ParameterUtil.addParameter(params, PARENT_PRODUCT_ID, goodsInstanceDo.getGoodsPackageId());
        ParameterUtil.addParameter(params, CHANNEL_ID, goodsInstanceDo.getChannelId());
        ParameterUtil.addParameter(params, PAY_WAY, String.valueOf(goodsInstanceDo.getPayWay()));
        ParameterUtil.addParameter(params, PAY_TYPE, String.valueOf(goodsInstanceDo.getPayWay()));
        ParameterUtil.addParameter(params, RIGHTS_TYPE, goodsInstanceDo.getRightsType());
        ParameterUtil.addParameter(params, RIGHTS_SERVICE_ID, goodsInstanceDo.getRightsExtInfoObj() != null ?
                goodsInstanceDo.getRightsExtInfoObj().getRightsServiceId() : null);

        if (orderCacheMap.get(goodsInstanceDo.getOrderId()) == null) {
            log.error("[GoodsInstanceDomainService] buildMemberSubscription order is null of : {}", goodsInstanceDo.getGoodsInstanceId());
            throw new ServiceException("buildMemberSubscription order is null");
        }
        // subChannel
        // 如果有商品实例, 没订单
        String subChannel = getSubChannel(goodsInstanceDo, orderCacheMap.get(goodsInstanceDo.getOrderId()));
        ParameterUtil.addParameter(params, SUB_CHANNEL, subChannel);
        // 2024/12/08，新增subWay
        memberSubscription.setSubWay(getSubWay(goodsInstanceDo, subChannel));


        if (goodsInstanceDo.getRenewTime() != null) {
            ParameterUtil.addParameter(params, RENEW_DATE, DateUtil.format(TimeUtil.getDay(goodsInstanceDo.getRenewTime(), -1),
                    DatePattern.NORM_DATETIME_FORMAT));
        }
        if (goodsInstanceDo.getRightsStatusEnum() != null) {
            ServiceUtil.addParameter(params, RIGHTS_STATUS, String.valueOf(goodsInstanceDo.getRightsStatusEnum().getStatus()));
        }

        if (packageGoodsInstanceMap.get(goodsInstanceDo.getGoodsPackageInstanceId()) != null) {
            GoodsInstanceDo packageGoodsInstanceDo = packageGoodsInstanceMap.get(goodsInstanceDo.getGoodsPackageInstanceId());
            if (packageGoodsInstanceDo.getRenewTime() != null) {
                ParameterUtil.addParameter(params, PARENT_RENEW_DATE, DateUtil.format(TimeUtil.getDay(packageGoodsInstanceDo.getRenewTime(), -1),
                        DatePattern.NORM_DATETIME_FORMAT));
            }

            if (packageGoodsInstanceOrderMap.get(packageGoodsInstanceDo.getOrderId()) != null) {
                OrderDo packageOrderDo = packageGoodsInstanceOrderMap.get(packageGoodsInstanceDo.getOrderId());
                Map<String, Object> extInfo = packageOrderDo.getExtInfo();
                if (extInfo != null) {
                    Object parentContractStatus = extInfo.get(OrderDo.ExtInfoKeys.CONTRACT_STATUS);
                    if (parentContractStatus != null) {
                        ParameterUtil.addParameter(params, Constant.PARENT_CONTRACT_STATUS, parentContractStatus.toString());
                    }
                }
            }

        }

        GoodsDo goodsDo = goodsCacheMap.get(goodsInstanceDo.getGoodsId());
        if (goodsDo != null) {
            ServiceUtil.addParameter(params, Constant.PRODUCT_NAME, goodsDo.getGoodsName());

            ParameterUtil.addParameter(params, PRODUCT_NAME, goodsDo.getGoodsName());
            memberSubscription.setServiceID(Optional.ofNullable(goodsDo.getCapacityPolicy())
                    .map(capacityPolicy -> capacityPolicy.getMcsServiceId()).orElse(null));
            memberSubscription.setIsFreeFlow(isFreeFlow(goodsDo) ? IS_FREE_FLOW : IS_NOT_FREE_FLOW);

            GoodsExtVo goodsExtVo = goodsDo.getGoodsExt();
            if (goodsExtVo != null) {

                memberSubscription.setReserve3(goodsExtVo.getHyGoodsSrc());

                ParameterUtil.addParameter(params, OWNER_ID, goodsExtVo.getOwnerId());
                ParameterUtil.addParameter(params, PROV_CUSTOM, goodsExtVo.getProvCustom());
                ParameterUtil.addParameter(params, TIPS_V4, goodsExtVo.getTipsV4());
                ParameterUtil.addParameter(params, RIGHTS_MODEL, goodsExtVo.getRightsModel());
                ParameterUtil.addParameter(params, IS_SUPPORT_UNSUBSCRIBE, goodsExtVo.getIsUnSubscribe());
                ParameterUtil.addParameter(params, DISPLAY_IMAGE_SELECTION, goodsExtVo.getDisplayImageSelection());
                ParameterUtil.addParameter(params, CHILDGOODS_ACTIVE_START_TIME, String.valueOf(goodsExtVo.getChildGoodsActiveStartTime()));
                ParameterUtil.addParameter(params, FAMILY_PACKAGE_GIFT_GOODS_ID, goodsExtVo.getFamilyPackageGiftGoodsId());
                ParameterUtil.addParameter(params, FAMILY_PACKAGE_GIFT_QUOTA, String.valueOf(goodsExtVo.getFamilyPackageGiftQuota()));

                ParameterUtil.addParameter(params, MEMBER_LEVEL, goodsExtVo.getMemberLevel());
            }

            //2024/12/28 新增
            GoodsTimePlanDo timePlan = goodsDo.getTimePlan();
            if (timePlan != null && timePlan.getUnSubTimePlanPolicy() != null) {
                ParameterUtil.addParameter(params, EXPIRY_POLICY, String.valueOf(timePlan.getUnSubTimePlanPolicy().getPolicy()));
            }
        }

        GoodsDo goodsPackage = goodsCacheMap.get(goodsInstanceDo.getGoodsPackageId());
        if (goodsPackage != null) {
            memberSubscription.setParentProductName(goodsPackage.getGoodsName());
            ParameterUtil.addParameter(params, PARENT_PRODUCT_NAME, String.valueOf(goodsPackage.getGoodsName()));
        }

        if (Objects.nonNull(goodsInstanceDo.getExtInfo())) {
            JSONObject map = JSONUtil.parseObj(goodsInstanceDo.getExtInfo());
            ServiceUtil.addParameter(params, Constant.SELFOWNSUB, map.getStr(Constant.SELFOWNSUB));
            ServiceUtil.addParameter(params, Constant.ACTURAL_BONUS_POINT, map.getStr(Constant.ACTURAL_BONUS_POINT));
            ServiceUtil.addParameter(params, Constant.ACTURALPRICE, map.getStr(Constant.ACTURALPRICE));
        }

        // 获取权益订购失败报文
        String raiSubFailBody = orderId2RaiSubFailBodyMap.get(goodsInstanceDo.getOrderId());
        if (StringUtils.hasText(raiSubFailBody)) {
            ServiceUtil.addParameter(params, Constant.FAIL_REASON, raiSubFailBody);
        }

        if (!CollectionUtils.isEmpty(params)) {
            NamedParameterList extensionInfo = new NamedParameterList();
            extensionInfo.setNamedParameters(params);
            memberSubscription.setExtensionInfo(extensionInfo);
        }


        // 获取订单信息上下文
        OrderDo orderDo = orderCacheMap.get(goodsInstanceDo.getOrderId());
        if (orderDo != null && Objects.nonNull(orderDo.getExtInfo())) {
            JSONObject map = JSONUtil.parseObj(orderDo.getExtInfo());
            // 新增展示七天无理由暂停展示参数
            ServiceUtil.addParameter(params, Constant.SEVEN_REFUNDING, map.getStr(Constant.SEVEN_REFUNDING));

            // 新增返回活动id
            ServiceUtil.addParameter(params, Constant.ACTIVITY_ID, map.getStr(Constant.ACTIVITY_ID));

            // 新增返回父合约状态
            ServiceUtil.addParameter(params, OrderDo.ExtInfoKeys.CONTRACT_STATUS, map.getStr(OrderDo.ExtInfoKeys.CONTRACT_STATUS));
        }
        ServiceUtil.addParameter(params, Constant.CHANNELID, goodsInstanceDo.getChannelId());
        memberSubscription.setPayWay(goodsInstanceDo.getPayWay());
        memberSubscription.setUnSubTime(goodsInstanceDo.getUnsubTime());
        memberSubscription.setUpdateTime(goodsInstanceDo.getUpdateTime());
        return memberSubscription;
    }


    /**
     * 构建返回订购结果
     * 去除设置subWay的逻辑，允许order为空了
     *
     * @param goodsInstanceDo
     * @param goodsCacheMap
     * @return
     */
    private MemberSubscription buildMemberSubscriptionV2(GoodsInstanceDo goodsInstanceDo,
                                                         Map<String, GoodsDo> goodsCacheMap,
                                                         UserDo user) {
        MemberSubscription memberSubscription = new MemberSubscription();
        List<NamedParameter> params = new ArrayList<>();

        memberSubscription.setContractID(goodsInstanceDo.getGoodsId());
        memberSubscription.setSubscriptionId(goodsInstanceDo.getGoodsInstanceId());
        memberSubscription.setStatus(goodsInstanceDo.getStateEnum() != null ? String.valueOf(goodsInstanceDo.getStateEnum().getState()) : null);
        memberSubscription.setAccount(user.getMsisdn());

        GoodsInstanceDo goodsInstanceExtendDo = goodsInstanceDo;
        if (goodsInstanceExtendDo != null) {
            memberSubscription.setSubTime(goodsInstanceExtendDo.getSubTime());
            memberSubscription.setStartTime(goodsInstanceExtendDo.getEffectiveStartTime());
            memberSubscription.setEndTime(goodsInstanceExtendDo.getEffectiveEndTime());
            memberSubscription.setProductType(goodsInstanceExtendDo.getSaleType() != null ? String.valueOf(goodsInstanceExtendDo.getSaleType()) : null);
            memberSubscription.setPayWay(goodsInstanceExtendDo.getPayWay());
            memberSubscription.setOrderID(goodsInstanceExtendDo.getOrderId());
            memberSubscription.setPrice(Optional.ofNullable(goodsInstanceExtendDo.getDealPrice())
                    .map(dealPrice -> String.valueOf(dealPrice.intValue())).orElse(null));
            memberSubscription.setParentSubscriptionId(goodsInstanceExtendDo.getGoodsPackageInstanceId());
            memberSubscription.setParentProductId(goodsInstanceExtendDo.getGoodsPackageId());

            ParameterUtil.addParameter(params, CYCLE_TYPE, String.valueOf(goodsInstanceDo.getCycleType()));
            ParameterUtil.addParameter(params, CYCLE_COUNT, String.valueOf(goodsInstanceDo.getCycleCount()));
            ParameterUtil.addParameter(params, CHARGING_TYPE, String.valueOf(goodsInstanceDo.getChargeType()));
            ParameterUtil.addParameter(params, PARENT_SUBSCRIPTION_ID, goodsInstanceDo.getGoodsPackageInstanceId());
            ParameterUtil.addParameter(params, PARENT_PRODUCT_ID, goodsInstanceDo.getGoodsPackageId());
            ParameterUtil.addParameter(params, CHANNEL_ID, goodsInstanceDo.getChannelId());
            ParameterUtil.addParameter(params, PAY_WAY, String.valueOf(goodsInstanceDo.getPayWay()));
            ParameterUtil.addParameter(params, PAY_TYPE, String.valueOf(goodsInstanceDo.getPayWay()));
            ParameterUtil.addParameter(params, RIGHTS_TYPE, goodsInstanceDo.getRightsType());
            // userID
            ParameterUtil.addParameter(params, USER_ID, goodsInstanceDo.getUserId());


            if (goodsInstanceExtendDo.getRenewTime() != null) {
                ParameterUtil.addParameter(params, RENEW_DATE, DateUtil.format(TimeUtil.getDay(goodsInstanceExtendDo.getRenewTime(), -1),
                        DatePattern.NORM_DATETIME_FORMAT));
            }
            if (goodsInstanceDo.getRightsStatusEnum() != null) {
                ServiceUtil.addParameter(params, RIGHTS_STATUS, String.valueOf(goodsInstanceDo.getRightsStatusEnum().getStatus()));
            }
            GoodsDo goodsDo = goodsCacheMap.get(goodsInstanceExtendDo.getGoodsId());
            if (goodsDo != null) {
                ServiceUtil.addParameter(params, Constant.PRODUCT_NAME, goodsDo.getGoodsName());

                ParameterUtil.addParameter(params, PRODUCT_NAME, goodsDo.getGoodsName());
                memberSubscription.setServiceID(Optional.ofNullable(goodsDo.getCapacityPolicy())
                        .map(capacityPolicy -> capacityPolicy.getMcsServiceId()).orElse(null));
                memberSubscription.setIsFreeFlow(isFreeFlow(goodsDo) ? IS_FREE_FLOW : IS_NOT_FREE_FLOW);

                GoodsExtVo goodsExtVo = goodsDo.getGoodsExt();
                if (goodsExtVo != null) {

                    memberSubscription.setReserve3(goodsExtVo.getHyGoodsSrc());

                    ParameterUtil.addParameter(params, OWNER_ID, goodsExtVo.getOwnerId());
                    ParameterUtil.addParameter(params, PROV_CUSTOM, goodsExtVo.getProvCustom());
                    ParameterUtil.addParameter(params, TIPS_V4, goodsExtVo.getTipsV4());
                    ParameterUtil.addParameter(params, RIGHTS_MODEL, goodsExtVo.getRightsModel());
                    ParameterUtil.addParameter(params, IS_SUPPORT_UNSUBSCRIBE, goodsExtVo.getIsUnSubscribe());
                    ParameterUtil.addParameter(params, DISPLAY_IMAGE_SELECTION, goodsExtVo.getDisplayImageSelection());
                    ParameterUtil.addParameter(params, CHILDGOODS_ACTIVE_START_TIME, String.valueOf(goodsExtVo.getChildGoodsActiveStartTime()));
                    ParameterUtil.addParameter(params, FAMILY_PACKAGE_GIFT_GOODS_ID, goodsExtVo.getFamilyPackageGiftGoodsId());
                    ParameterUtil.addParameter(params, FAMILY_PACKAGE_GIFT_QUOTA, String.valueOf(goodsExtVo.getFamilyPackageGiftQuota()));

                }

                //2024/12/28 新增
                GoodsTimePlanDo timePlan = goodsDo.getTimePlan();
                if (timePlan != null && timePlan.getUnSubTimePlanPolicy() != null) {
                    ParameterUtil.addParameter(params, EXPIRY_POLICY, String.valueOf(timePlan.getUnSubTimePlanPolicy().getPolicy()));
                }
            }

            GoodsDo goodsPackage = goodsCacheMap.get(goodsInstanceExtendDo.getGoodsPackageId());
            if (goodsPackage != null) {
                memberSubscription.setParentProductName(goodsPackage.getGoodsName());
                ParameterUtil.addParameter(params, PARENT_PRODUCT_NAME, String.valueOf(goodsPackage.getGoodsName()));
            }
        }

        if (Objects.nonNull(goodsInstanceDo.getExtInfo())) {
            JSONObject map = JSONUtil.parseObj(goodsInstanceDo.getExtInfo());
            ServiceUtil.addParameter(params, Constant.SELFOWNSUB, map.getStr(Constant.SELFOWNSUB));
            ServiceUtil.addParameter(params, Constant.ACTURAL_BONUS_POINT, map.getStr(Constant.ACTURAL_BONUS_POINT));
            ServiceUtil.addParameter(params, Constant.ACTURALPRICE, map.getStr(Constant.ACTURALPRICE));
        }

        // 获取工单属性
        List<WorkOrderDo> workOrderList = ActivationServiceFacade.getWorkOrderAttrList(goodsInstanceDo.getUserId(),
                Collections.singletonList(goodsInstanceDo.getOrderId()), Collections.singletonList(WorkAttrCodeConstant.RAI_SUB_FAIL_BODY));
        Map<String, List<WorkOrderAttrDo>> orderId2workAttrsMap = CollUtil.emptyIfNull(workOrderList).stream()
                .filter(work -> CollUtil.isNotEmpty(work.getWorkOrderAttrDoList()))
                .collect(Collectors.toMap(WorkOrderDo::getOrderId, WorkOrderDo::getWorkOrderAttrDoList, (oldVal, newVal) -> newVal));
        if (CollUtil.isNotEmpty(orderId2workAttrsMap)) {
            List<WorkOrderAttrDo> workOrderAttrDos = orderId2workAttrsMap.get(goodsInstanceDo.getOrderId());
            for (WorkOrderAttrDo attrDo : CollUtil.emptyIfNull(workOrderAttrDos)) {
                if (WorkAttrCodeConstant.RAI_SUB_FAIL_BODY.equals(attrDo.getAttrCode())) {
                    ServiceUtil.addParameter(params, Constant.FAIL_REASON, attrDo.getAttrVal());
                    break;
                }
            }
        }

        if (!CollectionUtils.isEmpty(params)) {
            NamedParameterList extensionInfo = new NamedParameterList();
            extensionInfo.setNamedParameters(params);
            memberSubscription.setExtensionInfo(extensionInfo);
        }


        ServiceUtil.addParameter(params, Constant.CHANNELID, goodsInstanceExtendDo.getChannelId());
        memberSubscription.setPayWay(goodsInstanceExtendDo.getPayWay());
        memberSubscription.setUnSubTime(goodsInstanceExtendDo.getUnsubTime());
        memberSubscription.setUpdateTime(goodsInstanceExtendDo.getUpdateTime());

        memberSubscription.setSubWay(String.valueOf(goodsInstanceExtendDo.getSubWay()));
        memberSubscription.setContractID(goodsInstanceDo.getGoodsId());
        memberSubscription.setParentProductId(goodsInstanceExtendDo.getGoodsPackageInstanceId());
        memberSubscription.setParentSubscriptionId(goodsInstanceExtendDo.getGoodsPackageInstanceId());

        return memberSubscription;
    }

    /**
     * 获取实例订购渠道
     *
     * @param goodsInstanceExtendDo
     * @return
     */
    private String getSubChannel(GoodsInstanceDo goodsInstanceExtendDo, OrderDo orderDo) {
        if ((CharSequenceUtil.isNotEmpty(goodsInstanceExtendDo.getOrderId()) &&
                Constant.BOOS_OREDERID_LENGTH == goodsInstanceExtendDo.getOrderId().length()) || SyncOrderConstant.BOSS_REVERSE_ORDERING.equals(orderDo.getSubChannelId())) {
            return String.valueOf(Constant.BOOS_CHANNEL);
        }
        return String.valueOf(Constant.NO_BOOS_CHANNEL);
    }


    /**
     * 获取实例的subWay字段
     * iSBO的历史逻辑处理
     *
     * @param goodsInstanceExtendDo
     * @return
     */
    private String getSubWay(GoodsInstanceDo goodsInstanceExtendDo, String subChannel) {
        if (SyncOrderConstant.BOSS_SUB_CHANNEL_FLAG.equals(subChannel)) {
            return SyncOrderConstant.ISBO_BOSS_SUB_CHANNEL_FLAG;
        }
        return String.valueOf(goodsInstanceExtendDo.getSubWay());
    }


    /**
     * 判断是否可以按时间计划商品分组Id合并
     *
     * @param goodsDo
     * @return
     */
    private boolean canMergeByTimePlanGoodsGroupId(GoodsDo goodsDo) {
        return goodsDo != null
                && goodsDo.getTimePlan() != null
                && SubTimePlanPolicyEnum.PUT_OFF.equals(goodsDo.getTimePlan().getSubTimePlanPolicy());
    }


    /**
     * 构建商品缓存map
     *
     * @param goodsIdList
     * @return
     */
    public Map<String, GoodsDo> buildGoodsCacheMap(List<String> goodsIdList) {
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return Collections.emptyMap();
        }

        DomainServiceContext goodsServiceContext = new DomainServiceContext(GoodsDo.class, GoodsServiceId.QUERY_GOODS_INFO);
        QueryGoodsCondition queryGoodsCondition = new QueryGoodsCondition();
        queryGoodsCondition.setGoodsIdList(goodsIdList.stream().distinct().collect(Collectors.toList()));
        List<GoodsDo> goodsCacheList = goodsServiceContext.read(queryGoodsCondition, GoodsDo.class);
        if (CollectionUtils.isEmpty(goodsCacheList)) {
            return Collections.emptyMap();
        }

        return goodsCacheList.stream()
                .collect(Collectors.toMap(GoodsDo::getGoodsId, goodsDo -> goodsDo));
    }


    public Map<String, String> buildRaiSubFailBodyMap(String userId, List<String> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyMap();
        }

        Map<String, String> orderId2RaiSubFailBodyMap = new HashMap<>();
        // 每1000条一批处理
        int batchSize = 1000;
        for (int i = 0; i < orderIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, orderIds.size());
            List<String> batchOrderIds = new ArrayList<>(orderIds.subList(i, endIndex));

            // 获取工单属性(权益订购失败原因）
            List<WorkOrderDo> workOrderList = ActivationServiceFacade.getWorkOrderAttrList(userId,
                    batchOrderIds, Collections.singletonList(WorkAttrCodeConstant.RAI_SUB_FAIL_BODY));
            // 构建orderId到权益订购失败报文的映射关系
            Map<String, String> tempOrderId2RaiSubFailBodyMap = CollUtil.emptyIfNull(workOrderList).stream()
                    .filter(work -> CollUtil.isNotEmpty(work.getWorkOrderAttrDoList()))
                    .collect(Collectors.toMap(WorkOrderDo::getOrderId,
                            workOrderDo -> {
                                List<WorkOrderAttrDo> workOrderAttrDos = workOrderDo.getWorkOrderAttrDoList();
                                return workOrderAttrDos.stream()
                                        .filter(attrDo -> WorkAttrCodeConstant.RAI_SUB_FAIL_BODY.equals(attrDo.getAttrCode()))
                                        .findFirst()
                                        .map(WorkOrderAttrDo::getAttrVal)
                                        .orElse(SymbolConstant.EMPTY);
                            }, (oldVal, newVal) -> newVal));

            orderId2RaiSubFailBodyMap.putAll(tempOrderId2RaiSubFailBodyMap);
        }
        return orderId2RaiSubFailBodyMap;
    }


    /**
     * 批量查询，构建订单缓存map
     * 超过100条，切分查询
     *
     * @param orderIdList
     * @param userDo
     * @return
     */
    public Map<String, OrderDo> buildOrdersCacheMapV2(List<String> orderIdList, UserDo userDo) {
        // 检查orderIdList是否为空
        if (CollectionUtils.isEmpty(orderIdList)) {
            return Collections.emptyMap();
        }

        // 检查userDo是否为空
        if (userDo == null || userDo.getUserId() == null) {
            return Collections.emptyMap();
        }

        // 去重
        List<String> distinctOrderIds = orderIdList.stream()
                .filter(Objects::nonNull) // 过滤掉null的订单号
                .distinct()
                .collect(Collectors.toList());

        // 如果去重后订单号列表为空，直接返回空Map
        if (CollectionUtils.isEmpty(distinctOrderIds)) {
            return Collections.emptyMap();
        }

        // 切分订单号列表，每批最多100个
        int batchSize = 100;
        List<List<String>> orderIdBatches = new ArrayList<>();
        for (int i = 0; i < distinctOrderIds.size(); i += batchSize) {
            // 计算当前批次的结束索引
            int end = Math.min(i + batchSize, distinctOrderIds.size());

            // 创建一个新的 ArrayList 来存储当前批次的数据
            List<String> batch = new ArrayList<>(distinctOrderIds.subList(i, end));

            // 将当前批次添加到 orderIdBatches 中
            orderIdBatches.add(batch);
        }

        // 查询并合并结果
        Map<String, OrderDo> resultMap = new HashMap<>();
        DomainServiceContext orderDoContext = new DomainServiceContext(OrderDo.class, OrderServiceId.QUERY_ORDER);

        for (List<String> batch : orderIdBatches) {
            // 检查batch是否为空
            if (CollectionUtils.isEmpty(batch)) {
                continue;
            }

            QueryOrderCondition queryOrderCondition = new QueryOrderCondition();
            queryOrderCondition.setOrderNoList(batch);
            queryOrderCondition.setUserId(userDo.getUserId());

            // 检查查询结果是否为空
            List<OrderDo> orderCacheList = orderDoContext.read(queryOrderCondition, OrderDo.class);
            if (CollectionUtils.isEmpty(orderCacheList)) {
                continue;
            }

            // 将查询结果添加到结果Map中
            orderCacheList.stream()
                    .filter(Objects::nonNull) // 过滤掉null的OrderDo对象
                    .filter(orderDo -> orderDo.getOrderNo() != null) // 过滤掉key为null的项
                    .forEach(orderDo -> resultMap.put(orderDo.getOrderNo(), orderDo)); // 处理key重复时保留后者
        }

        return resultMap;
    }


    /**
     * 设置查询商品实例条件
     *
     * @param userDo
     * @param queryGoodsInstanceReq
     * @return
     */
    public List<QueryGoodsInstanceCondition> buildQueryGoodsInstanceConditionList(UserDo userDo,
                                                                                  QueryGoodsInstanceReq queryGoodsInstanceReq) {
        List<QueryGoodsInstanceCondition> queryGoodsInstanceConditionList = new ArrayList<>();
        // 判断GoodsSalesType和GoodsSalesTypeEnumList都为空，则查询全部
        if (queryGoodsInstanceReq.getGoodsSalesType() == null && CollectionUtils.isEmpty(queryGoodsInstanceReq.getGoodsSalesTypeEnumList())) {
            // 会员、空间商品不按goodIdList过滤
            QueryGoodsInstanceCondition queryMemberAndSpaceGoodsInstanceCondition =
                    buildQueryGoodsInstanceCondition(userDo, queryGoodsInstanceReq,
                            queryGoodsInstanceReq.isFilterByGoodsIdsForMemberAndSpace() ? queryGoodsInstanceReq.getGoodsIdList() : null);
            queryMemberAndSpaceGoodsInstanceCondition.setGoodsSalesTypeList(Arrays.asList(GoodsSalesTypeEnum.MEMBER,
                    GoodsSalesTypeEnum.SPACE));
            // 不需要根据rightsType
            queryMemberAndSpaceGoodsInstanceCondition.setRightsTypeList(null);
            queryGoodsInstanceConditionList.add(queryMemberAndSpaceGoodsInstanceCondition);

            QueryGoodsInstanceCondition queryRightsAndSpaceGoodsInstanceCondition = buildQueryGoodsInstanceCondition(userDo, queryGoodsInstanceReq, queryGoodsInstanceReq.getGoodsIdList());
            queryRightsAndSpaceGoodsInstanceCondition.setGoodsSalesTypeList(Arrays.asList(GoodsSalesTypeEnum.RIGHTS));
            queryGoodsInstanceConditionList.add(queryRightsAndSpaceGoodsInstanceCondition);

        } else if (queryGoodsInstanceReq.getGoodsSalesType() == GoodsSalesTypeEnum.MEMBER ||
                queryGoodsInstanceReq.getGoodsSalesType() == GoodsSalesTypeEnum.SPACE) {
            // 会员、空间商品不按goodIdList过滤
            QueryGoodsInstanceCondition queryMemberAndSpaceGoodsInstanceCondition =
                    buildQueryGoodsInstanceCondition(userDo, queryGoodsInstanceReq,
                            queryGoodsInstanceReq.isFilterByGoodsIdsForMemberAndSpace() ? queryGoodsInstanceReq.getGoodsIdList() : null);
            queryMemberAndSpaceGoodsInstanceCondition.setGoodsSalesTypeList(Collections.singletonList(queryGoodsInstanceReq.getGoodsSalesType()));
            // 不需要根据rightsType
            queryMemberAndSpaceGoodsInstanceCondition.setRightsTypeList(null);
            queryGoodsInstanceConditionList.add(queryMemberAndSpaceGoodsInstanceCondition);
        } else if (queryGoodsInstanceReq.getGoodsSalesType() == GoodsSalesTypeEnum.RIGHTS) {
            QueryGoodsInstanceCondition queryRightsAndSpaceGoodsInstanceCondition = buildQueryGoodsInstanceCondition(userDo, queryGoodsInstanceReq, queryGoodsInstanceReq.getGoodsIdList());
            queryRightsAndSpaceGoodsInstanceCondition.setGoodsSalesTypeList(Collections.singletonList(GoodsSalesTypeEnum.RIGHTS));
            queryGoodsInstanceConditionList.add(queryRightsAndSpaceGoodsInstanceCondition);
        } else if (!CollectionUtils.isEmpty(queryGoodsInstanceReq.getGoodsSalesTypeEnumList())) {
            // 将3个状态封装到一个条件按集合中
            for (GoodsSalesTypeEnum goodsSalesTypeEnum : queryGoodsInstanceReq.getGoodsSalesTypeEnumList()) {
                if (goodsSalesTypeEnum == GoodsSalesTypeEnum.MEMBER || goodsSalesTypeEnum == GoodsSalesTypeEnum.SPACE) {
                    QueryGoodsInstanceCondition condition = buildQueryGoodsInstanceCondition(userDo, queryGoodsInstanceReq,
                            queryGoodsInstanceReq.isFilterByGoodsIdsForMemberAndSpace() ? queryGoodsInstanceReq.getGoodsIdList() : null);
                    condition.setGoodsSalesTypeList(Collections.singletonList(goodsSalesTypeEnum));
                    condition.setRightsTypeList(null);
                    queryGoodsInstanceConditionList.add(condition);
                } else if (goodsSalesTypeEnum == GoodsSalesTypeEnum.RIGHTS) {
                    QueryGoodsInstanceCondition condition = buildQueryGoodsInstanceCondition(userDo, queryGoodsInstanceReq, queryGoodsInstanceReq.getGoodsIdList());
                    condition.setGoodsSalesTypeList(Collections.singletonList(goodsSalesTypeEnum));
                    queryGoodsInstanceConditionList.add(condition);
                }
            }
        }

        return queryGoodsInstanceConditionList;
    }


    /**
     * 设置查询商品实例条件
     *
     * @param userDo
     * @param req
     * @return
     */
    public List<QueryGoodsInstanceCondition> buildQueryGoodsInstanceConditionListV2(UserDo userDo,
                                                                                    QueryGoodsInstanceReq req) {
        List<QueryGoodsInstanceCondition> queryGoodsInstanceConditionList = new ArrayList<>();
        // 会员、空间商品不按goodIdList过滤
        QueryGoodsInstanceCondition queryGoodsInstanceCondition = new QueryGoodsInstanceCondition();
        queryGoodsInstanceCondition.setUserId(userDo.getUserId());
        queryGoodsInstanceCondition.setGoodsInstanceIdList(req.getGoodsInstanceIdList());
        queryGoodsInstanceCondition.setOrderIdList(req.getOrderIdList());
        queryGoodsInstanceCondition.setGoodsIdList(req.getGoodsIdList());
        queryGoodsInstanceConditionList.add(queryGoodsInstanceCondition);
        return queryGoodsInstanceConditionList;
    }

    /**
     * 设置查询商品实例条件
     *
     * @param userDo
     * @param req
     * @param goodsIdList
     * @return
     */
    private QueryGoodsInstanceCondition buildQueryGoodsInstanceCondition(UserDo userDo, QueryGoodsInstanceReq req, List<String> goodsIdList) {
        Date now = new Date();
        QueryGoodsInstanceCondition queryGoodsInstanceCondition = new QueryGoodsInstanceCondition();
        queryGoodsInstanceCondition.setUserId(userDo.getUserId());
        queryGoodsInstanceCondition.setChargeType(req.getChargeType());
        queryGoodsInstanceCondition.setGoodsInstanceIdList(req.getGoodsInstanceIdList());
        queryGoodsInstanceCondition.setRightsTypeList(req.getRightsTypeList());
        queryGoodsInstanceCondition.setExcludeAccountingAddition(true);
        queryGoodsInstanceCondition.setSubTimeSort(SortEnum.DESC);
        if (StringUtils.hasLength(req.getOrderId())) {
            queryGoodsInstanceCondition.setOrderIdList(Arrays.asList(req.getOrderId()));
        }

        if (req.getGoodsSalesType() != null) {
            queryGoodsInstanceCondition.setGoodsSalesTypeList(Arrays.asList(req.getGoodsSalesType()));
        } else {
            queryGoodsInstanceCondition.setGoodsSalesTypeList(Arrays.asList(GoodsSalesTypeEnum.MEMBER,
                    GoodsSalesTypeEnum.SPACE, GoodsSalesTypeEnum.RIGHTS));
        }

        if (req.isQueryPaused()) {
            queryGoodsInstanceCondition.setQueryPaused(true);
            queryGoodsInstanceCondition.setStateList(Arrays.asList(GoodsInstanceStateEnum.PAUSE, GoodsInstanceStateEnum.NORMAL, GoodsInstanceStateEnum.UN_SUB));
        } else {
            queryGoodsInstanceCondition.setStateList(Arrays.asList(GoodsInstanceStateEnum.NORMAL, GoodsInstanceStateEnum.UN_SUB));
        }

        if (!req.isQueryExpired()) {
            queryGoodsInstanceCondition.setEffectiveEndTimeStart(now);
        }


        boolean includeGoodsIdsCondition = false;
        Set<String> includeGoodsIds = new HashSet<>();

        if (!CollectionUtils.isEmpty(goodsIdList)) {
            includeGoodsIdsCondition = true;
            includeGoodsIds.addAll(goodsIdList);
        }

        if (req.isExcludeGoodsWithOwnerId()) {
            //过滤掉有独立空间ownerId的商品
            DomainServiceContext goodsServiceContext = new DomainServiceContext(GoodsDo.class, GoodsServiceId.QUERY_GOODS_INFO);
            QueryGoodsCondition queryGoodsCondition = new QueryGoodsCondition();
            queryGoodsCondition.setWithOwnerId(true);
            List<GoodsDo> goodsWithOwnerId = goodsServiceContext.read(queryGoodsCondition, GoodsDo.class);
            if (!CollectionUtils.isEmpty(goodsWithOwnerId)) {
                Set<String> goodsIdsWithOwnerId = goodsWithOwnerId.stream()
                        .map(goodsDo -> goodsDo.getGoodsId()).collect(Collectors.toSet());
                if (!CollectionUtils.isEmpty(includeGoodsIds) && includeGoodsIdsCondition) {
                    // 取差集
                    includeGoodsIds = includeGoodsIds.stream()
                            .filter(goodsId -> !goodsIdsWithOwnerId.contains(goodsId))
                            .collect(Collectors.toSet());
                } else {
                    queryGoodsInstanceCondition.setExcludeGoodsIdList(goodsWithOwnerId.stream()
                            .map(goodsDo -> goodsDo.getGoodsId()).collect(Collectors.toList()));
                }
            }

        } else if (!CollectionUtils.isEmpty(req.getOwnerIdList())) {
            includeGoodsIdsCondition = true;
            DomainServiceContext goodsServiceContext = new DomainServiceContext(GoodsDo.class, GoodsServiceId.QUERY_GOODS_INFO);
            QueryGoodsCondition queryGoodsCondition = new QueryGoodsCondition();
            queryGoodsCondition.setOwnerIdList(req.getOwnerIdList());
            List<GoodsDo> goodsWithOwnerId = goodsServiceContext.read(queryGoodsCondition, GoodsDo.class);

            if (CollectionUtils.isEmpty(goodsWithOwnerId)) {
                includeGoodsIds.clear();
            } else {
                Set<String> goodsIdsWithOwnerId = goodsWithOwnerId.stream()
                        .map(goodsDo -> goodsDo.getGoodsId()).collect(Collectors.toSet());
                // 取交集
                if (!CollectionUtils.isEmpty(includeGoodsIds)) {
                    includeGoodsIds = includeGoodsIds.stream()
                            .filter(goodsId -> goodsIdsWithOwnerId.contains(goodsId))
                            .collect(Collectors.toSet());
                } else {
                    includeGoodsIds.addAll(goodsIdsWithOwnerId);
                }
            }
        }

        if (req.isOnlyCurrentlyEffective()) {
            queryGoodsInstanceCondition.setEffectiveStartTimeEnd(new Date());
        }

        if (includeGoodsIdsCondition) {
            if (!CollectionUtils.isEmpty(includeGoodsIds)) {
                queryGoodsInstanceCondition.setGoodsIdList(includeGoodsIds.stream().collect(Collectors.toList()));
            } else {
                // 如果没有符合条件的商品，则返回空
                queryGoodsInstanceCondition.setExcludeAllRecord(true);
            }
        }

        if (StringUtils.hasLength(req.getGoodsPackageInstanceId())) {
            queryGoodsInstanceCondition.setGoodsPackageInstanceIdList(Collections.singletonList(req.getGoodsPackageInstanceId()));
        }

        if (StringUtils.hasLength(req.getSubStartTime())) {
            queryGoodsInstanceCondition.setSubTimeStart(DateUtils.format(req.getSubStartTime(), DateUtils.SUB_DATE_PATTERN));
        }

        if (null != req.getOrderBySubTime()) {
            queryGoodsInstanceCondition.setSubTimeSort(req.getOrderBySubTime() ? SortEnum.ASC : SortEnum.DESC);
        }

        return queryGoodsInstanceCondition;
    }


    /**
     * 补充子商品领取信息（组合产品）
     *
     * @param rightsSubscriptionList
     * @param goodsInstanceExtendList
     * @param userDo
     * @param goodsCache
     */
    private void fillChildGoodInfo(List<MemberSubscription> rightsSubscriptionList,
                                   List<GoodsInstanceDo> goodsInstanceExtendList, UserDo userDo,
                                   Map<String, GoodsDo> goodsCache) {
        if (CollectionUtils.isEmpty(rightsSubscriptionList) || CollectionUtils.isEmpty(goodsInstanceExtendList)) {
            return;
        }

        // 收集所有子商品Id
        Map<String, Map<String, List<String>>> childGoodsOfGoodsPackageInstanceMap = new HashMap<>();
        // 停车类商品Id，用于补全子商品领取信息
        Set<String> parkGoodsIds = new HashSet<>();
        for (GoodsInstanceDo goodsInstanceExtendDo : goodsInstanceExtendList) {

            GoodsDo goodsDo = goodsCache.get(goodsInstanceExtendDo.getGoodsId());
            if (goodsDo == null || !goodsDo.isPackageCategory() || !(goodsDo instanceof GoodsPackageDo)) {
                continue;
            }

            GoodsPackageDo goodsPackageDo = (GoodsPackageDo) goodsDo;
            if (CollectionUtils.isEmpty(goodsPackageDo.getChildGoods(true))) {
                continue;
            }

            //groupId -> childGoodsIdList
            Map<String, List<String>> childGoodsGroupMap = goodsPackageDo.getChildGoods(true).stream()
                    .filter(childGoodsDo -> !CollectionUtils.isEmpty(childGoodsDo.getGoodsList()) && MAN_ACTIVE.equals(childGoodsDo.getGoodsActiveType()))
                    .collect(Collectors.toMap(ChildGoodsGroupDo::getGoodsGroupId, childGoodsDo ->
                            childGoodsDo.getGoodsList().stream().map(GoodsDo::getGoodsId).distinct().collect(Collectors.toList())));

            if (!CollectionUtils.isEmpty(childGoodsGroupMap)) {
                childGoodsOfGoodsPackageInstanceMap.put(goodsInstanceExtendDo.getGoodsInstanceId(), childGoodsGroupMap);
            }
            GoodsDo parkGoods = goodsPackageDo.getChildGoodsByProductId(ProductDo.ProductId.PARK_RIGHTS);
            if (parkGoods != null) {
                parkGoodsIds.add(parkGoods.getGoodsId());
            }
        }

        if (CollectionUtils.isEmpty(childGoodsOfGoodsPackageInstanceMap)) {
            return;
        }

        Date now = new Date();
        // 获取子商品的激活信息
        DomainServiceContext validateServiceContext = new DomainServiceContext(QUERY_SUB_VALIDATE_DETAIL_INFO);
        SubRuleValidateCondition subRuleValidateCondition = new SubRuleValidateCondition();
        subRuleValidateCondition.setChildGoodsOfGoodsPackageInstanceMap(childGoodsOfGoodsPackageInstanceMap);
        subRuleValidateCondition.setPhoneNumber(userDo.getMsisdn());
        subRuleValidateCondition.setSubTime(now);
        subRuleValidateCondition.setTriggerTimming(ValidateTriggerTimmingEnum.SUB);
        List<SubRuleValidateResultDo> subRuleValidateResultList = validateServiceContext.read(subRuleValidateCondition, SubRuleValidateResultDo.class);

        if (CollectionUtils.isEmpty(subRuleValidateResultList)) {
            return;
        }

        Map<String, List<SubRuleValidateResultDo>> subRuleValidateResultCache = subRuleValidateResultList
                .stream()
                .filter(subRuleValidateResultDo -> !parkGoodsIds.contains(subRuleValidateResultDo.getGoodsId()))
                .collect(Collectors.groupingBy(SubRuleValidateResultDo::getGoodsPackageInstanceId));


        for (MemberSubscription rightsSubscription : rightsSubscriptionList) {
            //已过期和未生效的不构建领取记录信息
            if (now.after(rightsSubscription.getEndTime()) || now.before(rightsSubscription.getStartTime())) {
                continue;
            }
            List<SubRuleValidateResultDo> subRuleValidateResultDoList = subRuleValidateResultCache.get(rightsSubscription.getSubscriptionId());

            if (CollectionUtils.isEmpty(subRuleValidateResultDoList)) {
                continue;
            }
            Set<String> uniqueGoodsIdSet = new HashSet<>(subRuleValidateResultDoList.size());
            List<ChildProductActiveInfo> childProductActiveInfoList = subRuleValidateResultDoList.stream()
                    .filter(subRuleValidateResultDo -> uniqueGoodsIdSet.add(subRuleValidateResultDo.getGoodsId() + subRuleValidateResultDo.getChildGoodsActiveGroupId()))
                    .map(this::convertToChildProductActiveInfo).collect(Collectors.toList());

            NamedParameterList namedParameterList = rightsSubscription.getExtensionInfo();
            if (namedParameterList == null) {
                namedParameterList = new NamedParameterList();
                rightsSubscription.setExtensionInfo(namedParameterList);
            }
            List<NamedParameter> params = namedParameterList.getNamedParameters();
            if (params == null) {
                params = new ArrayList<>();
                namedParameterList.setNamedParameters(params);
            }
            ParameterUtil.addParameter(params, CHILD_PRODUCTS, JsonUtil.toJson(childProductActiveInfoList));

        }


    }


    /**
     * 转换商品领取信息
     *
     * @param subRuleValidateResultDo
     * @return
     */
    public ChildProductActiveInfo convertToChildProductActiveInfo(SubRuleValidateResultDo subRuleValidateResultDo) {
        ChildProductActiveInfo childProductActiveInfo = new ChildProductActiveInfo();
        childProductActiveInfo.setProductId(subRuleValidateResultDo.getGoodsId());
        childProductActiveInfo.setActiveStrategy(Optional.ofNullable(subRuleValidateResultDo.getChildGoodsActiveStrategy()).map(activeStrategy -> String.valueOf(activeStrategy.getStrategy())).orElse(null));
        childProductActiveInfo.setActiveGroupId(subRuleValidateResultDo.getChildGoodsActiveGroupId());
        childProductActiveInfo.setExpireDate(subRuleValidateResultDo.getChildGoodsCurrentActiveCycleEndTime());
        childProductActiveInfo.setActiveStatus(Optional.ofNullable(subRuleValidateResultDo.getChildGoodsActiveState()).map(state -> String.valueOf(state.getState())).orElse(null));

        if (N_CHOOSE_1_STRATEGY.equals(subRuleValidateResultDo.getChildGoodsActiveStrategy())) {
            childProductActiveInfo.setActiveCount(Optional.ofNullable(subRuleValidateResultDo.getChildGoodsMaxActiveCountInGroup()).map(maxActiveCount -> String.valueOf(maxActiveCount)).orElse(null));
        } else {
            childProductActiveInfo.setActiveCount(Optional.ofNullable(subRuleValidateResultDo.getChildGoodsMaxActiveCount()).map(maxActiveCount -> String.valueOf(maxActiveCount)).orElse(null));
        }

        return childProductActiveInfo;
    }

    /**
     * 获取用户信息
     *
     * @param phoneNumber
     * @param userDomainId
     * @return
     */
    public UserDo getAndCheckUser(String phoneNumber, String userDomainId) {

        log.debug("[APP] getAndCheckUser start. phoneNumber: {}, userDomainId: {}",
                phoneNumber, userDomainId);

        if (!StringUtils.hasLength(phoneNumber) && !StringUtils.hasLength(userDomainId)) {
            log.error("[APP] getAndCheckUser error. phoneNumber and userDomainId is null");
            throw new ServiceException();
        }
        if (ErrorCode.ILLEGAL_TELEPHONE.equals(phoneNumber)) {
            log.error("[APP] getAndCheckUser error. phoneNumber is illegal");
            throw new ServiceException();
        }


        DomainServiceContext userServiceContext = new DomainServiceContext(UserServiceId.USER_LSB_SERVICE);
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setAccount(phoneNumber);
        queryUserCondition.setUserDomainId(userDomainId);
        queryUserCondition.setQueryGotoneParams(false);
        queryUserCondition.setStatusList(Collections.singletonList(UserStatusEnum.NORMAL));

        UserDo userDo = userServiceContext.readFirst(queryUserCondition, UserDo.class);
        if (null == userDo) {
            log.error("[APP] getAndCheckUser error. the user is not exist,msisdn: {}, userDomainId: {}",
                    phoneNumber, userDomainId);
            throw new ServiceException();
        }
        return userDo;
    }


    /**
     * 是否是免流
     *
     * @param goodsDo
     * @return
     */
    private boolean isFreeFlow(GoodsDo goodsDo) {
        //商品是否存在免流产品
        if (goodsDo instanceof GoodsPackageDo) {
            return ((GoodsPackageDo) goodsDo)
                    .getChildGoodsByProductId(BenefitGoodsDo.BenefitNo.BENEFIT_NO_FREE_FLOW_PKG) != null;
        }
        return false;
    }

    public UnSubscribeResp unSubscribe(UnSubscribeReq unSubscribeReq) {
        return subscribeDomainService.unSubscribe(unSubscribeReq);
    }


    /**
     * 获取包月商品的父商品实例列表
     *
     * @param childGoodsInstanceList
     * @param userDo
     * @return
     */
    private List<GoodsInstanceDo> getMonthlyGoodsParentGoodsInstanceList(List<GoodsInstanceDo> childGoodsInstanceList,
                                                                         UserDo userDo) {
        if (CollectionUtils.isEmpty(childGoodsInstanceList) || userDo == null) {
            return Collections.emptyList();
        }

        List<String> packageGoodsInstanceIds = childGoodsInstanceList.stream()
                .filter(instance -> StringUtils.hasLength(instance.getGoodsPackageInstanceId()))
                .filter(instance -> ChargeTypeEnum.MONTHLY.typeEquals(instance.getChargeType()))
                .map(GoodsInstanceDo::getGoodsPackageInstanceId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(packageGoodsInstanceIds)) {
            return Collections.emptyList();
        }

        DomainServiceContext goodsInstanceServiceContext = GoodsInstanceServiceId
                .getDomainServiceContext(GoodsInstanceServiceId.QUERY_GOODS_INSTANCE);
        QueryGoodsInstanceCondition queryCondition = new QueryGoodsInstanceCondition();
        queryCondition.setGoodsInstanceIdList(packageGoodsInstanceIds);
        queryCondition.setUserId(userDo.getUserId());

        return goodsInstanceServiceContext.read(queryCondition, GoodsInstanceDo.class);
    }

    /**
     * 商品实例转换为map<br/>
     * key: 商品实例id<br/>
     * value: 商品实例<br/>
     *
     * @param parentGoodsInstanceList
     * @return
     */
    private Map<String, GoodsInstanceDo> getPackageGoodsInstanceMap(List<GoodsInstanceDo> parentGoodsInstanceList) {
        if (CollectionUtils.isEmpty(parentGoodsInstanceList)) {
            log.info("[APP] getPackageGoodsInstanceMap error. parentGoodsInstanceList is empty");
            return Collections.emptyMap();
        }

        return parentGoodsInstanceList.stream()
                .collect(Collectors.toMap(
                        GoodsInstanceDo::getGoodsInstanceId,
                        goodsInstance -> goodsInstance,
                        (o1, o2) -> o1));
    }

    /**
     * 获取商品实例对应的订单信息<br/>
     * key: 订单id<br/>
     * value: 订单信息<br/>
     *
     * @param parentGoodsInstanceList
     * @param userDo
     * @return
     */
    private Map<String, OrderDo> getPackageGoodsOrdersMap(List<GoodsInstanceDo> parentGoodsInstanceList,
                                                          UserDo userDo) {
        if (CollectionUtils.isEmpty(parentGoodsInstanceList) || userDo == null) {
            log.info("[APP] getPackageGoodsOrdersMap error. parentGoodsInstanceList is empty or userDo is null");
            return Collections.emptyMap();
        }

        List<String> orderIds = parentGoodsInstanceList.stream()
                .map(GoodsInstanceDo::getOrderId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderIds)) {
            log.info("[APP] getPackageGoodsOrdersMap error. orderIds is empty");
            return Collections.emptyMap();
        }

        DomainServiceContext orderServiceContext = OrderServiceId.getOrderDomainServiceContext(OrderServiceId.QUERY_ORDER);
        QueryOrderCondition queryOrderCondition = new QueryOrderCondition();
        queryOrderCondition.setOrderNoList(orderIds);
        queryOrderCondition.setUserId(userDo.getUserId());

        List<OrderDo> orderList = orderServiceContext.read(queryOrderCondition, OrderDo.class);
        if (CollectionUtils.isEmpty(orderList)) {
            log.info("[APP] getPackageGoodsOrdersMap error. orderList is empty");
            return Collections.emptyMap();
        }

        return orderList.stream()
                .collect(Collectors.toMap(OrderDo::getOrderNo, order -> order, (o1, o2) -> o1));

    }
}
