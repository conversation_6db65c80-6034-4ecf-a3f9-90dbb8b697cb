package com.zyhl.yun.member.order.remote;

import com.zyhl.yun.member.jifei.config.FeignConfig;
import com.zyhl.yun.member.order.dto.ManageSubscribeRelationReq;
import com.zyhl.yun.member.order.dto.SyncAppOrderResp;
import com.zyhl.yun.member.order.vo.ManageSubscribeRelationResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

/**
 * 功能描述。旧版vsbo接口代理
 * 包名称:  com.zyhl.yun.member.order.remote
 * 类名称:  VsboClient
 * 类描述:  。
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/11/5 10:21
 */
@FeignClient(name = "vsboClient", url = "${vsbo-url}",configuration = FeignConfig.class)
public interface VsboClient {

    @PostMapping(value = "/cloudSEE/openApi/syncAppOrder", consumes = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE},
            produces = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE})
    SyncAppOrderResp syncAppOrder(String syncAppOrderReq);

    @PostMapping(value = "/cloudSEE/openApi/manageSubscribeRelation", consumes = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE},
            produces = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE})
    ManageSubscribeRelationResp manageSubscribeRelation(@RequestHeader Map<String, String> headers, ManageSubscribeRelationReq syncAppOrderReq);

    // 管理订阅关系 - 返回String格式（兼容SLB客户端）
    @PostMapping(value = "/cloudSEE/openApi/manageSubscribeRelation", consumes = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE},
            produces = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE})
    String manageSubscribeRelationString(@RequestHeader Map<String, String> headers, @RequestBody String syncAppOrderReq);

    // 权益回退
    @PostMapping(value = "/cloudSEE/openApi/rollBackRight", consumes = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE},
            produces = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE})
    String rollBackRight(@RequestHeader Map<String, String> headers, @RequestBody String rollBackRightReq);

    // 管理订阅关系V2
    @PostMapping(value = "/cloudSEE/openApi/manageSubscribeRelationV2", consumes = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE},
            produces = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE})
    String manageSubscribeRelationV2(@RequestHeader Map<String, String> headers, @RequestBody String syncAppOrderReq);

    // 条件查询订阅信息
    @PostMapping(value = "/cloudSEE/openApi/queryConditionSubScription", consumes = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE},
            produces = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE})
    String queryConditionSubScription(@RequestHeader Map<String, String> headers, @RequestBody String queryConditionSubScriptionReq);
}
