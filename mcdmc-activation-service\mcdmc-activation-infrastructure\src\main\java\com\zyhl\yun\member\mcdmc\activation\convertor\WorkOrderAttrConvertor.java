package com.zyhl.yun.member.mcdmc.activation.convertor;

import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderAttrDo;
import com.zyhl.yun.member.mcdmc.activation.po.WorkOrderAttrPo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/06/04 19:14
 */
@Mapper(componentModel = "spring")
public interface WorkOrderAttrConvertor {

    WorkOrderAttrPo toPO(WorkOrderAttrDo workOrderAttrDo);

    WorkOrderAttrDo toDo(WorkOrderAttrPo workOrderAttrPo);

    List<WorkOrderAttrDo> toDoList(List<WorkOrderAttrPo> poList);

}
