package com.zyhl.yun.member.mcdmc.activation.remote.client;

import lombok.Data;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

import java.util.Map;

/**
 * 远程请求体
 *
 * @param <R> 响应体类型
 * <AUTHOR>
 * @since 2024/07/16 13:49
 */
@Data
public class RemoteRequest<R> {
    /**
     * 请求地址
     */
    private String requestUrl;

    /**
     * 请求参数
     */
    private String body;
    /**
     * 请求头
     */
    private Map<String, String> headerMap;

    /**
     * 请求体类型
     */
    private MediaType mediaType;

    /**
     * 请求方法
     */
    private HttpMethod httpMethod;

    /**
     * 响应体类型
     */
    private Class<R> responseClass;

    /**
     * 请求超时时间，单位：秒
     *
     * @apiNote 默认5秒
     */
    private Long timeoutSec;
    private static final Long DEFAULT_TIMEOUT_SEC = 5L;

    private RemoteRequest() {
    }

    public static <R> RemoteRequest<R> getPostInstance(String requestUrl, String body, Map<String, String> headerMap,
                                                       MediaType mediaType, Class<R> responseClass, Long timeoutSec) {
        RemoteRequest<R> request = new RemoteRequest<>();
        request.setRequestUrl(requestUrl);
        request.setBody(body);
        request.setHeaderMap(headerMap);
        request.setMediaType(mediaType);
        request.setHttpMethod(HttpMethod.POST);
        request.setResponseClass(responseClass);
        if (timeoutSec == null || timeoutSec <= 0) {
            request.setTimeoutSec(DEFAULT_TIMEOUT_SEC);
        } else {
            request.setTimeoutSec(timeoutSec);
        }
        return request;
    }

    public static <R> RemoteRequest<R> getPostInstance(String requestUrl, String body, Map<String, String> headerMap,
                                                       MediaType mediaType, Class<R> responseClass) {
        // 默认超时时间5秒
        return getPostInstance(requestUrl, body, headerMap, mediaType, responseClass, DEFAULT_TIMEOUT_SEC);
    }

}
