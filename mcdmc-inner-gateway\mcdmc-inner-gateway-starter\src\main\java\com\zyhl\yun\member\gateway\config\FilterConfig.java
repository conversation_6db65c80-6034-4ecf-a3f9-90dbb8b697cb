package com.zyhl.yun.member.gateway.config;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zyhl.hcy.plugin.auth.AuthConfiguration;
import com.zyhl.hcy.plugin.auth.util.AESUtil;
import com.zyhl.yun.member.common.Constant;
import com.zyhl.yun.member.common.JsonUtil;
import com.zyhl.yun.member.common.NamedParameterList;
import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.domain.mono.Objects;
import com.zyhl.yun.member.common.domain.mono.Tuple;
import com.zyhl.yun.member.common.util.MDCUtils;
import com.zyhl.yun.member.common.util.ParameterUtil;
import com.zyhl.yun.member.common.util.RequestUtil;
import com.zyhl.yun.member.common.util.XmlUtil;
import com.zyhl.yun.member.gateway.AccountAndUserDomainId;
import com.zyhl.yun.member.gateway.ExpressProperties;
import com.zyhl.yun.member.gateway.UserDomainGateway;
import com.zyhl.yun.member.gateway.util.TraceIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.filter.factory.rewrite.ModifyRequestBodyGatewayFilterFactory;
import org.springframework.cloud.gateway.filter.factory.rewrite.ModifyResponseBodyGatewayFilterFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.server.reactive.ServerHttpRequest;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.net.URI;
import java.nio.charset.StandardCharsets;

/**
 * 功能描述。
 * 包名称:  com.zyhl.yun.member.gateway.config
 * 类名称:  FilterConfig
 * 类描述:  filter配置。
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/7/3 17:08
 */
@Configuration
@Slf4j
public class FilterConfig {

    @Resource
    private AuthConfiguration.RequestAuthEncryptConfig requestAuthEncryptConfig;

    @Resource
    private ModifyRequestBodyGatewayFilterFactory modifyRequestBodyGatewayFilterFactory;

    @Resource
    private ModifyResponseBodyGatewayFilterFactory modifyResponseBodyGatewayFilterFactory;

    @Resource
    private ExpressProperties expressProperties;

    @Resource
    private UserDomainGateway userDomainGateway;


    /**
     * 入参解密全局拦截器
     *
     * @return
     */
    @Bean
    @Order(1)
    public GlobalFilter customModifyRequestGlobalFilter() {
        return (exchange, chain) -> {
            return modifyRequestBodyGatewayFilterFactory
                    .apply(modifyRequestFilter())
                    .filter(exchange, chain);
        };
    }


    /**
     * 出参加密全局拦击器
     *
     * @return
     */
    @Bean
    public ModifyRequestBodyGatewayFilterFactory.Config modifyRequestFilter() {
        ModifyRequestBodyGatewayFilterFactory.Config config =
                new ModifyRequestBodyGatewayFilterFactory.Config();

        return config.setRewriteFunction(byte[].class, byte[].class, (exchange, s) -> {
            if (Objects.isNull(s)) {
                return Mono.empty();
            }

            String traceId = TraceIdUtils.getTraceId(exchange.getRequest());
            MDCUtils.initMdc(traceId);

            //打印日志
            URI uri = exchange.getRequest().getURI();
            String reqBody = new String(s, StandardCharsets.UTF_8);
            log.info("[GATEWAY] gateway uri: {}, req: {}", uri, reqBody);

            byte[] bytes;
            ServerHttpRequest request = exchange.getRequest();
            //需要进行解密
            if (requestAuthEncryptConfig.authEncrypted(request)) {
                String secret = requestAuthEncryptConfig
                        .getAuthEncryptInfos()
                        .getAuthEncryptSecret(request);
                bytes = AESUtil.decryptIV(secret, s);
            } else {
                bytes = s;
            }

            // userDomainId转换
            String requestStr = new String(bytes, StandardCharsets.UTF_8);
            Mono<String> afterTransform = userDomainIdTransform(requestStr, request);
            return afterTransform.flatMap(string -> Mono.just(string.getBytes(StandardCharsets.UTF_8)));
        });
    }

    /**
     * userDomainId转换
     *
     * @param requestStr
     * @param request
     * @return
     */
    private Mono<String> userDomainIdTransform(String requestStr, ServerHttpRequest request) {
        String path = request.getPath().pathWithinApplication().value();
        String interfaceName = path.substring(path.lastIndexOf("/") + 1);
        JSONObject entries;
        try {
            entries = JsonUtil.parseObject(requestStr);
        } catch (Exception e) {
            log.warn("requestStr error, please check, requestStr: {},header is {},detail exception is {}", requestStr, RequestUtil.getHeadersStr(), e.getMessage());
            throw new ServiceException("requestStr error, please check. ", e);
        }

        //获取对应的表达式
        String userDomainIdExpress =
                expressProperties.getInterfaceUserDomainIdExpressConfig().get(interfaceName);
        String accountExpress =
                expressProperties.getInterfaceAccountExpressConfig().get(interfaceName);
        //如果没定义则直接返回
        if (CharSequenceUtil.isEmpty(userDomainIdExpress) || CharSequenceUtil.isEmpty(accountExpress)) {
            return Mono.just(requestStr);
        }

        //解析表达式
        String[] userDomainIdSplit = userDomainIdExpress.split("\\|");
        String[] accountSplit = accountExpress.split("\\|");

        if (userDomainIdSplit.length != accountSplit.length) {
            log.error("express error, please check, interfaceName: {}", interfaceName);
            throw new ServiceException("express error, please check");
        }

        return Flux.range(0, userDomainIdSplit.length)
                .flatMap(i -> {
                    AccountAndUserDomainId accountAndUserDomainId = parseRequest(entries, userDomainIdSplit[i], accountSplit[i]);

                    if (i == 0 && request.getHeaders().containsKey(Constant.HttpHeader.USER_DOMAIN_ID)
                            && request.getHeaders().containsKey(Constant.HttpHeader.ACCOUNT)) {
                        String userDomainId = request.getHeaders().get(Constant.HttpHeader.USER_DOMAIN_ID).get(0);
                        String account = request.getHeaders().get(Constant.HttpHeader.ACCOUNT).get(0);
                        if (CharSequenceUtil.isNotEmpty(accountAndUserDomainId.getUserDomainId()) && !userDomainId.equals(accountAndUserDomainId.getUserDomainId())) {
                            log.error("userDomainId basic message not match");
                            return Mono.error(new ServiceException("basic message not match, please check"));
                        }

                        if (CharSequenceUtil.isNotEmpty(accountAndUserDomainId.getAccount()) && !account.equals(accountAndUserDomainId.getAccount())) {
                            log.error("account basic message not match");
                            return Mono.error(new ServiceException("basic message not match, please check"));
                        }

                    }

                    //接口不存在userDomainId入参，不需要进行转换
                    if (CharSequenceUtil.isEmpty(accountAndUserDomainId.getUserDomainId())) {
                        return Mono.just(Tuple.of("", accountSplit[i]));
                    }

                    return userDomainGateway.userDomainId2PhoneNumber(accountAndUserDomainId.getUserDomainId())
                            .map(phone -> Tuple.of(phone, accountSplit[i]));
                })
                .collectList()
                .map(tuples -> {
                    tuples.forEach(tuple -> {
                        if (CharSequenceUtil.isNotEmpty(tuple.getFirst())) {
                            replaceAccount(tuple.getFirst(), entries, tuple.getSecond());
                        }
                    });
                    return XmlUtil.toXml(entries).replace("<JSONObject>", "").replace("</JSONObject>", "");
                });
    }

    /**
     * 替换对应的account
     *
     * @param account
     * @param entries
     * @param expression
     */
    private void replaceAccount(String account, JSONObject entries, String expression) {
        Object expressValue = entries.getByPath(expression);
        if (isSingleValue(expressValue) || Objects.isNull(expressValue)) {
            entries.putByPath(expression, account);
        } else {
            //特殊处理
            NamedParameterList bean = JSONUtil.toBean((JSONObject) expressValue, NamedParameterList.class);
            ParameterUtil.replaceOrAdd(bean.getNamedParameters(), "account", account);
            entries.putByPath(expression, JSONUtil.parseObj(bean));
        }
    }

    /**
     * 解析请求参数里面的account和userDomainId
     *
     * @return
     */
    private AccountAndUserDomainId parseRequest(JSONObject entries, String userDomainIdExpression, String accountExpress) {
        AccountAndUserDomainId accountAndUserDomainId = new AccountAndUserDomainId();

        //解析出接口的入参中的手机号码和userDomainId
        Object expressionValue = entries.getByPath(userDomainIdExpression);
        if (isSingleValue(expressionValue)) {
            accountAndUserDomainId.setUserDomainId(String.valueOf(expressionValue));
        } else {
            //特殊处理
            NamedParameterList bean = JSONUtil.toBean((JSONObject) expressionValue, NamedParameterList.class);
            String userDomainId = ParameterUtil.getParameter(bean, "userDomainId");
            accountAndUserDomainId.setUserDomainId(userDomainId);
        }

        Object accountExpressValue = entries.getByPath(accountExpress);
        if (isSingleValue(accountExpressValue)) {
            accountAndUserDomainId.setAccount(String.valueOf(accountExpressValue));
        } else {
            //特殊处理
            NamedParameterList bean = JSONUtil.toBean((JSONObject) accountExpressValue, NamedParameterList.class);
            String account = ParameterUtil.getParameter(bean, "account");
            accountAndUserDomainId.setAccount(account);
        }
        return accountAndUserDomainId;
    }

    /**
     * 是否是单独的值
     *
     * @param expression
     * @return
     */
    private boolean isSingleValue(Object expression) {
        if (expression instanceof String) {
            return true;
        }
        if (expression instanceof Long) {
            return true;
        }
        return false;
    }
}
