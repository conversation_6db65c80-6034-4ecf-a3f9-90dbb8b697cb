package com.zyhl.yun.member.mcdmc.activation.domain.mq.message;

import lombok.Data;

/**
 * 畅想会员订购权益订购消息
 *
 * <AUTHOR>
 * @date 2023/12/2
 * @description 广告平台外部权益下单进行发送mq
 **/
@Data
public class AdverMessage {

    /**
     * 系统生成的唯一标识
     */
    private String msgID;


    /**
     * 订单id
     */
    private String orderID;

    /**
     * 用户标识
     */
    private String userID;

    /**
     * 商品编码
     */
    private String productID;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 权益状态
     */
    private String status;

    /**
     * 权益生效时间
     */
    private String startTime;

    /**
     * 权益失效时间
     */
    private String endTime;

    /**
     * 权益领取时间
     */
    private String subTime;

    private String userDomainId;

    @Override
    public String toString() {
        return "AdverMessage{" +
                "msgID='" + msgID + '\'' +
                ", orderID='" + orderID + '\'' +
                ", userID='" + userID + '\'' +
                ", productID='" + productID + '\'' +
                ", productName='" + productName + '\'' +
                ", status='" + status + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", subTime='" + subTime + '\'' +
                ", userDomainId='" + userDomainId + '\'' +
                '}';
    }
}
