package com.zyhl.yun.member.common.cache.wrapper;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zyhl.hcy.commons.utils.MD5Util;
import com.zyhl.yun.member.common.JsonUtil;
import com.zyhl.yun.member.common.cache.ConditionCacheable;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
@Slf4j
public class ConditionWrapper implements CacheValueWrapper {


    private String conditionDigest;

    /**
     * po对象的cacheKey
     */
    private List<Serializable> keyList;

    public ConditionWrapper(ConditionCacheable condition, Object keyList) {
        this(getConditionHashKey(condition), keyList);
    }


    public ConditionWrapper(String conditionDigest, Object keyList) {
        this.conditionDigest = conditionDigest;
        if (keyList == null) {
            this.keyList = Collections.emptyList();
        } else if (keyList instanceof List) {
            this.keyList = (List<Serializable>) keyList;
        } else if (keyList instanceof ConditionWrapper) {
            this.keyList = (List<Serializable>) ((ConditionWrapper) keyList).getValue();
        }
    }


    public static ConditionWrapper fromJson(String jsonString) {
        try {
            JSONObject jsonObject = JsonUtil.parseObject(jsonString);
            String conditionDigest = jsonObject.get("conditionDigest", String.class);

            JSONArray keyListJsonArray = jsonObject.getJSONArray("keyList");
            List<String> value = Collections.emptyList();
            if (keyListJsonArray != null) {
                value = keyListJsonArray.toList(String.class);
            }
            return new ConditionWrapper(conditionDigest, value);
        } catch (Exception e) {
            log.warn("[CACHE] parse json error: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] parse json error.", e);
            }
            return null;
        }
    }


    public static String getConditionHashKey(ConditionCacheable conditionCacheable) {
        try {
            return MD5Util.encryptMD5String(JsonUtil.toJson(conditionCacheable));
        } catch (IOException e) {
            log.error("[CACHE] getConditionHashKey error: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] getConditionHashKey error", e);
            }
            return null;
        }
    }

    @Override
    @JsonIgnore
    public Serializable getKey() {
        return conditionDigest;
    }

    @Override
    @JsonIgnore
    public Serializable getValue() {
        return new ArrayList<>(keyList);
    }

    public List<Serializable> getKeyList() {
        return new ArrayList<>(keyList);
    }

}
