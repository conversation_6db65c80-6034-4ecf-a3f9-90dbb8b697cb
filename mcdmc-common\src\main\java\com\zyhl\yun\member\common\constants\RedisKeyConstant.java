package com.zyhl.yun.member.common.constants;

/**
 * <AUTHOR>
 * @date 2024/2/22 10:35
 * @description RedisKey统一管理类
 */
public class RedisKeyConstant {

    // --------------------------用户域二期redis缓存key---------------------------------
    /** 用户域二期用户状态信息缓存token */
    public static final String UD2_USERSTATUSINFO = "vsbo:userdomain2:userStatusInfo:";

    /** 用户域二期海外版本用户缓存 */
    public static final String UD2_OVERSEAS_VERSION_USER = "vsbo:userdomain2:overseasVersionUser:";

    // 亲情号手机号key
    public static final String FAMILY_NUMBER_PHONE_KEY = "familyNumber:phone:%s";
    // 亲情号userDomainId key
    public static final String FAMILY_NUMBER_USER_DOMAIN_ID_KEY = "familyNumber:userDomainId:%s";

    // 亲情号被被绑定者手机号key
    public static final String FAMILY_NUMBER_BIND_PHONE_KEY = "familyNumber:bindPhone:%s";
    public static final String FAMILY_NUMBER_BIND_USER_DOMAIN_ID_KEY = "familyNumber:bindUserDomainId:%s";

    public static final String FAMILY_PACKAGE_MSISDN_KEY = "familyPackage:msisdn:%s";
    public static final String FAMILY_PACKAGE_USER_DOMAIN_ID_KEY = "familyPackage:userDomainId:%s";

    public static final String FAMILY_PACKAGE_BOUND_MSISDN_KEY = "familyPackage:boundMsisdn:%s";
    public static final String FAMILY_PACKAGE_BOUND_USER_DOMAIN_ID_KEY = "familyPackage:boundUserDomainId:%s";

}
