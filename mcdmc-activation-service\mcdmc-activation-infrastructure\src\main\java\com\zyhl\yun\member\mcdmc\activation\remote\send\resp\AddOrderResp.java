package com.zyhl.yun.member.mcdmc.activation.remote.send.resp;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/3 10:37
 * @descrition 集运平台免流订购响应
 */
@Data
@ToString
public class AddOrderResp {
    /**
     * 响应码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应体
     */
    private AddOrderData data;

    /**
     * 是否成功
     */
    private Boolean success;


    /**
     * <AUTHOR>
     * @version 1.0
     * @date 2024/7/3 10:37
     * @descrition 集运平台，免流订购响应消息内容
     */
    @Data
    @ToString
    public class AddOrderData {
        /**
         * 活动ID
         */
        private long activityId;

        /**
         * 来源订单ID
         */
        private String sourceOrderNo;

        /**
         * 手机号码
         */
        private String telephone;
    }
}
