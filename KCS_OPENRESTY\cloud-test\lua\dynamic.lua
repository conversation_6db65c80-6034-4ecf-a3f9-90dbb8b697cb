dynamic={}
-- TODO-2024/9/30: 新增接口鉴权
function dynamic.refresh()
    local headers = ngx.req.get_headers()
    local auth = headers["auth"]
    if not auth or auth ~= "AspireOpenresty12138" then
        -- 鉴权失败
        return 0
    end
    local query_string = ngx.var.query_string
    local args = dynamic.parse_query_string(query_string)
    local type = args.type
    local keyStr = args.keyStr
    ngx.log(ngx.ERR,"type=",type)
    local white_dict = ngx.shared.white_dict
    if type == "add" then
        dynamic.addWhiteList(white_dict,keyStr)
    elseif type == "del" then
        dynamic.delWhiteList(white_dict,keyStr)
    elseif type == 'updateSwitch' then
        -- 开启全局开关，走向新vsbo
        local conf_dict = ngx.shared.conf_dict
        conf_dict:set("globalSwitch",keyStr)
        ngx.say("update globalSwitch successfully,current globalSwitch is ",conf_dict:get("globalSwitch"))
    elseif type == 'delSwitch' then
        -- 关闭全局开关
        local conf_dict = ngx.shared.conf_dict
        conf_dict:delete("globalSwitch")
        ngx.say("delete globalSwitch successfully,current globalSwitch is ",conf_dict:get("globalSwitch"))
    elseif type == 'getSwitch' then
        -- 关闭全局开关
        local conf_dict = ngx.shared.conf_dict
        ngx.say("current globalSwitch is ",conf_dict:get("globalSwitch"))
    elseif type == 'refreshSwitch' then
        local file = require "aspire/file"
        local globalSwitch=file.get_value_by_key("globalSwitch")
        local conf_dict = ngx.shared.conf_dict
        conf_dict:set("globalSwitch",globalSwitch)
        ngx.say("current globalSwitch is ",conf_dict:get("globalSwitch"))
    else
        dynamic.getWhiteList(white_dict)
    end
    return 1
end

function dynamic.addWhiteList(white_dict,keyStr)
    ngx.log(ngx.ERR,"add:keyStr=",keyStr)
    for key in string.gmatch(keyStr, "[^,]+") do
        -- 获取对应的白名单列表
        if key then 
            white_dict:set(key,"exit")
        end 
    end
    dynamic.getWhiteList(white_dict)
end

function dynamic.delWhiteList(white_dict,keyStr)
    ngx.log(ngx.ERR,"del:keyStr=",keyStr)
    for key in string.gmatch(keyStr, "[^,]+") do
        -- 获取对应的白名单列表
        white_dict:delete(key)
    end
    dynamic.getWhiteList(white_dict)
end

function dynamic.getWhiteList(white_dict)
    -- 用于存储键的表
    local keys = {}
    local whiteKeys = white_dict:get_keys()
    -- 遍历字典并将键添加到表中
    for _, key in ipairs(whiteKeys) do
        table.insert(keys, key)
    end
    
    -- 将键合并成一个字符串
    local keys_string = table.concat(keys, ", ")
    ngx.say("Dictionary refreshed successfully,current keys is ",keys_string)
end
-- 将查询字符串转换为键值对
function dynamic.parse_query_string(query_string)
    local args = {}
    for key, value in string.gmatch(query_string, '([^&]+)=([^&]+)') do
        args[key] = value
    end
    return args
end

function dynamic.refreshConfig()
    local headers = ngx.req.get_headers()
    local auth = headers["auth"]
    if not auth or auth ~= "AspireOpenresty12138" then
        -- 鉴权失败
        return 0
    end
    local query_string = ngx.var.query_string
    local args = dynamic.parse_query_string(query_string)
    local type = args.type
    local operator = args.operator
    local configKey = args.configKey
    local configValue = args.configValue
    ngx.log(ngx.ERR,"type=[",type,"], operator=[",operator,"], configKey=[",configKey,"],configValue=[",configValue,"]")
    -- 默认用xml
    local dict=ngx.shared.xml_path_dict;
    if type == "json" then
         dict = ngx.shared.json_path_dict
    end
    if operator == "add" then
        dynamic.addConfig(dict,configKey,configValue)
    elseif operator == "put" then
        dynamic.putConfig(dict,configKey,configValue)
    elseif operator == "del" then
        dynamic.delConfig(dict,configKey,configValue)
    else
        dynamic.getConfigList(dict,configKey)
    end
    return 1
end

function dynamic.addConfig(dict,configKey,configValue)
    local oldConfigValue = dict:get(configKey)
    if oldConfigValue then
        ngx.say("configKey=[",configKey,"] is already exit,current configValue=[",oldConfigValue,"]")
        return
    end
    dict:set(configKey,configValue)
    dynamic.getConfigList(dict,configKey)
end

function dynamic.putConfig(dict,configKey,configValue)
    local oldConfigValue = dict:get(configKey)
    dict:set(configKey,configValue)
    if oldConfigValue then
        ngx.say(ngx.ERR,"configKey=[",configKey,"] is already exit,oldConfigValue=[",oldConfigValue,"],current value =[",configValue,"]")
    else
        dynamic.getConfigList(dict,configKey)
    end
end

function dynamic.delConfig(dict,configKey,configValue)
    local oldConfigValue = dict:get(configKey)
    -- 传入的configValue和内存里的一样才能做删除逻辑
    if oldConfigValue and oldConfigValue ~= configValue then
        ngx.say("configKey=[",configKey,"]:oldConfigValue is not equals inputConfigValue,oldConfigValue=[",oldConfigValue,"],inputConfigValue=[",configValue"]")
        return false
    end
    dict:delete(configKey)
    dynamic.getConfigList(dict,configKey)
    return true
end

function dynamic.getConfigList(dict,configKey)
    local configValues
    local index = 0
    while true do
        local key = dynamic.getNextKey(configKey,index)
        local value=dict:get(key)
        if not value then
            break
        end
        if not configValues then
            configValues = value
        else
            configValues = configValues .. "," .. value
        end
        index = index + 1
    end
    ngx.say("configKey=[",configKey,"],configValue=[",configValues,"]")
end

function dynamic.getNextKey(configKey,index)
    if index < 1 then
        return configKey
    end
    return configKey .. '_' .. index
end

return dynamic