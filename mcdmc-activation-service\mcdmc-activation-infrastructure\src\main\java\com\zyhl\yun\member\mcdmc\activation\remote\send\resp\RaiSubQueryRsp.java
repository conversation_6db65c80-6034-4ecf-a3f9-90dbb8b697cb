package com.zyhl.yun.member.mcdmc.activation.remote.send.resp;

import com.zyhl.yun.member.mcdmc.activation.util.RaiUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/07/22 15:51
 */
@Data
public class RaiSubQueryRsp {
    private ContractRoot contractRoot;

    @Data
    public static class ContractRoot {
        private Head head;
        private Body body;
    }

    @Data
    public static class Head {
        private String apiId;// "65001",
        private String channelCode;// "65041",
        private String transactionId;// "201806281112121200",
        private String reqTime;// "20180628000000",
        private String sign;// "xxxxxxxxx",
        private String version;// "1.0"
    }

    @Data
    public static class Body {
        /**
         * 结果码
         */
        private String resultCode;
        /**
         * 处理结果的信息
         */
        private String resultMsg;
        /**
         * 订单状态
         */
        private String status;
        /**
         * 支付网关记录的交易流水号
         */
        private String requestId;
        /**
         * 实际支付的金额，以分为单位
         */
        private Integer amount;
        /**
         * 用户完成支付的时间yyyyMMddHHmmss
         */
        private String payDate;
        /**
         * 销售品ID
         */
        private String salesId;
        /**
         * 销售品名称
         */
        private String salesName;
        /**
         * 订单列表
         */
        private List<OrderListEntity> orderList;
    }

    @Data
    public static class OrderListEntity {
        /**
         * 主订单项ID
         */
        private String orderId;
        /**
         * 订单项ID
         */
        private String orderItemId;
        /**
         * 主订单状态
         * 主订单状态：0-未处理，1-待支付，2-支付异常，3-待开通，4-开通异常 ，5-订单完成
         */
        private Integer mainStatus;
        /**
         * 开通状态
         * 子订单状态：0-未处理，1-开通成功，2-开通失败，3-超时，4-开通中
         */
        private Integer status;
        /**
         * 数量
         */
        private Integer quantity;
        /**
         * 销售品ID
         */
        private String salesId;
        /**
         * 销售品名称
         */
        private String salesName;
        /**
         * 产品ID
         */
        private String prodId;
        /**
         * 产品ID
         */
        private String prodName;
        private String skuId;
        /**
         * Sku名称
         */
        private String skuName;
        /**
         * 订单完成的时间
         * yyyy-MM-dd HH:mm:ss
         */
        private String updateTime;
        /**
         * 兑换码
         */
        private String redeemCode;
        /**
         * 兑换密码
         */
        private String exchangePassword;
        /**
         * 主订单备注
         */
        private String mainRemark;
        /**
         * 子订单备注
         */
        private String itemRemark;
    }

    public static boolean isSuccess(RaiSubQueryRsp rsp) {
        return rsp != null && rsp.getContractRoot() != null && rsp.getContractRoot().getBody() != null
                && RaiUtil.RECEIVE_SUCCESS_RESULT_CODE.equals(rsp.getContractRoot().getBody().getResultCode())
                && RaiUtil.RECEIVE_ORDER_SUCCESS_STATUS.equals(rsp.getContractRoot().getBody().getStatus());
    }

    @Getter
    @AllArgsConstructor
    public enum MainStatus {
        /**
         * 主订单状态：0-未处理，1-待支付，2-支付异常，3-待开通，4-开通异常 ，5-订单完成
         */
        UNPROCESSED(0, "未处理"),
        PENDING_PAYMENT(1, "待支付"),
        PAYMENT_EXCEPTION(2, "支付异常"),
        PENDING_ACTIVATION(3, "待开通"),
        ACTIVATION_EXCEPTION(4, "开通异常"),
        ORDER_COMPLETED(5, "订单完成");
        private final int code;
        private final String desc;

        public boolean codeEquals(Integer anotherCode) {
            return anotherCode != null && this.code == anotherCode;
        }
    }
}
