package com.zyhl.yun.member.mcdmc.activation.flow.free_flow;

import com.zyhl.yun.member.mcdmc.activation.flow.base.BaseDefaultServiceFlow;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/9 16:55
 * @descrition 集运免流流程
 */
@Component
public class JiyunFlowSpecFlow extends BaseDefaultServiceFlow {


    @Override
    protected boolean isAllowContinueFlow() {
        return true;
    }
}