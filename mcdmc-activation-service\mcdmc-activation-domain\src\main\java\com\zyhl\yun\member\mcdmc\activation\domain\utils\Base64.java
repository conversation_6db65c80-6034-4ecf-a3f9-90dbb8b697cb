/*
 * 文件名：Base64.java
 * 版权：Copyright 2006-2012 Huawei Tech. Co. Ltd. All Rights Reserved.
 * 描述： Base64.java
 * 修改人：z43898
 * 修改时间：2012-3-20
 * 修改内容：新增
 */

package com.zyhl.yun.member.mcdmc.activation.domain.utils;

import java.io.UnsupportedEncodingException;

/**
 * Base64 编解码算法
 *
 * <AUTHOR>
 * @version RCS V100R002 2012-3-20
 * @since RCS V100R002C02
 */
public class Base64 {
    /**
     * BASE64编码使用的ASIIC码范围
     */
    private static final char[] S_BASE64CHAR =
            {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V',
                    'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q',
                    'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '+', '/'};

    private static final char S_BASE64PAD = '=';

    /**
     * BASE64解码使用的byte数组
     */
    private static final byte[] S_DECODETABLE = new byte[128];

    static {
        for (int i = 0; i < S_DECODETABLE.length; i++) {
            S_DECODETABLE[i] = Byte.MAX_VALUE; // 127 0x7F
        }

        for (int i = 0; i < S_BASE64CHAR.length; i++) {
            // 0 to 63
            S_DECODETABLE[S_BASE64CHAR[i]] = (byte) i;
        }

    }

    /**
     * base64编码
     *
     * @param bytes 字节流
     * @return 编码结果
     */
    public static String encode(byte bytes[]) {
        int code = 0;
        StringBuffer sb = new StringBuffer((bytes.length - 1) / 3 << 6);
        for (int i = 0; i < bytes.length; i++) {
            code |= bytes[i] << 16 - (i % 3) * 8 & 255 << 16 - (i % 3) * 8;
            if (i % 3 == 2 || i == bytes.length - 1) {
                sb.append(S_BASE64CHAR[(code & 16515072) >>> 18]);
                sb.append(S_BASE64CHAR[(code & 258048) >>> 12]);
                sb.append(S_BASE64CHAR[(code & 4032) >>> 6]);
                sb.append(S_BASE64CHAR[code & 63]);
                code = 0;
            }
        }

        if (bytes.length % 3 > 0) {
            sb.setCharAt(sb.length() - 1, '=');
        }
        if (bytes.length % 3 == 1) {
            sb.setCharAt(sb.length() - 2, '=');
        }

        return sb.toString();
    }

    /**
     * base64编码
     *
     * @param data 字符串
     * @return 编码结果
     * @throws UnsupportedEncodingException
     */
    public static String encode(String data) throws UnsupportedEncodingException {
        return encode(data.getBytes("UTF-8"));
    }

    /**
     * 指定字符集进行base64编码
     *
     * @param data    字符串
     * @param charset 字符集
     * @return 编码结果
     * @throws UnsupportedEncodingException
     */
    public static String encode(String data, String charset) throws UnsupportedEncodingException {
        try {
            return encode(data.getBytes(charset));
        } catch (UnsupportedEncodingException e) {
            return encode(data.getBytes("UTF-8"));
        }
    }

    /**
     * base64解码
     *
     * @param data base64编码后的字符
     * @return 解码流
     */
    public static byte[] decode(String data) {
        char[] ibuf = new char[4];
        int ibufcount = 0;
        byte[] obuf = new byte[data.length() / 4 * 3 + 3];
        int obufcount = 0;
        for (int i = 0; i < data.length(); i++) {
            char ch = data.charAt(i);
            if (ch == S_BASE64PAD || ch < S_DECODETABLE.length && S_DECODETABLE[ch] != Byte.MAX_VALUE) {
                ibuf[ibufcount++] = ch;
                if (ibufcount == ibuf.length) {
                    ibufcount = 0;
                    obufcount += decode(ibuf, obuf, obufcount);
                }
            }
        }

        if (obufcount == obuf.length) {
            return obuf;
        }

        byte[] ret = new byte[obufcount];
        System.arraycopy(obuf, 0, ret, 0, obufcount);
        return ret;
    }

    /**
     * 从指定字符做解码，输出到目标字节数组
     *
     * @param ibuf 解码原字符流
     * @param obuf 输出流
     * @param wp   偏移
     * @return 解码长度
     */
    private static int decode(char[] ibuf, byte[] obuf, int wp) {
        int outlen = 3;
        if (ibuf[3] == S_BASE64PAD) {
            outlen = 2;
        }

        if (ibuf[2] == S_BASE64PAD) {
            outlen = 1;
        }

        int b0 = S_DECODETABLE[ibuf[0]];
        int b1 = S_DECODETABLE[ibuf[1]];
        int b2 = S_DECODETABLE[ibuf[2]];
        int b3 = S_DECODETABLE[ibuf[3]];
        switch (outlen) {
            case 1:
                obuf[wp] = (byte) (b0 << 2 & 0xfc | b1 >> 4 & 0x3);
                return 1;
            case 2:
                obuf[wp++] = (byte) (b0 << 2 & 0xfc | b1 >> 4 & 0x3);
                obuf[wp] = (byte) (b1 << 4 & 0xf0 | b2 >> 2 & 0xf);
                return 2;
            case 3:
                obuf[wp++] = (byte) (b0 << 2 & 0xfc | b1 >> 4 & 0x3);
                obuf[wp++] = (byte) (b1 << 4 & 0xf0 | b2 >> 2 & 0xf);
                obuf[wp] = (byte) (b2 << 6 & 0xc0 | b3 & 0x3f);
                return 3;
            default:
                throw new RuntimeException("base64 Couldn't decode.");
        }
    }

}
