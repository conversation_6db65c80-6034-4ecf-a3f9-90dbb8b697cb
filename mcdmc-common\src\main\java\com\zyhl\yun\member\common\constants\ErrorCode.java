package com.zyhl.yun.member.common.constants;


/**
 * @Projectname: member
 * @Filename: ErrorCode
 * @Author: zou<PERSON><PERSON>
 * @Data: 2023/7/19 14:24
 * @Description: member模块下ErrorCode业务逻辑实现
 */
public interface ErrorCode {
    String ORDER_NOT_EXISTS_MESSAGE_CN = "订单不存在";
    /**
     * 产品不存在
     */
    String PRODUCT_NOT_EXISTS_MESSAGE_CN = "产品不存在";
    /**
     * 订购关系不存在
     */
    String SUBCONTRACT_NOT_EXISTS_MESSAGE_CN = "订购关系不存在";
    /**
     * 订购关系已经退订，周期末失效
     */
    String SUBSCRIPTION_HAS_BEEN_UNSUBSCRIBED_DESC_CN = "订购关系已经退订，周期末失效";

    /**
     * 积分支付不允许退订
     */
    String BONUS_POINTS_PAY_NOT_UNSUB_DESC_CN = "积分支付不允许退订";
    /**
     * 退订按次产品失败,mcsServiceSubId为空
     */
    String UNSUB_FAIL_DESC_CN = "退订按次产品失败,mcsServiceSubId为空";

    /**
     * BMP查询用户不存在
     */
    String ACCOUNT_NO_EXIST = "9430";

    /**
     * BMP调用失败
     */
    String BMP_FAIL = "to BMP getUserAccount fail.";

    /**
     * OMP调用失败
     */
    String OMP_FAIL = "to OMP manageAccount fail.";

    /**
     * 成功
     */
    String SUCCESS = "0";

    /**
     * 会员失败业务状态码
     */
    String FAIL = "-1";


    /**
     * 注释内容
     */
    String SUCCESS_MESSAGE = "SUCCESS";

    /**
     * 成功内容响应文案
     */
    String SUCCESS_MESSAGE_CONTEXT = "已开通，无须重复开通";

    /**
     * 退订成功
     */
    String SUCCESS_UNSUBSCRIBE = "000";

    /**
     * 注释内容
     */
    String SUCCESS_UNSUBSCRIBE_MESSAGE = "退订成功";

    /**
     * 退订失败
     */
    String FAILED_UNSUBSCRIBE = "001";

    /**
     * 注释内容
     */
    String FAILED_UNSUBSCRIBE_MESSAGE = "退订失败";

    /**
     * 退订失败
     */
    String FAILED_RESUBSCRIBE = "002";

    /**
     * 注释内容
     */
    String FAILED_RESUBSCRIBE_MESSAGE = "重试订购失败";

    /**
     * 处理出错
     */
    String FAILED = "**********";

    String SIGN_FAILED = "20003";
    String SIGN_FAILED_MSG = "签名失败";

    /**
     * 该用户订购全球通套餐等级不匹配
     */
    String PRODUCT_CONTRACT_NOTMATCH_MSG = "userLevel and gotoneGrade no match!";

    /**
     * 注释内容
     */
    String FAILED_MESSAGE = "Processing error: ";

    /**
     * 参数错误
     */
    String ILLEGAL_PARAMETER = "1809111400";

    /**
     * 注释内容
     */
    String ILLEGAL_PARAMETER_MESSAGE = "Illegal_parameter: ";

    /**
     * 订单重复
     */
    String DUPLICATE_ORDER = "9015";

    /**
     * 请求报文为空
     */
    String EMPTY_BODY = "1809010029";

    /**
     * 注释内容
     */
    String EMPTY_BODY_MESSAGE = "Empty request body!";

    /**
     * 请求报文为空
     */
    String EMPTY_RESPONSE = "1809120016";

    /**
     * 注释内容
     */
    String EMPTY_RESPONSE_MESSAGE = "Empty response content!";


    /**
     * 没有该接口
     */
    String NO_SUCH_INTERFACE = "1809112404";

    /**
     * 注释内容
     */
    String NO_SUCH_INTERFACE_MESSAGE = "No such interface name, please check!";

    /**
     * 没有该合约
     */
    String NO_SUCH_CONTRACT = "1809020004";

    /**
     * 该合约已存在
     */
    String CONTRACT_EXISTED = "1809020003";

    /**
     * 注释内容
     */
    String NO_SUCH_CONTRACT_MESSAGE = "No such contractID, please check!";

    /**
     * 产品正在使用中,无法删除
     */
    String PRODUCT_USING = "1809099999";

    /**
     * 注释内容
     */
    String PRODUCT_USING_MESSAGE = "the product is being using and can not be deleted!";

    /**
     * 数据库错误
     */
    String DATABASE_ERROR = "1809000001";

    /**
     * 注释内容
     */
    String DATABASE_ERROR_MESSAGE = "there is something wrong with the database,please check!";

    /**
     * 履约失败
     */
    String PERFORM_CONTRACT_FAILED = "**********";

    /**
     * 注释内容
     */
    String PERFORM_CONTRACT_FAILED_MESSAGE = "Perform the contract failed!";

    /**
     * 注释内容
     */
    String UNSUBSCRIBE_PERFORM_CONTRACT_FAILED_MESSAGE = "Unsubscribe perform the contract failed!";

    /**
     * 删除订单失败
     */
    String DELETE_ORDER_FAILED = "**********";

    /**
     * 注释内容
     */
    String DELETE_ORDER_FAILED_MESSAGE = "Delete order failed!";

    /**
     * Unauthorized/Unauthentication: 1、IP鉴权失败 2、用户账号鉴权失败
     */
    String UNAUTHORIZED = "1809111401";

    /**
     * 注释内容
     */
    String UNAUTHORIZED_MESSAGE = "Authorization Failed!";

    /**
     * 鉴权失败错误码
     */
    String X_NE_UNAUTHORIZED_MESSAGE = "X_NE_AUTH_DSDP Authorization Failed!";

    /**
     * 注释内容
     */
    String IP_UNAUTHORIZED_MESSAGE = "check contract request ip address is not in ipwhitelist!";

    /**
     * 注释内容
     */
    String SIGNERROR_MESSAGE = "Input Sign is error.";

    /**
     * 系统内部错误
     */
    String SERVICE_INTERNAL_ERROR = "**********";

    /**
     * 注释内容
     */
    String SERVICE_INTERNAL_ERROR_MESSAGE = "Service internal error!";

    /**
     * 参数检查错误码（通用）
     */
    String PARAMETER_CHECK_ERROR = "1809111400";

    /**
     * 注释内容
     */
    String PARAMETER_CHECK_ERROR_MESSAGE = "Parameter check error!";

    /**
     * 请求超时
     */
    String REQUEST_TIOMEOUT = "1809120004";

    /**
     * 订单有效中无法删除
     */
    String ORDER_VALID_CANNOT_DELETE = "1809125801";

    /**
     * 注释内容
     */
    String ORDER_VALID_CANNOT_DELETE_MESSSAGE = "order is valid cannot be deleted";

    /**
     * 注释内容
     */
    String REQUEST_TIOMEOUT_MESSSAGE = "request timeout";

    /**
     * Unauthorized1/Unauthentication1: Sign进行匹配，如果匹配失败;who值不是12
     */
    String UNAUTHORIZED1 = "1809111401";

    /**
     * 注释内容
     */
    String UNAUTHORIZED_MESSAGE1 = "Input Sign is error.";

    /**
     * 注释内容
     */
    String UNAUTHORIZED_MESSAGE2 = "Input who is not 12.";

    /**
     * 注释内容
     */
    String UNAUTHORIZED_MESSAGE3 = "Input who can not be 12.";

    /**
     * 注释内容
     */
    String UNAUTHORIZED_MESSAGE4 = "Input who can not be 13.";

    /**
     * 注释内容
     */
    String UNAUTHORIZED_MESSAGE5 = "Input who should be 13.";

    /**
     * 注释内容
     */
    String UNAUTHORIZED_MESSAGE6 = "No subscribeRelation , please check parameter!";

    /**
     * 注释内容
     */
    String UNAUTHORIZED_MESSAGE7 = "No subscribeRelationRecords , please check parameter!";

    /**
     * 注释内容
     */
    String UNAUTHORIZED_MESSAGE8 = "Input x_ne_auth_dsdp is error.";

    /**
     * 注释内容
     */
    String UNAUTHORIZED_MESSAGE9 =
            "Input productOfferingID should be 0 when productOfferingID is in querySubscribeRelationV1.";

    /**
     * 注释内容
     */
    String UNAUTHORIZED_MESSAGE10 = "Input who should be 21.";

    /**
     * 注释内容
     */
    String UNAUTHORIZED_MESSAGE11 = "Input who can not be 21.";

    /**
     * 创建会员订购关系失败
     */
    String CREATE_SUBSCRIBRE_FAILED = "**********";

    /**
     * 注释内容
     */
    String CREATE_SUBSCRIBRE_MESSAGE = "Create subscribe relation failed!";

    /**
     * 已存在包月订购的订购关系
     */
    String CREATE_HYSUBSCRIBREM_MESSAGE = "Create subscribe relation failed! Monthly subscription relationship exists";

    /**
     * 已存在按次订购的订购关系
     */
    String CREATE_HYSUBSCRIBREN_MESSAGE = "Create subscribe relation failed! Times-based subscription relationship exists";

    /**
     * 注释内容
     */
    String ORDERID_IS_NULL = "**********";

    /**
     * 注释内容
     */
    String ORDERID_IS_NULL_MESSAGE = "orderID is null!";

    /**
     * 注释内容
     */
    String USERID_IS_NULL = "**********";

    /**
     * 注释内容
     */
    String USERID_IS_NULL_MESSAGE = "userID is null!";

    /**
     * 注释内容
     */
    String OLDACCOUNT_IS_NULL_MESSAGE = "oldAccount is null!";

    /**
     * 注释内容
     */
    String NEWACCOUNT_IS_NULL_MESSAGE = "newAccount is null!";

    /**
     * 注释内容
     */
    String PRODUCTOFFERINGID_IS_NULL = "**********";

    /**
     * 注释内容
     */
    String PRODUCTOFFERINGID_IS_NULL_MESSAGE = "productofferingID is null!";

    /**
     * 注释内容
     */
    String WHO_IS_NULL_MESSAGE = "who is null!";

    /**
     * 注释内容
     */
    String PRODUCTOFFERINGID_IS_NOT_EXIST = "productOfferingID is not exist";

    /**
     * 注释内容
     */
    String INPUT_PARAMETER_TYPE_IS_ERROR = "Input parameter type is error: ";

    /**
     * 手机号不合法
     */
    String ILLEGAL_TELEPHONE = "Illegal_parameter";

    /**
     * 注释内容
     */
    String FAILED_UNSUBSCRIBE_SYNC = "**********";

    /**
     * 注释内容
     */
    String FAILED_UNSUBSCRIBE_SYNC_MESSAGE = "Failed to unsubscribe.";

    /**
     * 注释内容
     */
    String FAILED_OUTFEE_SYNC = "**********";

    /**
     * 注释内容
     */
    String FAILED_OUTFEE_SYNC_MESSAGE = "Failed to outFee.";

    /**
     * 注释内容
     */
    String FAILED_ACTIVATE_SYNC = "1809120005";

    /**
     * 注释内容
     */
    String FAILED_ACTIVATE_SYNC_MESSAGE = "Failed to activation.";

    /**
     * 同类型订单不存在错误码
     */
    String SAME_ORDER_TYPER_NOT_EXISIT = "1809125802";

    /**
     * 同类型订单不存在错误描述
     */
    String SAME_ORDER_TYPER_NOT_EXISIT_MESSAGE = "The same type order does not exist.";

    /**
     * 产品不支持退订错误码
     */
    String PRODUCT_NOT_SUPPORT_UNSUBSCRIBE = "1809220001";

    /**
     * 产品不支持退订错误码
     */
    String PRODUCT_NOT_SUPPORT_UNSUBSCRIBE_MESSAGE = "product not support unsubscribe";

    /**
     * 产品查询失败
     */
    String PRODUCT_QUERY_FAILED = "product query failed";

    /**
     * 产品不存在
     */
    String CONTRACT_NOT_EXISTS = "1809010017";

    /**
     * 产品不存在
     */
    String CONTRACT_NOT_EXISTS_MESSAGE = "contract not exists";

    /**
     * 父产品reserve19为空
     */
    String PARENT_PRODUCT_RESERVE19_IS_NULL = "1809010019";

    /**
     * 父产品reserve19为空
     */
    String PARENT_PRODUCT_RESERVE19_IS_NULL_MESSAGE = "parent product reserve19 is null";

    /**
     * 销售类型不存在
     */
    String SALE_NOT_EXISTS_MESSAGE = "sale not exists";

    /**
     * 订单不存在错误码
     */
    String ORDER_NOT_EXISTS = "1809020001";

    /**
     * 订单不存在错误描述
     */
    String ORDER_NOT_EXISTS_MESSAGE = "Order is not exists";

    /**
     * 用户手机号码已经存在
     */
    String USER_TELPHONE_ALREADY_EXISTS = "1809120003";

    /**
     * 订购渠道受限
     */
    String ORDER_CHANNEL_LIMIT = "**********";

    /**
     * 订购渠道受限错误描述
     */
    String ORDER_CHANNEL_LIMIT_MESSAGE = "Order Channel isn’t match";

    /**
     * TPS超限错误码
     */
    String TPS_LIMIT_EXCEED = "180911999";

    /**
     * TPS超限错误描述
     */
    String TPS_LIMIT_EXCEED_MSG = "TPS limit exceed";

    /**
     * 订单状态错误
     */
    String ORDER_STATUS_ERROR_MSG = "order status error";

    /**
     * 订购关系不存在
     */
    String SUBCONTRACT_NOT_EXISTS = "1809030017";


    /**
     * 营销赠送的家庭云空间套餐订购关系当月不允许退订
     */
    String FAMILY_PACKAGE_GIFT_BANNED_UNSUB = "1809030020";

    /**
     * 订购关系不存在
     */
    String SUBCONTRACT_NOT_EXISTS_MESSAGE = "subRelation not exists";

    /**
     * 没有订购操作历史记录
     */
    String NO_OPERATION_HISTORY_ORDERED = "No operation history ordered";

    /**
     * 字段不可为空
     */
    String EMPTY_PARAMETER_MESSAGE = "parameter can not be empty: ";

    /**
     * 产品不存在
     */
    String PRODUCT_NOT_EXISTS_MESSAGE = "product not exists";

    /**
     * 产品不存在
     */
    String PARENT_PRODUCT_NOT_EXISTS_MESSAGE = "parent product not exists";

    /**
     * 包含特定省份的产品不存在
     */
    String PRODUCT_NOT_EXISTS_MESSAGE_SPECIAL_PROVCODE = "product not exists with the user's provcode";
    String PRODUCT_NOT_EXISTS_MESSAGE_SPECIAL_PROVCODE_CODE = "1809120017";

    /**
     * buyType枚举值
     */
    String ILLEGAL_BUYTYPE_MESSAGE = "buyType. should be a value in [1,2,3,4,78,79]";

    /**
     * accessSource枚举值
     */
    String ILLEGAL_ACCESSSOURCE_MESSAGE = "accsessSource. should be a value in ";

    /**
     * startTime不应该大于当前时间
     */
    String ILLEGAL_STARTTIME_MESSAGE = "startTime. can not greater than current time";

    /**
     * endTime应该大于开始时间
     */
    String ILLEGAL_ENDTIME_MESSAGE = "endTime. should be greater than startTime";

    /**
     * endTime或者startTime有一个无法解析为Date对象
     */
    String ILLEGAL_TIMESTRING_MESSAGE = "startTime or endTime. can not parse to a Date value";

    /**
     * 时间转换异常
     */
    String ILLEGAL_HYTIMESTRING_MESSAGE = "startTime 、 subTime or updateTime. can not parse to a Date value";
    /**
     * 订单重复支付
     */
    String ORDER_REPEAT_PAID = "1809013100";

    /**
     * 订单重复支付描述
     */
    String ORDER_REPEAT_PAID_MESSAGE = "Order repeat paid";

    /**
     * 不支持的支付方式
     */
    String CHARGETYPE_NOT_BEEN_SUPPORTTED = "1809013200";


    /**
     * 订购关系冲突(包月与按次产品不能同时订购)
     */
    String SBUSCRIPTION_REL_CONFLICT = "1809013400";

    /**
     * 点播超出订购次数限制
     */
    String OVER_SUB_COUNT = "1809013402";

    /**
     * 点播超出订购次数限制
     */
    String OVER_SUB_COUNT_DESC = "Subscribe fail, because the anci product over subscribe count.";

    /**
     * 超过当日订购限额
     */
    String TODAY_QUOTA_EXCEED = "1809013403";

    /**
     * 超过当日订购限额
     */
    String TODAY_QUOTA_EXCEED_DESC = "Today quota exceed.";

    /**
     * 渠道参数为空
     */
    String CHANNEL_ID_IS_NULL = "1809013404";

    /**
     * 渠道参数为空
     */
    String CHANNEL_ID_IS_NULL_DESC = "Channel id param is null.";

    /**
     * 退订失败
     */
    String UNSUBSCRIBE_FAIL_GOTONE = "**********";

    /**
     * 重复退订
     */
    String DUPLICATE_UNSUBSCRIBE_GOTONE = "1809030018";

    /**
     * 重复退订描述
     */
    String DUPLICATE_UNSUBSCRIBE_GOTONE_MSG = "duplicate unsubscribe";

    /**
     * 订购关系冲突描述
     */
    String SBUSCRIPTION_REL_CONFLICT_DESC = "Subscription relationship is conflict,"
            + "the monthly product can not be ordered with times, and the month product can not be order with other month product which status is normal";

    /**
     * 激活策略未配置描述
     */
    String SBUSCRIPTION_STRATEGY_CONFIG_DESC = "parent product does not config child product active stratege，child product cannot be actived";

    /**
     * 激活策略冲突描述
     */
    String SBUSCRIPTION_ACTIVE_CONFLICT_DESC = "child product only active once per monthly";

    /**
     * 订购关系不止一条
     */
    String SBUSCRIPTION_MORE_THAN_ONE = "1809013401";

    /**
     * 订购关系不止一条
     */
    String SBUSCRIPTION_MORE_THAN_ONE_DESC = "Subscription relationship for familyCloud is more than one";

    /**
     * 不支持的支付方式描述
     */
    String DESC_CHEARGETYPE_NOT_SUPPORT = "the chargeType is not support by the product";

    /**
     * 已存在绑定关系
     */
    String BINGING_RELATUINSHIP_ALREADY_EXISTS = "1809120007";
    /**
     * 被邀请者存在绑定关系
     */
    String BINGING_RELATUINSHIP_ALREADY_EXISTS_INVITED = "1809121116";
    /**
     * 当前自然月已经绑定过相关亲情号状态码
     */
    String BINGING_RELATUINSHIP_ALREADY_EXISTS_CODE = "**********";
    /**
     * 当前自然月已经绑定过相关亲情号
     */
    String BINGING_RELATUINSHIP_ALREADY_EXISTS_DESC = "the inviting number is already binded in month";

    /**
     * 已经存在绑定关系
     */
    String BINDING_RELATIONSHIP = "binding relationship already exists";

    /**
     * 赠送人没有订购全球通
     */
    String ACCOUNT_NO_ORDER_GOTONE = "**********";

    /**
     * 赠送人没有订购全球通描述
     */
    String ACCOUNT_NO_ORDER_GOTONE_DESC = "the donor did not order gotone";

    /**
     * 未联调接口不允许使用
     */
    String INTERFACEFILTER_ERROR = "**********";

    /**
     * 未联调接口不允许使用
     */
    String INTERFACEFILTER_ERROR_MSG = "unjoined interface is not allowed";

    /**
     * 用户号码状态校验失败返回码
     */
    String USER_STATUS_CHECK_FAILD = "**********";

    /**
     * 用户号码状态校验失败返回描述
     */
    String USER_STATUS_CHECK_FAILD_DESC = "user status check faild";

    /**
     * 用户业务资格校验失败返回码
     */
    String USER_QUALIFICATION_CHECK_FAILD = "**********";

    /**
     * 用户业务资格校验失败返描述
     */
    String USER_QUALIFICATION_CHECK_FAILD_DESC = "user qualification check faild";

    /**
     * 产品非法
     */
    String SUB_PRODUCT_INVALID = "product is invalid";

    /**
     * 赠送订购关系接口，用户在bmp不存在
     */
    String USER_NOT_EXIST_BMP = "**********";

    /**
     * 赠送订购关系接口，用户在bmp不存在
     */
    String USER_NOT_EXIST_BMP_MSG = "The user is not a bmp user.";

    /**
     * 赠送订购关系接口，用户在bmp不存在
     */
    String USER_NOT_EXIST_BMP_MSG_OTHER = "Failed to invoke BMP getUserAccount .";

    /**
     * 激活订单校验失败描述
     */
    String ACTIVE_FAIL_MSG =
            "Activation is not allowed，Because the value of isFixesSubscribe is not 1 and the order status is 1 or 2 or 8.";

    /**
     * 积分产品请求必填参数为空
     */
    String ILLEGAL_BONUS_POINT_MESSAGE =
            "Mandatory parameters [selfOwnSub , acturalBonusPoint] for bonus point payment are empty or illegal. ";

    /**
     * selfOwnSub枚举值非法
     */
    String SELFOWNSUB_VALUE_INVALID = "The value of selfOwnSub is invalid. ";

    /**
     * spaceExtendType枚举值非法
     */
    String SPACE_EXTEND_TYPE_INVALID = "The value of spaceExtendType is invalid. ";

    /**
     * spaceExtendDay非法
     */
    String SPACE_EXTEND_DAY_INVALID = "The value of spaceExtendDay is invalid. ";


    /**
     * 请求扩展参数非法
     */
    String RETURN_EXTINFO_TYPE_ILLEGAL = "Request ExtensionInfo[ isReturnExtInfo ] illegal.";

    /**
     * 请求扩展参数非法
     */
    String ILLEGAL_REQ_REVERSEPERIODUNSUB = "Request ExtensionInfo[ reversePeriodUnSub ] illegal.";

    /**
     * 积分产品订购，实际支付积分值请求必填参数为空
     */
    String ILLEGAL_ACTURALPRICE_MESSAGE =
            "Mandatory parameters acturalPrice for bonus point payment are empty or illegal. ";

    /**
     * 按次退订，参数不能为空
     */
    String UNSUBSCRIPTION_BY_TIMES_PARAMTER = "unsub by times parameter can not be empty: ";

    /**
     * 订购关系已经退订，周期末失效
     */
    String SUBSCRIPTION_HAS_BEEN_UNSUBSCRIBED_CODE = "1809120011";

    /**
     * 非移动手机号码
     */
    String USER_NOT_CMCC_USER = "1809120012";


    /**
     * 订购关系已经退订，周期末失效
     */
    String SUBSCRIPTION_HAS_BEEN_UNSUBSCRIBED_DESC =
            "The subscription relationship has been unsubscribed from and expires at the end of the period.";

    /**
     * 积分支付不允许退订
     */
    String BONUS_POINTS_PAY_NOT_UNSUB_CODE = "1809120010";

    /**
     * 积分支付不允许退订
     */
    String BONUS_POINTS_PAY_NOT_UNSUB_DESC = "Unsubscription is not allowed for bonus point payment.";

    /**
     * 退订按次产品失败,mcsServiceSubId为空
     */
    String UNSUB_FAIL_DESC = "unsub times-based subscription relationship. mcsServiceSubId is null";

    /**
     * 合约未到期，不支持暂停
     */
    String SUBSCRIPTION_NOT_EXPIRE_CODE = "1809120013";

    /**
     * 合约未到期，不支持暂停
     */
    String SUBSCRIPTION_NOT_EXPIRE_DESC = "subscription is not expire";

    /**
     * 合约已经暂停
     */
    String SUBSCRIPTION_ALREADY_PAUSE_CODE = "1809120014";

    /**
     * 合约已经暂停
     */
    String SUBSCRIPTION_ALREADY_PAUSE_DESC = "subscription is already pause";

    /**
     * 合约已经暂停
     */
    String SUBSCRIPTION_STATUS_NOT_RENEW_CODE = "1809120015";

    /**
     * 合约已经暂停
     */
    String SUBSCRIPTION_STATUS_NOT_RENEW_DESC = "subscription status is not support renew";

    /**
     * 不是激活所需状态
     */
    String SUBSCRIPTION_STATUS_NOT_ACTIVITY_CODE = "1809120017";

    String SUBSCRIPTION_STATUS_NOT_ACTIVITY_DESC = "subscription status is not support activation";

    /**
     * 集运订购接口限流状态码
     */
    String JIYUN_SUB_INTERFACE_LIMIT_CODE = "180912000";

    /**
     * 集运订购接口限流状态描述
     */
    String JIYUN_SUB_INTERFACE_LIMIT_DESC = "Jiyun subscribe interface had limited, VSBO is retrying!";

    /**
     * 免流接口限制访问
     */
    String SUBSCRIBE_FLOW_INTERFACE_LIMIT_CODE = "180912001";

    /**
     * 免流接口限制访问,稍后请重试
     */
    String SUBSCRIBE_FLOW_INTERFACE_LIMIT_CODE_ERROR_MESSAGE = "subscribe flow interface limit!";

    /**
     * 订购接口并发控制
     */
    String MUTUALLY_LIMIT_CODE = "180911888";

    /**
     * 订购接口并发控制描述
     */
    String MUTUALLY_LIMIT_DESC = "Multiple synchronizations are not allowed within the mutually exclusive period.";
    /**
     * 免流接口sourcegoodsid 不存在
     */
    String SUBSCRIBE_FLOWNOTIFY_SOURCEGOODSID_NOT_EXIST = "180912002";

    /**
     * 免流接口sourcegoodsid 不存在消息体
     */
    String SUBSCRIBE_FLOWNOTIFY_SOURCEGOODSID_NOT_EXIST_MESSAGE = "subscribe flow notify interface sourcegoodsid not exist!";

    String CAN_NOT_SUPPORT_OTEHER_OPERATOR_CODE = "181812102";
    String CAN_NOT_SUPPORT_OTEHER_OPERATOR = "this product can not support other operator";

    String ALREADY_PURCHASED_CODE = "1809013507";

    /**
     * 七天无理由退费仅支持普通会员
     */
    String SEVEN_DAY_UNSUBSCRIBE_ONLY_SUPPORT_NORMAL_CODE = "1809013500";
    String SEVEN_DAY_UNSUBSCRIBE_ONLY_SUPPORT_NORMAL_MESSAGE = "seven day unsubscribe only support normal product";

    String SUBSCRIBE_PROVCODE_ERROR = "1819323540";
    String ORDER_TYPER_EXISIT = "1809525802";
    String RAI_RESULT_CODE = "0000";

    String SUCCESS_MSG = "Success";
    String ROLLBACK_FAIL_CODE = "1819990440";
    String SCENE_CHECK_ERROR = "1818254400";
    String PARAMS_CHECK_ERROR = "1822144300";
    String USER_DOMAIN_ID_NOT_EXISTS_CODE = "1872144301";

    String USER_DOMAIN_ID_NOT_EXISTS_MESSAGE = "the user is not been open";
    String BIND_FAMILY_NUMBER_FAIL_CODE = "1872174381";
    // 绑定亲情号失败
    String BIND_FAMILY_NUMBER_FAIL_MESSAGE = "bind family number fail";
    String UNBIND_FAMILY_NUMBER_FAIL_CODE = "1872174381";
    // 解绑亲情号失败
    String UNBIND_FAMILY_NUMBER_FAIL_MESSAGE = "unbind family number fail";

    String IOT_LIMIT = "1809013408";
    String IOT_LIMIT_MESSAGE = "IoT users are not able to attend the event.";

    // 用户域id与手机号不匹配
    String PHONE_AND_USER_DOMAIN_ID_NOT_MATCH = "1809013409";
    String PHONE_NOT_EQUAL_USER_DOMAIN_MESSAGE = "the phone number does not match the user domain ID";
}
