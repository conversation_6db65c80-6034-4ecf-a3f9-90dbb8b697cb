package com.zyhl.yun.member.mcdmc.activation.flow.base;

import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import lombok.extern.slf4j.Slf4j;


/**
 * 退费统一流程
 *
 * <AUTHOR>
 * @since 2024/12/10 20:51
 */
@Slf4j
public abstract class BaseRefundServiceFlow extends BaseDefaultServiceFlow {


    /**
     * 流程成功后执行动作
     */
    @Override
    protected void doFlowSuccess(WorkOrderDo workOrderDo, boolean isFinally, IFlowResult lastFaceResult) {
        ComSendInterfaceReq comSendReq = JSONUtil.toBean(workOrderDo.getWorkAttrs(), ComSendInterfaceReq.class);
        String resourceId = lastFaceResult == null ? null : lastFaceResult.getResourceId();
        // 退费成功更新商品实例表状态
        MemberContextUtil.updateGoodsInstance(comSendReq, resourceId, isFinally, true, SendOperation.REFUND);

        // 执行原流程结束动作
        super.doFlowSuccess(workOrderDo, isFinally, lastFaceResult);
    }

    @Override
    protected void doFlowFail(WorkOrderDo workOrderDo) {
        ComSendInterfaceReq comSendReq = JSONUtil.toBean(workOrderDo.getWorkAttrs(), ComSendInterfaceReq.class);
        // 退费失败更新商品实例表状态
        MemberContextUtil.updateGoodsInstance(comSendReq, null, true, false, SendOperation.REFUND);
    }

}
