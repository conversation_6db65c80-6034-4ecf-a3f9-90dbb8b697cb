package com.zyhl.yun.member.common.domain.framework.service;

import org.apache.coyote.http11.Http11NioProtocol;
import org.apache.coyote.http2.Http2Protocol;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.tomcat.TomcatProtocolHandlerCustomizer;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Configuration;

/**
 * 功能描述。http2配置类
 * 包名称:  com.zyhl.yun.member.common.domain.framework.service
 * 类名称:  Http2Configuration
 * 类描述:  。
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/11/22 17:43
 */
@Configuration
public class Http2Configuration implements WebServerFactoryCustomizer<TomcatServletWebServerFactory> {

    @Value("${server.compressFlag:true}")
    private Boolean compressFlag;

    @Override
    public void customize(TomcatServletWebServerFactory factory) {
        TomcatProtocolHandlerCustomizer<Http11NioProtocol> handler = s -> {
            s.addUpgradeProtocol(new Http2Protocol());
            if (Boolean.TRUE.equals(compressFlag)) {
                s.setCompression("on");
                s.setCompressionMinSize(2048);
                s.setCompressibleMimeType("text/html,text/xml,text/plain,application/json,application/xml");
            }
        };
        factory.addProtocolHandlerCustomizers(handler);
    }

}
