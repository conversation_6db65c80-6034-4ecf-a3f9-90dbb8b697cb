package com.zyhl.yun.member.mcdmc.activation.util;

/**
 * 全局工具类
 *
 * <AUTHOR>
 */
public class NumberUtil {
    private NumberUtil() {
    }

    private static final int ZERO = 0;

    public static int nullToZero(Integer num) {
        return nullToDefault(num, ZERO);
    }

    public static <T extends Number> T nullToDefault(T num, T defaultValue) {
        if (null == num) {
            return defaultValue;
        }
        return num;
    }
}
