package com.zyhl.yun.member.mcdmc.activation.flow.free_flow;

import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.facade.GoodsInstanceServiceFacade;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.resource.domain.ResourceDo;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.ActivationContextUtil;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.flow.base.BaseSubServiceFlow;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import com.zyhl.yun.member.mcdmc.activation.util.OrderContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;


/**
 * 一级能开免流订购流程
 * <p>
 * serviceCode->Flow
 * serviceCode->outInterfaceList
 *
 * <AUTHOR>
 * @since 2024/07/03 11:22
 */
@Slf4j
@Component
public class OpenSyncFlow extends BaseSubServiceFlow {
    @Resource
    private MemberContextUtil memberContextUtil;

    @Override
    protected IFlowResult beforeFlow(WorkOrderDo workOrderDo) {
        // 先判断对应商品实例
        super.beforeFlow(workOrderDo);
        // 判断是否开通免流
        ResourceDo currentResourceDo = memberContextUtil.updateResourceEndTime(workOrderDo, true);
        // 如果存在生效的资产，则不能走流程
        if (null != currentResourceDo && null != currentResourceDo.getEffectiveEndTime()
                && currentResourceDo.getEffectiveEndTime().after(new Date())) {
            log.info("userId={} contain valid resource,skip {}. orderId={}", workOrderDo.getUserId(), this.getClass().getSimpleName(), workOrderDo.getOrderId());
            return IFlowResult.finish(currentResourceDo.getResourceId(),
                    "early skip flow,because it contain valid resource,workOrder detail is ", workOrderDo.toSimpleLogStr());
        }
        return IFlowResult.result(NextHintEnum.NEXT, null);
    }

    @Override
    protected boolean isJudgeSubGoodInstanceEffective() {
        // 需要判断对应商品实例是否生效中，如果不生效，则不能走流程
        return true;
    }

    @Override
    protected void doFlowSuccess(WorkOrderDo workOrderDo, boolean isFinally, IFlowResult lastFaceResult) {
        ComSendInterfaceReq comSendReq = JSONUtil.toBean(workOrderDo.getWorkAttrs(), ComSendInterfaceReq.class);
        String resourceId = lastFaceResult == null ? null : lastFaceResult.getResourceId();
        String parentOrderId = comSendReq.getParentOrderId();
        String userId = comSendReq.getUserId();
        if (StringUtils.isEmpty(parentOrderId)) {
            log.info("parentOrderId is empty,will use goodsInstanceId to query parentOrderId,order detail is {}", workOrderDo.toSimpleLogStr());
            GoodsInstanceDo parentGoodsInstanceDo = GoodsInstanceServiceFacade.qryParentGoodsInstance(userId, comSendReq.getGoodsInstanceId());
            parentOrderId = Objects.isNull(parentGoodsInstanceDo) ? null : parentGoodsInstanceDo.getOrderId();
        }
        if (StringUtils.isNotEmpty(parentOrderId)) {
            OrderContextUtil.updateOrderFlowOrderNo(userId, parentOrderId, resourceId);
        } else {
            log.error("parentOrderId is empty,can not update parentOrderFlowOrderNo,order detail is {}", workOrderDo.toSimpleLogStr());
        }
        //成功更新领取表状态
        MemberContextUtil.updateGoodsInstance(comSendReq, resourceId, isFinally, true, SendOperation.SUB);
        // 所有流程都走完要更新工单状态
        ActivationContextUtil.updateFinallyWorkOrder(workOrderDo, isFinally);
    }

    @Override
    protected boolean hasCallback() {
        return true;
    }


}
