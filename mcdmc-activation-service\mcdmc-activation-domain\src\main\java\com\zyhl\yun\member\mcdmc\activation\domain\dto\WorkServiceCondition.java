package com.zyhl.yun.member.mcdmc.activation.domain.dto;

import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.ServiceTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/06/05 15:13
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WorkServiceCondition extends BaseCondition {

    /**
     * 服务类型
     */
    private ServiceTypeEnum serviceType;

    /**
     * 服务ID
     */
    private String serviceId;

    public WorkServiceCondition(ServiceTypeEnum serviceType) {
        this.serviceType = serviceType;
    }

    public WorkServiceCondition(ServiceTypeEnum serviceType, String serviceId) {
        this.serviceType = serviceType;
        this.serviceId = serviceId;
    }
}
