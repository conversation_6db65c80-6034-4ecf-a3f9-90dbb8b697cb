package com.zyhl.yun.member.common.domain.framework.config;

import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class LoggingRequestInterceptor implements ClientHttpRequestInterceptor {
    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
        // 打印请求信息
        logRequest(request, body);

        // 执行请求
        ClientHttpResponse response = execution.execute(request, body);

        // 打印响应信息
        logResponse(response);

        return response;
    }

    private void logRequest(HttpRequest request, byte[] body) {
        System.out.println("---------- 请求日志 ----------");
        System.out.println("请求方法: " + request.getMethod());
        System.out.println("请求URL: " + request.getURI());
        System.out.println("请求头: " + request.getHeaders());
        System.out.println("请求体: " + new String(body, StandardCharsets.UTF_8));
    }

    private void logResponse(ClientHttpResponse response) throws IOException {
        System.out.println("---------- 响应日志 ----------");
        System.out.println("响应状态码: " + response.getStatusCode());
        System.out.println("响应头: " + response.getHeaders());

    }
}