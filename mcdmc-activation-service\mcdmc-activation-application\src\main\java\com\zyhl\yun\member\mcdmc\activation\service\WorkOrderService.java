package com.zyhl.yun.member.mcdmc.activation.service;

import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.activation.WorkOrderServiceId;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.CallbackException;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.FlowStaticConfig;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.req.SyncOrderReq;
import com.zyhl.yun.member.mcdmc.activation.result.ResultCode;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.ICallback;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.CallbackRoute;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 工单app入口
 *
 * <AUTHOR>
 * @since 240527
 */
@Slf4j
@Data
@Service
public class WorkOrderService {

    @Resource
    private Map<String, ICallback> notifyMap;

    /**
     * 正向开通流程
     */
    public Mono<Integer> syncOrder(SyncOrderReq orderReq) {
        //新建当前服务开通领域的上下文
        DomainServiceContext workServiceContext = new DomainServiceContext(WorkOrderServiceId.INSERT_WORK_ORDER);
        //设置extParams 只支持json
        String extParamsStr = orderReq.getExtParams();

        //设置工单实例
        WorkOrderDo workServiceDo = workServiceContext.newInstance(WorkOrderDo.class);
        BeanUtils.copyProperties(orderReq, workServiceDo);
        workServiceDo.setWorkAttrs(extParamsStr);
        // 写入工单
        int result = workServiceContext.writeAndFlush();

        return Mono.just(result);
    }

    /**
     * 回调流程
     */
    public Mono<String> callback(Map<String, String> headers, String body, HttpServletRequest request) {
        //获取回调实现类

        String uri = request.getRequestURI().substring(request.getContextPath().length());
        String notifyName = CallbackRoute.getNotifyNameByUrl(uri);
        ICallback iCallback = notifyMap.get(notifyName);
        if (null == iCallback) {
            throw new CallbackException(ResultCode.UNKNOWN_CALLBACK_TYPE);
        }
        // 执行回调
        String result = iCallback.execCallback(uri, headers, body);
        return Mono.just(result);
    }

    public Mono<String> refreshFlowConfig() {
        // 从数据库中获取配置刷新到内存里
        FlowStaticConfig.refreshConfig();
        return Mono.just("success");
    }
}
