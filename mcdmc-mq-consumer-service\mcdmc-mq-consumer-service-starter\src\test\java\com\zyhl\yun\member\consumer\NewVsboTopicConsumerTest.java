package com.zyhl.yun.member.consumer;

import com.zyhl.yun.member.common.JsonUtil;
import com.zyhl.yun.member.consumer.application.consume.instance.NewVsboTopicConsumer;
import com.zyhl.yun.member.mq.domain.MqEventContext;
import com.zyhl.yun.member.mq.domain.MqExtInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * NewVsboTopicConsumer 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class NewVsboTopicConsumerTest {

    @InjectMocks
    private NewVsboTopicConsumer newVsboTopicConsumer;

    @Test
    public void testCurrentlyConsume() {
        // 构造测试消息
        MqEventContext mqEventContext = new MqEventContext();
        mqEventContext.setOrdUserID("13800138000");
        mqEventContext.setTraceID("test-trace-id-123");

        MqExtInfo extInfo = new MqExtInfo();
        extInfo.setSubscriptionId("sub123456");
        extInfo.setOrderID("order123456");
        extInfo.setActionType("REFRESH");
        extInfo.setStatus("1");
        extInfo.setOrderEndTime("20250331235959");
        extInfo.setPayWay("1");
        extInfo.setPaymentOrderId("payment123456");

        mqEventContext.setExtInfo(extInfo);

        String message = JsonUtil.toJson(mqEventContext);

        System.out.println("测试消息: " + message);

        // 调用消费方法
        try {
            newVsboTopicConsumer.currentlyConsume(message, "test-topic", "msg-id-123");
            System.out.println("消息消费成功!");
        } catch (Exception e) {
            System.out.println("消息消费异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testCurrentlyConsumeWithRefreshAction() {
        // 测试REFRESH操作类型的消息
        String message = "{\"ordUserID\":\"13800138000\",\"traceID\":\"test-trace-id-456\",\"extInfo\":{\"subscriptionId\":\"sub789012\",\"orderID\":\"order789012\",\"actionType\":\"REFRESH\",\"status\":\"1\",\"orderEndTime\":\"20250430235959\",\"payWay\":\"2\",\"paymentOrderId\":\"payment789012\"}}";

        System.out.println("REFRESH操作测试消息: " + message);

        MqEventContext eventMessage = JsonUtil.fromJson(message, MqEventContext.class);

        if (eventMessage != null) {
            System.out.println("解析成功!");
            System.out.println("用户ID: " + eventMessage.getOrdUserID());
            System.out.println("跟踪ID: " + eventMessage.getTraceID());

            if (eventMessage.getExtInfo() != null) {
                System.out.println("订阅ID: " + eventMessage.getExtInfo().getSubscriptionId());
                System.out.println("订单ID: " + eventMessage.getExtInfo().getOrderID());
                System.out.println("操作类型: " + eventMessage.getExtInfo().getActionType());
                System.out.println("状态: " + eventMessage.getExtInfo().getStatus());
                System.out.println("订单结束时间: " + eventMessage.getExtInfo().getOrderEndTime());
                System.out.println("支付方式: " + eventMessage.getExtInfo().getPayWay());
            }
        } else {
            System.out.println("解析失败!");
        }

        try {
            newVsboTopicConsumer.currentlyConsume(message, "test-topic", "msg-id-456");
            System.out.println("REFRESH消息消费成功!");
        } catch (Exception e) {
            System.out.println("REFRESH消息消费异常: " + e.getMessage());
        }
    }

    @Test
    public void testCurrentlyConsumeWithActiveAction() {
        // 测试ACTIVE操作类型的消息
        String message = "{\"ordUserID\":\"13800138001\",\"traceID\":\"test-trace-id-789\",\"extInfo\":{\"subscriptionId\":\"sub345678\",\"orderID\":\"order345678\",\"actionType\":\"ACTIVE\",\"status\":\"2\",\"orderEndTime\":\"20250531235959\",\"payWay\":\"3\"}}";

        System.out.println("ACTIVE操作测试消息: " + message);

        try {
            newVsboTopicConsumer.currentlyConsume(message, "test-topic", "msg-id-789");
            System.out.println("ACTIVE消息消费成功!");
        } catch (Exception e) {
            System.out.println("ACTIVE消息消费异常: " + e.getMessage());
        }
    }

    @Test
    public void testCurrentlyConsumeWithPauseAction() {
        // 测试PAUSE操作类型的消息
        String message = "{\"ordUserID\":\"13800138002\",\"traceID\":\"test-trace-id-101\",\"extInfo\":{\"subscriptionId\":\"sub901234\",\"orderID\":\"order901234\",\"actionType\":\"PAUSE\",\"status\":\"3\",\"orderEndTime\":\"20250630235959\",\"payWay\":\"1\"}}";

        System.out.println("PAUSE操作测试消息: " + message);

        try {
            newVsboTopicConsumer.currentlyConsume(message, "test-topic", "msg-id-101");
            System.out.println("PAUSE消息消费成功!");
        } catch (Exception e) {
            System.out.println("PAUSE消息消费异常: " + e.getMessage());
        }
    }

    @Test
    public void testCurrentlyConsumeWithUnsubscribeAction() {
        // 测试UNSUBSCRIBE操作类型的消息
        String message = "{\"ordUserID\":\"13800138003\",\"traceID\":\"test-trace-id-202\",\"extInfo\":{\"subscriptionId\":\"sub567890\",\"orderID\":\"order567890\",\"actionType\":\"UNSUBSCRIBE\",\"status\":\"4\",\"orderEndTime\":\"20250731235959\",\"payWay\":\"2\"}}";

        System.out.println("UNSUBSCRIBE操作测试消息: " + message);

        try {
            newVsboTopicConsumer.currentlyConsume(message, "test-topic", "msg-id-202");
            System.out.println("UNSUBSCRIBE消息消费成功!");
        } catch (Exception e) {
            System.out.println("UNSUBSCRIBE消息消费异常: " + e.getMessage());
        }
    }

    @Test
    public void testCurrentlyConsumeWithInvalidMessage() {
        // 测试无效消息
        String message = "{\"invalid\":\"message\"}";

        System.out.println("无效消息测试: " + message);

        try {
            newVsboTopicConsumer.currentlyConsume(message, "test-topic", "msg-id-invalid");
            System.out.println("无效消息处理完成!");
        } catch (Exception e) {
            System.out.println("无效消息处理异常: " + e.getMessage());
        }
    }

    @Test
    public void testCurrentlyConsumeWithNullMessage() {
        // 测试空消息
        String message = null;

        System.out.println("空消息测试");

        try {
            newVsboTopicConsumer.currentlyConsume(message, "test-topic", "msg-id-null");
            System.out.println("空消息处理完成!");
        } catch (Exception e) {
            System.out.println("空消息处理异常: " + e.getMessage());
        }
    }

    @Test
    public void testCurrentlyConsumeWithMissingExtInfo() {
        // 测试缺少extInfo的消息
        String message = "{\"ordUserID\":\"13800138004\",\"traceID\":\"test-trace-id-303\"}";

        System.out.println("缺少extInfo的消息测试: " + message);

        try {
            newVsboTopicConsumer.currentlyConsume(message, "test-topic", "msg-id-303");
            System.out.println("缺少extInfo的消息处理完成!");
        } catch (Exception e) {
            System.out.println("缺少extInfo的消息处理异常: " + e.getMessage());
        }
    }

    @Test
    public void testCurrentlyConsumeWithHuaweiPay() {
        // 测试华为支付的消息
        MqEventContext mqEventContext = new MqEventContext();
        mqEventContext.setOrdUserID("13800138005");
        mqEventContext.setTraceID("test-trace-id-404");

        MqExtInfo extInfo = new MqExtInfo();
        extInfo.setSubscriptionId("sub111222");
        extInfo.setOrderID("order111222");
        extInfo.setActionType("REFRESH");
        extInfo.setStatus("1");
        extInfo.setOrderEndTime("20250831235959");
        extInfo.setPayWay("4"); // 华为支付
        extInfo.setPaymentOrderId("hw_payment123456");

        mqEventContext.setExtInfo(extInfo);

        String message = JsonUtil.toJson(mqEventContext);

        System.out.println("华为支付测试消息: " + message);

        try {
            newVsboTopicConsumer.currentlyConsume(message, "test-topic", "msg-id-404");
            System.out.println("华为支付消息消费成功!");
        } catch (Exception e) {
            System.out.println("华为支付消息消费异常: " + e.getMessage());
        }
    }
}