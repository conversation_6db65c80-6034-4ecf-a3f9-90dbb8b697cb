package com.zyhl.yun.member.white.application.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.white.application.common.dto.FeatureData;
import com.zyhl.yun.member.white.application.common.enums.UserInfoType;
import com.zyhl.yun.member.white.application.common.mq.base.MqOrderProducer;
import com.zyhl.yun.member.white.application.common.mq.message.WhiteNotifyMessage;
import com.zyhl.yun.member.white.application.common.mq.properties.WhiteNotifyProperties;
import com.zyhl.yun.member.white.application.common.properties.WhiteProperties;
import com.zyhl.yun.member.white.application.common.req.*;
import com.zyhl.yun.member.white.application.common.rsp.UpdateWhiteUserRsp;
import com.zyhl.yun.member.white.application.common.rsp.ValidUserWhiteRsp;
import com.zyhl.yun.member.white.application.common.service.WhiteUserService;
import com.zyhl.yun.member.white.application.common.util.MsisdnUtil;
import com.zyhl.yun.member.white.application.common.util.RsaUtil;
import com.zyhl.yun.member.white.common.exception.ServiceException;
import com.zyhl.yun.member.white.common.result.ResultCode;
import com.zyhl.yun.member.white.common.util.NumberUtil;
import com.zyhl.yun.member.white.domain.domains.UserInfoDo;
import com.zyhl.yun.member.white.domain.domains.WhiteSegmentDo;
import com.zyhl.yun.member.white.domain.domains.WhiteUserInfoDo;
import com.zyhl.yun.member.white.domain.enums.BlackSwitchEnum;
import com.zyhl.yun.member.white.domain.enums.WhiteUserStateEnum;
import com.zyhl.yun.member.white.domain.gateway.UserDomainGateway;
import com.zyhl.yun.member.white.infra.db.repository.UserInfoRepository;
import com.zyhl.yun.member.white.infra.db.repository.WhiteUserInfoRepository;
import com.zyhl.yun.member.white.infra.db.repository.impl.WhiteSegmentRepository;
import com.zyhl.yun.member.white.infra.redis.RedisComponent;
import com.zyhl.yun.member.white.infra.redis.RedisKeyConstant;
import com.zyhl.yun.member.white.infra.redis.RedisTimeProperties;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.security.interfaces.RSAPrivateKey;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/23 17:11
 */
@Slf4j
@Service
public class WhiteUserServiceImpl implements WhiteUserService {
    @Resource
    private UserInfoRepository userInfoRepository;

    @Resource
    private UserDomainGateway userDomainGateway;

    @Resource
    private WhiteSegmentRepository whiteSegmentRepository;
    @Resource
    private WhiteUserInfoRepository whiteUserInfoRepository;
    @Resource
    private RedisComponent redisComponent;
    @Resource
    private RedisTimeProperties redisTimeProperties;
    @Resource
    private WhiteNotifyProperties whiteNotifyProperties;
    @Resource
    private WhiteProperties whiteProperties;

    @Resource
    private MqOrderProducer mqOrderProducer;

    @Override
    public List<ValidUserWhiteRsp> validate(ValidUserWhiteReq req) {
        List<String> keyList = req.getKeyList();
        // 构建响应体
        List<ValidUserWhiteRsp> rspList = new ArrayList<>(keyList.size());
        if (WhiteProperties.ValidFlowSwitch.OPEN.codeEquals(whiteProperties.getValidFlowSwitch())) {
            // 全局开关打开，则全部返回白名单
            return keyList.stream().map(key -> ValidUserWhiteRsp.whiteRsp(key, BlackSwitchEnum.CLOSE.getCode()))
                    .collect(Collectors.toList());
        }
        // 获取手机号码
        Map<String, String> key2AccountMap = this.getKey2Account(req.getType(), keyList);
        List<String> accountList = new ArrayList<>(key2AccountMap.values());
        // 获取白名单用户
        Map<String, WhiteUserInfoDo> account2WhiteUserMap = this.getWhiteUser(accountList, false);

        for (String key : keyList) {
            String account = key2AccountMap.get(key);
            WhiteUserInfoDo whiteUserInfoDo = account2WhiteUserMap.get(account);
            if (Objects.nonNull(whiteUserInfoDo)) {
                // 获得黑名单开关
                Integer blackSwitch = NumberUtil.nullToDefault(whiteUserInfoDo.getBlackSwitch(), BlackSwitchEnum.CLOSE.getCode());
                rspList.add(ValidUserWhiteRsp.whiteRsp(key, blackSwitch));
            } else {
                rspList.add(ValidUserWhiteRsp.blackRsp(key));
            }
        }
        return rspList;
    }

    @Override
    public boolean flowValidate(FlowValidReq req) {
        String key = req.getKey();
        if ("notifyPaymentResultOfThirdParty".equals(req.getUri())) {
            //小红书回调接口手机号需要解密获取
            key = getRsaPhone(req.getKey());
        }
        // 获取去国家码的手机号码
        if (UserInfoType.ACCOUNT.typeEquals(req.getType())) {
            key = MsisdnUtil.getCellPhoneNo(key);
        }
        Map<String, String> key2AccountMap = this.getKey2Account(req.getType(), Collections.singletonList(key));
        log.info("req is {},input key ={},account={}", req, key, key2AccountMap);
        // 判断是否白名单用户
        List<String> accountList = new ArrayList<>(key2AccountMap.values());
        Map<String, WhiteUserInfoDo> account2WhiteUserMap = this.getWhiteUser(accountList, true);
        String account = key2AccountMap.get(key);
        WhiteUserInfoDo whiteUserInfoDo = account2WhiteUserMap.get(account);
        if (whiteUserInfoDo == null || BlackSwitchEnum.isOpen(whiteUserInfoDo.getBlackSwitch())) {
            // 如果不存在白名单或者白名单用户的黑名单开关打开则直接返回非白名单
            log.info("account={} is not whiteUser, current whiteUserInfo is {}", account, whiteUserInfoDo);
            return false;
        }
        return true;
    }

    @SneakyThrows
    private static String getRsaPhone(String featureDataStr) {
        FeatureData featureData = JSONUtil.toBean(featureDataStr, FeatureData.class);
        RSAPrivateKey privateKey = RsaUtil.getPrivateKey("MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKx4bJ26q1kXjaglk7jX/Vz55iRczXtUvppqxkCez0KshzQEoxrwuCirpo0L3Xxqxp6ehpkEeoDxRBAYTHt6e9aj4apiszD6uMhgV4nnYyFMAip4t13Cce+ngZoIi0/eZlbaXaB9zvsqHqgZs10MuDsXQdTjVb+TNRNt6LuHOAvzAgMBAAECgYBvg3WHDlkXw7jdzV4UF+NDqHh7bl2VrpeGtB73TTdpPrA0nALxuk74TPPlIpTUix1dFdalPVYP5O9DENLyy6EheQ9bi0yvPjohQ0Wlmvd6/D4f8qeTym0ojZg2CuHNh4NlwzOvf4X9M9h6PNzAImRd5PAWekQKfKctKwYm0HxiQQJBANa3MYJVsFTMVdzozkc1XQNmTPTRkhG+jYsHpUI4siNhi5QCdLzTmZAu9r/arxw8EwRNdMH6J/Vk/FQQulVbgzECQQDNodK+s3Hdgje72qbjRLl4m4Bsi1cszFpG6Cd63bY272ZOgNpqqjpt+w/wkwovbwt9ejWDCFxesaR4Lc5h5VBjAkEAw122h5/OLKor6jBOGM6+TiyUEyJo5QpcIceYH7QUqkrERK0jaPijvmRtEc46DNfAeea8OQ+Tsh3r1dXOpYH50QJBALqBSHTgqqjev5Api+5b2eDzITLR2tpt6n+fdpdw2iDPV7piEjhmdyJjcRyhML6+9+vx2PEktyrH8kVyPlr6MsMCQDCIM2ll/4b9VExoLaQgSIYv5NZvzz3UCpOgVF0o57HL46rpiqCYqI9zNDOvBHb/T1kzcC3nGNK0hQW6VWILons=");
        return RsaUtil.privateDecrypt(featureData.getTelNumber(), privateKey);
    }

    /**
     * 获取白名单用户
     *
     * @param accountList 用户列表
     * @return 白名单用户
     */
    private Map<String, WhiteUserInfoDo> getWhiteUser(List<String> accountList, boolean isReset) {
        if (CollectionUtils.isEmpty(accountList)) {
            return Collections.emptyMap();
        }
        List<String> newAccountList = accountList.stream().distinct().filter(Objects::nonNull).collect(Collectors.toList());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("getByAccounts");
        // 获取白名单用户列表
        List<WhiteUserInfoDo> accountDoList = whiteUserInfoRepository.getByAccounts(newAccountList);
        stopWatch.stop();
        Map<String, WhiteUserInfoDo> account2DoMap = accountDoList.stream()
                .collect(Collectors.toMap(WhiteUserInfoDo::getAccount, Function.identity(), (ow, nw) -> nw));
        // 获取白名单号段
        List<WhiteSegmentDo> segmentDoList = null;
        Map<String, WhiteUserInfoDo> account2WhiteUserMap = new HashMap<>(account2DoMap.size());
        for (String account : newAccountList) {
            // 白名单用户信息
            WhiteUserInfoDo whiteUserInfoDo = account2DoMap.get(account);
            LocalDateTime userOperateTime = LocalDateTime.now();
            // 校验白名单用户表
            if (WhiteUserInfoDo.isWhiteUser(whiteUserInfoDo)) {
                stopWatch.start("whiteUserCheck");
                if (WhiteUserStateEnum.EFFECT == whiteUserInfoDo.getState() ||
                        this.isSilentUser(whiteUserInfoDo, userOperateTime, null, isReset)) {
                    log.info("account：{} is effected white user or silent user,state={}", account, whiteUserInfoDo.getState());
                    // 属于白名单用户且已经生效
                    account2WhiteUserMap.put(account, whiteUserInfoDo);
                }
                stopWatch.stop();
            } else if (!WhiteUserInfoDo.isBlackUser(whiteUserInfoDo)) {
                stopWatch.start("whiteSegmentUserCheck");
                // 不是黑名单则校验白名单号段表
                if (segmentDoList == null) {
                    // 减少号段查询动作
                    segmentDoList = whiteSegmentRepository.listSegment();
                }
                whiteUserInfoDo = getWhiteSegmentUser(segmentDoList, account, userOperateTime, isReset);
                if (Objects.nonNull(whiteUserInfoDo)) {
                    account2WhiteUserMap.put(account, whiteUserInfoDo);
                }
                stopWatch.stop();
            }
        }
        log.info("getWhiteUser cost time:{}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        return account2WhiteUserMap;
    }

    /**
     * 获取白名单号段用户
     *
     * @param segmentDoList   白名单号段列表
     * @param account         用户号码
     * @param userOperateTime 用户操作时间
     * @param isReset         是否需要修改缓存及发送mq
     * @return 白名单用户信息
     */
    private WhiteUserInfoDo getWhiteSegmentUser(List<WhiteSegmentDo> segmentDoList, String account, LocalDateTime userOperateTime, boolean isReset) {
        WhiteSegmentDo whiteSegmentDo = null;
        for (WhiteSegmentDo segmentDo : segmentDoList) {
            if (account.startsWith(segmentDo.getSegment())) {
                log.info("用户{}属于白名单号段内[{}]", account, segmentDo);
                whiteSegmentDo = segmentDo;
                break;
            }
        }
        if (Objects.nonNull(whiteSegmentDo)) {
            WhiteUserInfoDo whiteUserInfoDo = WhiteUserInfoDo.getSegmentInstance(account, WhiteUserStateEnum.EFFECT,
                    whiteSegmentDo.getEffectTime(), whiteSegmentDo.getSilentTimeSec());
            if (isSilentUser(whiteUserInfoDo, userOperateTime, whiteSegmentDo.getSegment(), isReset)) {
                //  属于白名单用户且为静默用户
                log.info("用户：{} 为白名单号段用户，且为静默用户，号段={}，直接返回", account, whiteSegmentDo.getSegment());
                return whiteUserInfoDo;
            }
        }
        return null;
    }

    /**
     * 判断用户是否为静默用户
     *
     * @param whiteUserInfoDo 白名单用户信息
     * @param isFixRedis      是否需要修改缓存及发送mq
     * @return 是否为静默用户
     */
    private boolean isSilentUser(WhiteUserInfoDo whiteUserInfoDo, LocalDateTime userOperateTime, String phoneSegment, boolean isFixRedis) {
        if (whiteUserInfoDo == null) {
            return false;
        }
        // 设置用户操作时间
        whiteUserInfoDo.setUserLastOperateTime(userOperateTime);
        String silentUserKey = RedisKeyConstant.getSilentUserKey(whiteUserInfoDo.getAccount());
        LocalDateTime effectTime = whiteUserInfoDo.getEffectTime();
        Long silentTimeSec = whiteUserInfoDo.getSilentTimeSec();
        if (LocalDateTime.now().isBefore(effectTime) || redisComponent.hasKey(silentUserKey)) {
            // 未到生效时间则设置用户为待静默或待静默时间内收到而此请求
            if (Boolean.TRUE.equals(isFixRedis)) {
                redisComponent.set(silentUserKey, effectTime.toString(), silentTimeSec);
                // 发送mq，将白名单号段用户转换为白名单用户
                mqOrderProducer.send(whiteNotifyProperties.getTopic(), whiteNotifyProperties.getTag(),
                        WhiteNotifyMessage.getInstance(whiteUserInfoDo, phoneSegment), whiteUserInfoDo.getAccount());
            }
            return false;
        }
        if (Boolean.TRUE.equals(isFixRedis)) {
            // 发送mq，将白名单号段用户转换为白名单用户
            mqOrderProducer.send(whiteNotifyProperties.getTopic(), whiteNotifyProperties.getTag(),
                    WhiteNotifyMessage.getInstance(whiteUserInfoDo, phoneSegment), whiteUserInfoDo.getAccount());
        }
        return true;
    }


    /**
     * 根据传入类型和列表获取手机号码
     *
     * @param type    类型
     * @param keyList 用户信息列表
     * @return 传入用户信息: 手机号码
     */
    private Map<String, String> getKey2Account(Integer type, List<String> keyList) {
        if (UserInfoType.ACCOUNT.typeEquals(type)) {
            return CollUtil.emptyIfNull(keyList).stream()
                    .collect(Collectors.toMap(t -> t, t -> t, (t1, t2) -> t1));
        } else if (UserInfoType.USER_ID.typeEquals(type)) {
            List<UserInfoDo> userInfoDoList = userInfoRepository.qryByUserIds(keyList);
            return userInfoDoList.stream()
                    .collect(Collectors.toMap(UserInfoDo::getUserId, UserInfoDo::getMsisdn, (t1, t2) -> t1));
        } else if (UserInfoType.USER_DOMAIN_ID.typeEquals(type)) {
            return userDomainGateway.getUserList(keyList);
        }
        return Collections.emptyMap();
    }

    /**
     * 添加白名单用户
     */
    @Override
    public void addWhiteUser(AddWhiteUserReq req) {
        log.info("addWhiteUser, req:{}", req);
        List<WhiteUserInfoDo> whiteUserInfoDoList = whiteUserInfoRepository.getByAccounts(req.getAccountList());
        if (!CollectionUtils.isEmpty(whiteUserInfoDoList)) {
            List<String> existAccountList = whiteUserInfoDoList.stream().map(WhiteUserInfoDo::getAccount).distinct().collect(Collectors.toList());
            throw new ServiceException(ResultCode.PARAM_ERROR.getCode(), "白名单用户" + existAccountList + "已存在");
        }
        whiteUserInfoDoList = req.getAccountList().stream()
                .map(account -> WhiteUserInfoDo.getInstance(account, req.getType(), req.getSilentTimeSec()))
                .collect(Collectors.toList());
        whiteUserInfoRepository.batchInsert(whiteUserInfoDoList);
    }

    /**
     * 添加白名单号段
     */
    @Override
    public void addWhiteSegment(AddWhiteSegmentReq req) {
        String phoneSegmentKey = RedisKeyConstant.getPhoneSegmentKey();
        List<WhiteSegmentDo> segmentDoList = redisComponent.getJsonBeanList(phoneSegmentKey, WhiteSegmentDo.class);
        List<String> whiteSegmentList = segmentDoList.stream().map(WhiteSegmentDo::getSegment)
                .distinct().collect(Collectors.toList());
        // 校验号段是否已存在（包括号段是否前缀匹配）
        Set<String> existSegmentSet = new HashSet<>();
        for (String segment : req.getSegmentList()) {
            for (String whiteSegment : whiteSegmentList) {
                if (segment.startsWith(whiteSegment)) {
                    existSegmentSet.add(segment);
                    break;
                }
            }
        }
        if (!CollectionUtils.isEmpty(existSegmentSet)) {
            throw new ServiceException(ResultCode.PARAM_ERROR.getCode(), "白名单号段" + existSegmentSet + "已存在");
        }
        List<WhiteSegmentDo> whiteSegmentDoList = req.getSegmentList().stream()
                .map(segment -> WhiteSegmentDo.getInstance(segment, req.getSilentTimeSec()))
                .collect(Collectors.toList());
        boolean isSuccess = whiteSegmentRepository.batchInsert(whiteSegmentDoList);
        if (isSuccess) {
            // 重新设置缓存
            segmentDoList.addAll(whiteSegmentDoList);
            redisComponent.set(phoneSegmentKey, JSONUtil.toJsonStr(segmentDoList), redisTimeProperties.getSegmentExpireTimeDay(), TimeUnit.DAYS);
        }
    }

    public List<UpdateWhiteUserRsp> updateWhiteUser(UpdateWhiteUserReq req) {
        if (CollectionUtils.isEmpty(req.getAccountList())) {
            return Collections.emptyList();
        }
        List<UpdateWhiteUserRsp> rspList = new ArrayList<>(req.getAccountList().size());
        for (String account : req.getAccountList()) {
            boolean isUpdate;
            String message = "success";
            try {
                isUpdate = whiteUserInfoRepository.update(account, req.getBlackSwitch());
                if (Boolean.FALSE.equals(isUpdate)) {
                    message = "database update record is empty";
                }
            } catch (Exception e) {
                isUpdate = false;
                message = CharSequenceUtil.str(e.getMessage());
            }
            rspList.add(new UpdateWhiteUserRsp(account, isUpdate, message));
        }
        return rspList;
    }
}
