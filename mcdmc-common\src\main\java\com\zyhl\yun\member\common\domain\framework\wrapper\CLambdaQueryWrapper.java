package com.zyhl.yun.member.common.domain.framework.wrapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;

import java.util.List;

/**
 * 自定义lambda查询条件
 *
 * <AUTHOR>
 * @since 2024/09/07 14:35
 */
public class CLambdaQueryWrapper<T> extends LambdaQueryWrapper<T> {

    @Override
    public CLambdaQueryWrapper<T> eq(SFunction<T, ?> function, Object value) {
        return (CLambdaQueryWrapper<T>) super.eq(function, value);
    }

    @Override
    public CLambdaQueryWrapper<T> eq(boolean condition, SFunction<T, ?> function, Object value) {
        return (CLambdaQueryWrapper<T>) super.eq(condition, function, value);
    }

    public CLambdaQueryWrapper<T> eqWhileNotNull(SFunction<T, ?> function, Object value) {
        if (value != null) {
            this.eq(function, value);
        }
        return this;
    }

    public CLambdaQueryWrapper<T> inWhileNotEmpty(SFunction<T, ?> function, List<?> value) {
        if (value != null && !value.isEmpty()) {
            this.in(function, value);
        }
        return this;
    }
}
