package com.zyhl.yun.member.mcdmc.activation.remote.send.resp;

import lombok.Data;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2024/06/11 14:04
 */
@Data
@XmlRootElement(name = "result")
@XmlAccessorType(XmlAccessType.FIELD)
public class BmsNscRsp {
    @XmlAttribute(name = "resultCode")
    private String resultCode;

    @XmlElement(name = "notifySubChangeRsp")
    private NotifySubChangeRsp notifySubChangeRsp;


    @Data
    @XmlRootElement(name = "notifySubChangeRsp")
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class NotifySubChangeRsp {
        /**
         * result
         */
        private String resultCode;

        /**
         * result
         */
        private String resultDesc;

        /**
         * result
         */
        private String seqId;
    }

    public static boolean isSuccess(BmsNscRsp rsp) {
        return rsp != null && rsp.getNotifySubChangeRsp() != null &&
                "0".equals(rsp.getNotifySubChangeRsp().getResultCode());
    }
}
