package com.zyhl.yun.member.mcdmc.activation.flow.rai.refund;

import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.flow.base.BaseRefundServiceFlow;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 常规权益退费流程
 * (不做重试）
 *
 * <AUTHOR>
 * @since 2024/07/03 12:00
 */
@Slf4j
@Component
public class RaiRefundNormalFlow extends BaseRefundServiceFlow {

    /**
     * 权益退费前需要判断商品实例状态是否为空
     * （存量数据中，未订购时rightsStatus也为空）
     *
     * @param workOrderDo 工单信息
     * @return boolean
     * @apiNote 如果为true，则直接以成功结束工单流程，不会生成接口日志
     */
    @Override
    protected IFlowResult beforeFlow(WorkOrderDo workOrderDo) {
        // 退费前需要判断是否已经订购了
        ComSendInterfaceReq comSendReq = JSONUtil.toBean(workOrderDo.getWorkAttrs(), ComSendInterfaceReq.class);
        String subGoodsInstanceId = comSendReq.getGoodsInstanceId();
        GoodsInstanceDo subGoodsInstanceDo = MemberContextUtil.qryGoodsInstanceDo(workOrderDo.getUserId(), subGoodsInstanceId);
        if (Objects.isNull(subGoodsInstanceDo)) {
            log.error("rai refund error, goodsInstance is null,workOrder detail is {},goodsInstanceId={}",
                    workOrderDo.toSimpleLogStr(), subGoodsInstanceId);
            // 查不到商品实例，则直接终止流程
            throw new FlowTerminationException(this.getClass(), workOrderDo,
                    "rai refund error, goodsInstance is null,workOrder detail is {},goodsInstanceId=%s",
                    workOrderDo.toSimpleLogStr(), subGoodsInstanceId);
        }
        // 判断是否发起订购，若没有订购就退费则为延迟发货（发货前退费）
        if (Objects.isNull(subGoodsInstanceDo.getRightsStatusEnum())) {
            log.error("rai refund error, goodsInstance is not sub,workOrder detail is {},goodsInstanceId={}",
                    workOrderDo.toSimpleLogStr(), subGoodsInstanceId);
            return IFlowResult.result(NextHintEnum.FINISH, null);
        }
        workOrderDo.putExtData(OtherFieldConstants.GOODS_INSTANCE_DO, subGoodsInstanceDo);
        log.info("start refund rai,workOrder detail is {},goodsInstanceId={},instance state={}",
                workOrderDo.toSimpleLogStr(), subGoodsInstanceId, subGoodsInstanceDo.getRightsStatusEnum());
        return IFlowResult.result(NextHintEnum.NEXT, null);
    }

}
