package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.bmsuite;

import cn.hutool.http.ContentType;
import com.zyhl.hcy.plugin.neauth.util.ThirdAuthHeaderUtil;
import com.zyhl.yun.member.common.domain.framework.service.MdcLogInterceptor;
import com.zyhl.yun.member.facade.GoodsInstanceServiceFacade;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.ProductTypeEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.bmsuite.BmsChangeTypeEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.bmsuite.BmsSubUserTypeEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.bmsuite.EventTypeEnum;
import com.zyhl.yun.member.common.enums.NationCodeEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.SendTemplate;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.AESUtils;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.NEAuthUtil;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.BmsuiteProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.BmsNscReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.BmsNscRsp;
import com.zyhl.yun.member.product.domain.goods.GoodsDo;
import com.zyhl.yun.member.product.domain.goods.facade.GoodsServiceFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 调用bmsuite外部接口（无回调）
 * com.huawei.jaguar.vsbo.service.serviceimpl.NotifySendProductServiceImpl#mcsSpaceNotify
 *
 * <AUTHOR>
 * @since 2024/06/15
 */
@Slf4j
public abstract class BmsuiteIFace extends SendTemplate<BmsNscReq, BmsNscRsp> {

    private static final String BMP_SEQ_FIXED_VALUE = "seqId00000";
    @Resource
    private BmsuiteProperties bmsuiteProperties;

    @Override
    protected void doCheckParam(InterfaceContext<BmsNscReq, BmsNscRsp> interfaceContext) throws FlowTerminationException {
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        if (StringUtils.isEmpty(comSendReq.getMsisdn())) {
            throw new FlowTerminationException(this.getClass(), interfaceContext.getWorkOrderDo(), "手机号不能为空");
        }
    }

    @Override
    public BmsNscReq getRequestBody(InterfaceContext<BmsNscReq, BmsNscRsp> interfaceContext) {
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        BmsNscReq.NotifySubChangeReq nscReq = new BmsNscReq.NotifySubChangeReq();
        nscReq.setAccount(AESUtils.encrypt(comSendReq.getMsisdn()));
        nscReq.setServiceId(comSendReq.getProServiceId());
        nscReq.setEndDate(comSendReq.getEffectiveEndTime());
        nscReq.setProductType(ProductTypeEnum.iSpace.getType());
        nscReq.setTransactionId(comSendReq.getGoodsInstanceId().replace("-", ""));
        // 填充用户空间类型
        this.fillSubUserType(nscReq, comSendReq.getNationCode(), comSendReq.getGoodsId());
        //  通过调会员域获取对应的seqId，根据seqId存在与否判断订购或续订
        GoodsInstanceDo goodsInstanceDo = (GoodsInstanceDo) interfaceContext.getWorkOrderDo().getExtData(OtherFieldConstants.GOODS_INSTANCE_DO);
        if (Objects.isNull(goodsInstanceDo)) {
            // 正常是会有的，以防万一做兼容
            goodsInstanceDo = GoodsInstanceServiceFacade.qryGoodsInstance(comSendReq.getUserId(), comSendReq.getGoodsInstanceId());
        }
        String seqId = goodsInstanceDo.getResourceId();
        //为了适配个人云：个人云产品首次订购时携带了固定值：seqId00000(旧代码中为seqId00000时则直接走订购流程)
        if (!BMP_SEQ_FIXED_VALUE.equals(seqId) && !SendOperation.SUB.equals(this.getSendOperation())) {
            // 退订需要查询seqId
            if (null == seqId) {
                // 退订查不到订单则直接结束流程
                throw new FlowTerminationException(this.getClass(),
                        interfaceContext.getWorkOrderDo(), "续订/退订失败，未找到订单信息");
            }
            nscReq.setSeqId(seqId);
            nscReq.setEventType(EventTypeEnum.UPDATE.getType());
            if (SendOperation.UN_SUB.equals(this.getSendOperation())) {
                nscReq.setChangeType(BmsChangeTypeEnum.UNSUBSCRIBE.getType());
            } else {
                nscReq.setChangeType(BmsChangeTypeEnum.RENEW.getType());
            }
            return new BmsNscReq(nscReq);
        }
        // 不是退订，则直接创建订购
        nscReq.setEventType(EventTypeEnum.ESTABLISH.getType());
        nscReq.setBeginDate(comSendReq.getEffectiveStartTime());
        return new BmsNscReq(nscReq);
    }

    /**
     * 填充入参的用户类型
     *
     * @param nscReq 待填充的请求入参
     */
    private void fillSubUserType(BmsNscReq.NotifySubChangeReq nscReq, String nationCode, String goodsId) {
        // 大陆用户需要去产品域查询是否是独立空间
        String subUserType = null;
        if (NationCodeEnum.LAND.codeNotEquals(nationCode)) {
            subUserType = BmsSubUserTypeEnum.getByNationCode(nationCode);
        }
        if (subUserType != null) {
            // 非大陆用户
            nscReq.setSubUserType(subUserType);
            return;
        }
        String ownerId = null;
        String diskType = null;
        // 从产品域获取ownerId
        GoodsDo goodsDo = GoodsServiceFacade.getGoods(goodsId, false);
        if (null != goodsDo && null != goodsDo.getGoodsExt()) {
            ownerId = goodsDo.getGoodsExt().getOwnerId();
            diskType = goodsDo.getGoodsExt().getDiskType();
        }
        if (StringUtils.isNotBlank(ownerId)) {
            // 独立空间用户
            nscReq.setSubUserType(BmsSubUserTypeEnum.INDEPENDENCE.getSubUserType());
            nscReq.setOwnerId(ownerId);
            return;
        }
        if ("4".equals(diskType)) {
            // 挂载盘用户
            nscReq.setSubUserType(BmsSubUserTypeEnum.MOUNT.getSubUserType());
        } else {
            // 默认为云盘产品大陆用户
            nscReq.setSubUserType(BmsSubUserTypeEnum.YUN_PRODUCT.getSubUserType());
        }
    }


    @Override
    public Map<String, String> getRequestHeader(InterfaceContext<BmsNscReq, BmsNscRsp> interfaceContext) {
        // 获取第三方鉴权
        Map<String, String> thirdHeaderMap = ThirdAuthHeaderUtil.generateHeaderMap(bmsuiteProperties.getAppKey(),
                bmsuiteProperties.getAppSecretId(), bmsuiteProperties.getAppSecret(), MdcLogInterceptor.getCurrentTraceId(), bmsuiteProperties.getAlgorithmVersion());

        Map<String, String> headers = new HashMap<>(thirdHeaderMap);
        //"text/xml;charset=UTF-8"
        String appXml = ContentType.build(ContentType.TEXT_XML, StandardCharsets.UTF_8);
        headers.put(HttpHeaders.CONTENT_TYPE, appXml);
        headers.put(HttpHeaders.ACCEPT, appXml);
        try {
            String neAuthHeader = NEAuthUtil.getNeAuthHeader(bmsuiteProperties.getNeName(), bmsuiteProperties.getNePasswd(), bmsuiteProperties.getAesEncryptPasswd());
            headers.put("x-ne-auth", neAuthHeader);
        } catch (Exception e) {
            log.error("获取网元鉴权失败,异常信息为：", e);
        }
        return headers;
    }


    @Override
    public boolean isSuccess(BmsNscRsp rsp) {
        return BmsNscRsp.isSuccess(rsp);
    }

    /**
     * 获取用户调用外部接口的操作类型
     */
    protected abstract SendOperation getSendOperation();

    @Override
    protected String getCallbackCondition(InterfaceContext<BmsNscReq, BmsNscRsp> context) {
        return context.getInterfaceRspObj().getNotifySubChangeRsp().getSeqId();
    }

    @Override
    protected Class<BmsNscReq> getReqClass() {
        return BmsNscReq.class;
    }

    @Override
    protected Class<BmsNscRsp> getRspClass() {
        return BmsNscRsp.class;
    }
}
