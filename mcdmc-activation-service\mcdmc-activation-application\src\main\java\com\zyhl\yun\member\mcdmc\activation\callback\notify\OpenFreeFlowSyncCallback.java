package com.zyhl.yun.member.mcdmc.activation.callback.notify;

import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.ResourceServiceId;
import com.zyhl.yun.member.common.enums.MemberResourceStateEnum;
import com.zyhl.yun.member.common.enums.ResourceTypeEnum;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.resource.domain.ResourceDo;
import com.zyhl.yun.member.facade.ResourceServiceFacade;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.CallbackException;
import com.zyhl.yun.member.mcdmc.activation.result.BaseResult;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.CallbackContext;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.CallbackTemplate;
import com.zyhl.yun.member.mcdmc.activation.callback.req.OpenFreeFlowSyncNotifyReq;
import com.zyhl.yun.member.mcdmc.activation.callback.rsp.OpenFreeFlowSyncNotifyRsp;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 一级能开免流开通回调流程
 * <p>
 * com.huawei.jaguar.vsbo.service.action.OrderAction#salesorderResult
 * </p>
 *
 * <AUTHOR>
 * @since 2024/06/24 11:33
 */
@Slf4j
@Component
public class OpenFreeFlowSyncCallback extends CallbackTemplate<OpenFreeFlowSyncNotifyReq, OpenFreeFlowSyncNotifyRsp> {

    @Resource
    private MemberContextUtil memberContextUtil;

    @Override
    protected void doCheck(CallbackContext<OpenFreeFlowSyncNotifyReq, OpenFreeFlowSyncNotifyRsp> callbackContext) throws CallbackException {
        doCheckEmpty(callbackContext.getCallbackReq());
    }

    @Override
    protected void doAfterNotify(CallbackContext<OpenFreeFlowSyncNotifyReq, OpenFreeFlowSyncNotifyRsp> callbackContext,
                                 GoodsInstanceDo goodsInstanceExtendDo) {
        String userId = callbackContext.getComSendReq().getUserId();
        ResourceDo validResourceDo = ResourceServiceFacade.getValidResource(userId, ResourceTypeEnum.OPEN_FREE_FLOW);
        if (validResourceDo == null) {
            // 正常是不可能不存在用户资产（因为订购时就已经生成对应资产）
            log.error("userId={} can not found valid resource", userId);
            return;
        }
        // 更新资产状态
        DomainServiceContext memberContext = new DomainServiceContext(ResourceServiceId.UPDATE_RESOURCE_INFO);
        boolean isSuccess = isSuccess(callbackContext.getCallbackReq());
        MemberResourceStateEnum memberResourceStateEnum = isSuccess ?
                MemberResourceStateEnum.SUCCESS : MemberResourceStateEnum.FAIL;
        if (!memberResourceStateEnum.getState().equals(validResourceDo.getState())) {
            validResourceDo.setState(memberResourceStateEnum.getState());
            memberContext.putInstance(validResourceDo);
            memberContext.writeAndFlush();
        }
        // 开通成功需要发送mq退订的定时任务
        if (isSuccess) {
            String comSendReqStr = callbackContext.getSendServiceFlowLogDo().getExtParams();
            memberContextUtil.sendUnsubscribeMessage(userId, goodsInstanceExtendDo.getOrderId(),
                    comSendReqStr, validResourceDo.getEffectiveEndTime());
        }
    }


    @Override
    protected boolean isSuccess(OpenFreeFlowSyncNotifyReq notifyReq) {
        return "1".equals(notifyReq.getOrderResult());
    }

    @Override
    protected BaseResult getSuccessResult() {
        return BaseResult.sc("0", "ok");
    }

    @Override
    protected String getSearchCondition(OpenFreeFlowSyncNotifyReq notifyReq) {
        return notifyReq.getOrderId();
    }

    @Override
    protected OpenFreeFlowSyncNotifyRsp buildNotifyRsp(CallbackContext<OpenFreeFlowSyncNotifyReq, OpenFreeFlowSyncNotifyRsp> callbackContext,
                                                       BaseResult result) {
        OpenFreeFlowSyncNotifyRsp notifyRsp = new OpenFreeFlowSyncNotifyRsp();
        notifyRsp.setHRet(result.getResultCode());
        notifyRsp.setVersion(callbackContext.getCallbackReq().getVersion());
        return notifyRsp;
    }

    @Override
    protected MediaType getMediaType() {
        return MediaType.APPLICATION_XML;
    }

    @Override
    protected Class<OpenFreeFlowSyncNotifyReq> getNotifyReqClass() {
        return OpenFreeFlowSyncNotifyReq.class;
    }

    @Override
    protected SendOperation getSendOperation() {
        return SendOperation.SUB;
    }
}
