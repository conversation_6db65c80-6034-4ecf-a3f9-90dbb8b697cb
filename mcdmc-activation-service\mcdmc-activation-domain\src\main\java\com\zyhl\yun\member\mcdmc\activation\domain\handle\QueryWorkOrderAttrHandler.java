package com.zyhl.yun.member.mcdmc.activation.domain.handle;

import cn.hutool.core.collection.CollUtil;
import com.zyhl.yun.member.common.domain.framework.BaseAroundPersistenceHandler;
import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.common.domain.framework.DomainEntityPersistenceWrapper;
import com.zyhl.yun.member.common.domain.serviceid.activation.WorkOrderServiceId;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderAttrDo;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.conditions.QueryWorkOrderCondition;
import com.zyhl.yun.member.mcdmc.activation.facade.ActivationServiceFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/01/06 17:56
 */
@Slf4j
@Component
public class QueryWorkOrderAttrHandler extends BaseAroundPersistenceHandler<WorkOrderDo> {

    @Override
    public Serializable handle(DomainEntityPersistenceWrapper wrapper,
                               Callable<Serializable> executor) {
        Serializable result = super.handle(wrapper, executor);
        BaseCondition condition = (BaseCondition) wrapper.getData();
        if (!(condition instanceof QueryWorkOrderCondition)) {
            log.error("condition type error,can not support this type");
            throw new IllegalArgumentException("condition type error,can not support this type");
        }
        List<WorkOrderDo> workOrderDoList = (List<WorkOrderDo>) result;
        if (CollectionUtils.isEmpty(workOrderDoList)) {
            return result;
        }
        // 填充工单属性
        List<String> workIdList = workOrderDoList.stream().map(WorkOrderDo::getWorkId)
                .distinct().collect(Collectors.toList());
        QueryWorkOrderCondition qryCondition = (QueryWorkOrderCondition) condition;
        List<WorkOrderAttrDo> workOrderAttrDoList = ActivationServiceFacade.
                getWorkOrderAttrs(qryCondition.getUserId(), workIdList, qryCondition.getAttrKeyList());
        Map<String, List<WorkOrderAttrDo>> workId2AttrsMap = CollUtil.emptyIfNull(workOrderAttrDoList).stream()
                .collect(Collectors.groupingBy(WorkOrderAttrDo::getWorkId));
        workOrderDoList.stream().filter(workOrderDo -> workId2AttrsMap.containsKey(workOrderDo.getWorkId()))
                .forEach(workOrderDo -> workOrderDo.setWorkOrderAttrDoList(workId2AttrsMap.get(workOrderDo.getWorkId())));
        for (WorkOrderDo workOrderDo : workOrderDoList) {
            workOrderDo.setWorkOrderAttrDoList(workId2AttrsMap.get(workOrderDo.getWorkId()));
        }
        // 去除掉查不到对应属性的工单
        List<WorkOrderDo> resultList = workOrderDoList.stream()
                .filter(workOrderDo -> workId2AttrsMap.containsKey(workOrderDo.getWorkId()))
                .collect(Collectors.toList());
        return (Serializable) resultList;
    }

    @Override
    protected List<Class> getSupportedClassList() {
        return Collections.singletonList(WorkOrderDo.class);
    }

    @Override
    protected List<String> getSupportedServiceList() {
        return Collections.singletonList(WorkOrderServiceId.QUERY_ACTIVATION_ATTR);
    }
}
