package com.zyhl.yun.member.mcdmc.activation.flow.rai.query;

import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.activation.WorkOrderServiceId;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.DbFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.conditions.QueryWorkOrderCondition;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.enums.WorkOrderStateEnum;
import com.zyhl.yun.member.mcdmc.activation.flow.base.BaseDefaultServiceFlow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;


/**
 * 权益订购状态主动查询流程
 * com.huawei.jaguar.vsbo.pub.rocketmq.ConsumerNotifySubStatusMessageThread#run
 *
 * <AUTHOR>
 * @since 2024/07/30 10:09
 */
@Slf4j
@Component
public class RaiQueryNormalFlow extends BaseDefaultServiceFlow {

    @Override
    protected IFlowResult beforeFlow(WorkOrderDo workOrderDo) {
        // 如果有回调则不做查询处理
        ComSendInterfaceReq comSendReq = JSONUtil.toBean(workOrderDo.getWorkAttrs(), ComSendInterfaceReq.class);
        // 获取订购工单id
        String subWorkId = comSendReq.getExtInfo(DbFieldConstants.WORK_ID);
        DomainServiceContext workContext = new DomainServiceContext(WorkOrderServiceId.QUERY_ACTIVATION);
        QueryWorkOrderCondition queryCondition = new QueryWorkOrderCondition();
        queryCondition.setWorkIdList(Collections.singletonList(subWorkId));
        queryCondition.setUserId(comSendReq.getUserId());
        WorkOrderDo subWorkOrderDo = workContext.readFirst(queryCondition, WorkOrderDo.class);
        if (null == subWorkOrderDo) {
            log.error("query workOrderDo is null,userId ={},orderId={},workId={}",
                    workOrderDo.getUserId(), workOrderDo.getOrderId(), subWorkId);
            throw new FlowTerminationException(this.getClass(), workOrderDo, "query workOrderDo is null," +
                    "userId =%s,orderId=%s,workId=%s", workOrderDo.getUserId(), workOrderDo.getOrderId(), subWorkId);
        }
        // 如果有回调则不做主动查询，直接结束
        if (WorkOrderStateEnum.CALLBACK_SC.equals(subWorkOrderDo.getState()) ||
                WorkOrderStateEnum.CALLBACK_FAIL.equals(subWorkOrderDo.getState())) {
            return IFlowResult.result(NextHintEnum.FINISH, null);
        }
        return IFlowResult.next();
    }
}
