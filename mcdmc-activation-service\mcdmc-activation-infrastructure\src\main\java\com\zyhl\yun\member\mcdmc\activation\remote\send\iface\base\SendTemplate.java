package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base;

import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowIFaceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowLogDo;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.FlowLogStateEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.FlowStaticConfig;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import lombok.extern.slf4j.Slf4j;


/**
 * 调用外部接口模板
 *
 * @param <T> 外部接口请求入参
 * @param <R> 外部接口响应体
 * <AUTHOR>
 * @since 2024/06/28 17:00
 */
@Slf4j
public abstract class SendTemplate<T, R> extends BaseSendInterface<T, R> {

    @Override
    public IFlowResult exec(WorkOrderDo workOrderDo, IFlowResult lastFaceResult) throws Exception {
        // 初始化接口上下文
        WorkServiceFlowIFaceDo serviceFlowIFaceDo = FlowStaticConfig.getFaceDoByIFaceClass(workOrderDo.getServiceCode(), this.getClass());
        InterfaceContext<T, R> interfaceContext = InterfaceContext.newInstance(workOrderDo,
                serviceFlowIFaceDo, getReqClass(), getRspClass(), lastFaceResult);
        // 可能为接口重试，根据上次调用情况来判断此次执行流程，减少重复动作
        WorkServiceFlowLogDo sendWorkServiceFlowLog = interfaceContext.getWorkServiceFlowLog();
        FlowLogStateEnum lastLogStateEnum = sendWorkServiceFlowLog.getState();
        try {
            if (FlowLogStateEnum.isNeedRemote(lastLogStateEnum)) {
                // 处理请求参数
                interfaceContext.doHandleParam(this::doCheckParam, this::getRequestBody,
                        this::getRequestHeader);
                // 远程调用
                interfaceContext.doHandleRemote(remoteClient, this::getCallbackConditionWhileSC);

            }
            if (FlowLogStateEnum.isNeedShipment(lastLogStateEnum)) {
                // 处理结果
                interfaceContext.doHandleResult(this::isSuccess, this::doBusiSuccess, this::doBusiFail, this::doBusiComplete);
            } else {
                log.info("current flow clas name ={} is shipment success，state=[{}],so not need to handle.", this.getClass().getSimpleName(), lastLogStateEnum);
            }
        } catch (Exception e) {
            // 异常处理
            doException(interfaceContext, e);
        } finally {
            // 检查是否有更新流程日志异常，有则最后尝试更新一次
            interfaceContext.checkAndFillFLowLog();
            this.doBusiFinally(interfaceContext);
        }
        return toFlowResult(interfaceContext);
    }

    private IFlowResult toFlowResult(InterfaceContext<T, R> interfaceContext) {
        NextHintEnum nextHintEnum = interfaceContext.getNextHintEnum();
        String callbackCondition = interfaceContext.getCallbackCondition();
        String resourceId = getResourceId(interfaceContext);
        if (NextHintEnum.FAIL_FINISH.equals(nextHintEnum)) {
            String failReason = String.format("Business fail,third platform response is [%s]", interfaceContext.getInterfaceRspObjStr());
            return new IFlowResult(nextHintEnum, callbackCondition, resourceId, failReason);
        }
        return IFlowResult.result(nextHintEnum, callbackCondition, resourceId);
    }
}
