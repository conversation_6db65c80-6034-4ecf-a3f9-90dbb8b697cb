---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    deployment.kubernetes.io/revision: '92'
    objectset.rio.cattle.io/applied: H4sIAAAAAAAA/5xU32/bNhD+Xw7YmyRLSlzbehsStHtoh2BZ+1IEw4k822wpkjuelGiG/veBcpLJ2A90Nfwg3n387td3PAEG84k4Gu+gAQwhroYKMvhqnIYGbilYP3bkBDLoSFCjIDQnQOe8oBjvYjr69gspiSQFG18oFLFUGL8yiWSn1G5Tbrf5dl3r/Lq+avN2TZTTFZa63qgaNxuYMrDYkp3pMITCYUfQQKd0p3IfyDFFGfNIPBhFuVBMOf1b4CPGIzRQl0qX1+t9+rVYEe439Vpvd221o6p9s96U281uu9Up/LcFTKgYUCXobJoyiIFUSjuwPzDFeEuorXF0T8o7HaF5U5YZMAVrFEZoqnQYTGr6TyaK5/G96YxAU5UZRLKkxHMi7FDU8f3/bsuUchJGocOY7rG31rjDx6BR6Mz7dN/zIRHV6x/SaPHpo8MBjcXWvpinDGQM6fTLBUGyUxfsC9lCFoppFsWvpqMo2AVoXG/t98x2WjRWeSdoHHGE5vMJtIs33u3NYZZeeFbh59PLCKNxB0s50+89RcmZUgiYsleAmI58n8Y5oO2TpVq6UVJ9Ehf+q6XfaX/hrGF6mLKU1p23Ro3QwI3toxC/NTyrhtxwkWBg4w5FYL83lmKBSsxAC8ZRj89d+CvqF4xjkIKc4jGI5yJgjI+e9eLeJxPqsr7+7Y/HWxpgesjAdDjPuSqLapf+2+vV121c/Vfvm6oo4fnqXW/ta1U/2kcc49J1T4pJLrofEGO+CJcfkVvPeZyhc1LfuGpe0/1iG5znDm0aH/ezCpmi71nRrCubVmj+UqGfJ5perM7zOFf/zpxvzJJYwMriEphwZ2pBlr9XHtWRdG+Jfz7XoGmPvZX81Z4wpHo2Mt54J/Qk0JzmleHOuHk73jEquiM2Xi+eiAvIB4oxtRglPWMrTcNq4c2tP8A/4l/yfWvsrCdv+44++N6lms+reLY9H6eHaZqmPwMAAP//UdN3xQcGAAA
    objectset.rio.cattle.io/id: 9cc97088-852d-423b-b5ee-e3a0d27c2a77
  labels:
    # 修改应用名,若跟随节点部署要加上节点标识，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的D列或H列，应用模块的英文名
    app.name: mcdmc-openresty-service-starter
  # 修改应用名,若跟随节点部署要加上节点标识，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的D列或H列，应用模块的英文名
  name: mcdmc-openresty-service-starter
  namespace: prod
spec:
  progressDeadlineSeconds: 600
  # 修改副本数,对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的【内网规划】sheet K列
  replicas: 6
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      # 修改应用名,若跟随节点部署要加上节点标识，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的D列或H列，应用模块的英文名
      app.name: mcdmc-openresty-service-starter
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        # 修改应用名,若跟随节点部署要加上节点标识，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的D列或H列，应用模块的英文名
        app.name: mcdmc-openresty-service-starter
    spec:
      initContainers:
        - name: init-log-dir
          image: busybox:1.28
          command:
            - sh
            - '-c'
            - chmod -R 777 /nginx-pod-logs
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
          resources: {}
          volumeMounts:
            - name: logs
              # 挂载到容器内的日志目录/nginx-pod-logs
              mountPath: /nginx-pod-logs
              # 在该pod中共享/nginx-pod-logs下的子目录nginx/web/$(POD_NAME)，该目录对pod中的所有容器可见
              # 目的是让nginx和logrotate都能操作该目录
              subPathExpr: nginx/web/$(POD_NAME)
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
      containers:
        # logrotate容器，用于nginx日志的分割、打包、存储
        - name: logrotate
          image: >-
            b08ypcg01-53ab9ddf.ecis.guangzhou-2.cmecloud.cn/prod/logrotate:1.2-user-hcaiyun
          env:
            # 不要改动
            - name: LOGROTATE_SU_USER
              value: root
            # 不要改动
            - name: LOGROTATE_SU_GROUP
              value: root
            # 不要改动
            - name: LOGROTATE_SIZE
              value: 10k
            # 参照前面的mountPath: /nginx-pod-logs
            - name: LOGS_DIRECTORIES
              value: /nginx-pod-logs
            # 每过多久分割一次日志文件，请按照生产实际需求调整，一般是hourly
            - name: LOGROTATE_INTERVAL
              value: daily
            # 历史日志存放多久，按照要求存放半年
            - name: LOGROTATE_COPIES
              value: '360'
            # 需要gzip压缩转储日志
            - name: LOGROTATE_COMPRESSION
              value: compress
            # 分割后的日志后缀格式，例如log-20240712145627.666，业务可自行调整
            - name: LOGROTATE_DATEFORMAT
              value: '-%Y%m%d%H%M%S.%s'
            # 不要修改
            - name: LOGROTATE_POSTROTATE_COMMAND
              value: >-
                kill -USR1 `ps aux | grep nginx | grep master | awk '{print $1}'`
            # 将hcaiyun替换为在【1.2】添加的组和用户
            - name: LOGROTATE_MODE
              value: create 0644 hcaiyun hcaiyun
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
          resources: {}
          volumeMounts:
            # 同样挂载到容器的日志目录/nginx-pod-logs，并共享目录，以便对logrotate可见
            - name: logs
              mountPath: /nginx-pod-logs
              subPathExpr: nginx/web/$(POD_NAME)
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
        - name: mcdmc-openresty-service-starter
          image: >-
            b08ypcg01-53ab9ddf.ecis.guangzhou-2.cmecloud.cn/prod/mcdmc-openresty-service-starter:202410111643
          env:
            - name: spring.profiles.active
              value: ydy-test
            - name: jasypt.encryptor.password
              value: Vip2024_zwDev
          resources:
            limits:
              cpu: '10'
              memory: 16Gi
            requests:
              cpu: '1'
              memory: 16Gi
          volumeMounts:
            - name: logs
              mountPath: /logs
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: paas-***********-harbor-secret
      nodeSelector:
        normal: 'true'
        # 一定要带上该字段shareProcessNamespace，否则logrotate无法正常工作
      shareProcessNamespace: true
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 60
      volumes:
        - hostPath:
            path: /var/lib/docker/logs
            type: DirectoryOrCreate
          name: logs
        - hostPath:
            path: /etc/localtime
            type: ''
          name: time-localtime
        ########### 有共享存储需求的模块需修改，具体修改内容请沟通确认，如已知不需要可移除
        ########### name为存储卷名称，需要与65行的对应；persistentVolumeClaim为pvc的名字，请沟通确认
        ##- name: vol-pvc-example
        ##  persistentVolumeClaim:
        ##    claimName: pvc-nas-gz3-ypcg-p00X-XXX
        ###########
        ###########
      dnsConfig:
        options:
          - name: single-request-reopen
          - name: timeout
            value: '1'
          - name: attempts
            value: '3'
          - name: ndots
            value: '2'

---
apiVersion: v1
kind: Service
metadata:
  annotations: {}
  labels:
    # 修改应用名,若跟随节点部署要加上节点标识，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的D列或H列，应用模块的英文名
    app.name: mcdmc-openresty-service-starter
  # 修改为【应用名-svc】,若跟随节点部署应用名后要加上节点标识
  name: mcdmc-openresty-service-starter-svc
  namespace: prod
spec:
  ipFamilies:
    - IPv6
  ports:
    # 修改为应用名-port
    - name: mcdmc-openresty-service-starter-port
      # 修改为应用端口，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的BB列
      port: 16888
      protocol: TCP
      # 修改为应用端口，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的BB列
      targetPort: 80
  selector:
    # 修改应用名,若跟随节点部署要加上节点标识，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的D列或H列，应用模块的英文名
    app.name: mcdmc-openresty-service-starter
  sessionAffinity: None
  type: ClusterIP

