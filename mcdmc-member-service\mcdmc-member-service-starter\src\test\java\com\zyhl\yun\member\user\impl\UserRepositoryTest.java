package com.zyhl.yun.member.user.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zyhl.yun.member.common.enums.UserStatusEnum;
import com.zyhl.yun.member.domain.user.domain.QueryUserCondition;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.user.UserRepository;
import com.zyhl.yun.member.user.convertor.UserConvertor;
import com.zyhl.yun.member.user.mapper.UserMapper;
import com.zyhl.yun.member.user.po.UserPO;
import com.zyhl.yun.member.user.util.UserIdGenerateByRedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * <AUTHOR>
 * @date 2024/11/28
 * @description
 */
@SpringBootTest(properties = "spring.profiles.active=dev")
@RunWith(SpringRunner.class)
@Slf4j
public class UserRepositoryTest {
    @Resource
    private UserRepository userRepository;

    @Resource
    private UserIdGenerateByRedisUtil userIdGenerateUtil;

    @Test
    public void selectPage_CacheHit_ReturnsGoodsInstance() {
        // 缓存的redisKey为GOODS_INSTANCE:Entity:1859536067334725632
        QueryUserCondition queryUserCondition = new QueryUserCondition();
//        queryUserCondition.setUserId("************");
//        queryUserCondition.setUserDomainId("1079751640745730310");
//        queryUserCondition.setAccount("***********");
        List<UserPO> userPOS = userRepository.selectPage(queryUserCondition);

        // Assert
        assertNotNull(userPOS);
    }

    @Test
    public void insertUserDo_CacheHit_ReturnsGoodsInstance() {
        // 缓存的redisKey为GOODS_INSTANCE:Entity:1859536067334725632
        String userId = userIdGenerateUtil.generateUserId();
        UserDo userDo = new UserDo();
        userDo.setUserId(userId);
        //userDo.setMsisdn("***********");
        userDo.setUserDomainId("1079751640745730310");
        userDo.setCreatedTime(DateUtil.yesterday());
       int i= userRepository.insertUser(userDo);

        // Assert
        assertNotNull(i);
    }

    //@Before
    public void setUp() {
        String userId = userIdGenerateUtil.generateUserId();
        UserDo userDo = new UserDo();
        userDo.setUserId(userId);
        userDo.setMsisdn("***********");
        userDo.setUserDomainId("1079751640745730310");
        userDo.setCreatedTime(DateUtil.yesterday());
        userRepository.insertUser(userDo);
    }

    @Resource
    private UserMapper userMapper;

    //@After
    public void deleteUser() {
        LambdaQueryWrapper<UserPO> wrapper = Wrappers.<UserPO>lambdaQuery();
        wrapper.eq(UserPO::getUserDomainId, "1079751640745730310");
        //wrapper.eq(UserPO::getMsisdn, "10642440516");
        wrapper.eq(UserPO::getStatus, "0");
        userMapper.delete(wrapper);
    }

    @Resource
    private UserConvertor userConvertor;

    @Test
    public void createData() {
        QueryUserCondition userCondition = new QueryUserCondition();
        userCondition.setUserDomainId("1079751640745730310");
        QueryUserCondition.judgeStatusIsNull(userCondition);
        List<UserPO> userPOS = userRepository.selectPage(userCondition);
        assertNotNull(userPOS);
        userPOS.forEach(userPO -> {
            log.info("userPO:{}", userPO);
            userPO.setStatus("0");
            userRepository.updateData(userConvertor.po2Do(userPO));
        });
        userCondition.setStatus("0");
        userPOS = userRepository.selectPage(userCondition);
        assertNotNull(userPOS);
        userPOS.forEach(userPO -> {
            log.info("userPO:{}", userPO);
            assertEquals("0", userPO.getStatus());
        });


    }
}
