package com.zyhl.yun.member.common.domain.framework.service;

import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.domain.framework.DomainException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 全局异常处理
 */
@Slf4j
@RestControllerAdvice
@Order(-5)
public class GlobalExceptionHandle {

    /**
     * 字段校验失败全局异常
     */
    @ExceptionHandler({MethodArgumentNotValidException.class})
    public BaseResult handleConstraintViolationException(MethodArgumentNotValidException ex) {
        log.error("GlobalExceptionHandler handleConstraintViolationException:", ex);
        List<String> errors = new ArrayList<>();
        for (ObjectError error : ex.getBindingResult().getAllErrors()) {
            StringBuilder errorMsg = new StringBuilder();
            if (Objects.nonNull(error.getArguments())) {
                String fieldName = Arrays.stream(error.getArguments())
                        .map(Object::toString)
                        .limit(1)
                        .map(s -> s.substring(s.lastIndexOf("[")))
                        .collect(Collectors.joining());
                errorMsg.append(fieldName).append(":");
            }
            errorMsg.append(error.getDefaultMessage());
            errors.add(errorMsg.toString());
        }
        // 返回错误信息给前端
        return BaseResult.of(false, ResultCode.VALIDATION_ERROR.getCode(), String.join(";", errors));
    }

    /**
     * 服务抛出异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(ServiceException.class)
    public ResponseEntity<BaseResult> handleServiceException(ServiceException e) {
        log.error("GlobalExceptionHandler handleServiceException:", e);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_XML);
        return new ResponseEntity<>(Result.error(e), headers, HttpStatus.OK);
    }


    /**
     * 服务抛出异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(DomainException.class)
    public ResponseEntity<BaseResult> handleDomainException(DomainException e) {
        log.error("GlobalExceptionHandler handleDomainException:", e);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_XML);
        if (e.getCauseServiceException() != null && e.getCauseServiceException() instanceof ServiceException) {
            return handleServiceException((ServiceException) e.getCauseServiceException());
        }

        return handleException(e);
    }


    /**
     * 运行时全局异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(Throwable.class)
    public ResponseEntity<BaseResult> handleException(Throwable e) {
        log.error("GlobalExceptionHandler handleException:", e);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_XML);
        return new ResponseEntity<>(BaseResult.fail(ResultCode.SYSTEM_ERROR), headers, HttpStatus.OK);
    }

}
