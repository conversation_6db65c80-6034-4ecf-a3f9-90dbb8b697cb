package com.zyhl.yun.member.mcdmc.activation.remote.send.resp;

import lombok.Data;

import java.util.Objects;

/**
 * 一级能力开放综合退订接口响应对象
 *
 * <AUTHOR>
 * @since 2024/06/25 09:08
 */
@Data
public class OpenReturnRsp {
    private String msgType;

    private String version;

    private Integer hRet;

    private String bizCode;

    private String bizDesc;

    @Override
    public String toString() {
        return "OpenReturnRsp [msgType=" +
                msgType +
                ", bizDesc=" +
                bizDesc +
                ", version=" +
                version +
                ", bizCode=" +
                bizCode +
                ", hRet=" +
                hRet +
                "]";
    }

    public static boolean isSuccess(OpenReturnRsp rsp) {
        return null != rsp && Objects.equals(0, rsp.getHRet())
                && "1".equals(rsp.getBizCode());
    }
}
