package com.zyhl.yun.member.common.domain.framework.config;

import cn.hutool.extra.spring.SpringUtil;
import lombok.Getter;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/01/17 19:31
 */
@Configuration
public class RestTemplateConfig {
    @Resource
    private RestTemplateProperties properties;
    @Getter
    private static RestTemplate domainRestTemplate;


    private static void setDomainRestTemplate(RestTemplate restTemplate) {
        RestTemplateConfig.domainRestTemplate = restTemplate;
    }

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(httpComponentsClientHttpRequestFactory());
        RestTemplateConfig.setDomainRestTemplate(restTemplate);
        return restTemplate;
    }

    private HttpComponentsClientHttpRequestFactory httpComponentsClientHttpRequestFactory() {
        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory(httpClientBuilder().build());
        requestFactory.setConnectTimeout(properties.getConnectTimeoutMs());
        requestFactory.setConnectionRequestTimeout(properties.getConnectTimeoutMs());
        requestFactory.setReadTimeout(properties.getSocketTimeoutMs());
        return requestFactory;
    }

    private HttpClientBuilder httpClientBuilder() {
        HttpClientBuilder httpClientBuilder = HttpClients.custom()
                .setConnectionManager(poolingConnectionManager());
        if (Boolean.TRUE.equals(properties.getEvictExpiredConnections())) {
            //定时清理过期连接的开关
            httpClientBuilder.evictExpiredConnections();
        }
        return httpClientBuilder;
    }

    private PoolingHttpClientConnectionManager poolingConnectionManager() {
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        //最大连接数
        connectionManager.setMaxTotal(properties.getMaxTotal());
        //每个路由（域名）最大连接数
        connectionManager.setDefaultMaxPerRoute(properties.getMaxPerRouter());
        return connectionManager;
    }

    public static boolean isRestTemplate() {
        RestTemplateProperties restTemplateProperties = SpringUtil.getBean(RestTemplateProperties.class);
        return Boolean.TRUE.equals(restTemplateProperties.getIsRestTemplate());
    }
}
