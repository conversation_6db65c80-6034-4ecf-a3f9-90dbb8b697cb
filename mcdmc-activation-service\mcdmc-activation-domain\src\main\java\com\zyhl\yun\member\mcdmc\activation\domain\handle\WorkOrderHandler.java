package com.zyhl.yun.member.mcdmc.activation.domain.handle;

import cn.hutool.core.date.StopWatch;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.yun.member.common.domain.framework.BaseAroundPersistenceHandler;
import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.common.domain.framework.DomainEntityPersistenceWrapper;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.framework.service.MdcLogInterceptor;
import com.zyhl.yun.member.common.domain.serviceid.activation.WorkOrderServiceId;
import com.zyhl.yun.member.common.properties.DriverProperties;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.DbFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowDo;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.FlowStaticConfig;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.ActivationContextUtil;
import com.zyhl.yun.member.mcdmc.activation.enums.WorkOrderStateEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.lock.ILock;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.CustomDidGenerator;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;

import static com.zyhl.yun.member.mcdmc.activation.enums.WorkOrderStateEnum.CANCELED;
import static com.zyhl.yun.member.mcdmc.activation.enums.WorkOrderStateEnum.FAILED;

/**
 * <AUTHOR>
 * @since 2024/06/05 10:25
 */
@Slf4j
@Component
public class WorkOrderHandler extends BaseAroundPersistenceHandler<WorkOrderDo> {

    @Resource
    private transient ILock redisLock;
    @Resource
    private DriverProperties driverProperties;

    public WorkOrderDo beforeWrite(DomainEntityPersistenceWrapper wrapper) {
        WorkOrderDo workOrderDo = (WorkOrderDo) wrapper.getData();
        // 根据事务id获取原来的工单id
        DomainServiceContext context = new DomainServiceContext(workOrderDo.getServiceCode());
        BaseCondition baseCondition = new BaseCondition();
        baseCondition.putCondition(DbFieldConstants.TRANSACTION_ID, workOrderDo.getTransactionId());
        baseCondition.putCondition(DbFieldConstants.USER_ID, workOrderDo.getUserId());
        WorkOrderDo dbWorkOrderDo = context.readFirst(baseCondition, WorkOrderDo.class);
        if (null != dbWorkOrderDo) {
            if (!Objects.equals(dbWorkOrderDo.getUserId(), workOrderDo.getUserId())) {
                log.error("workOrder userid is not match,inputUserId ={},work detail is {}", workOrderDo.getUserId(), dbWorkOrderDo.toSimpleLogStr());
                throw new ServiceException("工单信息用户id不匹配");
            }
            if (!Objects.equals(dbWorkOrderDo.getOrderId(), workOrderDo.getOrderId())) {
                log.error("workOrder orderId is not match,inputOrderId ={},work detail is {}", workOrderDo.getOrderId(), dbWorkOrderDo.toSimpleLogStr());
                throw new ServiceException("工单信息订单id不匹配");
            }
            if (!Objects.equals(dbWorkOrderDo.getServiceCode(), workOrderDo.getServiceCode())) {
                log.error("workOrder serviceCode is not match,inputServiceCode ={},work detail is {}", workOrderDo.getServiceCode(), dbWorkOrderDo.toSimpleLogStr());
                throw new ServiceException("工单信息流程编号不匹配");
            }
            // 若数据库不为空则以数据库为准,同时修改传入的工单属性
            dbWorkOrderDo.setWorkAttrs(workOrderDo.getWorkAttrs());
            return dbWorkOrderDo;

        } else {
            //   往工单添加补充字段
            workOrderDo.setWorkId(CustomDidGenerator.generateId());
            // 默认待处理
            workOrderDo.setState(WorkOrderStateEnum.PROCESSING);
        }
        return workOrderDo;
    }

    /**
     * 默认环绕处理
     */
    @Override
    public Serializable handle(DomainEntityPersistenceWrapper wrapper) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("distributedLock");
        WorkOrderDo workOrderDo = (WorkOrderDo) wrapper.getData();
        // 设置日志追踪id
        MDC.put(LogConstants.TRACE_ID, MdcLogInterceptor.getCurrentTraceId());
        Serializable result = 0;
        String lockId = null;
        try {
            //根据事务id加锁
            lockId = redisLock.distributedLock(workOrderDo.getOrderId(), 30);
            if (!StringUtils.hasText(lockId)) {
                // 获取不到锁标志表示工单正在执行中，不进行持久化
                log.error("work processing，don't persistence，work detail is ：{}", workOrderDo.toSimpleLogStr());
                throw new ServiceException("工单执行中");
            }
            stopWatch.stop();
            stopWatch.start("beforeWrite");
            workOrderDo = beforeWrite(wrapper);
            if (WorkOrderStateEnum.isDone(workOrderDo.getState())) {
                log.info("work is completed,don't persistence,work detail is ：{}", workOrderDo.toSimpleLogStr());
                // 完成则直接返回成功
                return 1;
            }
            stopWatch.stop();
            stopWatch.start("insertIgnoreWork");
            if (WorkOrderStateEnum.PROCESSING.equals(workOrderDo.getState())) {
                // 第一次处理需要持久化工单
                result = handle(wrapper, () -> wrapper.getExecutor().exec(wrapper));
            }
            stopWatch.stop();
            workOrderDo.putExtData(OtherFieldConstants.STOP_WATCH, stopWatch);
            afterWrite(workOrderDo, result, lockId);
        } catch (Exception e) {
            log.error("do handle error,work detail is ：{},exception :", workOrderDo.toSimpleLogStr(), e);
            throw e;
        } finally {
            // 释放锁
            if (StringUtils.hasText(lockId)) {
                redisLock.unlock(workOrderDo.getOrderId(), lockId);
            }
        }
        return result;
    }

    public void afterWrite(WorkOrderDo workOrderDo, Serializable result, String lockId) {
        StopWatch stopWatch = (StopWatch) workOrderDo.getExtData(OtherFieldConstants.STOP_WATCH);
        stopWatch.start("qryServiceCode");
        if ((int) result <= 0) {
            log.debug("工单未持久化, 持久化数据为:{}", workOrderDo);
        }
        // 查询服务工作流列表
        String serviceCode = workOrderDo.getServiceCode();
        WorkServiceFlowDo serviceFlowDo = FlowStaticConfig.getServiceFlowDo(serviceCode);
        if (null == serviceFlowDo) {
            log.error("The participating serviceCode does not have a flow configured,work detail is ：{}", workOrderDo.toSimpleLogStr());
            ActivationContextUtil.updateFailureStatusAndRecordReason(workOrderDo, CANCELED, "serviceCode not exist");
            return;
        }

        if (serviceFlowDo.isNeedRetry() && workOrderDo.getFailCount() - 1 > serviceFlowDo.getRetryCount()) {
            log.error("The configured retry count has been exceeded, no retry will be performed，work detail is ：{}",
                    workOrderDo.toSimpleLogStr());
            ActivationContextUtil.updateFailureStatusAndRecordReason(workOrderDo, FAILED, "retry count over");
            return;
        }
        stopWatch.stop();
        // 走业务流程
        FlowStaticConfig.getIFlow(serviceCode)
                .handle(workOrderDo, () -> redisLock.continueLock(workOrderDo.getOrderId(), lockId, 30));
    }

    @Override
    protected List<Class> getSupportedClassList() {
        return Collections.singletonList(WorkOrderDo.class);
    }

    @Override
    protected List<String> getSupportedServiceList() {
        return Collections.singletonList(WorkOrderServiceId.INSERT_WORK_ORDER);
    }
}
