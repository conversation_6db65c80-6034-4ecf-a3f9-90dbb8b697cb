package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.sub;

import cn.hutool.core.lang.UUID;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.consumer.common.producer.MqProducer;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.bmsuite.rai.RaiOrderStatusEnums;
import com.zyhl.yun.member.mcdmc.activation.domain.mq.properties.AdverMqProperties;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.domain.mq.message.AdverMessage;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.RaiSubReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.RaiSubRsp;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 畅享权益（CHANGX_VIP）订购
 * com.huawei.jaguar.vsbo.service.serviceimpl.NotifySendProductServiceImpl#sendOrderAccept
 *
 * <AUTHOR>
 * @apiNote 有回调，详情看 <hr>RaiNormalSubCallback<hr/> 类
 * @since 2024/07/01 09:35
 */
@Component
public class RaiSubChangXIFace extends RaiSubNormalIFace {
    @Resource
    private AdverMqProperties adverMqProperties;
    @Resource
    private MqProducer mqProducer;


    @Override
    protected void doBusiSuccess(InterfaceContext<RaiSubReq, RaiSubRsp> interfaceContext) {
        // 先执行父类发送权益查询消息
        super.doBusiSuccess(interfaceContext);
        // 发送畅享会员订购权益订购消息到rocketmq
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        AdverMessage message = new AdverMessage();
        // 设置消息id
        message.setMsgID(UUID.randomUUID().toString(true));
        // 设置订单id
        message.setOrderID(comSendReq.getOrderID());
        message.setUserID(comSendReq.getMsisdn());
        message.setProductID(comSendReq.getGoodsId());
        message.setProductName(comSendReq.getGoodsName());
        // 设置订购信息---领取状态
        message.setStatus(String.valueOf(RaiOrderStatusEnums.LAUNCH_OPEN_OK.getCode()));
        message.setStartTime(comSendReq.getEffectiveStartTime());
        message.setEndTime(comSendReq.getEffectiveEndTime());
        message.setSubTime(comSendReq.getSubTime());
        message.setUserDomainId(comSendReq.getUserDomainId());
        mqProducer.sendOuterMessage(adverMqProperties, JSONUtil.toJsonStr(message));
    }
}
