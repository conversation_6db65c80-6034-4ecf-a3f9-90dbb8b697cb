# 渠道单点登录类型字段实现文档

## 概述

本文档描述了在 `ChannelController#queryChannelAndGoodsShelveInfo` 接口的 `QueryChannelAndGoodsShelveResp` 响应中的 `ChannelAttr` 对象新增 `singleSignOn` 字段的实现方案。

## 需求描述

在渠道属性中新增单点登录类型字段：
- 字段名：`singleSignOn`
- 字段类型：`Integer`
- 字段含义：单点登录类型
  - 1: RCS TOKEN单点登录
  - 2: 临时票据单点登录规格
  - 3: 集团掌厅端内单点登录
  - 4: 统一认证单点登录

## 实现方案

### 1. 创建枚举类

**文件位置**: `mcdmc-common/src/main/java/com/zyhl/yun/member/common/enums/SingleSignOnTypeEnum.java`

```java
@Getter
@AllArgsConstructor
public enum SingleSignOnTypeEnum {
    RCS_TOKEN(1, "RCS TOKEN单点登录"),
    TEMPORARY_TICKET(2, "临时票据单点登录规格"),
    GROUP_HALL_INNER(3, "集团掌厅端内单点登录"),
    UNIFIED_AUTH(4, "统一认证单点登录");
    
    private final Integer type;
    private final String desc;
}
```

### 2. 数据流程修改

#### 2.1 领域对象层 (ChannelAttrVo)

**文件位置**: `mcdmc-product-service/mcdmc-product-common/src/main/java/com/zyhl/yun/member/product/domain/channel/ChannelAttrVo.java`

```java
/**
 * 单点登录类型
 * 1-RCS TOKEN单点登录, 2-临时票据单点登录规格, 3-集团掌厅端内单点登录, 4-统一认证单点登录
 * @see com.zyhl.yun.member.common.enums.SingleSignOnTypeEnum
 */
private Integer singleSignOn;
```

#### 2.2 响应DTO层 (ChannelAttr)

**文件位置**: `mcdmc-product-service/mcdmc-product-application/src/main/java/com/zyhl/yun/member/product/application/dto/response/ChannelAttr.java`

```java
/**
 * 单点登录类型
 * 1-RCS TOKEN单点登录, 2-临时票据单点登录规格, 3-集团掌厅端内单点登录, 4-统一认证单点登录
 * @see com.zyhl.yun.member.common.enums.SingleSignOnTypeEnum
 */
private Integer singleSignOn;
```

#### 2.3 应用层DTO (ChannelAttrDTO)

**文件位置**: `mcdmc-product-service/mcdmc-product-application/src/main/java/com/zyhl/yun/member/product/application/converter/dto/ChannelAttrDTO.java`

```java
/**
 * 单点登录类型
 * 1-RCS TOKEN单点登录, 2-临时票据单点登录规格, 3-集团掌厅端内单点登录, 4-统一认证单点登录
 * @see com.zyhl.yun.member.common.enums.SingleSignOnTypeEnum
 */
private Integer singleSignOn;
```

### 3. 转换器修改

#### 3.1 ChannelConverter

**文件位置**: `mcdmc-product-service/mcdmc-product-infrastructure/src/main/java/com/zyhl/yun/member/product/infra/channel/converter/ChannelConverter.java`

1. 添加属性常量：
```java
private static final String ATTR_KEY_SINGLE_SIGN_ON = "singleSignOn";
```

2. 在 `toChannelAttrVo` 方法中添加转换逻辑：
```java
} else if (Objects.equals(attrKey, ATTR_KEY_SINGLE_SIGN_ON)) {
    try {
        channelAttrVo.setSingleSignOn(Integer.valueOf(attrValue));
    } catch (NumberFormatException e) {
        log.warn("[INFRA] Failed to parse singleSignOn value: {}", attrValue);
    }
}
```

#### 3.2 ChannelAndGoodsShelveConverter

**文件位置**: `mcdmc-product-service/mcdmc-product-application/src/main/java/com/zyhl/yun/member/product/application/converter/ChannelAndGoodsShelveConverter.java`

在 `toChannelAttr` 方法中添加：
```java
channelAttr.setSingleSignOn(channelAttrVo.getSingleSignOn());
```

#### 3.3 ChannelAppConverter

**文件位置**: `mcdmc-product-service/mcdmc-product-application/src/main/java/com/zyhl/yun/member/product/application/converter/ChannelAppConverter.java`

在 `toChannelAttrDTO` 方法中添加：
```java
channelAttrDTO.setSingleSignOn(channelAttrVo.getSingleSignOn());
```

## 数据存储

渠道属性数据存储在 `channel_t_channel_attr` 表中，使用 key-value 结构：

- `channel_id`: 渠道ID
- `attr_key`: "singleSignOn"
- `attr_value`: "1", "2", "3", "4" (字符串形式的枚举值)

## 使用示例

### 数据库插入示例

```sql
INSERT INTO channel_t_channel_attr (channel_id, attr_key, attr_value, create_time, update_time) 
VALUES ('CHANNEL_001', 'singleSignOn', '3', NOW(), NOW());
```

### API响应示例

```json
{
  "channelInfo": {
    "channelId": "CHANNEL_001",
    "channelName": "测试渠道",
    "channelAttrs": {
      "smsSecondaryConfirm": true,
      "useChannelGoodsLimit": false,
      "singleSignOn": 3
    }
  }
}
```

### 枚举使用示例

```java
// 根据类型值获取枚举
SingleSignOnTypeEnum type = SingleSignOnTypeEnum.fromType(3);
// 结果: GROUP_HALL_INNER

// 获取描述
String desc = SingleSignOnTypeEnum.getDescByType(3);
// 结果: "集团掌厅端内单点登录"
```

## 测试

创建了单元测试文件验证实现：
- 文件位置: `mcdmc-product-service/mcdmc-product-service-starter/src/test/java/com/zyhl/yun/member/product/infra/channel/converter/ChannelConverterSingleSignOnTest.java`
- 测试场景：
  - 正常的单点登录类型转换
  - 无效值的处理
  - 缺失字段的处理
  - 枚举类的所有方法

## 注意事项

1. **向后兼容性**: 新增字段不会影响现有功能，未设置该属性的渠道该字段值为 `null`
2. **数据验证**: 转换器中包含了异常处理，无效的数值会被忽略并记录警告日志
3. **扩展性**: 如需新增单点登录类型，只需在枚举类中添加新的枚举值即可
4. **一致性**: 所有相关的DTO和转换器都已同步更新，确保数据流转的一致性

## 影响范围

- 新增枚举类：`SingleSignOnTypeEnum`
- 修改领域对象：`ChannelAttrVo`
- 修改响应DTO：`ChannelAttr`, `ChannelAttrDTO`
- 修改转换器：`ChannelConverter`, `ChannelAndGoodsShelveConverter`, `ChannelAppConverter`
- 新增测试：`ChannelConverterSingleSignOnTest`
