
import lombok.extern.slf4j.Slf4j;
import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.junit.jupiter.api.Test;


/**
 * <AUTHOR>
 * @since 2023/1/4 14:12
 */
@Slf4j
class JasyptTest {
    @Test
    void test() {

        StandardPBEStringEncryptor standardPBEStringEncryptor = new StandardPBEStringEncryptor();
        //盐值
        standardPBEStringEncryptor.setPassword("Vip2024_zwDev");

        String sftpUsername = "99EH!mox9WkY_DBSf(mj";
        //加密明文
        String code = standardPBEStringEncryptor.encrypt(sftpUsername);
        //第二次test,解密第一次test的密文
        log.info("原文:{}", standardPBEStringEncryptor.decrypt(code));
        log.info("code={}", code);
    }

}