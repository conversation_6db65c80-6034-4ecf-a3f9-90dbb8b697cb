package com.zyhl.yun.member.common.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2024/11/30 10:29
 */
@Component
@Slf4j
public class NoCacheEntityCacheImpl implements EntityCache {
    @Override
    public <T extends EntityCacheable> T getByKey(Serializable key, Class<T> clazz, Function<Serializable, T> dataGetter) {
        return dataGetter.apply(key);
    }

    @Override
    public <T extends EntityCacheable> List<T> getListByKey(String key, Class<T> clazz, Function<String, List<T>> dataGetter) {
        return dataGetter.apply(key);
    }

    @Override
    public <T extends EntityCacheable> List<T> getByKeyList(List<Serializable> keyList, Class<T> clazz, Function<List<? extends Serializable>, List<T>> dataByKeyGetter) {
        return dataByKeyGetter.apply(keyList);
    }

    @Override
    public <C extends ConditionCacheable, T extends EntityCacheable> List<T> getByCondition(C condition, Class<T> clazz, Function<List<? extends Serializable>, List<T>> dataByKeyGetter, Function<C, List<T>> dataByConditionGetter) {
        return dataByConditionGetter.apply(condition);
    }

    @Override
    public <C extends ConditionCacheable, T extends EntityCacheable> Long getCountByCondition(C condition, Class<T> clazz, Function<C, ? extends Number> totalCountByConditionGetter) {
        Number count = totalCountByConditionGetter.apply(condition);
        return count == null ? 0L : count.longValue();
    }

    @Override
    public void deleteByKeyList(List<Serializable> keyList, ConditionCacheable condition) {

    }

    @Override
    public <C extends ConditionCacheable, T extends EntityCacheable> Long deleteByKeyForUpdate(String key, Function<String, T> dataGetter, Callable<? extends Number> dataUpdater, Function<T, C> conditionBuilder) {
        Number count = null;
        try {
            count = dataUpdater.call();
        } catch (Exception e) {
            log.error("[CACHE] deleteByKey error. msg: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] deleteByKey error.");
            }
            throw new CacheException("cache deleteByKey error");
        }
        return count == null ? 0L : count.longValue();
    }

    @Override
    public <C extends ConditionCacheable, T extends EntityCacheable> Long deleteByKeyForInsert(T entity, Function<T, ? extends Number> dataInserter, Function<T, C> conditionBuilder) {
        Number count = dataInserter.apply(entity);
        return count == null ? 0L : count.longValue();
    }

    @Override
    public void deleteByCondition(ConditionCacheable condition) {

    }

    @Override
    public String getCacheName() {
        return null;
    }

    @Override
    public <C extends ConditionCacheable, T extends EntityCacheable> void put(T entity,
                                                                              Function<T, C> conditionBuilder) {

    }


}
