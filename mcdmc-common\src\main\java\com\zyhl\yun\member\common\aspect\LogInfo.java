package com.zyhl.yun.member.common.aspect;

import com.zyhl.yun.member.common.util.RequestUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;

/**
 * 日志信息
 *
 * <AUTHOR>
 * @since 2025/05/27 09:58
 */
@Data
public class LogInfo {
    /**
     * 请求URI
     */
    private String requestUri;
    /**
     * 类名
     */
    private String className;
    /**
     * 方法名
     */
    private String methodName;
    /**
     * 总耗时
     */
    private long totalConsume;
    /**
     * 请求头
     */
    private String headersStr;
    /**
     * 请求参数
     */
    private String input;
    /**
     * 响应结果
     */
    private Object output;
    /**
     * 异常
     */
    private Throwable exception;
    /**
     * 日志类型
     */
    private LogType logType;

    /**
     * 自定义日志注解
     */
    private CustomLogger customLogger;

    public static LogInfo getErrorLogInfo(Method method, long startTime, String argsStr, Throwable e) {
        LogInfo logInfo = new LogInfo();
        logInfo.customLogger = method.getAnnotation(CustomLogger.class);
        logInfo.requestUri = RequestUtil.getRequestUri();
        logInfo.className = method.getDeclaringClass().getSimpleName();
        logInfo.methodName = method.getName();
        logInfo.totalConsume = System.currentTimeMillis() - startTime;
        logInfo.headersStr = RequestUtil.getHeadersStr();
        logInfo.input = argsStr;
        logInfo.exception = e;
        logInfo.logType = LogType.ERROR;
        return logInfo;
    }

    public static LogInfo getInfoLogInfo(Method method, long startTime, String argsStr, Object result) {
        LogInfo logInfo = new LogInfo();
        logInfo.customLogger = method.getAnnotation(CustomLogger.class);
        logInfo.requestUri = RequestUtil.getRequestUri();
        logInfo.className = method.getDeclaringClass().getSimpleName();
        logInfo.methodName = method.getName();
        logInfo.totalConsume = System.currentTimeMillis() - startTime;
        logInfo.headersStr = RequestUtil.getHeadersStr();
        logInfo.input = argsStr;
        logInfo.output = StringUtils.substring(result.toString(), 0, 150);
        logInfo.logType = LogType.INFO;
        return logInfo;
    }

    /**
     * 日志类型
     */
    public enum LogType {
        INFO, ERROR
    }

    public String toLogString() {
        StringBuilder sb = new StringBuilder(logType.name() + ":");
        if (customLogger == null || customLogger.logUri()) {
            sb.append("request uri is [").append(requestUri).append("], ");
        }
        if (customLogger == null || customLogger.logClassName()) {
            sb.append("method is [").append(className).append("#").append(methodName).append("(), ");
        }
        if (customLogger == null || customLogger.logTotalConsume()) {
            sb.append("total consume is [").append(totalConsume).append("ms, ");
        }
        if (customLogger == null || customLogger.logHeaders()) {
            sb.append("headers is [").append(headersStr).append("], ");
        }
        if (customLogger == null || customLogger.logInput()) {
            sb.append("input is [").append(input).append("], ");
        }
        if (LogType.INFO.equals(logType) && (customLogger == null || customLogger.logOutput())) {
            sb.append("output is [").append(output).append("], ");
        }
        return sb.toString();
    }

    public boolean isLogException() {
        return LogType.ERROR.equals(logType) && (customLogger == null || customLogger.logException());
    }

    public String getExceptionMessage() {
        if (exception != null) {
            return exception.getMessage();
        }
        return null;
    }
}
