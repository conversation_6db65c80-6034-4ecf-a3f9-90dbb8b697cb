package com.zyhl.yun.member.mcdmc.activation.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品-服务关系配置表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("activation_prod_service_relation")
public class ProdServiceRelationPo extends BasePo {
    /**
     * 主键id
     */
    @TableId
    private String relationId;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 服务id
     */
    private String serviceId;

    /**
     * 业务类型：订购，退订，退费，等等
     */
    private String relationType;

    /**
     * 状态
     */
    private short state;

}