package com.zyhl.yun.member.mcdmc.activation.remote.send.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 一级能开公共配置
 *
 * <AUTHOR>
 * @since 2024/07/05 17:51
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "platform.open.common")
public class OpenCommonProperties {

    private String shopCode = "OTTV0008";

    private String shopName = "互联网公司";

    private String sellerId = "C10000002224";

    private String chargeType = "2";
    /**
     * 能力开放综合销售订单接口二次确认行为日志
     */
    private String confirmLog = "clickOK";

    /**
     * 能力开放综合销售订单接口二次确认方式字段
     */
    private String confirmWay = "203";
    /**
     * 包月goodid
     * "/config/vsbo/goToneConfig/monthlyGoodsID:9991010000002650001"
     */
    private String monthlyGoodsID = "9991010000002650001";

    private String goodsTitle = "0元(1GB)";

    private String orderStatus = "00";

}
