package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.free_flow;

import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.constants.HeaderConstant;
import com.zyhl.yun.member.common.domain.framework.service.MdcLogInterceptor;
import com.zyhl.yun.member.common.enums.MessageServiceId;
import com.zyhl.yun.member.common.util.DateUtils;
import com.zyhl.yun.member.consumer.common.producer.MqProducer;
import com.zyhl.yun.member.domain.resource.domain.ResourceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.domain.mq.properties.FlowRetryMqProperties;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.SendTemplate;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.OpenFreeFlowProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.OpenFreeFlowCheckReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.OpenFreeFlowCheckRsp;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import com.zyhl.yun.member.message.domain.ActivationMqMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 免流/一级能开-业务办理资格校验
 * com.huawei.jaguar.vsbo.service.serviceimpl.asyncService.SubscribeNotifyFlowRetrySubThread#run
 * 配置地址：/config/vsbo/goToneConfig/bizQualificationCheckUrl
 *
 * <AUTHOR>
 * @since 2024/06/19 11:29
 */
@Slf4j
@Component
public class OpenFreeFlowCheckIFace extends SendTemplate<OpenFreeFlowCheckReq, OpenFreeFlowCheckRsp> {

    @Resource
    private MqProducer mqProducer;

    @Resource
    protected OpenFreeFlowProperties openFreeFlowProperties;
    @Resource
    private FlowRetryMqProperties flowRetryMqProperties;
    @Resource
    private MemberContextUtil memberContextUtil;

    @Override
    protected Map<String, String> getRequestHeader(InterfaceContext<OpenFreeFlowCheckReq, OpenFreeFlowCheckRsp> interfaceContext) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.CONTENT_TYPE, interfaceContext.getMediaType().toString());
        headerMap.put(HeaderConstant.SRC_MOD_NAME, HeaderConstant.SRC_MOD_NAME_VALUE);
        headerMap.put(HeaderConstant.MCS_FLOW_ID, MdcLogInterceptor.getCurrentTraceId());
        return headerMap;
    }

    @Override
    protected OpenFreeFlowCheckReq getRequestBody(InterfaceContext<OpenFreeFlowCheckReq, OpenFreeFlowCheckRsp> interfaceContext) {
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        OpenFreeFlowCheckReq openFreeFlowCheckReq = new OpenFreeFlowCheckReq();
        openFreeFlowCheckReq.setNumber(comSendReq.getMsisdn());
        openFreeFlowCheckReq.setGoodsId(openFreeFlowProperties.getMonthlyGoodsID());
        openFreeFlowCheckReq.setVersion(openFreeFlowProperties.getBizQualificationVersion());
        //业务办理检测mestype
        openFreeFlowCheckReq.setMsgType("BizQualificationReq");
        //业务号码类型1 - 手机号
        openFreeFlowCheckReq.setNumType("1");
        return openFreeFlowCheckReq;
    }

    @Override
    protected void doBusiFail(InterfaceContext<OpenFreeFlowCheckReq, OpenFreeFlowCheckRsp> interfaceContext) {
        String bizCode = interfaceContext.getInterfaceRspObj().getBizCode();
        // 如果返回的是3066错误码，表示已经开通过，直接成功，
        WorkOrderDo workOrderDo = interfaceContext.getWorkOrderDo();
        String userId = workOrderDo.getUserId();
        if (openFreeFlowProperties.getCheckAlreadySubErrorCode().contains(bizCode)) {
            // 已经开通过，则直接结束流程,更新会员资源表最大失效时间
            ResourceDo validResource = memberContextUtil.updateResourceEndTime(workOrderDo, true);
            if (validResource != null) {
                log.info("open free flow check is already sub and will finish,workOrder is {},resourceId is {}", workOrderDo.toSimpleLogStr(), validResource.getResourceId());
                interfaceContext.setCallbackCondition(validResource.getResourceId());
                interfaceContext.setNextHintEnum(NextHintEnum.FINISH);
            } else {
                log.debug("open free flow check fail and will restart,workOrder is {},because resourceId is null",
                        workOrderDo.toSimpleLogStr());
                // 查询资产为null，可能是资产信息未同步，通过重试来进行处理
                throw new FlowTerminationException(this.getClass(), interfaceContext.getWorkOrderDo(), NextHintEnum.RESTART,
                        "open free flow check fail and will restart,third platform return  is %s,workOrder is %s,because resourceId is null",
                        interfaceContext.getInterfaceRspObjStr(), workOrderDo.toSimpleLogStr());
            }
            return;
        }
        // 新增3012错误码,表示当前有业务在处理中，需要重试
        if (openFreeFlowProperties.getCheckProcessingErrorCode().contains(String.valueOf(interfaceContext.getInterfaceRspObj().getBizCode()))) {
            throw new FlowTerminationException(this.getClass(), interfaceContext.getWorkOrderDo(), NextHintEnum.RESTART,
                    "open free flow check fail and will restart,workOrder is %s,because third platform return is %s,it is means that order processing",
                    workOrderDo.toSimpleLogStr(), interfaceContext.getInterfaceRspObjStr());
        }
        // 失败需要考虑是否隔月重试(隔月重试：退费后vsbo失效而一级能开未失效，才需要隔月重试)
        boolean isNeedMonthRetry = openFreeFlowProperties.getCheckMonthlyRetryErrorCode().contains(bizCode);
        // 如果当月退订过，则隔月重试订购
        if (isNeedMonthRetry && openFreeFlowProperties.isOpenRetry()) {
            ActivationMqMessage messageDo = new ActivationMqMessage();
            // 一个用户一个月只能做一次隔月重试
            messageDo.setTraceId(MdcLogInterceptor.getCurrentTraceId());
            messageDo.setMsgId(userId + LocalDate.now().getYear() + LocalDate.now().getMonthValue());
            messageDo.setContext(workOrderDo.getWorkAttrs());
            messageDo.setOperatorType(MessageServiceId.Operator.CREATE);
            // 设置隔月开通时间为下个月月初某个时间范围内的随机时间
            LocalDateTime nextStartTimeOfMonth = DateUtils.getNextStartTimeOfMonth();
            LocalDateTime openTime = DateUtils.generateRandomTime(nextStartTimeOfMonth.plusHours(openFreeFlowProperties.getMonthRetryStartHour()),
                    nextStartTimeOfMonth.plusHours(openFreeFlowProperties.getMonthRetryEndHour()));
            messageDo.setOperateTime(Date.from(openTime.atZone(ZoneId.systemDefault()).toInstant()));
            messageDo.setUserId(userId);
            messageDo.setOrderId(workOrderDo.getOrderId());
            messageDo.setSopenServiceId(workOrderDo.getServiceCode());
            mqProducer.sendNewMessage(flowRetryMqProperties.getRetryTopic(), flowRetryMqProperties.getRetryTag(), messageDo);
        }
        // 结束流程
        interfaceContext.setNextHintEnum(NextHintEnum.FAIL_FINISH);
    }

    @Override
    protected boolean isSuccess(OpenFreeFlowCheckRsp rsp) {
        return OpenFreeFlowCheckRsp.isSuccess(rsp);
    }

    @Override
    protected Class<OpenFreeFlowCheckReq> getReqClass() {
        return OpenFreeFlowCheckReq.class;
    }

    @Override
    protected Class<OpenFreeFlowCheckRsp> getRspClass() {
        return OpenFreeFlowCheckRsp.class;
    }

}
