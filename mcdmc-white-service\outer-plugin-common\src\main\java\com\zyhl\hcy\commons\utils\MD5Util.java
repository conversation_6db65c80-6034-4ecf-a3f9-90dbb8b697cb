package com.zyhl.hcy.commons.utils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @Description MD5工具类
 * <AUTHOR>
 * @Date 2021/2/23 16:26
 **/
public class MD5Util {
    private MD5Util() {
    }

    public static byte[] encryptMD5(byte[] data) throws IOException {
        Object var1 = null;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(data);
            return bytes;
        } catch (NoSuchAlgorithmException e) {
            throw new IOException(e.getMessage());
        }
    }

    public static String encryptMD5String(String str) throws IOException {
       return byte2hex(encryptMD5(str.getBytes(StandardCharsets.UTF_8)));
    }

    public static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for(int i=0 ; i<bytes.length;i++){
           String hex = Integer.toHexString(bytes[i]&255);
           if(hex.length()==1){
               sign.append("0");
           }
           sign.append(hex.toUpperCase());
        }
        return sign.toString();
    }
}
