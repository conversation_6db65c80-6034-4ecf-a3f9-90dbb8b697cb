package com.zyhl.yun.member.consumer.application.consume;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.zyhl.yun.member.common.JsonUtil;
import com.zyhl.yun.member.common.PerformOrderConverter;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.enums.SubscribeTypeEnum;
import com.zyhl.yun.member.common.util.DateUtils;
import com.zyhl.yun.member.common.util.MDCUtils;
import com.zyhl.yun.member.consumer.application.common.properties.IndependentCancelProperties;
import com.zyhl.yun.member.consumer.common.constants.SwitchConstants;
import com.zyhl.yun.member.consumer.common.message.IndependentSpaceMqMessage;
import com.zyhl.yun.member.consumer.common.producer.MqProducer;
import com.zyhl.yun.member.consumer.white.gateway.WhiteGateway;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.goodsinstance.enums.GoodsInstanceStateEnum;
import com.zyhl.yun.member.notify.domain.PerformOrderDo;
import com.zyhl.yun.member.product.domain.goods.GoodsDo;

/**
 * Test class for {@link IndependentSpaceConsume}.
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/11/5
 */

@ExtendWith(MockitoExtension.class)
public class IndependentSpaceConsumeTest {

    @InjectMocks
    private IndependentSpaceConsume independentSpaceConsume;

    @Mock
    private IndependentCancelProperties independentCancelProperties;

    @Mock
    private PerformOrderConverter performOrderConverter;

    @Mock
    private WhiteGateway whiteGateway;

    @Mock
    private MqProducer mqProducer;

    private static final String DELETE_SPACE = "300001";
    private static final String TOPIC = "test-topic";
    private static final String TAG = "test-tag";
    private static final String USER_ID = "test-user-id";
    private static final String OWNER_ID = "test-owner-id";
    private static final String MESSAGE_ID = "test-message-id";
    private static final String PHONE = "13800138000";

    @BeforeEach
    public void setUp() {
        ReflectionTestUtils.setField(independentSpaceConsume, "topic", TOPIC);
        ReflectionTestUtils.setField(independentSpaceConsume, "tag", TAG);

        // Default property settings
        when(independentCancelProperties.getCancelIndependentSpaceTopicSwitch()).thenReturn(SwitchConstants.OPEN);
        // when(independentCancelProperties.isSendToOldPlatform()).thenReturn(true);
    }

    @Test
    public void testOnMessage_SwitchClosed() {
        // Given
        String message = createValidMessage();
        when(independentCancelProperties.getCancelIndependentSpaceTopicSwitch()).thenReturn(SwitchConstants.OFF);

        try (MockedStatic<JsonUtil> jsonUtilMock = Mockito.mockStatic(JsonUtil.class);
             MockedStatic<MDCUtils> mdcUtilsMock = Mockito.mockStatic(MDCUtils.class)) {
            mdcUtilsMock.when(MDCUtils::initMdc).thenAnswer(invocation -> null);
            mdcUtilsMock.when(MDCUtils::clear).thenAnswer(invocation -> null);

            independentSpaceConsume.onMessage(message);
            jsonUtilMock.verify(() -> JsonUtil.toJson(any(IndependentSpaceMqMessage.class)), never());
        }
    }

    @Test
    public void testOnMessage_InvalidJson() {
        // Given
        String message = "{\"invalid-json\": \"invalid-json\"}";

        // When
        try (MockedStatic<MDCUtils> mdcUtilsMock = Mockito.mockStatic(MDCUtils.class)) {
            independentSpaceConsume.onMessage(message);

            // Then
            mdcUtilsMock.verify(MDCUtils::initMdc);
            mdcUtilsMock.verify(MDCUtils::clear);
            verify(spy(independentSpaceConsume), never()).createUserQueryContext();
        }
    }

    @Test
    public void testOnMessage_NotDeleteSpaceEvent() {
        // Given
        IndependentSpaceMqMessage mqMessage = new IndependentSpaceMqMessage();
        mqMessage.setEventType("invalid-event");
        mqMessage.setUserId(USER_ID);
        mqMessage.setOwnerId(OWNER_ID);
        mqMessage.setMessageId(MESSAGE_ID);

        String message = JsonUtil.toJson(mqMessage);

        // When
        try (MockedStatic<MDCUtils> mdcUtilsMock = Mockito.mockStatic(MDCUtils.class)) {
            independentSpaceConsume.onMessage(message);

            // Then
            mdcUtilsMock.verify(MDCUtils::initMdc);
            mdcUtilsMock.verify(MDCUtils::clear);
            verify(spy(independentSpaceConsume), never()).createUserQueryContext();
        }
    }

    @Test
    public void testOnMessage_MissingRequiredFields() {
        // Given
        IndependentSpaceMqMessage mqMessage = new IndependentSpaceMqMessage();
        mqMessage.setEventType(DELETE_SPACE);
        mqMessage.setMessageId(MESSAGE_ID);
        // Missing userId and ownerId

        String message = JsonUtil.toJson(mqMessage);

        // When
        try (MockedStatic<MDCUtils> mdcUtilsMock = Mockito.mockStatic(MDCUtils.class)) {
            independentSpaceConsume.onMessage(message);

            // Then
            mdcUtilsMock.verify(MDCUtils::initMdc);
            mdcUtilsMock.verify(MDCUtils::clear);
            verify(spy(independentSpaceConsume), never()).createUserQueryContext();
        }
    }

    @Test
    public void testOnMessage_UserNotFound() {
        // Given
        String message = createValidMessage();

        // Mock DomainServiceContext for user query
        DomainServiceContext userContext = mock(DomainServiceContext.class);
        when(userContext.readFirst(any(), eq(UserDo.class))).thenReturn(null);

        try (MockedStatic<MDCUtils> mdcUtilsMock = Mockito.mockStatic(MDCUtils.class)) {
            // Mock the createUserQueryContext method to return our mocked context
            IndependentSpaceConsume spy = Mockito.spy(independentSpaceConsume);
            doReturn(userContext).when(spy).createUserQueryContext();

            // When
            spy.onMessage(message);

            // Then
            mdcUtilsMock.verify(MDCUtils::initMdc);
            mdcUtilsMock.verify(MDCUtils::clear);
            verify(whiteGateway, never()).validUserWhite(anyString());
        }
    }

    @Test
    public void testOnMessage_UserNotInWhitelist() {
        // Given
        String message = createValidMessage();

        // Mock user
        UserDo userDo = new UserDo();
        userDo.setMsisdn(PHONE);

        // Mock DomainServiceContext for user query
        DomainServiceContext userContext = mock(DomainServiceContext.class);
        when(userContext.readFirst(any(), eq(UserDo.class))).thenReturn(userDo);

        // User not in whitelist
        when(whiteGateway.validUserWhite(PHONE)).thenReturn(false);
        when(independentCancelProperties.isSendToOldPlatform()).thenReturn(true);

        try (MockedStatic<MDCUtils> mdcUtilsMock = Mockito.mockStatic(MDCUtils.class)) {
            // Mock the createUserQueryContext method to return our mocked context
            IndependentSpaceConsume spy = Mockito.spy(independentSpaceConsume);
            doReturn(userContext).when(spy).createUserQueryContext();

            // When
            spy.onMessage(message);

            // Then
            mdcUtilsMock.verify(MDCUtils::initMdc);
            mdcUtilsMock.verify(() -> MDCUtils.initMdc(MESSAGE_ID));
            mdcUtilsMock.verify(MDCUtils::clear);
//            verify(mqProducer).sendOldMessage(TOPIC, TAG, message);
        }
    }

    @Test
    public void testOnMessage_NoGoods() {
        // Given
        String message = createValidMessage();

        // Mock user
        UserDo userDo = new UserDo();
        userDo.setMsisdn(PHONE);
        userDo.setUserId("user-id-1");

        // Mock DomainServiceContext for user query
        DomainServiceContext userContext = mock(DomainServiceContext.class);
        when(userContext.readFirst(any(), eq(UserDo.class))).thenReturn(userDo);

        // User in whitelist
        when(whiteGateway.validUserWhite(PHONE)).thenReturn(true);

        // Mock DomainServiceContext for goods query
        DomainServiceContext goodsContext = mock(DomainServiceContext.class);
        when(goodsContext.read(any(), eq(GoodsDo.class))).thenReturn(Collections.emptyList());

        try (MockedStatic<MDCUtils> mdcUtilsMock = Mockito.mockStatic(MDCUtils.class)) {
            // Mock the methods to return our mocked contexts
            IndependentSpaceConsume spy = Mockito.spy(independentSpaceConsume);
            doReturn(userContext).when(spy).createUserQueryContext();
            doReturn(goodsContext).when(spy).createGoodsQueryContext();

            // When
            spy.onMessage(message);

            // Then
            mdcUtilsMock.verify(MDCUtils::initMdc);
            mdcUtilsMock.verify(() -> MDCUtils.initMdc(MESSAGE_ID));
            mdcUtilsMock.verify(MDCUtils::clear);
//            verify(mqProducer, never()).sendOldMessage(anyString(), anyString(), anyString());
        }
    }

    @Test
    public void testOnMessage_NoGoodsInstances() {
        // Given
        String message = createValidMessage();

        // Mock user
        UserDo userDo = new UserDo();
        userDo.setMsisdn(PHONE);
        userDo.setUserId("user-id-1");

        // Mock goods
        GoodsDo goodsDo = new GoodsDo();
        goodsDo.setGoodsId("goods-id-1");

        // Mock DomainServiceContext for user query
        DomainServiceContext userContext = mock(DomainServiceContext.class);
        when(userContext.readFirst(any(), eq(UserDo.class))).thenReturn(userDo);

        // User in whitelist
        when(whiteGateway.validUserWhite(PHONE)).thenReturn(true);

        // Mock DomainServiceContext for goods query
        DomainServiceContext goodsContext = mock(DomainServiceContext.class);
        when(goodsContext.read(any(), eq(GoodsDo.class))).thenReturn(Collections.singletonList(goodsDo));

        // Mock DomainServiceContext for goods instance query
        DomainServiceContext instanceContext = mock(DomainServiceContext.class);
        when(instanceContext.read(any(), eq(GoodsInstanceDo.class))).thenReturn(Collections.emptyList());

        try (MockedStatic<MDCUtils> mdcUtilsMock = Mockito.mockStatic(MDCUtils.class)) {
            // Mock the methods to return our mocked contexts
            IndependentSpaceConsume spy = Mockito.spy(independentSpaceConsume);
            doReturn(userContext).when(spy).createUserQueryContext();
            doReturn(goodsContext).when(spy).createGoodsQueryContext();
            doReturn(instanceContext).when(spy).createGoodsInstanceQueryContext();

            // When
            spy.onMessage(message);

            // Then
            mdcUtilsMock.verify(MDCUtils::initMdc);
            mdcUtilsMock.verify(() -> MDCUtils.initMdc(MESSAGE_ID));
            mdcUtilsMock.verify(MDCUtils::clear);
//            verify(mqProducer, never()).sendOldMessage(anyString(), anyString(), anyString());
        }
    }

    @Test
    public void testOnMessage_ProcessGoodsInstances() {
        // Given
        String message = createValidMessage();
        Date eventTime = DateUtils.parseDateTime(String.valueOf(System.currentTimeMillis()));

        // Mock user
        UserDo userDo = new UserDo();
        userDo.setMsisdn(PHONE);
        userDo.setUserId("user-id-1");

        // Mock goods
        GoodsDo goodsDo = new GoodsDo();
        goodsDo.setGoodsId("goods-id-1");

        // Mock DomainServiceContext for user query
        DomainServiceContext userContext = mock(DomainServiceContext.class);
        when(userContext.readFirst(any(), eq(UserDo.class))).thenReturn(userDo);

        // User in whitelist
        when(whiteGateway.validUserWhite(PHONE)).thenReturn(true);

        // Mock DomainServiceContext for goods query
        DomainServiceContext goodsContext = mock(DomainServiceContext.class);
        when(goodsContext.read(any(), eq(GoodsDo.class))).thenReturn(Collections.singletonList(goodsDo));

        // Create two goods instances - one normal and one expired
        GoodsInstanceDo normal = new GoodsInstanceDo();
        normal.setStateEnum(GoodsInstanceStateEnum.NORMAL);
        normal.setSubTime(DateUtils.parseDateTime(String.valueOf(System.currentTimeMillis() + 1000 * 60 * 60 * 24 * 30))); // Future date

        GoodsInstanceDo expired = new GoodsInstanceDo();
        expired.setStateEnum(GoodsInstanceStateEnum.NORMAL);
        expired.setSubTime(DateUtils.parseDateTime(String.valueOf(System.currentTimeMillis() - 1000 * 60 * 60 * 24 * 30))); // Past date

        // Mock DomainServiceContext for goods instance query
        DomainServiceContext instanceContext = mock(DomainServiceContext.class);
        when(instanceContext.read(any(), eq(GoodsInstanceDo.class))).thenReturn(Arrays.asList(normal, expired));

        // Mock DomainServiceContext for perform order
        DomainServiceContext performContext = mock(DomainServiceContext.class);

        try (MockedStatic<MDCUtils> mdcUtilsMock = Mockito.mockStatic(MDCUtils.class)) {

            // Mock static method for date parsing
            // dateUtilsMock.when(() -> DateUtils.parseDateTime(any())).thenReturn(eventTime);

            // Mock the methods to return our mocked contexts
            IndependentSpaceConsume spy = Mockito.spy(independentSpaceConsume);
            doReturn(userContext).when(spy).createUserQueryContext();
            doReturn(goodsContext).when(spy).createGoodsQueryContext();
            doReturn(instanceContext).when(spy).createGoodsInstanceQueryContext();
            doReturn(performContext).when(spy).getDomainServiceContext(any());

            // When
            spy.onMessage(message);

            // Then
            mdcUtilsMock.verify(MDCUtils::initMdc);
            mdcUtilsMock.verify(() -> MDCUtils.initMdc(MESSAGE_ID));
            mdcUtilsMock.verify(MDCUtils::clear);

            // Should only process the normal instance, not the expired one
            verify(spy, times(1)).getDomainServiceContext(expired);
            verify(spy, never()).getDomainServiceContext(normal);
            verify(performContext).writeAndFlush();
        }
    }

    /**
     * Helper method to create a valid message
     */
    private String createValidMessage() {
        IndependentSpaceMqMessage mqMessage = new IndependentSpaceMqMessage();
        mqMessage.setEventType(DELETE_SPACE);
        mqMessage.setUserId(USER_ID);
        mqMessage.setOwnerId(OWNER_ID);
        mqMessage.setMessageId(MESSAGE_ID);
        mqMessage.setEventTime(String.valueOf(System.currentTimeMillis()));
        return JsonUtil.toJson(mqMessage);
    }
}