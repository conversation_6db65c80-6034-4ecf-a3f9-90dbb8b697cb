package com.zyhl.yun.member.mcdmc.activation.mapping;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zyhl.yun.member.mcdmc.activation.po.WorkOrderAttrPo;
import com.zyhl.yun.member.mcdmc.activation.repository.WorkOrderAttrRepository;
import com.zyhl.yun.member.mcdmc.activation.serviceid.LocalServiceId;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DataIntegrityViolationException;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/07/01 14:11
 */
@Slf4j
public class WorkOrderAttrMethodMapping {
    @Getter
    @AllArgsConstructor
    public enum WRITE {
        INSERT(LocalServiceId.INSERT_OPERATION, "插入工单属性") {
            @Override
            public int exec(WorkOrderAttrRepository workOrderAttrRepository, WorkOrderAttrPo workOrderAttrPo) {
                workOrderAttrPo.setCreatedTime(new Date());
                workOrderAttrPo.setUpdatedTime(new Date());
                boolean isSave = workOrderAttrRepository.save(workOrderAttrPo);
                return isSave ? 1 : 0;
            }
        },
        UPSERT(LocalServiceId.UPSERT_OPERATION, "插入或更新（存在插入，不存在更新）") {
            @Override
            public int exec(WorkOrderAttrRepository workOrderAttrRepository, WorkOrderAttrPo workOrderAttrPo) {
                // 创建更新时间均使用数据库的时间
                workOrderAttrPo.setUpdatedTime(null);
                workOrderAttrPo.setCreatedTime(null);
                boolean isSave;
                try {
                    isSave = workOrderAttrRepository.save(workOrderAttrPo);
                } catch (DataIntegrityViolationException e) {
                    log.debug("DuplicateKeyException: {}", e.getMessage());
                    isSave = false;
                }
                boolean isUpdate = false;
                if (Boolean.FALSE.equals(isSave)) {
                    LambdaQueryWrapper<WorkOrderAttrPo> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(WorkOrderAttrPo::getWorkId, workOrderAttrPo.getWorkId())
                            .eq(WorkOrderAttrPo::getAttrCode, workOrderAttrPo.getAttrCode())
                            .eq(StringUtils.isNotEmpty(workOrderAttrPo.getUserId()),
                                    WorkOrderAttrPo::getUserId, workOrderAttrPo.getUserId());
                    isUpdate = workOrderAttrRepository.update(workOrderAttrPo, queryWrapper);
                }
                return isSave || isUpdate ? 1 : 0;
            }
        },

        UPDATE_BY_ID(LocalServiceId.UPDATE_BY_ID_OPERATION, "根据主键更新") {
            @Override
            public int exec(WorkOrderAttrRepository workOrderAttrRepository, WorkOrderAttrPo workOrderAttrPo) {
                workOrderAttrPo.setUpdatedTime(new Date());
                LambdaQueryWrapper<WorkOrderAttrPo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(WorkOrderAttrPo::getWorkId, workOrderAttrPo.getWorkId())
                        .eq(WorkOrderAttrPo::getAttrCode, workOrderAttrPo.getAttrCode())
                        .eq(StringUtils.isNotEmpty(workOrderAttrPo.getUserId()),
                                WorkOrderAttrPo::getUserId, workOrderAttrPo.getUserId());
                boolean isUpdate = workOrderAttrRepository.update(workOrderAttrPo, queryWrapper);
                return isUpdate ? 1 : 0;
            }
        };

        private final String serviceId;
        private final String msg;

        public abstract int exec(WorkOrderAttrRepository workOrderAttrRepository,
                                 WorkOrderAttrPo workOrderAttrPo);
    }

    public static WorkOrderAttrMethodMapping.WRITE getWriterByServiceId(String serviceId) {
        for (WorkOrderAttrMethodMapping.WRITE write : WorkOrderAttrMethodMapping.WRITE.values()) {
            if (write.serviceId.equals(serviceId)) {
                return write;
            }
        }
        return null;
    }
}
