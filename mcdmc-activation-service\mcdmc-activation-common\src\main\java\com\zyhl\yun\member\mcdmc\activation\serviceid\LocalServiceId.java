package com.zyhl.yun.member.mcdmc.activation.serviceid;

/**
 * @Description: 领域常量
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2024/4/25
 */
public interface LocalServiceId {

    /**
     * 数据库插入
     */
    String INSERT_OPERATION = "insertRecord";
    /**
     * 数据库插入(主键存在则忽略）
     */
    String INSERT_IGNORE_OPERATION = "insertIgnoreRecord";
    /**
     * 数据库updateById
     */
    String UPDATE_BY_ID_OPERATION = "updateRecordById";
    /**
     * 数据库upsert
     */
    String UPSERT_OPERATION = "upsertRecord";
}
