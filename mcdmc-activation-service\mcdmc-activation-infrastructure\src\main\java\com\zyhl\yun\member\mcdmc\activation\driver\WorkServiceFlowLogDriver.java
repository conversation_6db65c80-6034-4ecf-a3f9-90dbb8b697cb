package com.zyhl.yun.member.mcdmc.activation.driver;

import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.common.domain.framework.BaseLocalDriver;
import com.zyhl.yun.member.common.domain.framework.DomainEntityPersistenceWrapper;
import com.zyhl.yun.member.mcdmc.activation.convertor.WorkServiceFlowLogConvertor;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowLogDo;
import com.zyhl.yun.member.mcdmc.activation.domains.conditions.QueryWorkServiceFlowLogCondition;
import com.zyhl.yun.member.mcdmc.activation.mapping.FlowLogMethodMapping;
import com.zyhl.yun.member.mcdmc.activation.po.WorkServiceFlowLogPo;
import com.zyhl.yun.member.mcdmc.activation.repository.WorkServiceFlowLogRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/06/05 17:23
 */
@Slf4j
@Component
public class WorkServiceFlowLogDriver extends BaseLocalDriver<WorkServiceFlowLogDo> {

    @Resource
    private WorkServiceFlowLogRepository flowLogRepository;
    @Resource
    private WorkServiceFlowLogConvertor workServiceFlowLogConvertor;

    @Override
    public int doWrite(DomainEntityPersistenceWrapper domainEntityWrapper) {
        if (log.isDebugEnabled()) {
            log.debug("[INFRA] doWrite start. InstWorkServiceFlowLogDo:{}", domainEntityWrapper.getData());
        }
        String serviceId = domainEntityWrapper.getDomainServiceContext().getServiceId();
        WorkServiceFlowLogDo workFlowLogDo = (WorkServiceFlowLogDo) domainEntityWrapper.getData();
        WorkServiceFlowLogPo workFlowLogPo = workServiceFlowLogConvertor.toWorkFlowLogPO(workFlowLogDo);
        FlowLogMethodMapping.WRITE writer = FlowLogMethodMapping.getWriterByServiceId(serviceId);
        if (null == writer) {
            log.error("[INFRA] doWrite error. not supported entity type:{}", domainEntityWrapper.getData().getClass());
            throw new ServiceException("FlowLogDriver没有对应的writer,serviceId=" + serviceId);
        }
        return writer.exec(flowLogRepository, workFlowLogPo);
    }

    @Override
    public WorkServiceFlowLogDo doReadByPk(Serializable pk) {
        return null;
    }

    @Override
    public List<? extends WorkServiceFlowLogDo> doReadByCondition(BaseCondition condition) {
        List<WorkServiceFlowLogPo> workServiceFlowLogList = null;
        if (condition instanceof QueryWorkServiceFlowLogCondition) {
            workServiceFlowLogList = flowLogRepository.searchByCondition((QueryWorkServiceFlowLogCondition) condition);
        } else {
            workServiceFlowLogList = flowLogRepository.searchByCondition(condition);
        }
        return workServiceFlowLogConvertor.toDoList(workServiceFlowLogList);
    }

    @Override
    public Long doGetCount(BaseCondition condition) {
        return 0L;
    }

    @Override
    protected List<Class> getSupportedClass() {
        return Collections.singletonList(WorkServiceFlowLogDo.class);
    }

}
