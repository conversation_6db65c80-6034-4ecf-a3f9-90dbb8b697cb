// package com.zyhl.yun.member.consumer.application.consume;
//
// import cn.hutool.core.collection.CollUtil;
// import cn.hutool.core.date.DatePattern;
// import cn.hutool.core.text.CharSequenceUtil;
// import com.zyhl.yun.member.common.JsonUtil;
// import com.zyhl.yun.member.common.cache.EntityCacheManager;
// import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
// import com.zyhl.yun.member.common.domain.serviceid.*;
// import com.zyhl.yun.member.common.enums.OpTypeEnum;
// import com.zyhl.yun.member.common.enums.PriorSortEnum;
// import com.zyhl.yun.member.common.enums.SubscribeOpTypeEnum;
// import com.zyhl.yun.member.common.enums.SubscribeTypeEnum;
// import com.zyhl.yun.member.common.util.MDCUtils;
// import com.zyhl.yun.member.config.GoodsInstanceCacheConfig;
// import com.zyhl.yun.member.config.OrderCacheConfig;
// import com.zyhl.yun.member.consumer.GoodsInstance2NotifyConverter;
// import com.zyhl.yun.member.consumer.application.common.enums.Mcdmc2Mcdmc2JiFeiPayWayEnum;
// import com.zyhl.yun.member.consumer.application.req.ReqParamsHW;
// import com.zyhl.yun.member.common.util.CRC16Util;
// import com.zyhl.yun.member.domain.familypackage.condition.QueryFamilyPackageBindCondition;
// import com.zyhl.yun.member.domain.familypackage.domain.FamilyPackageBindDo;
// import com.zyhl.yun.member.domain.familypackage.domain.FamilyPackageGroupRelationDo;
// import com.zyhl.yun.member.domain.familypackage.enums.FamilyPackageStateEnum;
// import com.zyhl.yun.member.domain.familypackage.serviceid.FamilyPackageGroupRelationServiceId;
// import com.zyhl.yun.member.domain.familypackage.serviceid.FamilyPackageServiceId;
// import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
// import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
// import com.zyhl.yun.member.domain.user.domain.QueryUserCondition;
// import com.zyhl.yun.member.domain.user.domain.UserDo;
// import com.zyhl.yun.member.goodsinstance.enums.GoodsInstanceStateEnum;
// import com.zyhl.yun.member.jifei.condition.TPCTOrderDeliverReqVo;
// import com.zyhl.yun.member.jifei.resp.TPCTOrderDeliverResp;
// import com.zyhl.yun.member.mq.domain.MqEventContext;
// import com.zyhl.yun.member.mq.domain.MqExtInfo;

// import com.zyhl.yun.member.order.common.constants.CloudSpaceOrderConstant;
// import com.zyhl.yun.member.order.domain.dto.QueryOrderCondition;
// import com.zyhl.yun.member.order.domain.dto.QueryOrderDetailCondition;
// import com.zyhl.yun.member.product.common.enums.ChargeTypeEnum;
// import com.zyhl.yun.member.product.common.enums.PayWayEnum;
// import com.zyhl.yun.member.product.domain.goods.GoodsDo;
// import com.zyhl.yun.member.product.domain.goods.facade.GoodsServiceFacade;
// import com.zyhl.yun.member.support.common.circle.domain.GroupInfoDo;
// import com.zyhl.yun.member.support.common.circle.facade.CircleGroupFacade;
// import com.zyhl.yun.member.task.common.constants.DateConstant;
// import com.zyhl.yun.member.task.common.context.MessageNoticeEventDTO;
// import com.zyhl.yun.member.task.common.domain.MessageNoticeTaskDo;
// import com.zyhl.yun.member.task.common.domain.QueryMessageNoticeCondition;
// import com.zyhl.yun.member.task.common.enums.MessageEventTypeEnum;
// import lombok.extern.slf4j.Slf4j;
// import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
// import org.apache.rocketmq.spring.core.RocketMQListener;
// import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
// import org.springframework.data.redis.core.StringRedisTemplate;
// import org.springframework.stereotype.Service;
//
// import javax.annotation.Resource;
// import java.text.ParseException;
// import java.text.SimpleDateFormat;
// import java.util.*;
// import java.util.concurrent.TimeUnit;
//
// /**
//  * vsbo消费者
//  * 包名称:  com.zyhl.yun.member.consumer.application.consume
//  * 类名称:  VsboTopicConsume
//  * 类描述:  。
//  *
//  * <AUTHOR>
//  * @version V1.0.0
//  * @date 2025/3/7 11:45
//  */
// @Service
// @Slf4j
// @RocketMQMessageListener(
//         nameServer = "${rocketmq.consumer.old.nameServerAddr}",
//         topic = "${rocketmq.consumer.vsbo.topic}",
//         consumerGroup = "${rocketmq.consumer.vsbo.group}",
//         selectorExpression = "${rocketmq.consumer.vsbo.tag}"
// )
// @ConditionalOnProperty(name = "rocketmq.consumer.vsbo.enable", havingValue = "true", matchIfMissing = true)
// public class VsboTopicConsume implements RocketMQListener<MqEventContext> {
//
//     @Resource
//     private GoodsInstanceCacheConfig goodsInstanceCacheConfig;
//
//     @Resource
//     private OrderCacheConfig orderCacheConfig;
//
//     @Resource
//     private StringRedisTemplate stringRedisTemplate;
//
//     @Resource
//     private GoodsInstance2NotifyConverter goodsInstance2NotifyConverter;
//
//     // Redis lock key prefix
//     private static final String LOCK_KEY_PREFIX = "vsbo:expire:message:";
//     // Redis lock expiration time (in seconds)
//     private static final long LOCK_EXPIRE_TIME = 60;
//
//     @Override
//     public void onMessage(MqEventContext message) {
//         if (Objects.isNull(message)) {
//             log.warn("message is null");
//             return;
//         }
//         try {
//             MDCUtils.initMdc(message.getTraceID());
//             log.info("consume vsbo topic message: {}", message);
//             //查询用户信息
//             if (Objects.isNull(message.getOrdUserID())) {
//                 log.warn("user id is null");
//                 return;
//             }
//             DomainServiceContext domainServiceContext = createUserQueryContext();
//             QueryUserCondition queryUserCondition = new QueryUserCondition();
//             queryUserCondition.setAccount(message.getOrdUserID());
//             queryUserCondition.setQueryGotoneParams(false);
//             UserDo userDo = domainServiceContext.readFirst(queryUserCondition, UserDo.class);
//             if (Objects.isNull(userDo)) {
//                 log.error("user not found, userId: {}", message.getOrdUserID());
//                 return;
//             }
//
//             MqExtInfo extInfo = message.getExtInfo();
//             if (Objects.isNull(extInfo) || Objects.isNull(extInfo.getSubscriptionId())) {
//                 log.warn("message mqExtInfo or subscriptionId is null");
//                 return;
//             }
//
//             // ps: 因为新旧模块同步存在时延问题，加上缓存清除操作，避免部分问题
//             try {
//                 clearGoodsInstanceCache(userDo.getUserId(), extInfo.getSubscriptionId());
//                 clearOrderCache(userDo.getUserId(), extInfo.getOrderID());
//             } catch (Exception e) {
//                 log.error("clear goodsInstance cache error", e);
//             }
//
//             //查询实例记录
//             DomainServiceContext goodsInstanceServiceContext = createGoodsInstanceQueryConditionContext();
//             QueryGoodsInstanceCondition queryGoodsInstanceCondition = new QueryGoodsInstanceCondition();
//             queryGoodsInstanceCondition.setGoodsInstanceIdList(Collections.singletonList(extInfo.getSubscriptionId()));
//             queryGoodsInstanceCondition.setPhoneNumber(userDo.getMsisdn());
//             GoodsInstanceDo goodsInstanceDo = goodsInstanceServiceContext.readFirst(queryGoodsInstanceCondition, GoodsInstanceDo.class);
//
//             if (goodsInstanceDo == null) {
//                 log.warn("goodsInstance not found. goodsInstanceId: {}", extInfo.getSubscriptionId());
//             }
//
//             SimpleDateFormat sdf = new SimpleDateFormat(DatePattern.PURE_DATETIME_PATTERN);
//             Date noticeTime = null;
//             if (CharSequenceUtil.isBlank(extInfo.getOrderEndTime())) {
//                 if (Objects.nonNull(goodsInstanceDo)) {
//                     noticeTime = goodsInstanceDo.getEffectiveEndTime();
//                 } else {
//                     log.warn("goodsInstanceDo is null and orderEndTime is null, cannot get notice time");
//                     return;
//                 }
//             } else {
//                 noticeTime = sdf.parse(extInfo.getOrderEndTime());
//             }
//
//
//
//             OpTypeEnum byOpType = getOpTypeEnum(extInfo);
//
//             if (Objects.isNull(byOpType)) {
//                 log.warn("message opType is null");
//                 return;
//             }
//
//             switch (byOpType) {
//                 //订购
//                 case REFRESH:
//                     subMessageProcess(goodsInstanceDo, userDo, extInfo);
//                     break;
//                 case ACTIVE:
//                     deleteExpireMessageEvent(goodsInstanceDo);
//                     break;
//                 case PAUSE:
//                     insertExpireMessageEvent(goodsInstanceDo, userDo, extInfo);
//                     break;
//                 case UNSUBSCRIBE:
//                     insertExpireMessageEvent(goodsInstanceDo, userDo, extInfo);
//                     // 直接处理家庭套餐相关逻辑
//                     handleFamilyPackageForUnsubscribe(goodsInstanceDo, noticeTime);
//                     break;
//                 default:
//             }
//         } catch (Exception e) {
//             log.error("consume vsbo topic message error", e);
//         } finally {
//             MDCUtils.clear();
//         }
//     }
//
//     /**
//      * 获取操作类型
//      * 优先从actionType中获取，如果获取不到则从status中获取
//      *
//      * @param extInfo MQ扩展信息
//      * @return 操作类型枚举
//      */
//     private OpTypeEnum getOpTypeEnum(MqExtInfo extInfo) {
//         if (Objects.isNull(extInfo)) {
//             log.warn("message mqExtInfo is null");
//             return null;
//         }
//
//         // 优先从actionType获取操作类型
//         String actionType = extInfo.getActionType();
//         if (Objects.nonNull(actionType)) {
//             OpTypeEnum opTypeEnum = OpTypeEnum.getByOpType(actionType);
//             if (Objects.nonNull(opTypeEnum)) {
//                 return opTypeEnum;
//             }
//         }
//
//         // 如果actionType获取不到操作类型，尝试从status获取
//         String status = extInfo.getStatus();
//         if (Objects.nonNull(status)) {
//             SubscribeTypeEnum subscribeTypeEnum = SubscribeTypeEnum.fromCode(Integer.valueOf(status));
//             return SubscribeOpTypeEnum.getOpTypeEnumBySubscribeType(subscribeTypeEnum);
//         }
//         log.warn("message status is null");
//         return null;
//     }
//
//
//     /**
//      * 清除商品实例缓存
//      *
//      * @param userId          用户ID
//      * @param goodsInstanceId 商品实例ID
//      */
//     private void clearGoodsInstanceCache(String userId, String goodsInstanceId) {
//         if (CharSequenceUtil.isEmpty(userId) || CharSequenceUtil.isEmpty(goodsInstanceId)) {
//             return;
//         }
//
//         //清除商品实例缓存，参考GoodsInstancePo的getCacheKey方法
//         String goodsInstanceCacheKey = "{" + userId + "}:" + goodsInstanceId;
//         QueryGoodsInstanceCondition queryGoodsInstanceCondition = new QueryGoodsInstanceCondition();
//         queryGoodsInstanceCondition.setUserId(userId);
//         EntityCacheManager.getCache(GoodsInstanceCacheConfig.CacheNames.GOODS_INSTANCE, goodsInstanceCacheConfig.getCacheType())
//                 .deleteByKeyList(Arrays.asList(goodsInstanceCacheKey), queryGoodsInstanceCondition);
//     }
//
//
//     /**
//      * 清除订单缓存
//      *
//      * @param orderId 订单ID
//      * @param userId  用户ID
//      */
//     private void clearOrderCache(String userId, String orderId) {
//         if (CharSequenceUtil.isEmpty(orderId) || CharSequenceUtil.isEmpty(userId)) {
//             return;
//         }
//
//         //清除订单缓存，参考OrderPo的getCacheKey方法
//         String orderCacheKey = "{" + userId + "}:" + orderId;
//         QueryOrderCondition queryOrderCondition = new QueryOrderCondition();
//         queryOrderCondition.setUserId(userId);
//         EntityCacheManager.getCache(OrderCacheConfig.ORDER_CACHE_NAME, orderCacheConfig.getCacheType())
//                 .deleteByKeyList(Arrays.asList(orderCacheKey), queryOrderCondition);
//
//         //清除订单详情缓存，参考OrderDetailPo的getCacheKey方法
//         String orderDetailCacheKey = "{" + userId + "}:" + orderId;
//         QueryOrderDetailCondition queryOrderDetailCondition = new QueryOrderDetailCondition();
//         queryOrderDetailCondition.setUserId(userId);
//         EntityCacheManager.getCache(OrderCacheConfig.ORDER_DETAIL_CACHE_NAME, orderCacheConfig.getCacheType())
//                 .deleteByKeyList(Arrays.asList(orderDetailCacheKey), queryOrderDetailCondition);
//     }
//
//
//     /**
//      * 订购子流程处理
//      *
//      * @param goodsInstanceDo
//      */
//     private void subMessageProcess(GoodsInstanceDo goodsInstanceDo, UserDo userDo, MqExtInfo extInfo) throws ParseException {
//         if (Objects.isNull(goodsInstanceDo)) {
//             log.warn("message mqEventContext or productID or goodsInstanceDo is null");
//             return;
//         }
//
//         //查询商品信息
//         GoodsDo goodsDo = GoodsServiceFacade.getGoods(goodsInstanceDo.getGoodsId(), false);
//         if (Objects.isNull(goodsDo)) {
//             log.warn("goodsDo is null, productId:{}", goodsInstanceDo.getGoodsId());
//             return;
//         }
//
//         if (ChargeTypeEnum.BY_TIMES == goodsDo.getChargeType()) {
//             insertExpireMessageEvent(goodsInstanceDo, userDo, extInfo);
//         }
//
//         familyPackageProcess(goodsDo, goodsInstanceDo, userDo, extInfo);
//
//     }
//
//     /**
//      * 家庭套餐处理
//      *
//      * @param goodsDo
//      * @param goodsInstanceDo
//      * @param userDo
//      */
//     private void familyPackageProcess(GoodsDo goodsDo, GoodsInstanceDo goodsInstanceDo, UserDo userDo, MqExtInfo extInfo) {
//         // 华为支付需要调用计费接口进行通知
//         if (PayWayEnum.HUAWEI_PAY.getCode().toString().equals(extInfo.getPayWay())) {
//             // 通知计费中心发货
//             DomainServiceContext tpctOrderDeliverContext = new DomainServiceContext(JifeiServiceId.TPCT_ORDER_DELIVER);
//             TPCTOrderDeliverReqVo tpctOrderDeliverReqVo = TPCTOrderDeliverReqVo.builder()
//                     .orderId(extInfo.getPaymentOrderId())
//                     .requestTime(new SimpleDateFormat(DateConstant.DATE_PATTERN).format(goodsInstanceDo.getSubTime()))
//                     .payWay(Mcdmc2Mcdmc2JiFeiPayWayEnum.HW_CHANNEL.getPayWay())
//                     .busiType("05")
//                     .bizType(Mcdmc2Mcdmc2JiFeiPayWayEnum.HW_CHANNEL.getBizType())
//                     .reqParams(JsonUtil.toJson(ReqParamsHW.builder().orderStatus("SUCCESS").build()))
//                     .build();
//             tpctOrderDeliverContext.readFirst(tpctOrderDeliverReqVo, TPCTOrderDeliverResp.class);
//         }
//
//         //判断是否是家庭套餐订阅
//         if (Objects.isNull(goodsDo.getGoodsExt()) || CharSequenceUtil.isEmpty(goodsDo.getGoodsExt().getFamilyPackageGiftGoodsId())) {
//             log.info("goodsExt is null or familyPackageGiftGoodsId is null, productId:{}", goodsDo.getGoodsId());
//             return;
//         }
//
//         //subway==400的是赠送的家庭套餐会员，检查主套餐商品实例是否退订，若是，退订之
//         if (CloudSpaceOrderConstant.SubWayEnum.NOT_FAMILY_MASTER.equals(String.valueOf(goodsInstanceDo.getSubWay()))) {
//             log.info("subway is 400, goodsInstance is bound, need to handle family package for unsubscribe");
//             unsubcribeWhenMasterGoodsInstanceIsUnsub(goodsInstanceDo, userDo);
//             return;
//         }
//
//         if (!PayWayEnum.isInnerPayWay(goodsInstanceDo.getPayWay())) {
//             //端外订购的自动创圈
//             log.info("payWay is not inner pay way, payWay:{}", goodsInstanceDo.getPayWay());
//
//             GroupInfoDo defaultCircleGroup = CircleGroupFacade
//                     .createDefaultCircleGroup(userDo.getMsisdn(), userDo.getUserDomainId());
//
//             //插入家庭套餐圈子关系表
//             DomainServiceContext domainServiceContext = createFamilyPackageGroupRelationInsertContext();
//             FamilyPackageGroupRelationDo familyPackageGroupRelationDo = new FamilyPackageGroupRelationDo();
//             familyPackageGroupRelationDo.setMasterUserId(userDo.getUserId());
//             familyPackageGroupRelationDo.setMasterOrderId(goodsInstanceDo.getOrderId());
//             familyPackageGroupRelationDo.setMasterGoodsInstanceId(goodsInstanceDo.getGoodsInstanceId());
//             familyPackageGroupRelationDo.setGroupId(String.valueOf(defaultCircleGroup.getGroupId()));
//             domainServiceContext.putInstance(familyPackageGroupRelationDo);
//             domainServiceContext.writeAndFlush();
//         }
//     }
//
//     /**
//      * 插入过期消息事件
//      *
//      * @param goodsInstanceDo
//      */
//     public void insertExpireMessageEvent(GoodsInstanceDo goodsInstanceDo, UserDo userDo, MqExtInfo extInfo) {
//         if (Objects.isNull(goodsInstanceDo) || Objects.isNull(goodsInstanceDo.getOrderId()) || Objects.isNull(goodsInstanceDo.getUserId())) {
//             log.warn("goodsInstanceDo, orderId or userId is null, cannot insert expire message event");
//             return;
//         }
//
//         //查询商品信息，如果非家庭套餐，则不插入过期消息事件
//         GoodsDo goodsDo = GoodsServiceFacade.getGoods(goodsInstanceDo.getGoodsId(), false);
//         if (Objects.isNull(goodsDo)) {
//             log.warn("goodsDo is null, productId:{}", goodsInstanceDo.getGoodsId());
//             return;
//         }
//         if (goodsDo.getGoodsExt() == null || CharSequenceUtil.isEmpty(goodsDo.getGoodsExt().getFamilyPackageGiftGoodsId())) {
//             log.warn("goodsExt is null or familyPackageGiftGoodsId is null, productId:{}", goodsDo.getGoodsId());
//             return;
//         }
//
//
//         // 构建Redis锁的key，使用 orderId + userId + EVENT 确保对每个用户的过期事件进行精细化控制
//         String noticeWay = MessageNoticeTaskDo.NoticeWay.EVENT.getCode();
//         String lockKey = LOCK_KEY_PREFIX + goodsInstanceDo.getUserId() + ":" + goodsInstanceDo.getOrderId() + ":" + noticeWay;
//         String lockValue = String.valueOf(System.currentTimeMillis());
//         Boolean locked = false;
//
//
//         // 尝试获取Redis分布式锁，使用setIfAbsent方法（相当于SETNX命令）
//         locked = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
//
//         if (Boolean.FALSE.equals(locked)) {
//             log.info("Another thread is processing the expire message event for orderId: {}, skip this one", goodsInstanceDo.getOrderId());
//             return;
//         }
//
//         //如果已经存在过期消息事件，则不需要插入
//         DomainServiceContext queryServiceContext = createMessageNoticeQueryContext();
//         QueryMessageNoticeCondition queryMessageNoticeCondition = new QueryMessageNoticeCondition();
//         queryMessageNoticeCondition.setSourceId(goodsInstanceDo.getOrderId());
//         queryMessageNoticeCondition.setUserId(goodsInstanceDo.getUserId());
//         queryMessageNoticeCondition.setNoticeWay(MessageNoticeTaskDo.NoticeWay.EVENT.getCode());
//         List<MessageNoticeTaskDo> messageNoticeTaskDoList = queryServiceContext.read(queryMessageNoticeCondition, MessageNoticeTaskDo.class);
//
//         if (CollUtil.isNotEmpty(messageNoticeTaskDoList)) {
//             for (MessageNoticeTaskDo messageNoticeTaskDo : messageNoticeTaskDoList) {
//                 MessageNoticeEventDTO messageNoticeEventDTO =
//                         JsonUtil.fromJson((String) messageNoticeTaskDo.getContext(), MessageNoticeEventDTO.class);
//                 if (Objects.nonNull(messageNoticeEventDTO) && MessageEventTypeEnum.EXPIRE == messageNoticeEventDTO.getEventType()) {
//                     DomainServiceContext domainServiceContext = createMessageNoticeUpdateTimeContext();
//                     try {
//                         SimpleDateFormat sdf = new SimpleDateFormat(DatePattern.PURE_DATETIME_PATTERN);
//                         Date noticeTime = sdf.parse(extInfo.getOrderEndTime());
//                         messageNoticeTaskDo.setNoticeTime(noticeTime);
//                     } catch (Exception e) {
//                         log.warn("Parse orderEndTime failed: {}", extInfo.getOrderEndTime());
//                     }
//                     domainServiceContext.putInstance(messageNoticeTaskDo);
//                     domainServiceContext.writeAndFlush();
//                     return;
//                 }
//             }
//         }
//
//         DomainServiceContext domainServiceContext = createMessageNoticeSaveContext();
//         MessageNoticeTaskDo messageNoticeTaskDo = new MessageNoticeTaskDo();
//         messageNoticeTaskDo.setGoodsIds(goodsInstanceDo.getGoodsId());
//         messageNoticeTaskDo.setNoticeWay(MessageNoticeTaskDo.NoticeWay.EVENT.getCode());
//         messageNoticeTaskDo.setUserId(goodsInstanceDo.getUserId());
//         // 使用extInfo中的orderEndTime作为通知时间
//         if (Objects.nonNull(extInfo) && Objects.nonNull(extInfo.getOrderEndTime())) {
//             try {
//                 // orderEndTime 格式如: '20250331235959'
//                 SimpleDateFormat sdf = new SimpleDateFormat(DatePattern.PURE_DATETIME_PATTERN);
//                 Date noticeTime = sdf.parse(extInfo.getOrderEndTime());
//                 messageNoticeTaskDo.setNoticeTime(noticeTime);
//             } catch (Exception e) {
//                 // 解析失败时回退到使用goodsInstanceDo的过期时间
//                 log.warn("Parse orderEndTime failed: {}, fallback to goodsInstanceDo.getEffectiveEndTime", extInfo.getOrderEndTime());
//                 messageNoticeTaskDo.setNoticeTime(goodsInstanceDo.getEffectiveEndTime());
//             }
//         } else {
//             // 回退到使用goodsInstanceDo的过期时间
//             messageNoticeTaskDo.setNoticeTime(goodsInstanceDo.getEffectiveEndTime());
//         }
//         messageNoticeTaskDo.setPriorSort(PriorSortEnum.DEFAULT.getCode());
//         messageNoticeTaskDo.setSlice(CRC16Util.getSlice(goodsInstanceDo.getOrderId()));
//         messageNoticeTaskDo.setSourceId(goodsInstanceDo.getOrderId());
//         messageNoticeTaskDo.setReceiver(userDo.getMsisdn());
//         //填充context
//         MessageNoticeEventDTO messageNoticeEventDTO = new MessageNoticeEventDTO();
//         messageNoticeEventDTO.setUserId(goodsInstanceDo.getUserId());
//         messageNoticeEventDTO.setGoodsInstanceId(goodsInstanceDo.getGoodsInstanceId());
//         messageNoticeEventDTO.setEventType(MessageEventTypeEnum.EXPIRE);
//         messageNoticeTaskDo.setContext(JsonUtil.toJson(messageNoticeEventDTO));
//         domainServiceContext.putInstance(messageNoticeTaskDo);
//
//         domainServiceContext.writeAndFlush();
//     }
//
//     /**
//      * 删除过期消息事件
//      *
//      * @param goodsInstanceDo
//      */
//     private void deleteExpireMessageEvent(GoodsInstanceDo goodsInstanceDo) {
//         if (Objects.isNull(goodsInstanceDo)) {
//             log.warn("goodsInstanceDo is null");
//             return;
//         }
//         DomainServiceContext domainServiceContext = createDeleteExpireEventNoticeContext();
//         MessageNoticeTaskDo messageNoticeTaskDo = new MessageNoticeTaskDo();
//         messageNoticeTaskDo.setSourceId(goodsInstanceDo.getOrderId());
//         messageNoticeTaskDo.setNoticeWay(MessageNoticeTaskDo.NoticeWay.EVENT.getCode());
//         domainServiceContext.writeAndFlush();
//     }
//
//     /**
//      * 处理家庭套餐退订
//      *
//      * @param goodsInstanceDo 商品实例
//      */
//     private void handleFamilyPackageForUnsubscribe(GoodsInstanceDo goodsInstanceDo, Date effectiveEndTime) {
//         if (Objects.isNull(goodsInstanceDo)) {
//             log.warn("goodsInstanceDo is null");
//             return;
//         }
//         // 查询商品信息
//         GoodsDo goodsDo = GoodsServiceFacade.getGoods(goodsInstanceDo.getGoodsId(), false);
//         if (Objects.isNull(goodsDo)) {
//             log.warn("goodsDo is null, productId:{}", goodsInstanceDo.getGoodsId());
//             return;
//         }
//
//         // 判断是否是家庭套餐订阅
//         if (Objects.isNull(goodsDo.getGoodsExt()) || Objects.isNull(goodsDo.getGoodsExt().getFamilyPackageGiftGoodsId())) {
//             log.warn("goodsExt is null or familyPackageGiftGoodsId is null, productId:{}", goodsDo.getGoodsId());
//             return;
//         }
//
//         //查询家庭套餐绑定关系
//         DomainServiceContext domainServiceContext = createFamilyPackageQueryContext();
//         QueryFamilyPackageBindCondition queryFamilyPackageBindCondition = new QueryFamilyPackageBindCondition();
//         queryFamilyPackageBindCondition.setMasterUserId(goodsInstanceDo.getUserId());
//         queryFamilyPackageBindCondition.setMasterOrderId(goodsInstanceDo.getOrderId());
//         queryFamilyPackageBindCondition.setMasterGoodsInstanceId(goodsInstanceDo.getGoodsInstanceId());
//         List<FamilyPackageBindDo> familyPackageBindDoList = domainServiceContext.read(queryFamilyPackageBindCondition, FamilyPackageBindDo.class);
//
//         if (CollUtil.isEmpty(familyPackageBindDoList)) {
//             log.warn("familyPackageBindDoList is empty, userId:{}, orderId:{}", goodsInstanceDo.getUserId(), goodsInstanceDo.getOrderId());
//             return;
//         }
//
//         try {
//             // 遍历处理每个绑定关系
//             for (FamilyPackageBindDo familyPackageBindDo : familyPackageBindDoList) {
//                 // 查询绑定用户的商品实例
//                 DomainServiceContext boundGoodsInstanceContext = createGoodsInstanceQueryConditionContext();
//                 QueryGoodsInstanceCondition queryBoundGoodsInstanceCondition = new QueryGoodsInstanceCondition();
//                 queryBoundGoodsInstanceCondition.setGoodsInstanceIdList(Collections.singletonList(familyPackageBindDo.getBoundGoodsInstanceId()));
//                 queryBoundGoodsInstanceCondition.setUserId(familyPackageBindDo.getBoundUserId());
//                 queryBoundGoodsInstanceCondition.setPhoneNumber(familyPackageBindDo.getBoundUserPhoneNumber());
//                 GoodsInstanceDo boundGoodsInstanceDo = boundGoodsInstanceContext.readFirst(queryBoundGoodsInstanceCondition, GoodsInstanceDo.class);
//
//                 if (Objects.isNull(boundGoodsInstanceDo)) {
//                     log.warn("boundGoodsInstanceDo not found, userId:{}, goodsInstanceId:{}",
//                             familyPackageBindDo.getBoundUserId(), familyPackageBindDo.getBoundGoodsInstanceId());
//                     continue;
//                 }
//
//                 // 退订绑定用户的商品实例
//                 DomainServiceContext notifyServiceContext = createPerformOrderContext();
//                 PerformOrderDo performOrderDo = notifyServiceContext.newInstance(PerformOrderDo.class);
//                 goodsInstance2NotifyConverter.goodsInstance2Notify(boundGoodsInstanceDo, performOrderDo);
//                 performOrderDo.setOpType(SubscribeTypeEnum.UN_SUBSCRIBE_TYPE_ENUM);
//                 performOrderDo.setGoodsInstanceId(boundGoodsInstanceDo.getGoodsInstanceId());
//                 performOrderDo.setMsisdn(familyPackageBindDo.getBoundUserPhoneNumber());
//                 performOrderDo.setEffectiveEndTime(effectiveEndTime);
//                 notifyServiceContext.writeAndFlush();
//             }
//         } catch (Exception e) {
//             log.error("handleFamilyPackageForUnsubscribe error", e);
//         }
//     }
//
//     /**
//      * 根据主套餐是否退订，来判断是否退订当前实例
//      *
//      * @param goodsInstanceDo 商品实例
//      * @return 家庭套餐绑定关系列表
//      */
//     private void unsubcribeWhenMasterGoodsInstanceIsUnsub(GoodsInstanceDo goodsInstanceDo, UserDo userDo) {
//         if (Objects.isNull(goodsInstanceDo) ||
//                 Objects.isNull(goodsInstanceDo.getUserId()) ||
//                 Objects.isNull(goodsInstanceDo.getOrderId()) ||
//                 Objects.isNull(goodsInstanceDo.getGoodsInstanceId())) {
//             log.warn("goodsInstanceDo or its essential fields are null, cannot query family package bind");
//             return;
//         }
//
//         // 创建查询条件
//         DomainServiceContext domainServiceContext = createFamilyPackageQueryContext();
//         QueryFamilyPackageBindCondition queryFamilyPackageBindCondition = new QueryFamilyPackageBindCondition();
//         queryFamilyPackageBindCondition.setBoundUserId(goodsInstanceDo.getUserId());
//         queryFamilyPackageBindCondition.setBoundOrderId(goodsInstanceDo.getOrderId());
//         queryFamilyPackageBindCondition.setBoundGoodsInstanceId(goodsInstanceDo.getGoodsInstanceId());
//         queryFamilyPackageBindCondition.setFamilyPackageStateEnumList(Collections.singletonList(FamilyPackageStateEnum.BOUND));
//
//         // 执行查询
//         FamilyPackageBindDo familyPackageBindDo = domainServiceContext.readFirst(queryFamilyPackageBindCondition, FamilyPackageBindDo.class);
//
//         if (Objects.isNull(familyPackageBindDo)) {
//             log.info("No family package bind found for boundUserId: {}, boundOrderId: {}, boundGoodsInstanceId: {}",
//                     goodsInstanceDo.getUserId(), goodsInstanceDo.getOrderId(), goodsInstanceDo.getGoodsInstanceId());
//             return;
//         }
//
//         // 查询主商品实例信息
//         DomainServiceContext masterGoodsInstanceContext = createGoodsInstanceQueryConditionContext();
//         QueryGoodsInstanceCondition masterQueryCondition = new QueryGoodsInstanceCondition();
//         masterQueryCondition.setGoodsInstanceIdList(Collections.singletonList(familyPackageBindDo.getMasterGoodsInstanceId()));
//         masterQueryCondition.setUserId(familyPackageBindDo.getMasterUserId());
//         masterQueryCondition.setPhoneNumber(familyPackageBindDo.getMasterUserPhoneNumber());
//         GoodsInstanceDo masterGoodsInstanceDo = masterGoodsInstanceContext.readFirst(masterQueryCondition, GoodsInstanceDo.class);
//
//         if (Objects.isNull(masterGoodsInstanceDo)) {
//             log.warn("Master goods instance not found, masterUserId: {}, masterGoodsInstanceId: {}",
//                     familyPackageBindDo.getMasterUserId(), familyPackageBindDo.getMasterGoodsInstanceId());
//             return;
//         }
//
//         log.info("Found master goods instance: {}", masterGoodsInstanceDo.getGoodsInstanceId());
//
//         // 根据状态判断是否需要退订
//         if (masterGoodsInstanceDo.getStateEnum() == GoodsInstanceStateEnum.UN_SUB &&
//                 goodsInstanceDo.getStateEnum() != GoodsInstanceStateEnum.UN_SUB) {
//
//             log.info("Master instance is unsubscribed/expired, unsubscribing bound instance. MasterId: {}, BoundId: {}",
//                     masterGoodsInstanceDo.getGoodsInstanceId(), goodsInstanceDo.getGoodsInstanceId());
//
//             // 退订绑定用户的商品实例
//             try {
//                 DomainServiceContext notifyServiceContext = createPerformOrderContext();
//                 PerformOrderDo performOrderDo = notifyServiceContext.newInstance(PerformOrderDo.class);
//                 goodsInstance2NotifyConverter.goodsInstance2Notify(goodsInstanceDo, performOrderDo);
//                 performOrderDo.setOpType(SubscribeTypeEnum.UN_SUBSCRIBE_TYPE_ENUM);
//                 performOrderDo.setMsisdn(userDo.getMsisdn());
//                 performOrderDo.setEffectiveEndTime(masterGoodsInstanceDo.getEffectiveEndTime());
//                 notifyServiceContext.writeAndFlush();
//             } catch (Exception e) {
//                 log.error("Failed to unsubscribe bound instance: {}", goodsInstanceDo.getGoodsInstanceId(), e);
//             }
//         } else {
//             log.info("Master instance is still active, no need to unsubscribe bound instance");
//         }
//     }
//
//     /**
//      * 创建用户查询上下文
//      *
//      * @return 用户查询领域服务上下文
//      */
//     public DomainServiceContext createUserQueryContext() {
//         return new DomainServiceContext(UserServiceId.USER_LSB_SERVICE);
//     }
//
//     /**
//      * 创建商品实例查询条件上下文
//      *
//      * @return 商品实例查询条件领域服务上下文
//      */
//     public DomainServiceContext createGoodsInstanceQueryConditionContext() {
//         return new DomainServiceContext(GoodsInstanceExtendServiceId.QUERY_CONDITION_GOODS_INSTANCE);
//     }
//
//     /**
//      * 创建商品实例查询上下文
//      *
//      * @return 商品实例查询领域服务上下文
//      */
//     public DomainServiceContext createGoodsInstanceQueryContext() {
//         return new DomainServiceContext(GoodsInstanceServiceId.QUERY_GOODS_INSTANCE);
//     }
//
//     /**
//      * 创建消息通知查询上下文
//      *
//      * @return 消息通知查询领域服务上下文
//      */
//     public DomainServiceContext createMessageNoticeQueryContext() {
//         return new DomainServiceContext(MessageNoticeServiceId.QUERY);
//     }
//
//     /**
//      * 创建消息通知时间更新上下文
//      *
//      * @return 消息通知时间更新领域服务上下文
//      */
//     public DomainServiceContext createMessageNoticeUpdateTimeContext() {
//         return new DomainServiceContext(MessageNoticeServiceId.UPDATE_NOTICE_TIME_BY_TASK_ID);
//     }
//
//     /**
//      * 创建消息通知保存上下文
//      *
//      * @return 消息通知保存领域服务上下文
//      */
//     public DomainServiceContext createMessageNoticeSaveContext() {
//         return new DomainServiceContext(MessageNoticeServiceId.SAVE);
//     }
//
//     /**
//      * 创建过期事件通知删除上下文
//      *
//      * @return 过期事件通知删除领域服务上下文
//      */
//     public DomainServiceContext createDeleteExpireEventNoticeContext() {
//         return new DomainServiceContext(MessageNoticeServiceId.DELETE_EXPIRE_EVENT_NOTICE);
//     }
//
//     /**
//      * 创建家庭套餐查询上下文
//      *
//      * @return 家庭套餐查询领域服务上下文
//      */
//     public DomainServiceContext createFamilyPackageQueryContext() {
//         return new DomainServiceContext(FamilyPackageServiceId.QUERY_FAMILY_PACKAGE_BIND);
//     }
//
//     /**
//      * 创建家庭套餐组关系插入上下文
//      *
//      * @return 家庭套餐组关系插入领域服务上下文
//      */
//     public DomainServiceContext createFamilyPackageGroupRelationInsertContext() {
//         return new DomainServiceContext(FamilyPackageGroupRelationServiceId.INSERT_FAMILY_PACKAGE_GROUP_RELATION);
//     }
//
//     /**
//      * 创建订单执行上下文
//      *
//      * @return 订单执行领域服务上下文
//      */
//     public DomainServiceContext createPerformOrderContext() {
//         return new DomainServiceContext(PerformOrderServiceId.PERFORM_ORDER);
//     }
// }
