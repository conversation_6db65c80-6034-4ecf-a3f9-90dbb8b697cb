package com.zyhl.yun.member.common.domain.framework.executor;

/**
 * <AUTHOR>
 * @date 2024/08/02 16:00
 */

import com.zyhl.yun.member.common.domain.framework.BaseDriver;
import com.zyhl.yun.member.common.domain.framework.DomainEntityPersistenceWrapper;
import com.zyhl.yun.member.common.domain.framework.enums.ExecOperate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

@Data
@AllArgsConstructor
@Slf4j
public class DeleteDriverExecutor implements DriverExecutor {


    private static final long serialVersionUID = 7086903117369427097L;

    private BaseDriver driver;


    @Override
    public Serializable exec(DomainEntityPersistenceWrapper wrapper) {
        if (ExecOperate.DELETE.equals(wrapper.getExecOperate())) {
            return driver.getDelegateDriver(wrapper)
                    .doDelete(wrapper.getData());
        } else {
            return driver.getDelegateDriver(wrapper)
                    .doDeleteByPk(wrapper.getData());
        }
    }
}
