package com.zyhl.yun.member.mcdmc.activation.remote.send.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/3 14:54
 * @descrition 集运免流量配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "platform.jiyun")
public class JiyunProperties {
    private String flowSpecSwitch;
    private String jiyunSourceApp;
    private String jiyunSkuCode;
    private String jiyunGoodsID;
    private String jiyunActivityId;
    private String jiyunSecretId;
    private String jiyunSecretKey;
    private String flowSpec;
    private String subscribeFlowSpec;
    private String flowSpecV2;
    private String flowSpecV3;
    private String subscribeFlowSpecNotify;
    private Integer subscribeFlowSpecNotifyRedisExpirationTime;
    /**
     * 集运请求remot地址
     */

    private String jiyunURL;
    /**
     * 集运请求地址
     */
    private String givenPrdOrderRelQueryURL;

    private List<JiYunSkuInfoProperties> skuInfoList;

    public String getSkuIdByActivityId(String activityId) {
        Optional<JiYunSkuInfoProperties> skuInfo = skuInfoList.stream()
                .filter(info -> Objects.equals(activityId, info.getActivityId()))
                .findFirst();
        return skuInfo.map(JiYunSkuInfoProperties::getSkuId).orElse(null);
    }

    public String getActivityIdBySkuId(String skuId) {
        Optional<JiYunSkuInfoProperties> skuInfo = skuInfoList.stream()
                .filter(info -> Objects.equals(skuId, info.getSkuId()))
                .findFirst();
        return skuInfo.map(JiYunSkuInfoProperties::getActivityId).orElse(null);
    }

}
