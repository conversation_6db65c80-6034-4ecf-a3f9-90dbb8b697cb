package com.zyhl.yun.member.mcdmc.activation.callback.notify;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.enums.RightsStatusEnum;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.mcdmc.activation.callback.rsp.ReturnComNotifyRsp;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.CallbackException;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.FlowStaticConfig;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IServiceFlow;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.Md5Util;
import com.zyhl.yun.member.mcdmc.activation.flow.rai.check.RaiOrderReUnSubFlow;
import com.zyhl.yun.member.mcdmc.activation.flow.rai.check.RaiOrderReductionFlow;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiNormalProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiRefundProperties;
import com.zyhl.yun.member.mcdmc.activation.result.BaseResult;
import com.zyhl.yun.member.mcdmc.activation.result.ResultCode;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.CallbackContext;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.CallbackTemplate;
import com.zyhl.yun.member.mcdmc.activation.callback.req.ComReturnNotifyReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;

import static com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceServiceId.REFUND_GOODS_INSTANCE;

/**
 * 权益退费回调统一接口
 * 包括权益中心退费回调
 * com.huawei.jaguar.vsbo.service.action.RaiEquityNotifyResultAction#notifyRightsSalesReturnResult
 *
 * <AUTHOR>
 * @since 2024/06/22 12:07
 */
@Slf4j
@Component
public class RaiRefundComCallback extends CallbackTemplate<ComReturnNotifyReq, ReturnComNotifyRsp> {

    @Resource
    private RaiRefundProperties raiRefundProperties;
    @Resource
    private RaiNormalProperties raiProperties;

    @Override
    protected void doCheck(CallbackContext<ComReturnNotifyReq, ReturnComNotifyRsp> callbackContext) throws CallbackException {
        ComReturnNotifyReq notifyReq = callbackContext.getCallbackReq();
        // 参数校验
        super.doCheckEmpty(notifyReq);
        super.doCheckEmpty(notifyReq.getContractRoot());
        ComReturnNotifyReq.ContractRootDTO.Head head = notifyReq.getContractRoot().getHead();
        super.doCheckEmpty(head);
        ComReturnNotifyReq.ContractRootDTO.Body body = notifyReq.getContractRoot().getBody();
        super.doCheckEmpty(body);
        String transactionId = head.getTransactionId();
        super.doCheckEmpty(transactionId, "transactionId为空");
        super.doCheckEmpty(head.getSign(), "sign为空");
        super.doCheckEmpty(body.getChannelNo(), "订单号为空");
        super.doCheckEmpty(body.getReturnStatus(), "订单状态为空");
        // 验签
        String authStr = transactionId + head.getReqTime() +
                raiRefundProperties.getRightsSalesReturnKey() + JSONUtil.toJsonStr(body, JSONConfig.create().setNatureKeyComparator());
        authStr = authStr.replaceAll("\\s", "");
        String sign = Md5Util.md5Encode(authStr);
        if (!Objects.equals(sign, head.getSign())) {
            log.error("验签失败，订单id={}，传入sign:{}，实际sign:{}", body.getChannelNo(), sign, head.getSign());
            throw new CallbackException(ResultCode.SIGN_CHECK_ERROR);
        }
        IServiceFlow sendFlow = FlowStaticConfig.getIFlow(callbackContext.getServiceCode());
        // 核减补退不关心回调结果
        if (sendFlow instanceof RaiOrderReductionFlow || sendFlow instanceof RaiOrderReUnSubFlow) {
            throw new CallbackException(ResultCode.SUCCESS, null);
        }
    }

    @Override
    protected ReturnComNotifyRsp buildNotifyRsp(CallbackContext<ComReturnNotifyReq, ReturnComNotifyRsp> callbackContext, BaseResult result) {
        ReturnComNotifyRsp.Head head = new ReturnComNotifyRsp.Head();
        String curTime = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        head.setReqTime(curTime);
        head.setTransactionId(curTime);
        ReturnComNotifyRsp.Body body = new ReturnComNotifyRsp.Body(result.getResultCode(), result.getMessage());
        String authStr = head.getTransactionId() + head.getReqTime() + raiProperties.getEquityOrderSecretKey()
                + JSONUtil.toJsonStr(body, JSONConfig.create().setNatureKeyComparator());
        String sign = Md5Util.md5Encode(authStr);
        head.setSign(sign.toUpperCase());
        ReturnComNotifyRsp.ContractRoot contractRoot = new ReturnComNotifyRsp.ContractRoot(head, body);
        return new ReturnComNotifyRsp(contractRoot);
    }

    @Override
    protected void doNotifyOtherDomainStatus(CallbackContext<ComReturnNotifyReq, ReturnComNotifyRsp> callbackContext,
                                             GoodsInstanceDo goodsInstanceExtendDo) {
        // 负责更新父子订单的状态，以及会员域的相关订购
        DomainServiceContext memberContext = new DomainServiceContext(REFUND_GOODS_INSTANCE);
        GoodsInstanceDo goodsInstanceDo = memberContext.newInstance(GoodsInstanceDo.class);
        goodsInstanceDo.setUserId(goodsInstanceExtendDo.getUserId());
        goodsInstanceDo.setOrderId(goodsInstanceExtendDo.getOrderId());
        goodsInstanceDo.setGoodsInstanceId(goodsInstanceExtendDo.getGoodsInstanceId());
        goodsInstanceDo.setGoodsPackageInstanceId(goodsInstanceExtendDo.getGoodsPackageInstanceId());
        // 更新商品实例相关值
        boolean isSuccess = isSuccess(callbackContext.getCallbackReq());
        RightsStatusEnum rightsStatusEnum = isSuccess ? RightsStatusEnum.RETURN_SUCCESS : RightsStatusEnum.RETURN_FAIL;
        goodsInstanceDo.setRightsStatusEnum(RightsStatusEnum.fromStatus(rightsStatusEnum.getStatus()));
        goodsInstanceDo.setEffectiveEndTime(new Date());
        goodsInstanceDo.setUnsubTime(new Date());
        memberContext.writeAndFlush();
    }

    @Override
    protected boolean isSuccess(ComReturnNotifyReq notifyReq) {
        return ComReturnNotifyReq.RETURN_SUSS.equals(notifyReq.getContractRoot().getBody().getReturnStatus());
    }

    @Override
    protected BaseResult getSuccessResult() {
        return BaseResult.sc("0000", "SUCCESS");
    }

    @Override
    protected String getSearchCondition(ComReturnNotifyReq notifyReq) {
        return notifyReq.getContractRoot().getBody().getChannelNo();
    }

    @Override
    protected Class<ComReturnNotifyReq> getNotifyReqClass() {
        return ComReturnNotifyReq.class;
    }

    @Override
    protected SendOperation getSendOperation() {
        return SendOperation.REFUND;
    }
}
