package com.zyhl.yun.member.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/04 16:54
 * @descrition 集运免流是否查询异网枚举值
 */
@Getter
@AllArgsConstructor
public enum JiyunSwitchEnum {
    /**
     * 0关闭
     */
    OFF(0, "关闭"),
    /**
     * 1开启
     */
    ON(1, "开启"),
    ;

    private final Integer code;
    private final String desc;
}
