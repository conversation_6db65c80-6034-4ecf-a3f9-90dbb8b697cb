package com.zyhl.yun.member.mcdmc.activation.util;

import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.order.OrderServiceId;
import com.zyhl.yun.member.facade.GoodsInstanceServiceFacade;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.facade.OrderServiceFacade;
import com.zyhl.yun.member.order.domain.OrderDo;
import com.zyhl.yun.member.order.domain.dto.QueryOrderCondition;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 订单模块调用工具类
 *
 * <AUTHOR>
 * @since 2024/10/12 16:32
 */
@Slf4j
public class OrderContextUtil {
    private OrderContextUtil() {
    }

    public static OrderDo queryOrderDo(String orderNo, String userId) {
        return queryOrderDo(orderNo, userId, true);
    }

    public static OrderDo queryOrderDo(String orderNo, String userId, boolean isQueryDetail) {
        DomainServiceContext orderContext = new DomainServiceContext(OrderServiceId.QUERY_ORDER);
        QueryOrderCondition condition = new QueryOrderCondition();
        condition.setOrderNo(orderNo);
        condition.setUserId(userId);
        condition.setQueryOrderDetail(isQueryDetail);
        return orderContext.readFirst(condition, OrderDo.class);
    }

    /**
     * 查询对应的父订单
     *
     * @param goodInstanceId 子订单对应的商品实例id
     * @param userId         用户id
     * @param isQueryDetail  是否查询订单详情
     */
    public static OrderDo queryParentOrderDo(String goodInstanceId, String userId, boolean isQueryDetail) {
        GoodsInstanceDo parentGoodsInstanceDo = GoodsInstanceServiceFacade.qryParentGoodsInstance(userId, goodInstanceId);
        if (Objects.nonNull(parentGoodsInstanceDo)) {
            return OrderServiceFacade.getOrder(parentGoodsInstanceDo.getOrderId(), userId, isQueryDetail);
        }
        return null;
    }

    public static String queryParentOrderId(String goodInstanceId, String userId) {
        GoodsInstanceDo goodsInstanceDo = GoodsInstanceServiceFacade.qryGoodsInstance(userId, goodInstanceId);
        log.info("search goodInstance，goodsInstanceId：{}，userId：{}，goodsInstanceDo is {}", goodInstanceId, userId, goodsInstanceDo);
        if (Objects.isNull(goodsInstanceDo)) {
            return null;
        }
        GoodsInstanceDo parentGoodsInstanceDo = GoodsInstanceServiceFacade.qryGoodsInstance(userId, goodsInstanceDo.getGoodsPackageInstanceId());
        return Objects.isNull(parentGoodsInstanceDo) ? null : parentGoodsInstanceDo.getOrderId();
    }

    /**
     * 更新订单的免流订单号
     *
     * @param userId      用户id
     * @param orderId     订单id
     * @param flowOrderNo 免流订单号
     */
    public static void updateOrderFlowOrderNo(String userId, String orderId, String flowOrderNo) {
        // 更新订单域状态
        DomainServiceContext orderContext = new DomainServiceContext(OrderServiceId.UPDATE_ORDER);
        OrderDo orderDo = orderContext.newInstance(OrderDo.class);
        orderDo.setOrderNo(orderId);
        orderDo.setOrderUserId(userId);
        orderDo.setFlowOrderNo(flowOrderNo);
        orderContext.writeAndFlush();
    }
}
