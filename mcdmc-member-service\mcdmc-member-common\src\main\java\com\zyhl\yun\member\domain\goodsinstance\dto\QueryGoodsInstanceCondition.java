package com.zyhl.yun.member.domain.goodsinstance.dto;

import cn.hutool.core.collection.CollUtil;
import com.zyhl.yun.member.common.cache.ConditionCacheable;
import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.common.enums.RightsStatusEnum;
import com.zyhl.yun.member.common.enums.SortEnum;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.goodsinstance.enums.GoodsInstanceStateEnum;
import com.zyhl.yun.member.product.common.enums.ChargeTypeEnum;
import com.zyhl.yun.member.product.common.enums.GoodsSalesTypeEnum;
import com.zyhl.yun.member.product.common.enums.PayWayEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Author: zouzefeng
 * @Date: 2024/7/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class QueryGoodsInstanceCondition extends BaseCondition implements ConditionCacheable {


    private static final long serialVersionUID = -5015669208710421553L;

    /**
     * 订单Id
     */
    private String orderId;


    /**
     * 订单id列表
     */
    private List<String> orderIdList;


    /**
     * 商品实例id列表
     */
    private List<String> goodsInstanceIdList;

    /**
     * 排除商品id列表
     */
    private List<String> excludeGoodsIdList;


    /**
     * 商品包实例id列表
     */
    private List<String> goodsPackageInstanceIdList;


    /**
     * 商品id列表
     */
    private List<String> goodsIdList;

    /**
     * 产品id列表
     */
    private List<String> productIdList;


    /**
     * 资产id列表
     */
    private List<String> resourceIdList;

    /**
     * 权益类型列表
     */
    private List<String> rightsTypeList;


    /**
     * 计费类型
     */
    private ChargeTypeEnum chargeType;


    /**
     * 商品销售类型列表
     */
    private List<GoodsSalesTypeEnum> goodsSalesTypeList;

    /**
     * 状态列表
     */
    private List<GoodsInstanceStateEnum> stateList;


    /**
     * 权益状态列表
     */
    private List<RightsStatusEnum> excludeRightStateList;


    /**
     * 商品包实例id
     */
    private String packageInstanceId;

    /**
     * 订购时间开始
     */
    private Date subTimeStart;

    /**
     * 订购时间结束
     */
    private Date subTimeEnd;

    /**
     * 生效开始时间开始
     */
    private Date effectiveStartTimeStart;

    /**
     * 生效开始时间结束
     */
    private Date effectiveStartTimeEnd;

    /**
     * 生效结束时间开始
     */
    private Date effectiveEndTimeStart;

    /**
     * 生效结束时间结束
     */
    private Date effectiveEndTimeEnd;

    /**
     * 用户Id
     */
    private String userId;


    /**
     * 用户Id列表
     */
    private List<String> userIdList;


    /**
     * 手机号码
     */
    private String phoneNumber;


    /**
     * 时间分组Id
     */
    private String timePlanGoodsGroupId;

    /**
     * 是否排除核增核减
     */
    private boolean excludeAccounting;

    /**
     * 是否排除核算新增
     */
    private boolean excludeAccountingAddition;

    /**
     * 是否查询暂停
     */
    private boolean queryPaused;

    /**
     * 订购时间排序规则
     */
    private SortEnum subTimeSort;

    /**
     * 支付方式
     */
    private List<PayWayEnum> payWayList;

    /**
     * 分区
     */
    private Integer partition;

    /**
     * 检查时间开始
     */
    private Date checkTimeStart;

    /**
     * 检查时间结束
     */
    private Date checkTimeEnd;

    /**
     * 获取缓存key列表
     *
     * @return 返回缓存的key
     */
    @Override
    public List<Serializable> getCacheKeyList() {
        List<Serializable> userIds = new ArrayList<>();
        if (StringUtils.hasLength(userId)) {
            userIds.add(userId);
        }
        if (!CollectionUtils.isEmpty(userIdList)) {
            userIds.addAll(userIdList);
        }
        return userIds;
    }


    @Override
    public List<?> filter(List<?> goodsInstanceList) {
        if (CollectionUtils.isEmpty(goodsInstanceList)) {
            return goodsInstanceList;
        }
        List<GoodsInstanceDo> filtered = new ArrayList<>();
        for (Object obj : goodsInstanceList) {
            if (!(obj instanceof GoodsInstanceDo)) {
                continue;
            }
            GoodsInstanceDo inst = (GoodsInstanceDo) obj;
            // orderId
            if (StringUtils.hasLength(this.orderId) && !this.orderId.equals(inst.getOrderId())) {
                continue;
            }
            // orderIdList
            if (!CollectionUtils.isEmpty(this.orderIdList)
                    && (inst.getOrderId() == null || !this.orderIdList.contains(inst.getOrderId()))) {
                continue;
            }
            // goodsInstanceIdList
            if (!CollectionUtils.isEmpty(this.goodsInstanceIdList)
                    && (inst.getGoodsInstanceId() == null || !this.goodsInstanceIdList.contains(inst.getGoodsInstanceId()))) {
                continue;
            }
            // excludeGoodsIdList
            if (!CollectionUtils.isEmpty(this.excludeGoodsIdList)
                    && inst.getGoodsId() != null && this.excludeGoodsIdList.contains(inst.getGoodsId())) {
                continue;
            }
            // goodsPackageInstanceIdList
            if (!CollectionUtils.isEmpty(this.goodsPackageInstanceIdList)
                    && (inst.getGoodsPackageInstanceId() == null || !this.goodsPackageInstanceIdList.contains(inst.getGoodsPackageInstanceId()))) {
                continue;
            }
            // goodsIdList
            if (!CollectionUtils.isEmpty(this.goodsIdList)
                    && (inst.getGoodsId() == null || !this.goodsIdList.contains(inst.getGoodsId()))) {
                continue;
            }
            // productIdList
            if (!CollectionUtils.isEmpty(this.productIdList)
                    && (inst.getProductId() == null || !this.productIdList.contains(inst.getProductId()))) {
                continue;
            }
            // resourceIdList
            if (!CollectionUtils.isEmpty(this.resourceIdList)
                    && (inst.getResourceId() == null || !this.resourceIdList.contains(inst.getResourceId()))) {
                continue;
            }
            // rightsTypeList
            if (!CollectionUtils.isEmpty(this.rightsTypeList)
                    && (inst.getRightsType() == null || !this.rightsTypeList.contains(inst.getRightsType()))) {
                continue;
            }
            // chargeType
            if (this.chargeType != null
                    && (inst.getChargeType() == null || !this.chargeType.name().equals(inst.getChargeType()))) {
                continue;
            }
            // goodsSalesTypeList
            if (!CollectionUtils.isEmpty(this.goodsSalesTypeList)
                    && (inst.getSaleType() == null || this.goodsSalesTypeList.stream().noneMatch(e -> e.getType() == inst.getSaleType()))) {
                continue;
            }
            // stateList
            if (!CollectionUtils.isEmpty(this.stateList)
                    && (inst.getStateEnum() == null || !this.stateList.contains(inst.getStateEnum()))) {
                continue;
            }
            // excludeRightStateList
            if (!CollectionUtils.isEmpty(this.excludeRightStateList) && inst.getRightsStatusEnum() != null
                    && this.excludeRightStateList.contains(inst.getRightsStatusEnum())) {
                continue;
            }
            // packageInstanceId
            if (StringUtils.hasLength(this.packageInstanceId)
                    && !this.packageInstanceId.equals(inst.getGoodsPackageInstanceId())) {
                continue;
            }
            // subTimeStart
            if (this.subTimeStart != null
                    && (inst.getSubTime() == null || inst.getSubTime().before(this.subTimeStart))) {
                continue;
            }
            // subTimeEnd
            if (this.subTimeEnd != null
                    && (inst.getSubTime() == null || inst.getSubTime().after(this.subTimeEnd))) {
                continue;
            }
            // effectiveStartTimeStart
            if (this.effectiveStartTimeStart != null
                    && (inst.getEffectiveStartTime() == null || inst.getEffectiveStartTime().before(this.effectiveStartTimeStart))) {
                continue;
            }
            // effectiveStartTimeEnd
            if (this.effectiveStartTimeEnd != null
                    && (inst.getEffectiveStartTime() == null || inst.getEffectiveStartTime().after(this.effectiveStartTimeEnd))) {
                continue;
            }
            // effectiveEndTimeStart
            if (this.effectiveEndTimeStart != null
                    && (inst.getEffectiveEndTime() == null || inst.getEffectiveEndTime().before(this.effectiveEndTimeStart))) {
                continue;
            }
            // effectiveEndTimeEnd
            if (this.effectiveEndTimeEnd != null
                    && (inst.getEffectiveEndTime() == null || inst.getEffectiveEndTime().after(this.effectiveEndTimeEnd))) {
                continue;
            }
            // checkTimeStart
            if (this.checkTimeStart != null
                    && (inst.getCheckTime() == null || inst.getCheckTime().before(this.checkTimeStart))) {
                continue;
            }
            // checkTimeEnd
            if (this.checkTimeEnd != null
                    && (inst.getCheckTime() == null || inst.getCheckTime().after(this.checkTimeEnd))) {
                continue;
            }
            // userId
            if (StringUtils.hasLength(this.userId)
                    && !this.userId.equals(inst.getUserId())) {
                continue;
            }
            // userIdList
            if (!CollectionUtils.isEmpty(this.userIdList)
                    && (inst.getUserId() == null || !this.userIdList.contains(inst.getUserId()))) {
                continue;
            }
            // timePlanGoodsGroupId
            if (StringUtils.hasLength(this.timePlanGoodsGroupId)
                    && !this.timePlanGoodsGroupId.equals(inst.getTimePlanGoodsGroupId())) {
                continue;
            }
            // excludeAccounting
            // (not implemented: requires business logic)
            // excludeAccountingAddition
            // (not implemented: requires business logic)
            // queryPaused
            if (this.queryPaused
                    && (inst.getStateEnum() == null || !GoodsInstanceStateEnum.PAUSE.equals(inst.getStateEnum()))) {
                continue;
            }
            // payWayList
            if (!CollectionUtils.isEmpty(this.payWayList)
                    && (inst.getPayWay() == null || this.payWayList.stream().noneMatch(e -> e.getCode() == inst.getPayWay()))) {
                continue;
            }
            filtered.add(inst);
        }
        // sort if needed
        if (this.subTimeSort != null && !filtered.isEmpty()) {
            filtered.sort((a, b) -> {
                if (a.getSubTime() == null || b.getSubTime() == null) {
                    return 0;
                }
                return this.subTimeSort == SortEnum.ASC ? a.getSubTime().compareTo(b.getSubTime()) : b.getSubTime().compareTo(a.getSubTime());
            });
        }
        return filtered;
    }

    /**
     * 是否需要数据库分页查询
     */
    public boolean isNeedDbPageSearch() {
        return this.getSubTimeStart() == null
                && this.getSubTimeEnd() == null
                && this.getEffectiveStartTimeStart() == null
                && this.getEffectiveStartTimeEnd() == null
                && this.getEffectiveEndTimeEnd() == null
                && this.getEffectiveEndTimeStart() == null;
    }

    public boolean isQueryPaused() {
        if (CollUtil.isNotEmpty(this.stateList) && this.stateList.contains(GoodsInstanceStateEnum.PAUSE)) {
            return true;
        }
        return this.queryPaused;
    }
}
