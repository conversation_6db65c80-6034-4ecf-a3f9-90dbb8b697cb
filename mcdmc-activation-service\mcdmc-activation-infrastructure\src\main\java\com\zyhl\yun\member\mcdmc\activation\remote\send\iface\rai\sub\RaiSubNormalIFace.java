package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.sub;

import com.zyhl.yun.member.facade.OrderServiceFacade;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiBaseProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiNormalProperties;
import com.zyhl.yun.member.order.domain.OrderDo;
import com.zyhl.yun.member.product.domain.channel.enums.OrderSettlementFlagEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 常规权益中心发货
 * com.huawei.jaguar.vsbo.pub.thread.RaiOrderAcceptThread#run
 *
 * <AUTHOR>
 * @apiNote 有回调，详情看<hr/>RaiNormalSubCallback<hr/>
 * @since 2024/06/18 14:42
 */
@Slf4j
@Component
public class RaiSubNormalIFace extends RaiSubBaseIFace {
    @Resource
    private RaiNormalProperties raiProperties;

    // TODO-2024/12/19: 权益订购前需要添加主动查询机制
    @Override
    protected RaiBaseProperties getBaseRaiProperties() {
        return raiProperties;
    }

    @Override
    protected String getOrderChannelCode(String userId, String parentOrderId) {
        OrderDo parentOrder = OrderServiceFacade.getOrder(parentOrderId, userId, false);
        if (Objects.isNull(parentOrder)) {
            log.error("未找到父订单信息，默认使用原价渠道号，userId:{},parentOrderId:{}", userId, parentOrderId);
            return raiProperties.getOrderChannelCode(OrderSettlementFlagEnum.ORIGINAL.getCode());
        }
        String extInfoByKey = (String) parentOrder.getExtInfoByKey(OrderDo.ExtInfoKeys.ORDER_SETTLEMENT_FLAG);
        return raiProperties.getOrderChannelCode(extInfoByKey);
    }

}
