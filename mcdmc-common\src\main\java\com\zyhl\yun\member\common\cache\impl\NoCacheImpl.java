package com.zyhl.yun.member.common.cache.impl;

import com.zyhl.yun.member.common.cache.ConditionCacheable;
import com.zyhl.yun.member.common.cache.wrapper.CollectionWrapper;
import com.zyhl.yun.member.common.cache.wrapper.ConditionCountWrapper;
import com.zyhl.yun.member.common.cache.wrapper.ConditionWrapper;
import com.zyhl.yun.member.common.cache.wrapper.SimpleValueWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/20 20:46
 */
@Component
@Slf4j
public class NoCacheImpl extends BaseDelegationCacheImpl {

    @Override
    public SimpleValueWrapper get(String cacheSpace, Serializable key, Class clazz) {
        log.debug("[CACHE] get. cacheSpace: {}, key: {}, clazz: {}", cacheSpace, key, clazz.getName());
        return null;
    }

    @Override
    public CollectionWrapper getList(String cacheSpace, List<Serializable> keyList, Class clazz) {
        log.debug("[CACHE] getList. cacheSpace: {}, keyList: {}, clazz: {}", cacheSpace, keyList, clazz.getName());
        return null;
    }

    @Override
    public ConditionWrapper getKeyListByCondition(String cacheSpace, ConditionCacheable condition) {
        log.debug("[CACHE] getKeyListByCondition. cacheSpace: {}, condition: {}", cacheSpace, condition);
        return null;
    }

    @Override
    public ConditionCountWrapper getTotalCountByCondition(String cacheSpace, ConditionCacheable condition) {
        log.debug("[CACHE] getTotalCountByCondition. cacheSpace: {}, condition: {}", cacheSpace, condition);
        return null;
    }

    @Override
    public void put(String cacheSpace, Serializable key, SimpleValueWrapper value) {
        log.debug("[CACHE] put. cacheSpace: {}, key:{}, value: {}", cacheSpace, key, value);
    }

    @Override
    public void put(String cacheSpace, ConditionCacheable condition, ConditionWrapper conditionWrapper) {
        log.debug("[CACHE] put. cacheSpace: {}, condition:{}, conditionWrapper: {}", cacheSpace, condition, conditionWrapper);
    }

    @Override
    public void put(String cacheSpace, ConditionCacheable condition, ConditionCountWrapper conditionCountWrapper) {
        log.debug("[CACHE] put. cacheSpace: {}, condition:{}, conditionCountWrapper: {}", cacheSpace, condition, conditionCountWrapper);
    }

    @Override
    public void putList(String cacheSpace, CollectionWrapper collectionWrapper) {
        log.debug("[CACHE] putList. cacheSpace: {}, collectionWrapper: {}", cacheSpace, collectionWrapper);
    }

    @Override
    public void deleteByKeyList(String cacheSpace, List<Serializable> key) {
        log.debug("[CACHE] deleteByKeyList. cacheSpace: {}, key: {}", cacheSpace, key);
    }

    @Override
    public void clearConditions(String cacheSpace, ConditionCacheable condition) {
        log.debug("[CACHE] clearConditions. cacheSpace: {}, condition: {}", cacheSpace, condition);
    }
}
