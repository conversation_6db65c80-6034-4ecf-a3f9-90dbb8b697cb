@startuml
'https://plantuml.com/sequence-diagram

autonumber
[o->openresty: 外部发起请求
openresty->lua: 解析校验请求报文
lua->lua:根据uri查询映射规则及对应路径
alt 全局开关打开
    lua->openresty:转发的旧服务
    [o<-openresty:结束请求
end
alt 0号规则-直接获取路径值
    lua->lua: 根据路径直接获取对应值-account/userId/userDomainId
else 1号规则-userId_***
    lua->lua: 根据路径获取值，然后截取最前面的userId
else 2号规则-***_userId
    lua->lua: 根据路径获取值，然后截取最后面的userId
else 3号规则-调用远程获取account
    lua->lua: 根据路径获取外部订单id-outOrderId
    lua->newVsbo: 根据outOrderId查询手机号
else 4号规则-***_rft
    lua->openresty: 转发到新服务
    [o<-openresty: 结束请求
else 无映射规则
    lua->openresty: 添加uri前缀并转发旧服务
    [o<-openresty: 结束请求
end
lua->whiteService: 调用接口校验是否白名单
whiteService->lua: 返回白名单结果
lua->openresty: 根据白名单结果转发新旧服务
[o<-openresty: 结束请求
@enduml