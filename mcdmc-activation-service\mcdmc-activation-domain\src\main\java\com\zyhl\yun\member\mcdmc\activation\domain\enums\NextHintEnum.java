package com.zyhl.yun.member.mcdmc.activation.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/06/06 09:41
 */
@Getter
@AllArgsConstructor
public enum NextHintEnum {
    NEXT("next", "下一步"),
    CANCEL("cancel", "取消"),
    RESET("reset", "重置流程"),
    RESTART("restart", "重新开始/重试"),
    FINISH("finish", "流程提前完成-成功"),
    FAIL_FINISH("fail_finish", "流程提前完成-失败不重试"),
    CRON("cron", "定时"),
    OTHER("default", "其他-默认值");
    private final String name;
    private final String message;

    public static NextHintEnum getEnum(String name) {
        for (NextHintEnum ele : NextHintEnum.values()) {
            if (ele.getName().equals(name)) {
                return ele;
            }
        }
        return OTHER;
    }
}
