ALTER TABLE member_sopen.activation_work_order MODIFY COLUMN state smallint NULL COMMENT '状态,0.待处理；1. 处理中；2.已完成；3.已取消；4.已失败；5.待回调；6.回调成功；7.回调失败';

-- 新增免流暂停流程-20250111
insert into member_sopen.activation_work_service_flow
(service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
values('openPauseServiceCode', '一级能开免流退费流程', 'openPauseFlow', 0, 3, 4, '10', 1, NOW(), NOW(), 'yaq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

insert into activation_work_service_flow_iface ( work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
select flow.work_service_flow_id as work_service_flow_id, flow.service_code as service_code, iface.iface_name, iface.iface_class_name, request_type, media_type, url, notify_uri, prior_sort, iface.state, now() as created_time, now() as updated_time, iface.operator, iface.tenant_id, iface.version_flag
from activation_work_service_flow_iface iface,activation_work_service_flow flow
where iface.service_code ='openRefundServiceCode' and flow.service_code ='openPauseServiceCode';