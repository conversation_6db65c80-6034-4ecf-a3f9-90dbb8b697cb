package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.refund;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.Md5Util;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.SendTemplate;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiRefundProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.RaiReturnReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.RaiReturnRsp;
import com.zyhl.yun.member.order.domain.OrderDo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.zyhl.yun.member.mcdmc.activation.constants.BusinessConstant.ORDER_SUFFIX;

/**
 * 权益中心回收/退费-统一实现
 * com.huawei.jaguar.vsbo.service.serviceimpl.RefundServiceImpl#rightUnSubscribeAndRefund
 *
 * <AUTHOR>
 * @apiNote 有回调 <hr/>RaiRefundComCallback <hr/>
 * @since 2024/06/17 18:48
 */
@Component
public class RaiRefundNormalIFace extends SendTemplate<RaiReturnReq, RaiReturnRsp> {
    @Resource
    protected RaiRefundProperties raiRefundProperties;

    @Override
    protected RaiReturnReq getRequestBody(InterfaceContext<RaiReturnReq, RaiReturnRsp> interfaceContext) {
        RaiReturnReq.Head head = new RaiReturnReq.Head();
        String formatCurTime = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        head.setApiId(raiRefundProperties.getRightsSalesReturnApiId());
        head.setChannelCode(raiRefundProperties.getRightsSalesReturnChannelCode());
        head.setTransactionId(formatCurTime);
        head.setReqTime(formatCurTime);
        RaiReturnReq.Body body = new RaiReturnReq.Body();
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        body.setChannelNo(CharSequenceUtil.subBefore(comSendReq.getOrderID(), ORDER_SUFFIX, true));
        body.setExtfield1(this.buildExtField1(comSendReq));
        String authStr = head.getTransactionId() + head.getReqTime() + raiRefundProperties.getRightsSalesReturnKey()
                + JSONUtil.toJsonStr(body, JSONConfig.create().setNatureKeyComparator());
        authStr = authStr.replaceAll("\\s", "");
        String sign = Md5Util.md5Encode((authStr));
        head.setSign(sign.toUpperCase());
        RaiReturnReq.ContractRoot contractRoot = new RaiReturnReq.ContractRoot(head, body);
        return new RaiReturnReq(contractRoot);
    }

    /**
     * 构建扩展信息
     *
     * @param comSendReq 支付方式
     * @return 请求报文里的extField1字段值
     */
    protected String buildExtField1(ComSendInterfaceReq comSendReq) {
        Map<String, String> extMap = new HashMap<>();
        extMap.put(OtherFieldConstants.PAY_WAY, comSendReq.getPayWay());
        if (StrUtil.isNotEmpty(comSendReq.getParentCampaignId())) {
            extMap.put(OrderDo.ExtInfoKeys.CAMPAIGN_ID, comSendReq.getParentCampaignId());
        }
        return JSONUtil.toJsonStr(extMap);
    }

    @Override
    protected boolean isSuccess(RaiReturnRsp rsp) {
        return true;
    }

    @Override
    protected boolean isNeedCacheFlowLog() {
        // 缓存发货流水日志，加快回调处理速度
        return true;
    }

    @Override
    protected String getCallbackCondition(InterfaceContext<RaiReturnReq, RaiReturnRsp> context) {
        // 权益退费时回调根据订单id去查
        return CharSequenceUtil.subBefore(context.getWorkOrderDo().getOrderId(), ORDER_SUFFIX, true);
    }

    @Override
    protected Class<RaiReturnReq> getReqClass() {
        return RaiReturnReq.class;
    }

    @Override
    protected Class<RaiReturnRsp> getRspClass() {
        return RaiReturnRsp.class;
    }

}
