package com.zyhl.yun.member.mcdmc.activation.remote.send.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 权益中心回收-退订接口响应
 *
 * <AUTHOR>
 * @since 2024/06/17 19:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RaiReturnRsp {
    private ContractRoot contractRoot;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContractRoot {
        private Head head;
        private Body body;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Head {

        private String reqTime;

        private String sign;

        private String transactionId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Body {

        private String returnOrderId;

        private String resultCode;

        private String resultMsg;
    }
}
