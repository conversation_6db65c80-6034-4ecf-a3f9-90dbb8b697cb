package com.zyhl.yun.member.mcdmc.activation.remote.send.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 一级能开订购免流配置
 *
 * <AUTHOR>
 * @since 2024/06/19 11:50
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "platform.open.sync")
public class OpenFreeFlowProperties {
    /**
     * 资格校验接口对应的版本
     */
    private String bizQualificationVersion;

    /**
     * 包月goodid
     */
    private String monthlyGoodsID;

    /**
     * 消息类型，IntergratedSalesOrderSyncReq
     */
    private String msgType = "IntergratedSalesOrderSyncReq";

    /**
     * 一级能开免流订单同步接口版本号
     * /config/vsbo/goToneConfig/version
     */
    private String version = "1.0.0";

    /**
     * /config/vsbo/goToneConfig/channelCode
     */
    private String channelCode = "100006";
    /**
     * /config/vsbo/goToneConfig/feedbackUrl
     */
    private String feedbackUrl;

    /**
     * /config/vsbo/goToneConfig/numberStateVersion:1.0.0
     */
    private String numberStateVersion = "1.0.0";


    /**
     * 2024-4-23 应集运需求0元200G免流渠道编码由现有的1000000002320200003 更新为 1000032022110600003
     */
    private String uniChannelId = "1000032022110600003";


    /**
     * 重试开关
     * 0关闭 1开启
     */
    private String retrySwitch = "1";

    /**
     * 隔月重试开始时间小时数，默认1点
     */
    private Integer monthRetryStartHour = 1;

    /**
     * 隔月重试结束时间小时数，默认23点
     */
    private Integer monthRetryEndHour = 23;

    /**
     * 业务资格校验返回码包含在该字段时，则需要隔月重试
     * /config/vsbo/goToneConfig/errorCodeConfig
     */
    private List<String> checkMonthlyRetryErrorCode = Arrays.asList("3004", "3011", "2000", "3006", "3022");

    /**
     * 业务资格校验接口返回码：表示已经开通了免流，不需要重试
     */
    private List<String> checkAlreadySubErrorCode = Collections.singletonList("3066");

    /**
     * 业务资格校验接口正在处理的错误码,需隔日重试
     */
    private List<String> checkProcessingErrorCode = Arrays.asList("3012", "13");


    /**
     * 是否开启重试
     */
    public boolean isOpenRetry() {
        return "1".equals(retrySwitch);
    }
}
