package com.zyhl.yun.member.mcdmc.activation.remote.send.properties;

import com.zyhl.yun.member.product.domain.channel.enums.OrderSettlementFlagEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 常规权益相关配置
 *
 * <AUTHOR>
 * @since 2024/06/18 14:43
 */
@Data
@Configuration
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties(prefix = "platform.rai.normal")
public class RaiNormalProperties extends RaiBaseProperties {

    /**
     * 权益订单权益到账短信发送的过期时间，单位为小时
     */
    private long smsExpireHourTime = 24;

    /**
     * 免费订单渠道编码
     */
    private String freeOrderChannelCode;

    /**
     * 原价订单渠道编码
     */
    private String originalOrderChannelCode;

    /**
     * 折扣订单渠道编码
     */
    private String discountOrderChannelCode;

    @Override
    public String getOrderChannelCode(String orderSettlementType) {
        if (OrderSettlementFlagEnum.FREE.codeEquals(orderSettlementType)) {
            return freeOrderChannelCode;
        }
        if (OrderSettlementFlagEnum.DISCOUNT.codeEquals(orderSettlementType)) {
            return discountOrderChannelCode;
        }
        return originalOrderChannelCode;
    }

}
