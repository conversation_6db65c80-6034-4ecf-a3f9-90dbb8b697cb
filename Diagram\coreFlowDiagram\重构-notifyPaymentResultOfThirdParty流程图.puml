@startuml
'https://plantuml.com/sequence-diagram

autonumber

participant member模块 as member
participant product模块 as product
participant order模块 as order
participant 计费中心 as ji<PERSON>ei

[o-> member: 调用notifyPaymentResultOfThirdParty接口
member -> member: 分布式锁校验
member -> product: 查询商品信息
member <-- product: 返回商品信息
[o<-- member: 商品不存在或为包月商品
member -> member: 解密手机号
[o<--member:手机号码解密失败
alt #LightBlue orderStatus in (20,29)
    member->member: 查询用户信息（queryOrCreate)
    member->order: 根据userId+orderId查询订单列表
    member-->order: 订单不存在则创建对应的订单
    [o<--member: 订单状态为已完成5则直接结束
    member-->member: 活动产品订单则需要扣减库存
    [o<--member:扣减库存失败则返回错误码（系统内部错误）
    member->jiFei: 通知计费发货
    jiFei-->member:响应发货结果
    [o<--member: 计费发货失败，响应计费返回错误码和错误信息
    [o<-- member:根据小红书或视频号店铺，解析计费返回结果，并将计费返回结果码返回
else #Pink orderStatus in (30,31)
    member->member: 查询用户信息（queryOrCreate)
    member->order: 根据userId+orderId查询状态为1的订单
    [o<-- member: 不存在订单直接返回结果
    member->order: 进行内部发货流程（经过order->task->activation)
else orderStatus为其它
    [o<-member: 响应成功
end
@enduml