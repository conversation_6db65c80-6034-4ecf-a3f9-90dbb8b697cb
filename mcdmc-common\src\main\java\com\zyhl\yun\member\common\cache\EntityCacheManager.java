package com.zyhl.yun.member.common.cache;

import com.zyhl.yun.member.common.cache.config.EntityCacheHotConfig;
import com.zyhl.yun.member.common.cache.impl.NoCacheImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/11/20 19:29
 */
@Component
@Slf4j
public class EntityCacheManager implements ApplicationContextAware {


    public static ApplicationContext appContext;

    private static ConcurrentHashMap<String, ConcurrentHashMap<String, EntityCache>>
            cacheMap = new ConcurrentHashMap<>();

    private static EntityCacheHotConfig entityCacheHotConfig;

    private static NoCacheEntityCacheImpl noCacheEntityCacheImpl;

    public static EntityCache getCache(String cacheName, Class<? extends DelegationCache> cacheBeanClass) {
        ConcurrentHashMap<String, EntityCache> cacheBeanMap = cacheMap
                .get(cacheBeanClass.getName());


        if (!isCacheEnable()) {
            return noCacheEntityCacheImpl;
        }

        if (cacheBeanMap == null) {
            cacheMap.putIfAbsent(cacheBeanClass.getName(), new ConcurrentHashMap<>());
            cacheBeanMap = cacheMap.get(cacheBeanClass.getName());
        }


        EntityCache entityCache = cacheBeanMap.get(cacheName);
        if (entityCache == null) {
            DelegationCache delegationCache = null;
            try {
                delegationCache = appContext.getBean(cacheBeanClass);
                if (delegationCache == null) {
                    log.error("[CACHE] cacheBeanClass not found. cacheClass: {}", cacheBeanClass.getName());
                    delegationCache = appContext.getBean(NoCacheImpl.class);
                }

            } catch (Exception e) {
                log.error("[CACHE] getCache error. cacheClass: {}, error msg: {}", cacheBeanClass.getName(), e.getMessage());
                if (log.isDebugEnabled()) {
                    log.error("[CACHE] getCache error. cacheClass: {}", cacheBeanClass.getName(), e);
                }

                delegationCache = appContext.getBean(NoCacheImpl.class);
            }

            entityCache = new EntityCacheImpl(delegationCache, cacheName);
            cacheBeanMap.putIfAbsent(cacheName, entityCache);
            entityCache = cacheBeanMap.get(cacheName);
        }

        return entityCache;
    }


    public static EntityCache getCache(String cacheName, EntityCacheType entityCacheType) {
        return getCache(cacheName, entityCacheType.getImlClass());
    }


    public static boolean isCacheEnable() {
        return entityCacheHotConfig.isCacheEnable();
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        appContext = applicationContext;
        entityCacheHotConfig = appContext.getBean(EntityCacheHotConfig.class);
        noCacheEntityCacheImpl = appContext.getBean(NoCacheEntityCacheImpl.class);
    }


}
