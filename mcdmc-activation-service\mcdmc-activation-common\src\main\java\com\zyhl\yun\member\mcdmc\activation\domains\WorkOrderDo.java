package com.zyhl.yun.member.mcdmc.activation.domains;

import com.zyhl.yun.member.common.domain.framework.BaseDo;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.mcdmc.activation.enums.WorkOrderStateEnum;
import com.zyhl.yun.member.mcdmc.activation.serviceid.LocalServiceId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 服务工单表
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WorkOrderDo extends BaseDo implements Serializable {

    private static final long serialVersionUID = 482933593095858675L;

    /**
     * 工单ID
     */
    private String workId;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 服务id
     */
    private String serviceCode;

    /**
     * 工单属性
     */
    private String workAttrs;

    /**
     * 事务流水号id（用于去重）
     *
     * @apiNote 唯一主键
     */
    private String transactionId;

    /**
     * 工单状态
     */
    private WorkOrderStateEnum state;
    /**
     * 流程进度，多个流程时会更新该进度，从0开始
     */
    private Integer flowProgress = 0;

    /**
     * 失败次数
     */
    private Integer failCount = 0;

    /**
     * 工单属性列表
     */
    private List<WorkOrderAttrDo> workOrderAttrDoList;

    public void updateStateAndSaveDb(WorkOrderStateEnum state) {
        this.setState(state);
        DomainServiceContext context = new DomainServiceContext(LocalServiceId.UPDATE_BY_ID_OPERATION);
        context.putInstance(this);
        context.writeAndFlush();
    }

    public String toSimpleLogStr() {
        return "workOrderDo[workId=" + workId + "，serviceCode=" + serviceCode + "，transactionId=" + transactionId +
                ", orderId=" + orderId + ", userId=" + userId + ",state=" + state + "]";
    }

}
