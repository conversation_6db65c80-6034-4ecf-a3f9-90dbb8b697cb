package com.zyhl.yun.member.gateway.filter;

import cn.hutool.core.collection.CollUtil;
import com.zyhl.yun.member.common.ErrorCode;
import com.zyhl.yun.member.common.util.XmlUtil;
import com.zyhl.yun.member.gateway.RestfulResponse;
import com.zyhl.yun.member.gateway.auth.AuthCheckHandler;
import com.zyhl.yun.member.gateway.config.InterfaceAuthTypeProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * 功能描述。
 * 包名称:  com.zyhl.yun.member.gateway.filter
 * 类名称:  AuthGlobalFilter
 * 类描述:  鉴权全局过滤器。
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/6/13 14:46
 */
@Component
@Order(-1)
@Slf4j
public class AuthGlobalFilter implements GlobalFilter, ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Resource
    private InterfaceAuthTypeProperties interfaceAuthTypeProperties;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        String path = exchange.getRequest().getPath().pathWithinApplication().value();
        List<String> authTypeByInterface = interfaceAuthTypeProperties
                .getAuthTypeByInterfacePath(path.substring(path.lastIndexOf("/") + 1));

        if (CollUtil.isEmpty(authTypeByInterface)) {
            return chain.filter(exchange);
        }

        return Flux.just(authTypeByInterface.toArray(new String[0]))
                .flatMap(authHandlerName -> {
                    AuthCheckHandler authHandler = applicationContext.getBean(authHandlerName, AuthCheckHandler.class);
                    return authHandler.authHandle(exchange);
                }).any(Boolean.TRUE::equals)
                .flatMap(authResult -> {
                    if (Boolean.TRUE.equals(authResult)) {
                        // Check if any auth handler has stored a mutated exchange in the attributes
                        Object mutatedExchange = exchange.getAttributes().get("MUTATED_EXCHANGE");
                        if (Objects.nonNull(mutatedExchange) && mutatedExchange instanceof ServerWebExchange) {
                            // Use the mutated exchange instead of the original one
                            return chain.filter((ServerWebExchange) mutatedExchange);
                        }
                        return chain.filter(exchange);
                    } else {
                        log.error("auth fail: {}", exchange.getRequest().getPath());
                        RestfulResponse errorResponse = buildRestfulResponse(ErrorCode.UNAUTHORIZED, ErrorCode.UNAUTHORIZED_MESSAGE);
                        String xml = XmlUtil.toXml(errorResponse);
                        ServerHttpResponse response = exchange.getResponse();
                        DataBuffer buffer = response.bufferFactory().wrap(xml.getBytes(StandardCharsets.UTF_8));
                        response.getHeaders().setContentType(MediaType.TEXT_XML);
                        return response.writeWith(Flux.just(buffer));
                    }
                });

    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    private RestfulResponse buildRestfulResponse(String errorCode, String message) {
        RestfulResponse response = new RestfulResponse();
        response.setResultCode(errorCode);
        response.setResultDesc(message);
        return response;
    }
}
