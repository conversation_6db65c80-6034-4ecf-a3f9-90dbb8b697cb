FROM 10.19.19.84/k8s/openjdk8-arthas-skywalking:1.0.0

#请修改为应用端口
EXPOSE 18084

ENV TZ=Asia/Shanghai

#请修改应用名，需要区分环境，研发（联调）环境需要加上dev后缀，测试环境需要加上test后缀
ENV applicationName=mcdmc-activation-service-test
#按照应用实际需求修改
ENV jvm_heap_size="-Xmx8g -Xms8g -Xss256K"
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
    && apk update

#请将编译后的jar放置到镜像根目录，建议先本地编译确认具体目录
#编译后jar目录类似yun-neauth-config-web/target/yun-neauth-config-web-1.0-SNAPSHOT.jar，版本可用*代替
#放到/目录可以改jar包名，把版本去掉
ADD mcdmc-activation-service/mcdmc-activation-service-stater/target/mcdmc-activation-service-starter-1.0.jar /mcdmc-activation-service-starter-1.0.jar

RUN apk add --update font-adobe-100dpi ttf-dejavu fontconfig
CMD timestamp=$(date +%Y%m%d%H%M%S) \
&& mkdir -p logs/${applicationName}/$HOSTNAME/gc \
&& java \
${jvm_heap_size} \

#skywalking-agent
-javaagent:/skywalking/agent/skywalking-agent.jar \
-Dskywalking.agent.service_name=${applicationName} \
-Dskywalking.collector.backend_service=a50-skywalking-t01.skywalking-a50-ypcg-t01-nacos-01:11800 \

-Djava.security.egd=file:/dev/./urandom \
-XX:+PrintClassHistogram -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintHeapAtGC \
-Xloggc:logs/${applicationName}/$HOSTNAME/gc/${applicationName}-gc-${timestamp}.log \

#请修改jar包名
-jar /mcdmc-activation-service-starter-1.0.jar
