package com.zyhl.yun.member.mcdmc.activation.domain.enums.rai;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RenewFlagEnum {
    /**
     * 首次订购
     */
    FIRST_SUB(true, 1, "首次订购"),

    /**
     * 续费
     */
    RE_NEW(false, 2, "续费"),


    /**
     * 点播
     */
    PAY_PER_VIEW(false, 3, "点播"),

    /**
     * 退费
     */
    REFUND(false, 4, "退费");


    /**
     * 标识
     */
    private final Boolean flag;

    /**
     * 续费表示
     * 如果chargeType=1（包月）时有效
     * 1：续费  0：首次订购
     */
    private final Integer opType;

    /**
     * 描述
     */
    private final String description;


    public static RenewFlagEnum getReNewFlag(Boolean flag) {
        for (RenewFlagEnum value : RenewFlagEnum.values()) {
            if (value.getFlag().equals(flag)) {
                return value;
            }
        }
        return null;
    }

    public String strCode() {
        return String.valueOf(this.opType);
    }
}
