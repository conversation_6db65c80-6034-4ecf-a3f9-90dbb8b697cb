package com.zyhl.yun.member.mcdmc.activation.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/06/13 11:15
 */
@Getter
@AllArgsConstructor
public enum NotifyTypeEnum {
    /**
     * 订购
     */
    SUBSCRIBE("1", "SUBSCRIBE"),

    /**
     * 退订
     */
    UNSUBSCRIBE("2", "UNSUBSCRIBE"),

    /**
     * 恢复
     */
    ACTIVE("3", "ACTIVE"),

    /**
     * 暂停
     */
    PAUSE("4", "PAUSE"),

    /**
     * 续订
     */
    RENEW("5", "RENEW");
    private final String type;
    private final String msg;

    public boolean typeEquals(Integer anotherType) {
        return typeEquals(String.valueOf(anotherType));
    }

    public boolean typeEquals(String anotherType) {
        return this.type.equals(anotherType);
    }
}
