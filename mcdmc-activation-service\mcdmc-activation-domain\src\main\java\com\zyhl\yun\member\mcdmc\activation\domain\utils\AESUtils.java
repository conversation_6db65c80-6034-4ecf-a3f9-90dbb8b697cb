package com.zyhl.yun.member.mcdmc.activation.domain.utils;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Base64;


/**
 * AES 加密工具类
 *
 * <AUTHOR>
 * @since 2024/01/05 09:30
 */

@Slf4j
public class AESUtils {
    private AESUtils() {
    }

    /**
     * 使用 AES 算法进行加密。
     */
    private static final String ALGORITHM = "AES";

    /**
     * 安全的加密模式和填充方案 - 使用CBC模式替代不安全的ECB模式
     * Security fix: Changed from ECB to CBC mode for better security
     */
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
    
    /**
     * @deprecated Use secure CBC mode instead
     */
    @Deprecated
    private static final String LEGACY_TRANSFORMATION = "AES/ECB/PKCS5Padding";

    /**
     * aes算法的对应密钥
     */
    private static String aesKey;

    // Security fix: Removed hardcoded key - now using configuration-based key management
    private static String getTransformation() {
        return TRANSFORMATION;
    }

    private static String getAlgorithm() {
        return ALGORITHM;
    }


    public static void setKey(String aesKey) {
        if (aesKey == null || aesKey.isEmpty()) {
            throw new IllegalArgumentException("AES key cannot be null or empty");
        }
        AESUtils.aesKey = aesKey;
    }
    
    /**
     * Generate a random IV for CBC mode
     */
    private static byte[] generateIV() {
        byte[] iv = new byte[16]; // AES block size is 16 bytes
        new SecureRandom().nextBytes(iv);
        return iv;
    }


    /**
     * 使用AES算法加密字符串 (CBC模式，更安全)
     *
     * @param encryptStr 要加密的字符串
     * @return 加密后的字符串 (包含IV)
     */
    public static String encrypt(String encryptStr) {
        if (aesKey == null || aesKey.isEmpty()) {
            throw new IllegalStateException("AES key not initialized. Call setKey() first.");
        }
        
        try {
            byte[] iv = generateIV();
            SecretKeySpec secretKey = new SecretKeySpec(aesKey.getBytes(StandardCharsets.UTF_8), getAlgorithm());
            Cipher cipher = Cipher.getInstance(getTransformation());
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);
            
            byte[] encryptedBytes = cipher.doFinal(encryptStr.getBytes(StandardCharsets.UTF_8));
            
            // Combine IV and encrypted data
            byte[] combined = new byte[iv.length + encryptedBytes.length];
            System.arraycopy(iv, 0, combined, 0, iv.length);
            System.arraycopy(encryptedBytes, 0, combined, iv.length, encryptedBytes.length);
            
            return Base64.getEncoder().encodeToString(combined);
        } catch (Exception e) {
            log.error("加密出现异常,异常信息{}", e.getMessage());
            throw new RuntimeException("AES encryption failed", e);
        }
    }

    /**
     * 使用AES算法解密字符串 (CBC模式)
     *
     * @param encryptStr 加密的字符串 (包含IV)
     * @return 解密后的字符串
     */
    public static String decrypt(String encryptStr) {
        if (ObjectUtil.isEmpty(encryptStr)) {
            return null;
        }
        if (aesKey == null || aesKey.isEmpty()) {
            throw new IllegalStateException("AES key not initialized. Call setKey() first.");
        }
        
        try {
            byte[] combined = Base64.getDecoder().decode(encryptStr);
            
            // Extract IV and encrypted data
            byte[] iv = Arrays.copyOfRange(combined, 0, 16);
            byte[] encryptedBytes = Arrays.copyOfRange(combined, 16, combined.length);
            
            SecretKeySpec secretKey = new SecretKeySpec(aesKey.getBytes(StandardCharsets.UTF_8), getAlgorithm());
            Cipher cipher = Cipher.getInstance(getTransformation());
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec);
            
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("解密出现异常,异常信息{}", e.getMessage());
            throw new RuntimeException("AES decryption failed", e);
        }
    }

}
