package com.zyhl.yun.member.mcdmc.activation.domain.utils;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;


/**
 * AES 加密工具类
 *
 * <AUTHOR>
 * @since 2024/01/05 09:30
 */

@Slf4j
public class AESUtils {
    private AESUtils() {
    }

    /**
     * 使用 AES 算法进行加密。
     */
    private static final String ALGORITHM = "AES";

    /**
     * 更安全的加密模式和填充方案
     */
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";

    /**
     * aes算法的对应密钥
     */
    private static String aesKey;

    //private static final String KEY = "************************";
    private static String getTransformation() {
        return TRANSFORMATION;
    }

    private static String getAlgorithm() {
        return ALGORITHM;
    }


    public static void setKey(String aesKey) {
        AESUtils.aesKey = aesKey;
    }


    /**
     * 使用AES算法加密字符串
     *
     * @param encryptStr 要加密的字符串
     * @return 加密后的字符串
     */
    public static String encrypt(String encryptStr) {
        byte[] encryptedBytes;
        try {
            SecretKeySpec secretKey = new SecretKeySpec(aesKey.getBytes(StandardCharsets.UTF_8), getAlgorithm());
            Cipher cipher = Cipher.getInstance(getTransformation());
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            encryptedBytes = cipher.doFinal(encryptStr.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("加密出现异常,异常信息{}", e.getMessage());
            //throw new RCSException(ResultCode.AES_ALGORITHM, encryptStr);
            throw new RuntimeException(e);
        }
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 使用AES算法解密字符串
     *
     * @param encryptStr 加密的字符串
     * @return 解密后的字符串
     */
    public static String decrypt(String encryptStr) {
        if (ObjectUtil.isEmpty(encryptStr)) {
            return null;
        }
        byte[] decryptedBytes;
        try {
            SecretKeySpec secretKey = new SecretKeySpec(aesKey.getBytes(StandardCharsets.UTF_8), getAlgorithm());
            Cipher cipher = Cipher.getInstance(getTransformation());
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptStr);
            decryptedBytes = cipher.doFinal(encryptedBytes);
        } catch (Exception e) {
            log.error("加密出现异常,异常信息{}", e.getMessage());
            throw new RuntimeException(e);
        }
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

}
