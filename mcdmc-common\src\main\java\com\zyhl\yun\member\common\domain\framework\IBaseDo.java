package com.zyhl.yun.member.common.domain.framework;

import java.io.Serializable;

/**
 * 领域类接口
 *
 * @Description:
 * @Author: zouzef<PERSON>
 * @Date: 2024/4/26
 */
public interface IBaseDo extends Serializable {

    /**
     * 获取主键
     *
     * @return
     */
    <T extends Serializable> T getPk();

    /**
     * 设置主键
     */
    <T extends Serializable> void setPk(T pk);


    /**
     * 设置上下文
     *
     * @param serviceContext
     */
    void setDomainServiceContext(DomainServiceContext serviceContext);


    /**
     * 获取上下文
     *
     * @return
     */
    DomainServiceContext getDomainServiceContext();

    /**
     * 获取环绕处理器
     *
     * @param serviceContext
     * @return
     */
    AroundPersistenceHandler getAroundPersistenceHandler(DomainServiceContext serviceContext);

}
