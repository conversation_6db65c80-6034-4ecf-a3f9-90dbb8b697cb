package com.zyhl.yun.member.mcdmc.activation.callback.req;

import lombok.Data;

/**
 * 权益中心退订请求类
 *
 * <AUTHOR>
 * @date 2023/02/21
 */
@Data
public class RaiPayResultNotifyResp {
    /**
     * 消息类型，填写“PayResultNotifyResp”
     */
    String msgType;

    /**
     * 该接口消息的版本号，版本默认为“1.0”
     */
    String msgVer;

    /**
     * 返回码，0表示处理成功，1表示处理失败
     */
    Integer returnCode;

    /**
     * returnCode取值为1时有效，返回错误信息
     */
    String returnMsg;

}
