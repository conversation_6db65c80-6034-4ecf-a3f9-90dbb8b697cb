package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base;

import cn.hutool.extra.spring.SpringUtil;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.FlowLogStateEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IOutInterface;
import com.zyhl.yun.member.mcdmc.activation.redis.RedisComponent;
import com.zyhl.yun.member.mcdmc.activation.redis.RedisKeyConstant;
import com.zyhl.yun.member.mcdmc.activation.remote.client.RemoteClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 调用外部接口待实现的基类
 *
 * @param <T> 外部接口请求入参
 * @param <R> 外部接口响应体
 * <AUTHOR>
 * @since 2024/06/28 17:00
 */
@Slf4j
public abstract class BaseSendInterface<T, R> implements IOutInterface {


    /**
     * 远程客户端连接
     */
    protected RemoteClient remoteClient;

    protected RedisComponent redisComponent;

    @PostConstruct
    public void initClient() {
        remoteClient = SpringUtil.getBean(RemoteClient.class);
        redisComponent = SpringUtil.getBean(RedisComponent.class);
    }

    /**
     * 参数校验，若失败则直接结束
     *
     * @param interfaceContext 接口上下文
     */
    protected void doCheckParam(InterfaceContext<T, R> interfaceContext) throws FlowTerminationException {
    }

    /**
     * 获取接口请求入参
     *
     * @param interfaceContext 接口上下文
     * @return 接口请求入参
     */
    protected abstract T getRequestBody(InterfaceContext<T, R> interfaceContext);

    /**
     * 获取请求头
     *
     * @param interfaceContext 接口上下文
     * @return 请求头
     */
    protected Map<String, String> getRequestHeader(InterfaceContext<T, R> interfaceContext) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.CONTENT_TYPE, interfaceContext.getMediaType().toString());
        return headerMap;
    }

    /**
     * 返回响应类的class
     */
    protected abstract Class<T> getReqClass();

    /**
     * 返回响应类的class
     */
    protected abstract Class<R> getRspClass();

    /**
     * 有回调请求时重写该方法，返回回调时对应的订单id
     *
     * @param context 接口上下文
     */
    protected String getCallbackConditionWhileSC(InterfaceContext<T, R> context) {
        if (isSuccess(context.getInterfaceRspObj())) {
            // 成功的时候才访问该值，防止空指针
            return getCallbackCondition(context);
        }
        return null;
    }

    protected String getCallbackCondition(InterfaceContext<T, R> context) {
        return null;
    }

    /**
     * 是否需要缓存发货的流程日志（仅在流程完全成功的情况下）
     * 主要针对需要处理回调请求的发货接口流程
     *
     * @apiNote 如果需要，则将发货日志存入redis，供回调时使用
     */
    protected boolean isNeedCacheFlowLog() {
        return false;
    }

    /**
     * 获取订购资产id
     */
    protected String getResourceId(InterfaceContext<T, R> context) {
        return getCallbackConditionWhileSC(context);
    }

    /**
     * 判断接口返回是否成功响应
     *
     * @param rsp 接口响应结果
     */
    protected abstract boolean isSuccess(R rsp);

    /**
     * 业务成功响应后执行的动作
     *
     * @param interfaceContext 接口上下文
     */
    protected void doBusiSuccess(InterfaceContext<T, R> interfaceContext) {
        interfaceContext.setNextHintEnum(NextHintEnum.NEXT);
    }

    /**
     * 业务失败响应后执行的动作
     *
     * @param interfaceContext 接口上下文
     */
    protected void doBusiFail(InterfaceContext<T, R> interfaceContext) {
        interfaceContext.setNextHintEnum(NextHintEnum.FAIL_FINISH);
    }

    /**
     * 业务完成前执行流程，不管业务响应成功与否均会执行（处理结果时发生异常时可能不会执行）
     *
     * @param interfaceContext 接口上下文
     */
    protected void doBusiComplete(InterfaceContext<T, R> interfaceContext) {
        // 仅在走了发货流程才存入流程日志缓存
        String callbackCondition = this.getCallbackConditionWhileSC(interfaceContext);
        if (this.isNeedCacheFlowLog() && this.isSuccess(interfaceContext.getInterfaceRspObj()) && StringUtils.hasText(callbackCondition)) {
            // 将发货请求的接口日志写入redis，回调时可以先从redis查询
            String notifyUri = interfaceContext.getWorkServiceFlowIFaceDo().getNotifyUri();
            String sendFlowLogKey = RedisKeyConstant.getSendFlowLogKey(notifyUri, callbackCondition);
            redisComponent.setJson(sendFlowLogKey, interfaceContext.getWorkServiceFlowLog(), 20, TimeUnit.MINUTES);
        }
    }

    /**
     * 发生异常后执行的动作
     *
     * @param interfaceContext 接口上下文
     */
    protected void doException(InterfaceContext<T, R> interfaceContext, Exception e) throws Exception {
        throw e;
    }

    protected void doBusiFinally(InterfaceContext<T, R> interfaceContext) {
        FlowLogStateEnum curFlowLogStateEnum = interfaceContext.getCurFlowLogStateEnum();
        // 发货成功才需要存入流程日志缓存
        if (!FlowLogStateEnum.SHIPMENT_SUCCESS.equals(curFlowLogStateEnum)) {
            return;
        }
        String callbackCondition = this.getCallbackConditionWhileSC(interfaceContext);
        if (this.isNeedCacheFlowLog() && StringUtils.hasText(callbackCondition)) {
            // 将发货请求的接口日志写入redis，回调时可以先从redis查询
            String notifyUri = interfaceContext.getWorkServiceFlowIFaceDo().getNotifyUri();
            String sendFlowLogKey = RedisKeyConstant.getSendFlowLogKey(notifyUri, callbackCondition);
            redisComponent.setJson(sendFlowLogKey, interfaceContext.getWorkServiceFlowLog(), 20, TimeUnit.MINUTES);
        }
    }
}
