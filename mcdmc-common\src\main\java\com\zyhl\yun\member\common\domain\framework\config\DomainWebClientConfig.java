package com.zyhl.yun.member.common.domain.framework.config;

import com.zyhl.yun.member.common.domain.mono.SpringAppContextUtil;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.loadbalancer.reactive.ReactorLoadBalancerExchangeFilterFunction;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.HttpProtocol;
import reactor.netty.http.client.HttpClient;

/**
 * <AUTHOR>
 * @since 2025/01/17 12:38
 */
@Configuration
public class DomainWebClientConfig {
    // 设置默认值为16MB
    private static final int BUFFER_SIZE = 16 * 1024 * 1024;

    @Value("${http2.flag:true}")
    private Boolean http2Flag;

    @Getter
    private static WebClient domainWebClient;

    @Bean
    protected WebClient buildWebClient() {
        ReactorLoadBalancerExchangeFilterFunction function = SpringAppContextUtil.getApplicationContext().getBean(ReactorLoadBalancerExchangeFilterFunction.class);

        // 配置响应缓存大小
        ExchangeStrategies strategies = ExchangeStrategies.builder()
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(BUFFER_SIZE))
                .build();

        HttpClient httpClient = HttpClient.create().protocol(HttpProtocol.H2C).compress(true);
        WebClient.Builder builder = WebClient.builder();
        if (Boolean.TRUE.equals(http2Flag)) {
            builder.clientConnector(new ReactorClientHttpConnector(httpClient));
        }
        WebClient webClient = builder.exchangeStrategies(strategies).filter(function).build();
        DomainWebClientConfig.setDomainWebClient(webClient);
        return webClient;
    }

    private static void setDomainWebClient(WebClient webClient) {
        DomainWebClientConfig.domainWebClient = webClient;
    }
}
