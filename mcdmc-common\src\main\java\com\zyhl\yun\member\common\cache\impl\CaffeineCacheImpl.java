package com.zyhl.yun.member.common.cache.impl;

import com.zyhl.yun.member.common.cache.ConditionCacheable;
import com.zyhl.yun.member.common.cache.wrapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/11/20 20:46
 */
@Slf4j
public class CaffeineCacheImpl extends BaseDelegationCacheImpl {


    private CaffeineCacheManager caffeineCacheManager;

    public CaffeineCacheImpl(CaffeineCacheManager caffeineCacheManager) {
        log.info("[CACHE] CaffeineCacheImpl init");
        this.caffeineCacheManager = caffeineCacheManager;
    }

    @Override
    public SimpleValueWrapper get(String cacheSpace, Serializable key, Class clazz) {
        log.debug("[CACHE] get value from caffeine. cacheSpace: {}, key: {}", cacheSpace, key);
        Cache cache = caffeineCacheManager.getCache(cacheSpace);
        Cache.ValueWrapper value = cache.get(key);
        if (value != null) {
            return new SimpleValueWrapper(key, (Serializable) value.get());
        }
        return null;
    }

    @Override
    public CollectionWrapper getList(String cacheSpace, List<Serializable> keyList, Class clazz) {
        log.debug("[CACHE] get value list from caffeine. cacheSpace: {}, keyList: {}", cacheSpace, keyList);

        if (CollectionUtils.isEmpty(keyList)) {
            return new CollectionWrapper(keyList, null);
        }

        List<CacheValueWrapper> cacheValueWrapperList = new ArrayList<>();
        for (Serializable key : keyList) {
            log.debug("[CACHE] get value from caffeine. cacheName: {}, key: {}",
                    cacheSpace, key);
            SimpleValueWrapper simpleValueWrapper = get(cacheSpace, key, clazz);
            if (simpleValueWrapper != null) {
                cacheValueWrapperList.add(simpleValueWrapper);
            }
        }


        return new CollectionWrapper(keyList, cacheValueWrapperList);
    }

    @Override
    public ConditionWrapper getKeyListByCondition(String cacheSpace, ConditionCacheable condition) {
        log.debug("[CACHE] get key list from caffeine. cacheSpace: {}, condition: {}",
                cacheSpace, condition);

        String conditionHashKey = getConditionHashKey(condition);
        if (!StringUtils.hasLength(conditionHashKey)) {
            return null;
        }

        List<String> conditionKeyList = getConditionKeyList(cacheSpace, condition);
        String cacheName = getConditionCacheName(cacheSpace);
        Cache cache = caffeineCacheManager.getCache(cacheName);
        if (CollectionUtils.isEmpty(conditionKeyList)) {
            // 当条件缓存key列表为空时，直接cache中获取, 不通过Map作为同一维度条件的容器
            log.debug("[CACHE] get key list from caffeine. cacheName: {}, conditionHashKey: {}",
                    cacheName, conditionHashKey);

            Cache.ValueWrapper cacheWrapper = cache.get(conditionHashKey);
            if (cacheWrapper != null) {
                return (ConditionWrapper) cacheWrapper.get();
            }
            return null;
        }


        for (String key : conditionKeyList) {
            if (StringUtils.hasLength(key)) {
                log.debug("[CACHE] get key list from caffeine. cacheName: {}, condition: {}, conditionHashKey: {}",
                        cacheName, condition, conditionHashKey);
                Cache.ValueWrapper cacheWrapper = cache.get(key);
                if (cacheWrapper != null) {
                    Map<String, ConditionWrapper> conditionWrapperMap = (Map<String, ConditionWrapper>) cacheWrapper.get();
                    ConditionWrapper conditionWrapper = conditionWrapperMap.get(conditionHashKey);
                    if (conditionWrapper != null) {
                        return conditionWrapper;
                    }
                }
            }
        }

        return null;
    }

    @Override
    public ConditionCountWrapper getTotalCountByCondition(String cacheSpace, ConditionCacheable condition) {
        log.debug("[CACHE] get total count from caffeine. cacheSpace: {}, condition: {}",
                cacheSpace, condition);


        String conditionHashKey = getConditionHashKey(condition);
        if (!StringUtils.hasLength(conditionHashKey)) {
            return null;
        }

        List<String> conditionKeyList = getConditionKeyList(cacheSpace, condition);
        String cacheName = getConditionCountCacheName(cacheSpace);
        Cache cache = caffeineCacheManager.getCache(cacheName);

        if (CollectionUtils.isEmpty(conditionKeyList)) {
            // 当条件缓存key列表为空时，直接返回null，代表没查询到
            // 当条件缓存key列表为空时，直接cache中获取, 不通过Map作为同一维度条件的容器
            log.debug("[CACHE] get total count from caffeine. cacheName: {}, conditionHashKey: {}",
                    cacheName, conditionHashKey);

            Cache.ValueWrapper cacheWrapper = cache.get(conditionHashKey);
            if (cacheWrapper != null) {
                return (ConditionCountWrapper) cacheWrapper.get();
            }
            return null;
        }


        for (String key : conditionKeyList) {
            if (StringUtils.hasLength(key)) {
                log.debug("[CACHE] get total count from caffeine. cacheName: {}, key: {}, conditionHashKey: {}",
                        cacheName, key, conditionHashKey);
                Cache.ValueWrapper cacheWrapper = cache.get(key);
                if (cacheWrapper != null) {
                    Map<String, ConditionCountWrapper> conditionCountWrapperMap = (Map<String, ConditionCountWrapper>) cacheWrapper.get();
                    ConditionCountWrapper conditionCountWrapper = conditionCountWrapperMap.get(conditionHashKey);
                    if (conditionCountWrapper != null) {
                        return conditionCountWrapper;
                    }
                }
            }
        }

        return null;
    }

    @Override
    public void put(String cacheSpace, Serializable key, SimpleValueWrapper value) {
        log.debug("[CACHE] put value to caffeine. cacheSpace: {}, cacheName:{}, key: {}",
                cacheSpace, cacheSpace, key);
        Cache cache = caffeineCacheManager.getCache(cacheSpace);
        cache.put(key, value);
    }

    @Override
    public void put(String cacheSpace, ConditionCacheable condition, ConditionWrapper conditionWrapper) {

        log.debug("[CACHE] put condition to caffeine. cacheSpace: {}, condition: {}",
                cacheSpace, condition);

        if (!StringUtils.hasLength(conditionWrapper.getConditionDigest())) {
            return;
        }

        List<String> conditionKeyList = getConditionKeyList(cacheSpace, condition);
        String cacheName = getConditionCacheName(cacheSpace);
        Cache cache = caffeineCacheManager.getCache(cacheName);
        if (CollectionUtils.isEmpty(conditionKeyList)) {
            // 当条件缓存key列表为空时，直接cache中设置, 不通过Map作为同一维度条件的容器
            log.debug("[CACHE] get key list from caffeine. cacheName: {}, conditionHashKey: {}",
                    cacheName, conditionWrapper.getConditionDigest());
            cache.put(conditionWrapper.getConditionDigest(), conditionWrapper);
            return;
        }


        for (String key : conditionKeyList) {
            if (StringUtils.hasLength(key)) {

                log.debug("[CACHE] put condition to caffeine. cacheName: {},  conditionKey: {}, conditionHashKey: {}",
                        cacheName, key, conditionWrapper.getConditionDigest());
                Cache.ValueWrapper cacheWrapper = cache.get(key);
                if (cacheWrapper != null) {
                    Map<String, ConditionWrapper> conditionWrapperMap = (Map<String, ConditionWrapper>) cacheWrapper.get();
                    conditionWrapperMap.put(conditionWrapper.getConditionDigest(), conditionWrapper);
                } else {
                    Map<String, ConditionWrapper> conditionWrapperMap = new ConcurrentHashMap<>();
                    conditionWrapperMap.put(conditionWrapper.getConditionDigest(), conditionWrapper);
                    cache.putIfAbsent(key, conditionWrapperMap);
                }
            }
        }
    }

    @Override
    public void put(String cacheSpace, ConditionCacheable condition, ConditionCountWrapper conditionCountWrapper) {

        log.debug("[CACHE] put condition count to caffeine. cacheSpace: {}, condition: {}",
                cacheSpace, condition);

        if (!StringUtils.hasLength(conditionCountWrapper.getConditionDigest())) {
            return;
        }

        List<String> conditionKeyList = getConditionKeyList(cacheSpace, condition);
        String cacheName = getConditionCountCacheName(cacheSpace);
        Cache cache = caffeineCacheManager.getCache(cacheName);
        if (CollectionUtils.isEmpty(conditionKeyList)) {
            // 当条件缓存key列表为空时，直接cache中设置, 不通过Map作为同一维度条件的容器
            log.debug("[CACHE] put condition count to caffeine. cacheName: {}, conditionCountHashKey: {}",
                    cacheName, conditionCountWrapper.getConditionDigest());
            cache.put(conditionCountWrapper.getConditionDigest(), conditionCountWrapper);
            return;
        }


        for (String key : conditionKeyList) {
            if (StringUtils.hasLength(key)) {
                log.debug("[CACHE] put condition count to caffeine. cacheName: {}, conditionKey: {}, conditionCountHashKey: {}",
                        cacheSpace, key, conditionCountWrapper.getConditionDigest());
                Cache.ValueWrapper cacheWrapper = cache.get(key);
                if (cacheWrapper != null) {
                    Map<String, ConditionCountWrapper> conditionWrapperMap = (Map<String, ConditionCountWrapper>) cacheWrapper.get();
                    conditionWrapperMap.put(conditionCountWrapper.getConditionDigest(), conditionCountWrapper);
                } else {
                    Map<String, ConditionCountWrapper> conditionWrapperMap = new ConcurrentHashMap<>();
                    conditionWrapperMap.put(conditionCountWrapper.getConditionDigest(), conditionCountWrapper);
                    cache.putIfAbsent(key, conditionWrapperMap);
                }
            }
        }
    }


    @Override
    public void deleteByKeyList(String cacheSpace, List<Serializable> keyList) {

        log.debug("[CACHE] delete value list from caffeine. cacheSpace: {}, keyList: {}",
                cacheSpace, keyList);

        if (CollectionUtils.isEmpty(keyList)) {
            // 没有缓存key列表的不处理
            return;
        }

        Cache cache = caffeineCacheManager.getCache(cacheSpace);
        for (Serializable key : keyList) {
            log.debug("[CACHE] delete value from caffeine. cacheName: {}, key: {}",
                    cacheSpace, key);
            cache.evict(key);
        }

    }

    @Override
    public void clearConditions(String cacheSpace, ConditionCacheable condition) {
        log.debug("[CACHE] delete condition from caffeine. cacheSpace: {}, condition: {}",
                cacheSpace, condition);

        String conditionCacheName = getConditionCacheName(cacheSpace);
        Cache conditionCache = caffeineCacheManager.getCache(conditionCacheName);
        String conditionCountCacheName = getConditionCountCacheName(cacheSpace);
        Cache conditionCountCache = caffeineCacheManager.getCache(conditionCountCacheName);

        List<String> conditionKeyList = getConditionKeyList(cacheSpace, condition);
        if (CollectionUtils.isEmpty(conditionKeyList)) {
            log.debug("[CACHE] clear condition from caffeine. conditionCacheName: {}, conditionCountCacheName: {}",
                    conditionCacheName, conditionCountCacheName);
            conditionCache.clear();
            conditionCountCache.clear();
            return;
        }


        for (String key : conditionKeyList) {
            if (StringUtils.hasLength(key)) {
                log.debug("[CACHE] clear condition from caffeine. conditionCacheName: {}, conditionCountCacheName: {}, key: {}",
                        conditionCacheName, conditionCountCacheName, key);
                conditionCache.evict(key);
                conditionCountCache.evict(key);
            }
        }
    }


    private String getConditionCacheName(String cacheSpace) {
        return cacheSpace + ":CONDITION";
    }


    private String getConditionCountCacheName(String cacheSpace) {
        return cacheSpace + ":COUNT";
    }
}
