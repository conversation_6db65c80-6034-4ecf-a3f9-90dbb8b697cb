package com.zyhl.yun.member.mcdmc.activation.domain.exception;

import com.zyhl.yun.member.mcdmc.activation.result.ResultCode;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 接口回调异常
 *
 * <AUTHOR>
 * @since 2024/06/21 10:59
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CallbackException extends RuntimeException {
    /**
     * 错误码
     */
    private final String errorCode;
    /**
     * 错误信息
     */
    private final String errorMsg;

    public CallbackException(String errorCode, String errorMsg) {
        super(errorMsg);
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public CallbackException(ResultCode resultCode) {
        super(resultCode.getMsg());
        this.errorCode = resultCode.getCode();
        this.errorMsg = resultCode.getMsg();
    }

    public CallbackException(ResultCode resultCode, String errorMsg) {
        super(errorMsg);
        this.errorCode = resultCode.getCode();
        this.errorMsg = errorMsg;
    }
}
