package com.zyhl.yun.member.common.cache;

import com.zyhl.yun.member.common.cache.wrapper.CollectionWrapper;
import com.zyhl.yun.member.common.cache.wrapper.ConditionCountWrapper;
import com.zyhl.yun.member.common.cache.wrapper.ConditionWrapper;
import com.zyhl.yun.member.common.cache.wrapper.SimpleValueWrapper;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/20 14:35
 */
public interface DelegationCache {

    SimpleValueWrapper get(String cacheSpace, Serializable key, Class clazz);


    CollectionWrapper getList(String cacheSpace, List<Serializable> keyList, Class clazz);

    ConditionWrapper getKeyListByCondition(String cacheSpace, ConditionCacheable condition);


    ConditionCountWrapper getTotalCountByCondition(String cacheSpace, ConditionCacheable condition);

    void put(String cacheSpace, Serializable key, SimpleValueWrapper value);

    void put(String cacheSpace, ConditionCacheable condition, ConditionWrapper conditionWrapper);

    void put(String cacheSpace, ConditionCacheable condition, ConditionCountWrapper conditionCountWrapper);

    void putList(String cacheSpace, CollectionWrapper collectionWrapper);

    void deleteByKeyList(String cacheSpace, List<Serializable> key);

    void clearConditions(String cacheSpace, ConditionCacheable condition);
}
