package com.zyhl.yun.member.consumer.application.consume;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zyhl.yun.member.common.JsonUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.UserServiceId;
import com.zyhl.yun.member.common.enums.SubscribeTypeEnum;
import com.zyhl.yun.member.common.util.MDCUtils;
import com.zyhl.yun.member.common.util.MsisdnUtil;
import com.zyhl.yun.member.common.util.SnowflakeUtil;
import com.zyhl.yun.member.consumer.common.constants.ChangeTypeConstant;
import com.zyhl.yun.member.consumer.common.message.NDAContentMqMessage;
import com.zyhl.yun.member.consumer.common.message.UserStatusInfoMqMessage;
import com.zyhl.yun.member.consumer.common.producer.MqProducer;
import com.zyhl.yun.member.consumer.white.gateway.WhiteGateway;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.user.domain.QueryUserCondition;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.facade.GoodsInstanceServiceFacade;
import com.zyhl.yun.member.goodsinstance.enums.GoodsInstanceStateEnum;
import com.zyhl.yun.member.notify.domain.PerformOrderDo;

import com.zyhl.yun.member.order.common.constants.OrderPrimaryKeyGenerate;
import com.zyhl.yun.member.order.domain.OrderDo;
import com.zyhl.yun.member.order.domain.dto.QueryOrderCondition;
import com.zyhl.yun.member.product.common.enums.ChargeTypeEnum;
import com.zyhl.yun.member.product.common.enums.GoodsSalesTypeEnum;
import com.zyhl.yun.member.product.common.enums.PayWayEnum;
import com.zyhl.yun.member.product.domain.goods.BenefitGoodsDo;
import com.zyhl.yun.member.product.domain.goods.GoodsDo;
import com.zyhl.yun.member.product.domain.goods.facade.GoodsServiceFacade;
import com.zyhl.yun.member.product.domain.goods.pack.GoodsPackageDo;
import com.zyhl.yun.member.wrappers.DeliverGoodsWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceServiceId.QUERY_GOODS_INSTANCE;
import static com.zyhl.yun.member.common.domain.serviceid.OrderServiceId.QUERY_ORDER;
import static com.zyhl.yun.member.common.domain.serviceid.PerformOrderServiceId.PERFORM_ORDER;

import static com.zyhl.yun.member.facade.GoodsInstanceServiceFacade.buildSubGoodsInstance;
import static com.zyhl.yun.member.facade.OrderServiceFacade.buildProductOrder;
import static com.zyhl.yun.member.facade.ReceivedRecServiceFacade.createReceivedRec;
import static com.zyhl.yun.member.facade.ReceivedRecServiceFacade.createSubscribeRec;


/**
 * @Author：liuzhongcheng
 * @Package：com.zyhl.yun.member.consumer.application.consume
 * @Project：mcdmc
 * @name：NDAMessageConsume
 * @Date：2024/9/26 20:43
 */
@Component
@Slf4j
@RocketMQMessageListener(
        nameServer = "${rocketmq.consumer.nda.nameServerAddr}",
        accessKey = "${rocketmq.consumer.nda.accessKey}", // if accessKey is empty, it will read by `rocketmq.consumer.access-key` key
        secretKey = "${rocketmq.consumer.nda.secretKey}",
        topic = "${rocketmq.consumer.nda.topic}",
        consumerGroup = "${rocketmq.consumer.nda.group}",
        selectorExpression = "${rocketmq.consumer.nda.tag}",
        namespace = "${rocketmq.consumer.nda.instanceId}"
)
@ConditionalOnProperty(name = "rocketmq.consumer.nda.enable", havingValue = "true", matchIfMissing = true)
@RefreshScope
public class NDAMessageConsume implements RocketMQListener<String> {


    @Value("${rocketmq.consumer.nda.subActionType}")
    private String subActionType;

    @Resource
    private WhiteGateway whiteGateway;

    @Resource
    private MqProducer mqProducer;


    @Value("${rocketmq.producer.nda_mcdmc.topic}")
    private String topic;

    @Value("${rocketmq.producer.nda_mcdmc.tag}")
    private String tag;

    @Value("${rocketmq.producer.nda_mcdmc.switch}")
    private Boolean ndaSwitch;


    @PostConstruct
    public void init() {
        log.info("init NDAMessageConsume");
    }

    @Override
    public void onMessage(String message) {
        try {
            MDCUtils.initMdc();
            log.info("NDAMessageConsume message is:{}", message);
            //反序列化
            NDAContentMqMessage ndaContentMqMessage = JsonUtil.fromJson(message, NDAContentMqMessage.class);
            if (ObjectUtil.isEmpty(ndaContentMqMessage) || ObjectUtil.isEmpty(ndaContentMqMessage.getContent())) {
                log.error("NDAMessageConsume message or content is null,ndaContentMqMessage is:{}", ndaContentMqMessage);
                return;
            }
            UserStatusInfoMqMessage content = ndaContentMqMessage.getContent();
            //判断是不是携号转网
            if (StringUtils.isBlank(subActionType) || !subActionType.equals(content.getSubActionType())) {
                log.info("NDAMessageConsume failed,subActionType is:" + content.getSubActionType());
                return;
            }

//            if (Boolean.TRUE.equals(ndaSwitch)) {
//                if (!whiteGateway.validUserWhite(content.getOperAccount())) {
//                    log.info("NDAMessageConsume operAccount is not white,operAccount is:" + content.getOperAccount());
//                    mqProducer.sendOldMessage(topic, tag, message);
//                    return;
//                }
//            }

            UserDo userDo = getUserByPhone(content.getOperAccount());
            if (userDo == null) {
                return;
            }

            //查询用户对应的实例信息
            Date now = new Date();
            DomainServiceContext instanceServiceContext = new DomainServiceContext(QUERY_GOODS_INSTANCE);
            QueryGoodsInstanceCondition queryGoodsInstanceCondition = new QueryGoodsInstanceCondition();
            queryGoodsInstanceCondition.setUserId(userDo.getUserId());
            queryGoodsInstanceCondition.setStateList(Arrays.asList(GoodsInstanceStateEnum.NORMAL, GoodsInstanceStateEnum.PAUSE,
                    GoodsInstanceStateEnum.UN_SUB));
            queryGoodsInstanceCondition.setEffectiveEndTimeStart(now);
            List<GoodsInstanceDo> instanceDoList = instanceServiceContext.read(queryGoodsInstanceCondition, GoodsInstanceDo.class);
            if (CollUtil.isEmpty(instanceDoList)) {
                return;
            }

            Map<String, GoodsDo> goodsMap = GoodsServiceFacade.buildNormalGoodsCacheMap(instanceDoList.stream().map(GoodsInstanceDo::getGoodsId).collect(Collectors.toList()));
            if (CollUtil.isEmpty(goodsMap)) {
                log.info("NDAMessageConsume subFlow is error,goodsMap is empty");
                return;
            }

            List<GoodsInstanceDo> pauseAndNormalPackageInstances = instanceDoList.stream().filter(goodsInstanceDo -> {
                        String goodsId = goodsInstanceDo.getGoodsId();
                        if (goodsMap.containsKey(goodsId)) {
                            GoodsDo goodsDo = goodsMap.get(goodsId);
                            return goodsDo != null && goodsDo instanceof GoodsPackageDo;
                        }
                        return false;
                    }).filter(goodsInstanceDo -> ((goodsInstanceDo.getEffectiveEndTime().after(now)
                            || GoodsInstanceStateEnum.PAUSE.equals(goodsInstanceDo.getStateEnum()))
                            && goodsMap.containsKey(goodsInstanceDo.getGoodsId())))
                    .collect(Collectors.toList());

            List<GoodsInstanceDo> subFlowInstances = instanceDoList.stream().filter(goodsInstanceDo -> {
                        String goodsId = goodsInstanceDo.getGoodsId();
                        if (goodsMap.containsKey(goodsId)) {
                            GoodsDo goodsDo = goodsMap.get(goodsId);
                            return goodsDo != null && BenefitGoodsDo.BenefitNo.BENEFIT_NO_FREE_FLOW_PKG.equals(goodsDo.getProductId());
                        }
                        return false;
                    }).filter(goodsInstanceDo -> ((goodsInstanceDo.getEffectiveEndTime().after(now)
                            || GoodsInstanceStateEnum.PAUSE.equals(goodsInstanceDo.getStateEnum()))
                            && goodsMap.containsKey(goodsInstanceDo.getGoodsId())))
                    .collect(Collectors.toList());

            //携号转入
            if (ChangeTypeConstant.PORT_IN.equals(content.getChangeType())) {
                subFlow(userDo, pauseAndNormalPackageInstances, subFlowInstances, goodsMap);
            }
            //携号转出
            else if (ChangeTypeConstant.PORT_OUT.equals(content.getChangeType())) {
                queryGoodsInstanceAndUnsubscribeRecord(userDo, pauseAndNormalPackageInstances, subFlowInstances, goodsMap);
            }


        } catch (Exception e) {
            log.error("NDAMessageConsume is error,e:", e);
        } finally {
            MDCUtils.clear();
        }
    }

    private void queryGoodsInstanceAndUnsubscribeRecord(UserDo userDo, List<GoodsInstanceDo> pauseAndNormalPackageInstances, List<GoodsInstanceDo> subFlowInstances, Map<String, GoodsDo> goodsMap) {
        // 子的关联父的商品实例
        Map<String, GoodsInstanceDo> pauseAndNormalPackageInstancesMap = pauseAndNormalPackageInstances.stream()
                .collect(Collectors.toMap(GoodsInstanceDo::getGoodsInstanceId, Function.identity()));
        for (GoodsInstanceDo goodsInstanceDo : pauseAndNormalPackageInstances) {
            try {
                if (GoodsInstanceStateEnum.NORMAL.equals(goodsInstanceDo.getStateEnum())
                        && ChargeTypeEnum.MONTHLY.equals(ChargeTypeEnum.fromType(goodsInstanceDo.getChargeType()))
                        && (PayWayEnum.PHONE_BILLS.equals(PayWayEnum.fromCode(goodsInstanceDo.getPayWay()))
                        || PayWayEnum.BONUS_POINT_PAY.equals(PayWayEnum.fromCode(goodsInstanceDo.getPayWay()))
                        || PayWayEnum.BONUS_POINT_PHONE_BILLS_PAY.equals(PayWayEnum.fromCode(goodsInstanceDo.getPayWay())))) {
                    DomainServiceContext context = new DomainServiceContext(PERFORM_ORDER);
                    PerformOrderDo performOrderDo = context.newInstance(PerformOrderDo.class);
                    performOrderDo.setGoodsId(goodsInstanceDo.getGoodsId());
                    performOrderDo.setGoodsInstanceId(goodsInstanceDo.getGoodsInstanceId());
                    performOrderDo.setUserDo(userDo);
                    performOrderDo.setOpType(SubscribeTypeEnum.UN_SUBSCRIBE_TYPE_ENUM);
                    context.writeAndFlush();
                }
            } catch (Exception e) {
                log.error("NDAMessageConsume recycleFlow is error", e);
            }
        }
//        subFlowInstances = subFlowInstances.stream().filter(goodsInstanceDo -> {
//                    GoodsInstanceDo instanceDo = pauseAndNormalPackageInstancesMap.get(goodsInstanceDo.getGoodsPackageInstanceId());
//                    if (instanceDo != null) {
//                        return (!(ChargeTypeEnum.MONTHLY.equals(ChargeTypeEnum.fromType(instanceDo.getChargeType()))
//                                && (PayWayEnum.PHONE_BILLS.equals(PayWayEnum.fromCode(instanceDo.getPayWay()))
//                                || PayWayEnum.BONUS_POINT_PAY.equals(PayWayEnum.fromCode(instanceDo.getPayWay()))
//                                || PayWayEnum.BONUS_POINT_PHONE_BILLS_PAY.equals(PayWayEnum.fromCode(instanceDo.getPayWay())))));
//                    } else {
//                        return true;
//                    }
//                }).
//                collect(Collectors.toList());
        for (
                GoodsInstanceDo subFlowInstance : subFlowInstances) {
            try {
                DomainServiceContext context = new DomainServiceContext(PERFORM_ORDER);
                PerformOrderDo performOrderDo = context.newInstance(PerformOrderDo.class);
                performOrderDo.setGoodsId(subFlowInstance.getGoodsId());
                performOrderDo.setGoodsInstanceId(subFlowInstance.getGoodsInstanceId());
                performOrderDo.setUserDo(userDo);
                performOrderDo.setOrderId(subFlowInstance.getOrderId());
                performOrderDo.setGoodsDo(goodsMap.get(subFlowInstance.getGoodsId()));
                performOrderDo.setUserId(userDo.getUserId());
                performOrderDo.setSaleType(subFlowInstance.getSaleType());
                performOrderDo.setOpType(SubscribeTypeEnum.REFUND);
                context.writeAndFlush();
            } catch (Exception e) {
                log.error("NDAMessageConsume recycleFlow is error", e);
            }
        }

    }

    private void subFlow(UserDo userDo, List<GoodsInstanceDo> pauseAndNormalPackageInstances, List<GoodsInstanceDo> subFlowInstances, Map<String, GoodsDo> goodsMap) {

        Date now = new Date();
        Map<String, GoodsInstanceDo> subFlowInstancesMap = subFlowInstances.stream()
                .collect(Collectors.toMap(GoodsInstanceDo::getGoodsPackageInstanceId, Function.identity()));

        for (GoodsInstanceDo packageGoodsInstanceDo : pauseAndNormalPackageInstances) {
            GoodsDo goodsDo = goodsMap.get(packageGoodsInstanceDo.getGoodsId());
            if (goodsDo instanceof GoodsPackageDo) {
                GoodsPackageDo goodsPackageDo = (GoodsPackageDo) goodsDo;
                GoodsDo childGoodsByProductIdDo = goodsPackageDo.getChildGoodsByProductId(
                        BenefitGoodsDo.BenefitNo.BENEFIT_NO_FREE_FLOW_PKG);
                // 获取父的订单
                DomainServiceContext context = new DomainServiceContext(QUERY_ORDER);
                QueryOrderCondition queryOrderCondition = new QueryOrderCondition();
                queryOrderCondition.setOrderNo(packageGoodsInstanceDo.getOrderId());
                queryOrderCondition.setUserId(packageGoodsInstanceDo.getUserId());
                OrderDo parentOrderDo = context.readFirst(queryOrderCondition, OrderDo.class);
                if (null != childGoodsByProductIdDo && !subFlowInstancesMap.containsKey(packageGoodsInstanceDo.getGoodsInstanceId())
                        && !GoodsInstanceStateEnum.PAUSE.equals(packageGoodsInstanceDo.getStateEnum())) {
                    // 获取父的订单
                    if (parentOrderDo != null) {
                        packageGoodsInstanceDo.setOrderDo(parentOrderDo);
                    }
                    createSub(SubscribeTypeEnum.SUBSCRIBE_TYPE_ENUM, childGoodsByProductIdDo, userDo, packageGoodsInstanceDo);
                } else if ((null != childGoodsByProductIdDo && !subFlowInstancesMap.containsKey(packageGoodsInstanceDo.getGoodsInstanceId())
                        && GoodsInstanceStateEnum.PAUSE.equals(packageGoodsInstanceDo.getStateEnum()))) {
                    createPauseGoodsInstance(userDo, now, packageGoodsInstanceDo, childGoodsByProductIdDo, parentOrderDo);
                }
            }
        }
    }


//    private void subFlow(UserStatusInfoMqMessage userInfo) {
//
//
//        //遍历商品实例，查询对应的免流产品
////        for (GoodsInstanceDo goodsInstanceDo : instanceDoList) {
////            if ((goodsInstanceDo.getEffectiveEndTime().after(new Date()))
////                    && goodsMap.containsKey(goodsInstanceDo.getGoodsId())) {
////                // 校验产品是否是商品包
////                GoodsDo goodsDo = goodsMap.get(goodsInstanceDo.getGoodsId());
////                goodsDo.findAutoActiveChildGoods().forEach(child -> {
////                    if (BenefitGoodsDo.BenefitNo.BENEFIT_NO_FREE_FLOW_PKG.equals(child.getProductId())
////                            && !goodsInstanceDo.getGoodsId().equals(child.getGoodsId())) {
////                        createSub(SubscribeTypeEnum.SUBSCRIBE_TYPE_ENUM, child, userDo, goodsInstanceDo);
////                    }
////                });
////            } else if (GoodsInstanceStateEnum.PAUSE.equals(goodsInstanceDo.getStateEnum()) && goodsMap.containsKey(goodsInstanceDo.getGoodsId())) {
////
////            }
////            //免流产品
////                if (BenefitGoodsDo.BenefitNo.BENEFIT_NO_FREE_FLOW_PKG.equals(goodsDo.getProductId())) {
////                    //获取对应的产品
////                    DomainServiceContext productServiceContext =
////                            new DomainServiceContext(ProductDo.class, ProductServiceId.QUERY_PRODUCT_INFO);
////                    ProductDo productDo = productServiceContext.read(goodsDo.getProductId(), ProductDo.class);
////                    if (null == productDo) {
////                        log.error("[DOMAIN] product not found. productId:{}", goodsDo.getProductId());
////                        throw new ServiceException();
////                    }
////
////                    //获取对应订单信息
////                    DomainServiceContext orderServiceContext = new DomainServiceContext(OrderDo.class, null);
////                    OrderDo orderDo = orderServiceContext.read(goodsInstanceDo.getOrderId(), OrderDo.class);
////
////                    SubscribeTypeEnum subscribeTypeEnum = SubscribeTypeEnum.SUBSCRIBE_TYPE_ENUM;
////
////                    ProductServiceDo productServiceDo = productDo.getProductService(subscribeTypeEnum);
////                    if (productServiceDo == null) {
////                        log.warn("[DOMAIN] no need to sendMessage. productServiceDo is null. productId:{}, subscribeType:{}",
////                                productDo.getProductId(), subscribeTypeEnum);
////                        return;
////                    }
////
////                    if (CollectionUtils.isEmpty(goodsDo.getNotifyServiceList())) {
////                        log.warn("[DOMAIN] no need to sendMessage. notifyServiceList is empty. goodsId:{}", goodsDo.getGoodsId());
////                        return;
////                    }
////                    sendMessageDo(subscribeTypeEnum, orderDo, goodsDo, userDo, goodsInstanceDo, goodsInstanceDo.getPayWay());
////                }
////            }
////        }
//    }

    private static Map<String, GoodsDo> getGoodsDoMapByInstanceGIds(List<GoodsInstanceDo> instanceDoList) {
        //查询这些商品实例对应的产品
        Map<String, GoodsDo> goodsMap = new HashMap<>();
        List<GoodsDo> goodsList = GoodsServiceFacade.getGoodsList(instanceDoList.stream().map(GoodsInstanceDo::getGoodsId).collect(Collectors.toList()));
        goodsList.forEach(goodsDo -> {
            if (goodsDo instanceof GoodsPackageDo) {
                goodsMap.put(goodsDo.getGoodsId(), goodsDo);
                GoodsPackageDo goodsPackageDo = (GoodsPackageDo) goodsDo;
                GoodsDo childGoodsByProductIdDo = goodsPackageDo.getChildGoodsByProductId(
                        BenefitGoodsDo.BenefitNo.BENEFIT_NO_FREE_FLOW_PKG);
                if (null != childGoodsByProductIdDo) {
                    goodsMap.put(childGoodsByProductIdDo.getGoodsId(), childGoodsByProductIdDo);
                }
            }
        });
        if (CollUtil.isEmpty(goodsMap)) {
            log.debug("NDAMessageConsume goodsMap is empty,instanceDoList is:{}", instanceDoList);
            return null;
        }
        return goodsMap;
    }

    private static void createPauseGoodsInstance(UserDo userDo, Date now, GoodsInstanceDo packageGoodsInstanceDo, GoodsDo childGoodsByProductIdDo, OrderDo orderDo) {
        OrderDo productOrder = buildProductOrder(childGoodsByProductIdDo, orderDo, userDo.getUserId());
        GoodsInstanceDo goodsInstanceDo = initPauseGoodsInstanceDo(userDo, childGoodsByProductIdDo, productOrder, now, packageGoodsInstanceDo);
        createSubscribeRec(goodsInstanceDo, childGoodsByProductIdDo, now, productOrder, goodsInstanceDo.getGoodsInstanceId(), false);
        createReceivedRec(goodsInstanceDo, childGoodsByProductIdDo, now, productOrder, false);
        // 创建会员领取商品扩展表
        GoodsInstanceServiceFacade.createGoodsInstance(goodsInstanceDo, childGoodsByProductIdDo, now,
                productOrder, goodsInstanceDo.getGoodsInstanceId(), false);
    }

    private static GoodsInstanceDo initPauseGoodsInstanceDo(UserDo userDo, GoodsDo goodsDo, OrderDo orderDo, Date now, GoodsInstanceDo packageGoodsInstanceDo) {
        String goodsInstanceId = OrderPrimaryKeyGenerate.generateGoodInstanceId(orderDo.getSaleType(), packageGoodsInstanceDo.getGoodsInstanceId());

        GoodsInstanceDo goodsInstanceDo = new GoodsInstanceDo();
        goodsInstanceDo.setUserId(userDo.getUserId());
        goodsInstanceDo.setGoodsInstanceId(goodsInstanceId);
        goodsInstanceDo.setGoodsId(goodsDo.getGoodsId());
        goodsInstanceDo.setProductId(goodsDo.getProductId());
        goodsInstanceDo.setPayWay(null != orderDo.getPayWayEnum() ? orderDo.getPayWayEnum().getCode() : null);
        goodsInstanceDo.setSubTime(now);
        goodsInstanceDo.setSubWay(null != orderDo.getChannelId() ? Integer.valueOf(orderDo.getChannelId()) : null);
        goodsInstanceDo.setSaleType(orderDo.getSaleType());
        goodsInstanceDo.setChargeType(String.valueOf(goodsDo.getChargeType().getType()));
        goodsInstanceDo.setTimePlanGoodsGroupId(goodsDo.getTimePlanGoodsGroupId());
        goodsInstanceDo.setDealPrice(orderDo.getOrderDetailDos().get(0).getActualReceipt());
        goodsInstanceDo.setSubChannelId(orderDo.getSubChannelId());
        goodsInstanceDo.setChannelId(orderDo.getChannelId());
        goodsInstanceDo.setCycleType(goodsDo.getTimePlan().getCycleType().getType());
        goodsInstanceDo.setCycleCount(goodsDo.getTimePlan().getCycleCount());
        goodsInstanceDo.setUserId(goodsInstanceDo.getUserId());
        goodsInstanceDo.setOrderId(orderDo.getOrderNo());
        goodsInstanceDo.setStateEnum(GoodsInstanceStateEnum.NORMAL);
        goodsInstanceDo.setCreateTime(now);
        goodsInstanceDo.setUpdateTime(now);
        goodsInstanceDo.setOrderDo(orderDo);
        goodsInstanceDo.setUserDo(userDo);
        goodsInstanceDo.setUnsubTime(packageGoodsInstanceDo.getUnsubTime());
        goodsInstanceDo.setPauseTime(packageGoodsInstanceDo.getPauseTime());
        goodsInstanceDo.setActiveTime(packageGoodsInstanceDo.getActiveTime());
        goodsInstanceDo.setRightsType(goodsDo.getGoodsSalesType().equals(GoodsSalesTypeEnum.RIGHTS) ?
                String.valueOf(goodsDo.getNotifyServiceList().get(0).getNotifyAddType()) : null);
        goodsInstanceDo.setOpType(goodsInstanceDo.getOpType());
        goodsInstanceDo.setEffectiveStartTime(packageGoodsInstanceDo.getEffectiveStartTime());
        goodsInstanceDo.setEffectiveEndTime(packageGoodsInstanceDo.getEffectiveEndTime());
        goodsInstanceDo.setGoodsPackageInstanceId(packageGoodsInstanceDo.getGoodsInstanceId());
        goodsInstanceDo.setRightsType(packageGoodsInstanceDo.getRightsType());
        goodsInstanceDo.setStateEnum(GoodsInstanceStateEnum.PAUSE);
        goodsInstanceDo.setGoodsPackageRightsType(packageGoodsInstanceDo.getRightsType());
        return goodsInstanceDo;
    }

    /**
     * 创建产品订购流水
     *
     * @param goodsDo             产品
     * @param userDo              用户
     * @param parentGoodsInstance 父级商品实例
     */
    private void createSub(SubscribeTypeEnum subscribeTypeEnum, GoodsDo goodsDo, UserDo userDo, GoodsInstanceDo parentGoodsInstance) {
        // 创建订购流水
        OrderDo productOrder = buildProductOrder(goodsDo, parentGoodsInstance.getOrderDo(), userDo.getUserId());
        String childGoodsInstanceId = SnowflakeUtil.getNextString();
        DeliverGoodsWrapper deliverGoodsWrapper = DeliverGoodsWrapper.builder()
                .subscribeTypeEnum(subscribeTypeEnum)
                .goodsDo(goodsDo)
                .userDo(userDo)
                .orderDo(productOrder)
                .goodsInstanceId(childGoodsInstanceId)
                .parentGoodsInstance(parentGoodsInstance)
                .isTimeWithPackageGoodsInstance(true).build();
        buildSubGoodsInstance(deliverGoodsWrapper);
    }

    /**
     * 发送开通信息到mq中
     *
     * @param subscribeTypeEnum
     * @param childOrder
     * @param goodsDo
     * @param userDo
     * @param goodsInstanceExtendDo
     * @param payWay
     * @return
     */
//    private MessageDo sendMessageDo(SubscribeTypeEnum subscribeTypeEnum, OrderDo childOrder, GoodsDo goodsDo, UserDo userDo, GoodsInstanceDo goodsInstanceExtendDo, Integer payWay) {
//
//        if (!org.springframework.util.StringUtils.hasLength(goodsDo.getProductId())) {
//            log.error("[DOMAIN] productId is null. goodsId:{}", goodsDo.getGoodsId());
//            throw new ServiceException();
//        }
//
//        DomainServiceContext productContext = new DomainServiceContext(ProductDo.class, ProductServiceId.QUERY_PRODUCT_INFO);
//        ProductDo productDo = productContext.read(goodsDo.getProductId(), ProductDo.class);
//
//        if (null == productDo) {
//            log.error("[DOMAIN] product not found. productId:{}", goodsDo.getProductId());
//            throw new ServiceException();
//        }
//
//        ProductServiceDo productServiceDo = productDo.getProductService(subscribeTypeEnum);
//        if (productServiceDo == null) {
//            log.warn("[DOMAIN] no need to sendMessage. productServiceDo is null. productId:{}, subscribeType:{}", productDo.getProductId(), subscribeTypeEnum);
//            return null;
//        }
//
//        if (CollectionUtils.isEmpty(goodsDo.getNotifyServiceList())) {
//            log.warn("[DOMAIN] no need to sendMessage. notifyServiceList is empty. goodsId:{}", goodsDo.getGoodsId());
//            return null;
//        }
//
//        if (goodsDo.getNotifyServiceList().get(0).getNotifyAddType() != 3) {
//            // 获取对应的发货策略id
//            NotifyServiceVo notifyServiceVo = goodsDo.getNotifyServiceList().get(0);
//            String outerProductId = notifyServiceVo.getOuterProductId();
//            Integer notifyAddType = notifyServiceVo.getNotifyAddType();
//            ComSendInterfaceReq comSendInterfaceReq = ComSendInterfaceReq.builder()
////                    .activityPrice()
//                    .chargeType(String.valueOf(goodsDo.getChargeType().getType())).createTime(DateUtil.format(childOrder.getCreateTime(), DatePattern.PURE_DATETIME_PATTERN)).effectiveStartTime(DateUtil.format(goodsInstanceExtendDo.getEffectiveStartTime(), DatePattern.PURE_DATETIME_PATTERN)).effectiveEndTime(DateUtil.format(goodsInstanceExtendDo.getEffectiveEndTime(), DatePattern.PURE_DATETIME_PATTERN)).goodsId(goodsDo.getGoodsId()).msisdn(userDo.getMsisdn())
////                    .activityRenewPrice()
//                    .nationCode(userDo.getNationCode()).orderID(childOrder.getOrderNo())
////                    .notifyType(notifyAddType)
//                    .proServiceId(outerProductId)
////                    .outOrderId()
//                    .payWay(String.valueOf(payWay == null ? goodsInstanceExtendDo.getPayWay() : payWay)).subTime(DateUtil.format(goodsInstanceExtendDo.getSubTime(), DatePattern.PURE_DATETIME_PATTERN)).userDomainId(userDo.getUserDomainId()).userId(userDo.getUserId()).goodsName(goodsDo.getGoodsName()).goodsInstanceId(goodsInstanceExtendDo.getGoodsInstanceId()).totalAmount(childOrder.getTotalAmount()).build();
//            MessageDo messageDo = new MessageDo();
//            messageDo.setTraceId(goodsDo.getTraceId());
//            messageDo.setMsgId(goodsInstanceExtendDo.getGoodsInstanceId() + "_" + subscribeTypeEnum.getCode());
//            messageDo.setServiceId(MessageServiceId.ServiceOpen.SERVICE_ID_WORK_ORDER);
//            messageDo.setContext(JsonUtil.toJson(comSendInterfaceReq));
//            messageDo.setVersion("1");
//            // messageDo.setEnterpriseId(env.getProperty("enterpriseId"));
//            messageDo.setEventType(MessageServiceId.Operator.CREATE);
//            messageDo.setUserId(userDo.getUserId());
//            messageDo.setOrderId(childOrder.getOrderNo());
//            messageDo.setSopenServiceId(productServiceDo.getSoServiceCode());
//            messageDo.setOperateTime(new Date());
//            rocketMQTemplate.send(taskTopic, // 第一个参数：主题名
//                    MessageBuilder.withPayload(Objects.requireNonNull(JsonUtil.toJson(messageDo))).setHeader(RocketMQHeaders.TAGS, taskTag).build());
//            return messageDo;
//
//        }
//        return null;
//    }

    /**
     * 查询需要退订的记录，并进行退订
     *
     * @param userInfo
     */
//    private void queryGoodsInstanceAndUnsubscribeRecord(UserStatusInfoMqMessage userInfo) {
//
//        Date now = new Date();
//        UserDo userDo = getUserByPhone(userInfo.getOperAccount());
////        DomainServiceContext goodsInstanceDomainServiceContext = new DomainServiceContext(QUERY_GOODS_INSTANCE);
////        //查询空间类型
////        QueryGoodsInstanceCondition queryGoodsInstanceCondition = new QueryGoodsInstanceCondition();
////        queryGoodsInstanceCondition.setUserId(userDo.getUserId());
////        queryGoodsInstanceCondition.setEffectiveEndTimeStart(now);
////        queryGoodsInstanceCondition.setChargeType(ChargeTypeEnum.MONTHLY);
////        queryGoodsInstanceCondition.setStateList(Arrays.asList(GoodsInstanceStateEnum.NORMAL, GoodsInstanceStateEnum.PAUSE));
////        queryGoodsInstanceCondition.setPayWayList(Arrays.asList(PayWayEnum.PHONE_BILLS, PayWayEnum.BONUS_POINT_PAY
////                , PayWayEnum.BONUS_POINT_PHONE_BILLS_PAY));
////        List<GoodsInstanceDo> goodsInstanceDos = goodsInstanceDomainServiceContext.read(queryGoodsInstanceCondition, GoodsInstanceDo.class);
//
//        //查询会员类型
////        QueryGoodsInstanceExtendCondition memberBaseCondition = new QueryGoodsInstanceExtendCondition();
////        memberBaseCondition.setUserId(userDo.getUserId());
////        memberBaseCondition.setSaleType(Integer.valueOf(SaleTypeEnum.SPACE.getCode()));
////        List<GoodsInstanceDo> memberGoodsInstanceList = goodsInstanceDomainServiceContext.read(memberBaseCondition, GoodsInstanceDo.class);
//
////        List<GoodsInstanceDo> filterNeedToUnsubscribe = filterNeedToUnsubscribe(goodsInstanceDos);
////        List<GoodsInstanceDo> memberNeedToUnsubscribe = filterNeedToUnsubscribe(memberGoodsInstanceList);
//
//
//        DomainServiceContext goodsInstanceDomainServiceContext = new DomainServiceContext(QUERY_GOODS_INSTANCE);
//        //查询空间类型
//        QueryGoodsInstanceCondition queryGoodsInstanceCondition = new QueryGoodsInstanceCondition();
//        queryGoodsInstanceCondition.setUserId(userDo.getUserId());
//        queryGoodsInstanceCondition.setEffectiveEndTimeStart(now);
//        List<GoodsInstanceDo> goodsInstanceEffectDos = goodsInstanceDomainServiceContext.read(queryGoodsInstanceCondition, GoodsInstanceDo.class);
//
//        Map<String, GoodsDo> goodsMap = GoodsServiceFacade.buildNormalGoodsCacheMap(goodsInstanceEffectDos.stream().map(GoodsInstanceDo::getGoodsId).collect(Collectors.toList()));
//        if (CollUtil.isEmpty(goodsMap)) {
//            log.info("NDAMessageConsume subFlow is error,goodsMap is empty");
//            return;
//        }
//
//        List<GoodsInstanceDo> pauseAndNormalPackageInstances = goodsInstanceEffectDos.stream().filter(goodsInstanceDo -> {
//                    String goodsId = goodsInstanceDo.getGoodsId();
//                    if (goodsMap.containsKey(goodsId)) {
//                        GoodsDo goodsDo = goodsMap.get(goodsId);
//                        return goodsDo != null && goodsDo instanceof GoodsPackageDo;
//                    }
//                    return false;
//                }).filter(goodsInstanceDo -> ((goodsInstanceDo.getEffectiveEndTime().after(now)
//                        || GoodsInstanceStateEnum.PAUSE.equals(goodsInstanceDo.getStateEnum()))
//                        && goodsMap.containsKey(goodsInstanceDo.getGoodsId())))
//                .collect(Collectors.toList());
//
//
//        List<GoodsInstanceDo> subFlowInstances = goodsInstanceEffectDos.stream().filter(goodsInstanceDo -> {
//                    String goodsId = goodsInstanceDo.getGoodsId();
//                    if (goodsMap.containsKey(goodsId)) {
//                        GoodsDo goodsDo = goodsMap.get(goodsId);
//                        return goodsDo != null && BenefitGoodsDo.BenefitNo.BENEFIT_NO_FREE_FLOW_PKG.equals(goodsDo.getProductId());
//                    }
//                    return false;
//                }).filter(goodsInstanceDo -> ((goodsInstanceDo.getEffectiveEndTime().after(now)
//                        || GoodsInstanceStateEnum.PAUSE.equals(goodsInstanceDo.getStateEnum()))
//                        && goodsMap.containsKey(goodsInstanceDo.getGoodsId())))
//                .collect(Collectors.toList());
//
//        for (GoodsInstanceDo goodsInstanceDo : pauseAndNormalPackageInstances) {
//            try {
//                if (ChargeTypeEnum.MONTHLY.equals(ChargeTypeEnum.fromType(goodsInstanceDo.getChargeType()))
//                        && PayWayEnum.PHONE_BILLS.equals(PayWayEnum.fromCode(goodsInstanceDo.getPayWay()))
//                        && PayWayEnum.BONUS_POINT_PAY.equals(PayWayEnum.fromCode(goodsInstanceDo.getPayWay()))
//                        && PayWayEnum.BONUS_POINT_PHONE_BILLS_PAY.equals(PayWayEnum.fromCode(goodsInstanceDo.getPayWay()))) {
//                    DomainServiceContext context = new DomainServiceContext(PERFORM_ORDER_SLB);
//                    PerformSlbOrderDo performSlbOrderDo = context.newInstance(PerformSlbOrderDo.class);
//                    performSlbOrderDo.setGoodsId(goodsInstanceDo.getGoodsId());
//                    performSlbOrderDo.setGoodsInstanceId(goodsInstanceDo.getGoodsInstanceId());
//                    performSlbOrderDo.setMsisdn(userDo.getMsisdn());
//                    performSlbOrderDo.setOpType(SubscribeTypeEnum.UN_SUBSCRIBE_TYPE_ENUM);
//                    context.writeAndFlush();
//                }
//            } catch (Exception e) {
//                log.error("NDAMessageConsume recycleFlow is error", e);
//            }
//        }
//        for (GoodsInstanceDo subFlowInstance : subFlowInstances) {
//            try {
//                DomainServiceContext context = new DomainServiceContext(PERFORM_ORDER_SLB);
//                PerformSlbOrderDo performSlbOrderDo = context.newInstance(PerformSlbOrderDo.class);
//                performSlbOrderDo.setGoodsId(subFlowInstance.getGoodsId());
//                performSlbOrderDo.setGoodsInstanceId(subFlowInstance.getGoodsInstanceId());
//                performSlbOrderDo.setMsisdn(userDo.getMsisdn());
//                performSlbOrderDo.setOpType(SubscribeTypeEnum.REFUND);
//                context.writeAndFlush();
//            } catch (Exception e) {
//                log.error("NDAMessageConsume recycleFlow is error", e);
//            }
//        }
//
////        List<GoodsInstanceDo> collect = goodsInstanceEffectDos.stream().filter(goodsInstanceDo -> ChargeTypeEnum.MONTHLY.equals(ChargeTypeEnum.fromType(goodsInstanceDo.getChargeType()))
////                && goodsInstanceDo.getStateEnum().equals(GoodsInstanceStateEnum.NORMAL) && PayWayEnum.PHONE_BILLS.equals(PayWayEnum.fromCode(goodsInstanceDo.getPayWay()))
////                && PayWayEnum.BONUS_POINT_PAY.equals(PayWayEnum.fromCode(goodsInstanceDo.getPayWay()))
////                && PayWayEnum.BONUS_POINT_PHONE_BILLS_PAY.equals(PayWayEnum.fromCode(goodsInstanceDo.getPayWay()))).collect(Collectors.toList());
////        List<String> collect1 = collect.stream().map(GoodsInstanceDo::getGoodsInstanceId).collect(Collectors.toList());
////        goodsInstanceEffectDos.removeIf(c -> collect1.contains(c.getGoodsInstanceId()));
////
////        for (GoodsInstanceDo goodsInstanceDo : collect) {
////            try {
////                DomainServiceContext context = new DomainServiceContext(PERFORM_ORDER_SLB);
////                PerformSlbOrderDo performSlbOrderDo = context.newInstance(PerformSlbOrderDo.class);
////                performSlbOrderDo.setGoodsId(goodsInstanceDo.getGoodsId());
////                performSlbOrderDo.setGoodsInstanceId(goodsInstanceDo.getGoodsInstanceId());
////                performSlbOrderDo.setMsisdn(userDo.getMsisdn());
////                performSlbOrderDo.setOpType(SubscribeTypeEnum.UN_SUBSCRIBE_TYPE_ENUM);
////                context.writeAndFlush();
////            } catch (Exception e) {
////                log.error("unsub failed the goodsInstance is {}", goodsInstanceDo, e);
////            }
////        }
//
//
//        //订单退订
////        for (GoodsInstanceDo goodsInstanceDo : filterNeedToUnsubscribe) {
////            try {
////                DomainServiceContext context = new DomainServiceContext(PERFORM_ORDER_SLB);
////                PerformSlbOrderDo performSlbOrderDo = context.newInstance(PerformSlbOrderDo.class);
////                performSlbOrderDo.setGoodsId(goodsInstanceDo.getGoodsId());
////                performSlbOrderDo.setGoodsInstanceId(goodsInstanceDo.getGoodsInstanceId());
////                performSlbOrderDo.setMsisdn(userDo.getMsisdn());
////                performSlbOrderDo.setOpType(SubscribeTypeEnum.UN_SUBSCRIBE_TYPE_ENUM);
////                context.writeAndFlush();
////            } catch (Exception e) {
////                log.error("unsub failed the goodsInstance is {}", goodsInstanceDo, e);
////            }
////        }
//    }


    /**
     * 过滤出需要退订的
     *
     * @param goodsInstanceDoList
     * @return
     */
    private List<GoodsInstanceDo> filterNeedToUnsubscribe(List<GoodsInstanceDo> goodsInstanceDoList) {
        return goodsInstanceDoList.stream().filter(Objects::nonNull)
                //过滤出父商品实例
                .filter(goodsInstanceDo -> {
                    return CharSequenceUtil.isEmpty(goodsInstanceDo.getGoodsPackageInstanceId());
                })
                //还没有过期的记录
                .filter(goodsInstanceDo -> goodsInstanceDo.getEffectiveEndTime().after(new Date()))
                //话费支付、积分支付或者是两者混合的才需要退订
                .filter(goodsInstanceDo -> {
                    Integer payWay = goodsInstanceDo.getPayWay();
                    return String.valueOf(ChargeTypeEnum.MONTHLY.getType()).equals(goodsInstanceDo.getChargeType()) && (PayWayEnum.PHONE_BILLS.getCode().equals(payWay) || PayWayEnum.BONUS_POINT_PAY.getCode().equals(payWay) || PayWayEnum.NEW_ALI_PAY.getCode().equals(payWay));
                }).collect(Collectors.toList());
    }

    private UserDo getUserByPhone(String account) {
        String phone = MsisdnUtil.getCellPhoneNo(account);
        DomainServiceContext queryUser = new DomainServiceContext(UserServiceId.USER_LSB_SERVICE);
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setAccount(phone);
        UserDo userDo = queryUser.readFirst(queryUserCondition, UserDo.class);
        return userDo;
    }
}
