package com.zyhl.yun.member.mcdmc.activation.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.FlowLogStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/06/04 14:23
 * 接口日志表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("activation_work_service_flow_log")
public class WorkServiceFlowLogPo extends BasePo {

    /**
     * 日志ID
     */
    @TableId
    private String logId;

    /**
     * 服务ID
     */
    private String serviceCode;
    /**
     * 流程接口id
     */
    @TableField("iface_id")
    private Long iFaceId;

    /**
     * 工单ID
     */
    private String workId;
    /**
     * 用户ID
     */
    private String userId;

    /**
     * 订单Id
     */
    private String orderId;

    /**
     * 接口配置表id
     */
    private String workServiceFlowId;

    /**
     * 发送url
     */
    private String url;

    /**
     * 接口入参_header
     */
    private String requestHeader;

    /**
     * 接口入参_body
     */
    private String requestBody;

    /**
     * 调用接口返回的httpCode
     */
    private Integer responseHttpCode;
    /**
     * 接口出参_header
     */
    private String responseHeader;
    /**
     * 接口出参_body
     */
    private String responseBody;

    /**
     * 回调地址
     */
    private String callbackUrl;

    /**
     * 回调查询参数
     */
    private String callbackCondition;

    /**
     * 接口重试次数
     */
    private Integer retryCount;

    /**
     * 扩展字段上下文
     */
    private String extParams;

    /**
     * 接口日志状态,具体状态值见
     * {@link FlowLogStateEnum FlowLogStateEnum}
     */
    private Integer state;

}
