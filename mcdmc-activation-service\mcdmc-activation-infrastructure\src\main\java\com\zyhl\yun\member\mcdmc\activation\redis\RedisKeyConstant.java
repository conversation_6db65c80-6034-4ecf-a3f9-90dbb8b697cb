package com.zyhl.yun.member.mcdmc.activation.redis;

/**
 * <AUTHOR>
 * @since 2024/07/10 09:53
 */
public class RedisKeyConstant {
    private RedisKeyConstant() {
    }

    private static final String MCDMC_PREFIX = "mcdmc:activation:";

    /**
     * 获取权益订单权益到账短信发送的redis key
     *
     * @param msisdn          手机号
     * @param goodsInstanceId 权益商品实例id
     */
    public static String getRaiSubSmsNotifyExpireKey(String msisdn, String goodsInstanceId) {
        return String.format(MCDMC_PREFIX + "rai:sendSms:%s:%s", msisdn, goodsInstanceId);
    }

    public static String getSendFlowLogKey(String callbackUri, String callbackCondition) {
        return String.format(MCDMC_PREFIX + "work:log:%s:%s", callbackUri, callbackCondition);
    }
}
