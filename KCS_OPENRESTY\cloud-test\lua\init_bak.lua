-- 指定白名单列表
local whiteList = {"13590104821","14779767754","14779767755","14779767756","14779767757","13360302181","13424062603","13632753245","19898398810","800001477309","248900851819","800001482111","19878342211","800001477875","19878342212","312349646378","800006498816","224582019260","19878342213","363215608142","20240816172357552999","11180309173530006308","344963956374241292"}
-- 指定新旧机器配置信息
local newVsboArr = {"[fd11:1111:1111:15::69fd]:18188"}
local oldVsboArr = {"***********:8060"}
local oldAdArr = {"***********:8060"}
local oldPgwArr = {"***********:8060"}
local conf_dict = ngx.shared.conf_dict
-- 新vsbo机器
for i = 1,#newVsboArr do
    local ip, port = newVsboArr[i]:match("^(.+):(.+)$")
    local index =i-1
    local ipKey = "newVsboIp" .. index
    local portKey = "newVsboPort" .. index
    conf_dict:set(ipKey,ip)
    conf_dict:set(portKey,tonumber(port))
end
-- 旧vsbo机器
for i = 1,#oldVsboArr do
    local ip, port = oldVsboArr[i]:match("^(.+):(.+)$")
    local index =i-1
    local ipKey = "oldVsboIp" .. index
    local portKey = "oldVsboPort" .. index
    conf_dict:set(ipKey,ip)
    conf_dict:set(portKey,tonumber(port))
end

-- 旧Ad机器
for i = 1,#oldAdArr do
    local ip, port = oldAdArr[i]:match("^(.+):(.+)$")
    local index = i - 1
    local ipKey = "oldAdIp" .. index
    local portKey = "oldAdPort" .. index
    conf_dict:set(ipKey,ip)
    conf_dict:set(portKey,tonumber(port))
end
-- 旧Pgw机器
for i = 1,#oldPgwArr do
    local ip, port = oldPgwArr[i]:match("^(.+):(.+)$")
    local index = i - 1
    local ipKey = "oldPgwIp" .. index
    local portKey = "oldPgwPort" .. index
    conf_dict:set(ipKey,ip)
    conf_dict:set(portKey,tonumber(port))
end
conf_dict:set("newIndex",0)
conf_dict:set("oldIndex",0)
conf_dict:set("newTotal",#newVsboArr)
conf_dict:set("oldTotal",#oldVsboArr)

-- 设置白名单
local white_dict = ngx.shared.white_dict
for i = 1,#whiteList do
    white_dict:set(whiteList[i],"exit")
end

-- 设置json映射路径
local json_path_dict = ngx.shared.json_path_dict
-- userId获取规则为：lua脚本远程调用接口获取userId
json_path_dict:set("raiNotifyEquityResult_0","3_orderNo")
json_path_dict:set("notifyMovieEquityRightsResult_0","3_orderNo")
-- userId获取规则为：userId_***
json_path_dict:set("notifyRightsSalesReturnResult_0","1_contractRoot.body.channelNo")

-- 设置xml映射路径
local xml_path_dict = ngx.shared.xml_path_dict
xml_path_dict:set("createOrder_0","createOrderReq.userID")
xml_path_dict:set("payOrder_0","payOrderReq.userID")
xml_path_dict:set("queryOrders_0","queryOrdersReq.orderQueryCond.orderUserID")
xml_path_dict:set("queryOrdersV1_0","queryOrdersReq.orderQueryCond.orderUserID")
xml_path_dict:set("preOrderCheck_0","createOrderReq.userID")
xml_path_dict:set("cancelAccount_0","cancelAccountReq.account")
xml_path_dict:set("cancelAccount_1","cancelAccountReq.userDomainId")
xml_path_dict:set("queryOrCreateUserID_0","queryOrCreateUserIDReq.account.accountName")
xml_path_dict:set("syncAppOrder_0","SyncAppOrderReq.MSISDN")
xml_path_dict:set("querySubscribeRelation_0","querySubscribeRelationReq.userId")
xml_path_dict:set("querySubscribeRelationV1_0","querySubscribeRelationReq.userId")
xml_path_dict:set("querySubscribeRelationV2_0","querySubscribeRelationReq.userId")
xml_path_dict:set("createSubscribeRelation_0","createSubscribeRelationReq.userID")
xml_path_dict:set("queryContractListSubScription_0","queryContractListSubScriptionReq.account")
xml_path_dict:set("queryContractListSubScriptionV2_0","queryContractListSubScriptionReq.account")
xml_path_dict:set("queryContractListSubScriptionV3_0","queryContractListSubScriptionReq.account")
xml_path_dict:set("queryContractListSubScriptionV4_0","queryContractListSubScriptionReq.account")
xml_path_dict:set("queryContractListSubScriptionV5_0","queryContractListSubScriptionReq.account")
xml_path_dict:set("manageSubscribeRelation_0","manageSubscribeRelationReq.account")
xml_path_dict:set("manageSubscribeRelation_1","manageSubscribeRelationReq.userDomainId")
xml_path_dict:set("querySubscribeOperation_0","querySubscribeOperationReq.account")
xml_path_dict:set("querySubscribeOperation_1","querySubscribeOperationReq.userDomainId")
xml_path_dict:set("activeGotoneRights_0","activeGotoneRightsReq.account")
xml_path_dict:set("chkValidMonthSubscribe_0","chkValidMonthSubscribeReq.account")
xml_path_dict:set("chkValidMonthSubscribe_1","chkValidMonthSubscribeReq.userDomainId")
xml_path_dict:set("hySubscribe_0","hySubscribeReq.account")
xml_path_dict:set("hySubscribe_1","hySubscribeReq.userDomainId")
xml_path_dict:set("subscribeFlowSpec_0","subscribeFlowSpecReq.account")
xml_path_dict:set("querySubscribeFlowSpec_0","querySubscribeFlowSpecReq.account")
xml_path_dict:set("querySubscribeFlowSpecV2_0","querySubscribeFlowSpecReq.account")
xml_path_dict:set("querySubscribeFlowSpecV3_0","querySubscribeFlowSpecReq.account")
xml_path_dict:set("rollBackRight_0","rollBackRightReq.account")
xml_path_dict:set("queryRightsSubscribeRelation_0","querySpaceSubscribeRelationReq.account")
xml_path_dict:set("subscribeFlowSpecV2_0","subscribeFlowSpecReq.account")
xml_path_dict:set("queryReturnSubscription_0","subscribeFlowSpecReq.account")
xml_path_dict:set("queryGotone_0","queryGotoneReq.account.accountName")
xml_path_dict:set("bindFamilyNumber_0","bindFamilyNumberReq.account.userDomainId")
xml_path_dict:set("queryFamilyNumber_0","queryFamilyNumberReq.account.accountName")
xml_path_dict:set("queryFamilyNumber_1","queryFamilyNumberReq.account.userDomainId")
xml_path_dict:set("queryRights_0","queryRightsReq.account.accountName")
xml_path_dict:set("subscribeFlowSpecNotify_0","subscribeFlowSpecNotifyReq.telephone")
xml_path_dict:set("prepareRefund_0","prepareRefundReq.account")
xml_path_dict:set("refundNotify_0","msgReq.userID")
xml_path_dict:set("activeRightsPackage_0","activeRightsPackageReq.userId")
xml_path_dict:set("queryPhoneType_0","queryPhoneTypeReq.account.accountName")
xml_path_dict:set("queryPhoneType_1","queryPhoneTypeReq.account.userDomainId")
-- xml_path_dict:set("queryProductsV2_0","queryProductsReq.extensionInfo.namedParameters.value")
-- userId获取规则为：userId_***
xml_path_dict:set("notifyTpctPayResult_0","1_msgReq.orderId")
xml_path_dict:set("unSubscribe_0","1_unSubscribeReq.orderID")
xml_path_dict:set("notifyOrderPayResult_0","1_notifyOrderPayResultReq.orderID")
-- userId获取规则为：***_userId
xml_path_dict:set("notifyTradeStatus_0","2_msgReq.outOrderId")
-- ***_rft分割规则，以_rft结尾则直接设置结果为白名单用户
xml_path_dict:set("salesorderResult_0","4_OPFlowPkgSubsFdbkReq.orderId")
xml_path_dict:set("flowCardOrderResult_0","4_OPFlowCardOrderFdbkReq.orderId")

-- 下面几个接口的字段是给测试自己进行分流用的，实际接口不存在testEnv字段
xml_path_dict:set("createProduct_0","0_createProductReq.productInfo.testEnv")
xml_path_dict:set("updateProduct_0","0_updateProductReq.productInfo.testEnv")
xml_path_dict:set("queryProducts_0","0_queryProductsReq.testEnv")
xml_path_dict:set("queryProductsV2_0","0_queryProductsReq.testEnv")
xml_path_dict:set("orderReduction_0","0_orderReductionReq.testEnv")
xml_path_dict:set("queryProductsByType_0","0_queryProductsByTypeReq.testEnv")
xml_path_dict:set("deleteProduct_0","0_deleteProductReq.testEnv")
ngx.log(ngx.ERR,"init success")