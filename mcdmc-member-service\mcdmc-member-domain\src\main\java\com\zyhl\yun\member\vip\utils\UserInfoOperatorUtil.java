package com.zyhl.yun.member.vip.utils;

import cn.hutool.core.collection.CollUtil;
// import cn.hutool.core.date.StopWatch;
import com.zyhl.yun.member.common.Constant;
import com.zyhl.yun.member.common.JsonUtil;
import com.zyhl.yun.member.common.OperatorEnum;
import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.constants.ErrorCode;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.UserServiceId;
import com.zyhl.yun.member.common.enums.UserStatusEnum;
import com.zyhl.yun.member.common.properties.DriverProperties;
import com.zyhl.yun.member.domain.equity.doamin.PhoneTypeDo;
import com.zyhl.yun.member.domain.equity.gateway.EquityGateway;
import com.zyhl.yun.member.domain.ose.gateway.UserDomainGateway;
import com.zyhl.yun.member.domain.ose.req.GetUserReq;
import com.zyhl.yun.member.domain.ose.req.UserDomainOpenAccountReq;
import com.zyhl.yun.member.domain.ose.resp.GetUserResp;
import com.zyhl.yun.member.domain.ose.resp.UserDomainResult;
import com.zyhl.yun.member.domain.ose.resp.UserOpenAccount;
import com.zyhl.yun.member.domain.user.domain.QueryUserCondition;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.facade.UserServiceFacade;
import com.zyhl.yun.member.vip.gateway.UserCacheGateway;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.zyhl.yun.member.common.domain.serviceid.DefaultServiceId.DEFAULT;
import static com.zyhl.yun.member.facade.UserServiceFacade.getNormalDbUserDo;

/**
 * @Description:
 * @Author: zouzefeng
 * @Date: 2024/12/4
 */
@Component
@Slf4j
public class UserInfoOperatorUtil {

    public static final String USER_DOMAIN_PHONE = "user:getUserV2:%s";
    public static final String USER_DOMAIN_ID = "user:getUserByUserDomainIdV2:%s";
    public static final String USER_PHONE_TYPE = "user:phoneType:%s";

    public static final String USER_INSERT_LOCK = "user:userMessageLock:%s";

    private static final String USER_NOT_EXIST = "05010003";

    private static final String USER_NORMAL = "0000";
    private static final String SCRIPT = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";

    @Autowired
    private UserDomainGateway userDomainGateway;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private EquityGateway equityGateway;


    @Resource(name = "fillUserDomainIdTaskExecutor")
    private ThreadPoolTaskExecutor fillUserDomainIdTaskExecutor;
    @Resource
    private DriverProperties driverProperties;

    @Resource
    private UserCacheGateway userCacheGateway;

    /**
     * 获取用户信息
     *
     * @param msisdn 手机号码
     * @return 用户信息
     */
    public GetUserResp getUserDomainUserInfo(String msisdn, String userDomainId) {
        if (StringUtils.isNotEmpty(msisdn)) {
            // 查询缓存信息
            Object userMsg = redisTemplate.opsForValue().get(String.format(USER_DOMAIN_PHONE, msisdn));
            if (null != userMsg) {
                return (GetUserResp) userMsg;
            }
        }
        if (StringUtils.isNotEmpty(userDomainId)) {
            // 查询用户信息
            // 查询缓存信息
            Object userMsg = redisTemplate.opsForValue().get(String.format(USER_DOMAIN_ID, userDomainId));
            if (null != userMsg) {
                return (GetUserResp) userMsg;
            }
        }
        GetUserReq getUserReq = new GetUserReq();
        if (StringUtils.isEmpty(userDomainId) && StringUtils.isNotEmpty(msisdn)) {
            getUserReq.setPhoneNumber(msisdn);
        } else if (StringUtils.isNotEmpty(userDomainId) && StringUtils.isEmpty(msisdn)) {
            getUserReq.setUserDomainId(userDomainId);
        } else if (StringUtils.isNotEmpty(userDomainId) && StringUtils.isNotEmpty(msisdn)) {
            getUserReq.setUserDomainId(userDomainId);
        }
        UserDomainResult<GetUserResp> userDomainResult = userDomainGateway.getUser(getUserReq);
        if (userDomainResult == null || userDomainResult.getData() == null) {
            throw new ServiceException("user is not found");
        }
        GetUserResp user = null;
        if (userDomainResult.getCode().equals(USER_NORMAL) && Boolean.TRUE.equals(userDomainResult.getSuccess())) {
            user = userDomainResult.getData();
        } else if (userDomainResult.getCode().equals(USER_NOT_EXIST) && msisdn != null) {
            // 开户，再查询
            UserDomainOpenAccountReq openAccountReq = new UserDomainOpenAccountReq();
            openAccountReq.setPhoneNumber(msisdn);
            UserDomainResult<UserOpenAccount> openedAccount = userDomainGateway.openAccount(openAccountReq);
            if (openedAccount != null && openedAccount.getData() != null && StringUtils.isNotEmpty(openedAccount.getData().getUserDomainId())) {
                user = getUserDomainUserInfo(msisdn, null);
            } else {
                log.error("user open account fail");
                throw new ServiceException("user open account fail");
            }
        } else {
            throw new ServiceException("user is not found");
        }
        redisTemplate.opsForValue().set(String.format(USER_DOMAIN_PHONE, user.getPhoneNumber()), user, 24, TimeUnit.HOURS);
        redisTemplate.opsForValue().set(String.format(USER_DOMAIN_ID, user.getUserDomainId()), user, 24, TimeUnit.HOURS);

        return user;
    }


    public GetUserResp getUserByPhone(String msisdn) {
        if (StringUtils.isNotEmpty(msisdn)) {
            // 查询缓存信息
            Object userMsg = redisTemplate.opsForValue().get(String.format(USER_DOMAIN_PHONE, msisdn));
            if (null != userMsg) {
                return (GetUserResp) userMsg;
            }
        }
        GetUserReq getUserReq = new GetUserReq();
        getUserReq.setPhoneNumber(msisdn);
        try {
            UserDomainResult<GetUserResp> userDomainResult = userDomainGateway.getUser(getUserReq);
            if (userDomainResult != null && userDomainResult.getData() != null && StringUtils.isNotEmpty(userDomainResult.getData().getNationCode())) {
                redisTemplate.opsForValue().set(String.format(USER_DOMAIN_PHONE, msisdn), userDomainResult.getData(), 1, TimeUnit.HOURS);
                return userDomainResult.getData();
            }
        } catch (Exception e) {
            log.error("[DOMAIN] getUserByPhone error", e);
        }
        return null;
    }

    /**
     * 根据user
     *
     * @param userDomainId userDomainId
     * @return GetUserResp
     */
    public GetUserResp getUserByUserDomainId(String userDomainId) {
        if (StringUtils.isNotEmpty(userDomainId)) {
            // 查询缓存信息
            Object userMsg = redisTemplate.opsForValue().get(String.format(USER_DOMAIN_ID, userDomainId));
            if (null != userMsg) {
                return (GetUserResp) userMsg;
            }
        }
        GetUserReq getUserReq = new GetUserReq();
        getUserReq.setUserDomainId(userDomainId);
        try {
            UserDomainResult<GetUserResp> userDomainResult = userDomainGateway.getUser(getUserReq);
            if (userDomainResult != null && userDomainResult.getData() != null && StringUtils.isNotEmpty(userDomainResult.getData().getNationCode())) {
                redisTemplate.opsForValue().set(String.format(USER_DOMAIN_ID, userDomainId), userDomainResult.getData(), 1, TimeUnit.HOURS);
                return userDomainResult.getData();
            }
        } catch (Exception e) {
            log.error("[DOMAIN] getUserByUserDomainId error", e);
        }
        return null;
    }

    /**
     * 获取用户全球通信息
     *
     * @param msisdn 手机号
     * @return 全球通信息
     */
    public PhoneTypeDo getPhoneType(String msisdn) {
        // 根据手机号查询缓存
        if (StringUtils.isNotEmpty(msisdn)) {
            // 查询缓存信息
            Object userPhoneType = redisTemplate.opsForValue().get(String.format(USER_PHONE_TYPE, msisdn));
            if (null != userPhoneType) {
                log.info("msisdn is {}, getPhoneType from redis is {}", msisdn, userPhoneType);
                return JsonUtil.fromJson((String) userPhoneType, PhoneTypeDo.class);
            }
        }
        PhoneTypeDo phoneTypeDo = equityGateway.queryPhoneType(msisdn);
        if (null != phoneTypeDo) {
            log.info("msisdn is {}, getPhoneType from equityGateway is {}", msisdn, phoneTypeDo);
            redisTemplate.opsForValue().set(String.format(USER_PHONE_TYPE, msisdn), JsonUtil.toJson(phoneTypeDo), 1, TimeUnit.DAYS);
        }
        return phoneTypeDo;
    }


    /**
     * 获取锁
     *
     * @param lockKey    锁的key
     * @param identifier 唯一标识符
     * @param timeout    超时时间
     * @return boolean
     */
    public boolean acquireLock(String lockKey, String identifier, Integer timeout) {
        // 使用SETNX命令尝试获取锁，并设置唯一标识符
        Boolean result = redisTemplate.opsForValue().setIfAbsent(lockKey, identifier, timeout, TimeUnit.SECONDS);
        return result != null && result;
    }

    /**
     * 释放锁
     *
     * @param lockKey    锁的key
     * @param identifier 唯一标识符
     */
    public void releaseLock(String lockKey, String identifier) {
        // 使用Lua脚本确保原子性地检查和删除锁
        RedisScript<Long> redisScript = new DefaultRedisScript<>(SCRIPT, Long.class);
        redisTemplate.execute(redisScript, Collections.singletonList(lockKey), identifier);
    }


    /**
     * 填充外部用户域信息
     *
     * @param queryUserCondition 查询条件
     * @param userDosFromDB      用户信息
     * @return List<UserDo>
     */
    private List<UserDo> fillNormalUserDomainUserInfo(QueryUserCondition queryUserCondition, List<UserDo> userDosFromDB) {
        List<UserDo> userDos = new ArrayList<>();
        if (CollUtil.isNotEmpty(userDosFromDB)) {
            for (UserDo userDo : userDosFromDB) {
                if (UserStatusEnum.NORMAL.equals(userDo.getStatus())) {
                    fillUserDomainInfo(queryUserCondition, userDo);
                }
                userDos.add(userDo);
            }
        }
        return userDos;
    }


    /**
     * 用户外部字段信息补充
     *
     * @param queryUserCondition 查询条件
     * @param userDo             用户信息
     */
    private void fillUserDomainInfo(QueryUserCondition queryUserCondition, UserDo userDo) {
        if (Boolean.TRUE.equals(queryUserCondition.getQueryUserDomainFlag())) {

            GetUserResp userDomainInfo = this.getUserByPhone(userDo.getMsisdn());
            if (userDomainInfo == null) {
                return;
            }

            if (StringUtils.isNotBlank(userDomainInfo.getUserDomainId())
                    && !userDomainInfo.getUserDomainId().equals(userDo.getUserDomainId())) {
                repairUserDomainId(userDo);
                // 本地跟用户域的用户域id不一致，说明本地数据有问题，修复用户域id后需要重新查一次
                userDomainInfo = this.getUserByPhone(userDo.getMsisdn());
            }

            if (userDomainInfo == null) {
                return;
            }

            userDo.setUserDomainId(userDomainInfo.getUserDomainId());
            userDo.setProvCode(userDomainInfo.getUserExtendInfo().getProvince());
            userDo.setOperator(userDomainInfo.getUserExtendInfo().getImsiCode() == null ? null :
                    OperatorEnum.fromType(Integer.parseInt(userDomainInfo.getUserExtendInfo().getImsiCode())));
            userDo.setNationCode(userDomainInfo.getNationCode());

        }
    }

    /**
     * 异步填充数据库用户表用户域ID
     *
     * @param id           用户表的id主键
     * @param userId       用户表的userId
     * @param userDomainId 用户域ID
     */
    public void fillDbUserDomainId(Long id, String userId, String userDomainId) {
        UserDo updateUserDo = new UserDo();
        updateUserDo.setUserDomainId(userDomainId);
        updateUserDo.setUserId(userId);
        updateUserDo.setId(id);
        // 异步更新用户域ID
        CompletableFuture.runAsync(() -> UserServiceFacade.updateUser(updateUserDo), fillUserDomainIdTaskExecutor);
    }

    /**
     * 填充用户全球通信息
     *
     * @param queryGotoneFlag 判断
     * @param userDos         用户信息
     */
    public void fillUserGotoneInfo(boolean queryGotoneFlag, List<UserDo> userDos) {
        if (Boolean.TRUE.equals(queryGotoneFlag)) {
            for (UserDo userDo : userDos) {
                if (UserStatusEnum.NORMAL.equals(userDo.getStatus())) {
                    PhoneTypeDo phoneTypeDo = this.getPhoneType(userDo.getMsisdn());
                    if (phoneTypeDo != null) {
                        userDo.setGotoneUserLevel(phoneTypeDo.getUserLevel());
                        userDo.setGotoneCustType(phoneTypeDo.getCustType());
                        userDo.setFilledGotone(true);
                    }
                }
            }
        }

    }

    /**
     * 获取用户信息
     *
     * @param queryUserCondition
     * @param getter             获取用户信息的回调函数
     * @return List<UserDo>
     * @throws Exception 获取用户信息的回调函数
     */
    private List<UserDo> getUserDosFromDB(QueryUserCondition queryUserCondition, Callable<Serializable> getter) throws Exception {
        // stopWatch.start("getUserDosFromDB-getter");
        Serializable serializable = getter.call();
        // stopWatch.stop();
        // stopWatch.start("getUserDosFromDB-other");
        List<UserDo> userDos = new ArrayList<>();
        if (serializable instanceof List) {
            List<UserDo> userDoList = (List<UserDo>) serializable;
            userDos = userDoList.isEmpty() ? new ArrayList<>() : userDoList;
            if (CollUtil.isEmpty(userDos) && queryUserCondition.getStatusList().contains(UserStatusEnum.NORMAL)) {
                // 如果手机号不为空
                if ((StringUtils.isNotEmpty(queryUserCondition.getAccount()) || StringUtils.isNotEmpty(queryUserCondition.getUserId()))
                        && StringUtils.isNotEmpty(queryUserCondition.getUserDomainId())) {
                    UserDo userDo = new DomainServiceContext(DEFAULT).readFirst(QueryUserCondition.builder()
                            .account(queryUserCondition.getAccount()).status(UserStatusEnum.NORMAL.getStatus())
                            .userId(queryUserCondition.getUserId()).build(), UserDo.class);
                    if (null != userDo) {
                        if (StringUtils.isNotEmpty(userDo.getUserDomainId())
                                && StringUtils.isNotEmpty(queryUserCondition.getUserDomainId())
                                && !userDo.getUserDomainId().equals(queryUserCondition.getUserDomainId())) {
                            log.warn("[DOMAIN] getUserDosFromDB warning. db's userDomainId is not equal with condition's userDomain." +
                                    " userDo: {}, userDomainId from condition: {}", userDo, queryUserCondition.getUserDomainId());
                            repairUserDomainId(userDo);
                        }
                        userDos.add(userDo);
                    }
                    if (driverProperties.isDebugSwitch()) {
                        log.info("debugSwitch getUserDosFromDB twice query,queryUserCondition:{}", queryUserCondition);
                    }
                }
            }
        }
        // stopWatch.stop();
        return userDos;
    }

    /**
     * 用户域信息同步
     *
     * @param msisdn        手机号码
     * @param userDomainId  用户域id
     * @param isOpenAccount 是否开通账号
     * @param serviceId     服务id
     * @return UserDo
     */
    public UserDo userDomainSync(String msisdn, String userDomainId, boolean isOpenAccount, String serviceId) {
        UserDo userDo = new UserDo();
        userDo.setMsisdn(msisdn);
        userDo.setUserDomainId(userDomainId);
        userDo.setStatus(UserStatusEnum.NORMAL);
        GetUserResp userByUserDomain;
        if (StringUtils.isNotEmpty(userDo.getUserDomainId())) {
            clearUserDomainCache(null, userDomainId);
            userByUserDomain = this.getUserByUserDomainId(userDo.getUserDomainId());
            if (userByUserDomain != null) {
                if (StringUtils.isNotEmpty(userDo.getMsisdn())
                        && !userDo.getMsisdn().equals(userByUserDomain.getPhoneNumber())) {
                    log.error("[DOMAIN] userDomainSync error. userDomainId and phone not match. userDomainId: {}, " +
                                    "current phone with this userDomainId: {}, phone of current user: {}",
                            userByUserDomain.getUserDomainId(), userByUserDomain.getPhoneNumber(), userDo.getMsisdn());
                    throw new ServiceException(ErrorCode.PHONE_AND_USER_DOMAIN_ID_NOT_MATCH,
                            ErrorCode.PHONE_NOT_EQUAL_USER_DOMAIN_MESSAGE);
                }
                if (StringUtils.isEmpty(userDo.getMsisdn())) {
                    userDo.setMsisdn(userByUserDomain.getPhoneNumber());
                }
            }

        } else if (StringUtils.isNotEmpty(userDo.getMsisdn())) {
            clearUserDomainCache(msisdn, null);
            userByUserDomain = this.getUserByPhone(userDo.getMsisdn());
            if (userByUserDomain != null) {
                userDo.setUserDomainId(userByUserDomain.getUserDomainId());
            } else if (Boolean.TRUE.equals(isOpenAccount)) {
                UserDomainOpenAccountReq openAccountReq = new UserDomainOpenAccountReq();
                openAccountReq.setPhoneNumber(userDo.getMsisdn());
                openAccountReq.setIfOpenAccount(Constant.ONE_STR);
                UserDomainResult<UserOpenAccount> openedAccount = userDomainGateway.openAccount(openAccountReq);
                if (Boolean.FALSE.equals(openedAccount.getSuccess()) && null == openedAccount.getData()) {
                    throw new ServiceException(openedAccount.getCode(), openedAccount.getMessage());
                }
                userByUserDomain = this.getUserByPhone(userDo.getMsisdn());
                if (userByUserDomain != null) {
                    userDo.setUserDomainId(userByUserDomain.getUserDomainId());
                }
            }
        } else {
            return null;
        }
        if (userByUserDomain != null) {
            upsertUser(userDo, serviceId);
            userDo.setNationCode(userByUserDomain.getNationCode());
            userDo.setProvCode(userByUserDomain.getUserExtendInfo().getProvince());
            userDo.setOperator(userByUserDomain.getUserExtendInfo().getImsiCode() == null ? null :
                    OperatorEnum.fromType(Integer.parseInt(userByUserDomain.getUserExtendInfo().getImsiCode())));
        }
        return userDo;
    }

    /**
     * 新增用户
     *
     * @param userDo    用户信息
     * @param serviceId
     */
    private void upsertUser(UserDo userDo, String serviceId) {
        log.info("[DOMAIN] upsertUser start. userDo: {}", userDo);
        UserDo userDoFromDb = getNormalDbUserDo(userDo.getMsisdn());
        if (null == userDoFromDb) {
            insertNewOpenUserToDb(userDo, serviceId);
        } else {
            if (StringUtils.isEmpty(userDoFromDb.getUserDomainId())) {
                fillDbUserDomainId(userDoFromDb.getId(), userDoFromDb.getUserId(), userDo.getUserDomainId());
            }
            userDo.setUserId(userDoFromDb.getUserId());
        }

        log.info("[DOMAIN] upsertUser end. userId: {}", userDo.getUserId());
    }

    /**
     * 新增用户
     *
     * @param userDo    用户信息
     * @param serviceId 服务ID
     */
    private void insertNewOpenUserToDb(UserDo userDo, String serviceId) {
        log.info("[DOMAIN] insertNewOpenUserToDb start. userDo: {}", userDo);
        UUID lockIdentifier = UUID.randomUUID();
        try {
            boolean tried = acquireLock(String.format(USER_INSERT_LOCK, userDo.getMsisdn()),
                    lockIdentifier.toString(), 30);
            if (Boolean.TRUE.equals(tried)) {
                userCacheGateway.clearCache(userDo);
                UserDo normalUserDo = getNormalDbUserDo(userDo.getMsisdn());
                if (null == normalUserDo) {
                    if (UserServiceId.QUERY_AND_CREATE_USER_INFO.equals(serviceId)) {
                        UserServiceFacade.insertUser(userDo);
                        userDo.setStatus(UserStatusEnum.NORMAL);
                        userCacheGateway.clearCache(userDo);
                        log.info("[DOMAIN] insertNewOpenUserToDb success. userDo: {}, lockIdentifier: {}",
                                normalUserDo, lockIdentifier);
                    }
                } else {
                    userDo.setUserId(normalUserDo.getUserId());
                }
            } else {
                log.error("[DOMAIN] insertNewOpenUserToDb fail. acquire lock fail, userDo: {},  lockIdentifier: {}",
                        userDo, lockIdentifier);
                throw new ServiceException("user created is lock");
            }
        } catch (Exception e) {
            log.error("[DOMAIN] insertNewOpenUserToDb error.", e);
            throw new ServiceException(e);
        } finally {
            releaseLock(String.format(USER_INSERT_LOCK, userDo.getMsisdn()), lockIdentifier.toString());
            log.info("[DOMAIN] insertNewOpenUserToDb release lock. userDo: {}, lockIdentifier: {}",
                    userDo, lockIdentifier);
        }
        log.info("[DOMAIN] insertNewOpenUserToDb end. userId: {}", userDo.getUserId());
    }

    /**
     * 获取用户信息
     *
     * @param serviceContext     上下文
     * @param getter             获取用户信息的方法
     * @param queryUserCondition 查询条件
     * @param isOpenAccount      是否开启用户域
     * @return
     * @throws Exception 获取用户信息异常
     */
    public List<UserDo> getUserDos(DomainServiceContext serviceContext, Callable<Serializable> getter,
                                   QueryUserCondition queryUserCondition, Boolean isOpenAccount) throws Exception {
        // StopWatch stopWatch = new StopWatch();
        List<UserDo> userDosFromDB = getUserDosFromDB(queryUserCondition, getter);
        // stopWatch.start("fillNormalUserDomainUserInfo");
        List<UserDo> userResultDos = fillNormalUserDomainUserInfo(queryUserCondition, userDosFromDB);
        // stopWatch.stop();
        // stopWatch.start("userDomainSync");
        if (!isContainNormalUser(userResultDos) && CollUtil.isNotEmpty(queryUserCondition.getStatusList())
                && queryUserCondition.getStatusList().contains(UserStatusEnum.NORMAL)) {
            // 如果不存在生效的用户，需要进行同步
            if (Boolean.TRUE.equals(queryUserCondition.getUserSyncByUserDomainFlag())) {
                UserDo newOpenUserDo = userDomainSync(queryUserCondition.getAccount(), queryUserCondition.getUserDomainId(), isOpenAccount, serviceContext.getServiceId());
                if (null != newOpenUserDo && StringUtils.isNotEmpty(newOpenUserDo.getUserId())) {
                    userResultDos.add(newOpenUserDo);
                }
            }
        }
        // stopWatch.stop();
        // stopWatch.start("fillUserGotoneInfo");
        fillUserGotoneInfo(queryUserCondition.getQueryGotoneParams(), userResultDos);
        // stopWatch.stop();
        // if (driverProperties.isDebugSwitch()) {
        //     log.info("debugSwitch serviceId is {},queryUserCondition is {}, getUserDos cost time:{}",
        //             serviceContext.getServiceId(), queryUserCondition, stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        // }
        return userResultDos;
    }

    public boolean isContainNormalUser(List<UserDo> userDos) {

        if (CollectionUtils.isEmpty(userDos)) {
            return false;
        }

        for (UserDo userDo : userDos) {
            if (UserStatusEnum.NORMAL.equals(userDo.getStatus())) {
                return true;
            }
        }
        return false;
    }


    /**
     * 清除用户域缓存
     *
     * @param msisdn
     * @param userDomainId
     */
    public void clearUserDomainCache(String msisdn, String userDomainId) {
        if (StringUtils.isNotEmpty(msisdn)) {
            redisTemplate.delete(String.format(USER_DOMAIN_PHONE, msisdn));
        }
        if (StringUtils.isNotEmpty(userDomainId)) {
            redisTemplate.delete(String.format(USER_DOMAIN_ID, userDomainId));
        }
    }


    /**
     * 修复用户域id
     *
     * @param userDo
     */
    private void repairUserDomainId(UserDo userDo) {

        log.info("[DOMAIN] repairUserDomainId start. userId: {}, phone: {}, userDomainId before repairing :{}",
                userDo.getUserId(), userDo.getMsisdn(), userDo.getUserDomainId());

        clearUserDomainCache(userDo.getMsisdn(), userDo.getUserDomainId());

        GetUserResp userDomainInfoByPhone = this.getUserByPhone(userDo.getMsisdn());
        if (userDomainInfoByPhone == null || StringUtils.isBlank(userDomainInfoByPhone.getUserDomainId())) {
            log.warn("[DOMAIN] repairUserDomainId end. this user is not found in userDomain . userId: {}, phone: {}",
                    userDo.getUserId(), userDo.getMsisdn());
            return;
        }

        if (StringUtils.isBlank(userDo.getUserDomainId())) {
            userDo.setUserDomainId(userDomainInfoByPhone.getUserDomainId());
            fillDbUserDomainId(userDo.getId(), userDo.getUserId(), userDo.getUserDomainId());
            log.info("[DOMAIN] repairUserDomainId success. userId: {}, phone: {}, userDomainId after repairing: {}",
                    userDo.getUserId(), userDo.getMsisdn(), userDo.getUserDomainId());
            return;
        }

        if (userDo.getUserDomainId().equals(userDomainInfoByPhone.getUserDomainId())) {
            // 与用户域信息一致，无事发生
            return;
        }

        GetUserResp userDomainInfoByUserDomainId = this.getUserByUserDomainId(userDo.getUserDomainId());
        if (userDomainInfoByUserDomainId == null || StringUtils.isBlank(userDomainInfoByUserDomainId.getUserDomainId())) {
            // 错误的用户域id在用户域已不生效，说明该用户的在数据库的用户域id是错误的
            userDo.setUserDomainId(userDomainInfoByPhone.getUserDomainId());
            fillDbUserDomainId(userDo.getId(), userDo.getUserId(), userDo.getUserDomainId());
            log.info("[DOMAIN] repairUserDomainId success. userId: {}, phone: {}, userDomainId after repairing: {}",
                    userDo.getUserId(), userDo.getMsisdn(), userDo.getUserDomainId());
        } else {
            // 错误的用户域id在用户域已生效，说明该用户的在数据库的用户域id可能是换绑了其他号码
            log.error("[DOMAIN] repairUserDomainId error. userDomainId maybe bound with another phone. userDomainId: {}, " +
                            "current phone with this userDomainId: {}, phone of current user: {}",
                    userDomainInfoByUserDomainId.getUserDomainId(), userDomainInfoByUserDomainId.getPhoneNumber(), userDo.getMsisdn());
            throw new ServiceException(ErrorCode.PHONE_AND_USER_DOMAIN_ID_NOT_MATCH,
                    ErrorCode.PHONE_NOT_EQUAL_USER_DOMAIN_MESSAGE);
        }

    }

}
