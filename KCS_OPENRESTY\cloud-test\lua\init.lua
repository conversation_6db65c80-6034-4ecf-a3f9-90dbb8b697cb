-- 指定白名单列表
local whiteList = {"13590104821_0","14779767754_0","14779767755_0","14779767756_0","14779767757_0","13360302181_0",
"13424062603_0","13632753245_0","19898398810_0","800001477309_1","248900851819_1","800001482111_1","19878342211_0",
"800001477875_1","19878342212_0","312349646378_1","800006498816_1","224582019260_1","19878342213_0","363215608142_1",
"20240816172357552999_2","11180309173530006308_2","344963956374241292_2","19872814065_0","13411316919_0","19898398810_0",
"19878342214_0","13632753245_0","13825056269_0","13539860170_0","13310101007_0","13310101008_0","13310101009_0",
"13310101010_0","13310101011_0","13310101012_0","13310101013_0","13310101014_0","13310101015_0",
"13310101016_0","13310101017_0","13310101018_0","13310101019_0","13310101020_0","13310101021_0",
"13310101022_0","13310101023_0","13310101024_0","13310101025_0","13310101026_0","13310101027_0",
"13310101028_0","13310101029_0","13310101030_0","13310101031_0","13310101032_0"}
-- 指定新旧机器配置信息
local newVsboArr = {"[fd11:1111:1111:15::69fd]:18188"}
local oldVsboArr = {"***********:8060"}
local oldAdArr = {"***********:8060"}
local oldPgwArr = {"***********:9510"}
local oldMockArr ={"***********:12636"}
local conf_dict = ngx.shared.conf_dict
-- 新vsbo机器
for i = 1,#newVsboArr do
    local ip, port = newVsboArr[i]:match("^(.+):(.+)$")
    local index =i-1
    local ipKey = "newVsboIp" .. index
    local portKey = "newVsboPort" .. index
    conf_dict:set(ipKey,ip)
    conf_dict:set(portKey,tonumber(port))
end
-- 旧vsbo机器
for i = 1,#oldVsboArr do
    local ip, port = oldVsboArr[i]:match("^(.+):(.+)$")
    local index =i-1
    local ipKey = "oldVsboIp" .. index
    local portKey = "oldVsboPort" .. index
    conf_dict:set(ipKey,ip)
    conf_dict:set(portKey,tonumber(port))
end

-- 旧Ad机器
for i = 1,#oldAdArr do
    local ip, port = oldAdArr[i]:match("^(.+):(.+)$")
    local index = i - 1
    local ipKey = "oldAdIp" .. index
    local portKey = "oldAdPort" .. index
    conf_dict:set(ipKey,ip)
    conf_dict:set(portKey,tonumber(port))
end
-- 旧Pgw机器
for i = 1,#oldPgwArr do
    local ip, port = oldPgwArr[i]:match("^(.+):(.+)$")
    local index = i - 1
    local ipKey = "oldPgwIp" .. index
    local portKey = "oldPgwPort" .. index
    conf_dict:set(ipKey,ip)
    conf_dict:set(portKey,tonumber(port))
end


-- 旧mock机器
for i = 1,#oldMockArr do
    local ip, port = oldMockArr[i]:match("^(.+):(.+)$")
    local index = i - 1
    local ipKey = "oldMockIp" .. index
    local portKey = "oldMockPort" .. index
    conf_dict:set(ipKey,ip)
    conf_dict:set(portKey,tonumber(port))
end
-- 设置白名单
local white_dict = ngx.shared.white_dict
for i = 1,#whiteList do
    white_dict:set(whiteList[i],"exit")
end
conf_dict:set("newIndex",0)
conf_dict:set("oldIndex",0)
conf_dict:set("newTotal",#newVsboArr)
conf_dict:set("oldTotal",#oldVsboArr)


-- 设置json映射路径
local json_path_dict = ngx.shared.json_path_dict
-- userId获取规则为：lua脚本远程调用接口获取userId
json_path_dict:set("/payment/cancelPayment","2_Pgw_1_paymentId")
json_path_dict:set("notifyMovieEquityRightsResult","5_Ad_1_channelNo")
json_path_dict:set("raiNotifyEquityResult","5_Ad_1_channelNo")
json_path_dict:set("notifyRightsSalesReturnResult","7_Ad_old")
-- ******** 新增
json_path_dict:set("acceptClearedDetail","4_Vsbo")
-- 设置xml映射路径
local xml_path_dict = ngx.shared.xml_path_dict
xml_path_dict:set("/cgw/fsapay/notifyTradeStatus","2_Pgw_1_msgReq.outOrderId")
xml_path_dict:set("/cgw/fsapay/refundStatusNotify","2_Pgw_1_msgReq.outOrderId")
xml_path_dict:set("/payment/directPayment","2_Pgw_1_msgReq.outOrderId")

xml_path_dict:set("activeGotoneRights","0_Vsbo_0_activeGotoneRightsReq.account")
xml_path_dict:set("activeGotoneRights_1","0_Vsbo_2_activeGotoneRightsReq.userDomainId")
xml_path_dict:set("activeRightsPackage","0_Vsbo_1_activeRightsPackageReq.userId")
xml_path_dict:set("bindFamilyNumber","4_Vsbo")
xml_path_dict:set("preCheckFamilyNumber","4_Vsbo")
xml_path_dict:set("cancelAccount","0_Vsbo_0_cancelAccountReq.account")
xml_path_dict:set("chkValidMonthSubscribe","0_Vsbo_0_chkValidMonthSubscribeReq.account")
xml_path_dict:set("createOrder","0_Vsbo_0_createOrderReq.userID")
xml_path_dict:set("createOrder_1","0_Vsbo_2_createOrderReq.userDomainId")
xml_path_dict:set("createSubscribeRelation","0_Vsbo_0_createSubscribeRelationReq.userID")
xml_path_dict:set("createSubscribeRelation_1","0_Vsbo_2_createSubscribeRelationReq.userDomainId")
xml_path_dict:set("getSubscriptionByOrderId","0_Vsbo_0_getSubscriptionByOrderIdReq.account")
xml_path_dict:set("hySubscribe","0_Vsbo_0_hySubscribeReq.account")
xml_path_dict:set("manageSubscribeRelation","0_Vsbo_0_manageSubscribeRelationReq.account")
xml_path_dict:set("manageSubscribeRelation_1","0_Vsbo_2_manageSubscribeRelationReq.userDomainId")

xml_path_dict:set("manageSubscribeRelationV2","0_Vsbo_0_manageSubscribeRelationReq.account")
xml_path_dict:set("manageSubscribeRelationV2_1","0_Vsbo_2_manageSubscribeRelationReq.userDomainId")
xml_path_dict:set("modifyFamilyNumber","4_Vsbo")
xml_path_dict:set("newInitiateGroupBuy","0_Vsbo_0_newInitiateGroupBuyReq.account")
xml_path_dict:set("payOrder","0_Vsbo_0_payOrderReq.userID")
xml_path_dict:set("payOrder_1","0_Vsbo_2_payOrderReq.userDomainId")
xml_path_dict:set("preOrderCheck","0_Vsbo_0_createOrderReq.userID")
xml_path_dict:set("prepareRefund","0_Vsbo_0_prepareRefundReq.account")
xml_path_dict:set("queryActivitySubScription","0_Vsbo_0_queryActivitySubScriptionReq.account")
xml_path_dict:set("queryActivitySubScription_1","0_Vsbo_2_queryActivitySubScriptionReq.userDomainId")
xml_path_dict:set("queryContractListSubScription","0_Vsbo_0_queryContractListSubScriptionReq.account")
xml_path_dict:set("queryContractListSubScription_1","0_Vsbo_2_queryContractListSubScriptionReq.userDomainId")
xml_path_dict:set("queryContractListSubScriptionForDY","0_Vsbo_0_queryContractListSubScriptionReq.account")
xml_path_dict:set("queryContractListSubScriptionForDY_1","0_Vsbo_2_queryContractListSubScriptionReq.userDomainId")
xml_path_dict:set("queryContractListSubScriptionV2","0_Vsbo_0_queryContractListSubScriptionReq.account")
xml_path_dict:set("queryContractListSubScriptionV2_1","0_Vsbo_2_queryContractListSubScriptionReq.userDomainId")
xml_path_dict:set("queryContractListSubScriptionV3","0_Vsbo_0_queryContractListSubScriptionReq.account")
xml_path_dict:set("queryContractListSubScriptionV3_1","0_Vsbo_2_queryContractListSubScriptionReq.userDomainId")
xml_path_dict:set("queryContractListSubScriptionV4","0_Vsbo_0_queryContractListSubScriptionReq.account")
xml_path_dict:set("queryContractListSubScriptionV4_1","0_Vsbo_2_queryContractListSubScriptionReq.userDomainId")
xml_path_dict:set("queryContractListSubScriptionV5","0_Vsbo_0_queryContractListSubScriptionReq.account")
xml_path_dict:set("queryContractListSubScriptionV5_1","0_Vsbo_2_queryContractListSubScriptionReq.userDomainId")
xml_path_dict:set("queryFamilyNumber","4_Vsbo")
xml_path_dict:set("queryGotone","0_Vsbo_0_queryGotoneReq.account.accountName")
xml_path_dict:set("queryGotone_1","0_Vsbo_2_queryGotoneReq.account.userDomainId")
xml_path_dict:set("queryIndependentSpaceSub","0_Vsbo_0_queryIndependentSpaceSubReq.account")
xml_path_dict:set("queryOrCreateUserID","0_Vsbo_0_queryOrCreateUserIDReq.account.accountName")
xml_path_dict:set("queryOrCreateUserID_1","0_Vsbo_2_queryOrCreateUserIDReq.account.userDomainId")
xml_path_dict:set("queryOrderGroup","0_Vsbo_0_queryGroupBuyingFailReq.account")
xml_path_dict:set("queryOrders","0_Vsbo_0_queryOrdersReq.orderQueryCond.orderUserID")
xml_path_dict:set("queryOrdersForDY","0_Vsbo_0_queryOrdersReq.orderQueryCond.orderUserID")
xml_path_dict:set("queryOrdersV1","0_Vsbo_0_queryOrdersReq.orderQueryCond.orderUserID")
xml_path_dict:set("queryOrdersV3","0_Vsbo_0_queryOrdersReq.orderQueryCond.orderUserID")
xml_path_dict:set("queryPhoneType","0_Vsbo_0_queryPhoneTypeReq.account.accountName")
xml_path_dict:set("queryPhoneType_1","0_Vsbo_2_queryPhoneTypeReq.account.userDomainId")
xml_path_dict:set("queryReturnSubscription","0_Vsbo_0_queryReturnSubscriptionReq.account")
xml_path_dict:set("queryReturnSubscription_1","0_Vsbo_2_queryReturnSubscriptionReq.userDomainId")
xml_path_dict:set("queryRights","0_Vsbo_0_queryRightsReq.account.accountName")
xml_path_dict:set("queryRights_1","0_Vsbo_2_queryRightsReq.account.userDomainId")
xml_path_dict:set("queryRightsSubscribeRelation","0_Vsbo_0_querySpaceSubscribeRelationReq.account")
xml_path_dict:set("queryRightsSubscribeRelation_1","0_Vsbo_2_querySpaceSubscribeRelationReq.userDomainId")
xml_path_dict:set("querySubscribeFlow","0_Vsbo_0_querySubscribeFlowReq.account")
xml_path_dict:set("querySubscribeFlow_1","0_Vsbo_2_querySubscribeFlowReq.userDomainId")
xml_path_dict:set("querySubscribeFlowSpec","0_Vsbo_0_querySubscribeFlowSpecReq.account")
xml_path_dict:set("querySubscribeFlowSpec_1","0_Vsbo_2_querySubscribeFlowSpecReq.userDomainId")
xml_path_dict:set("querySubscribeFlowSpecV2","0_Vsbo_0_querySubscribeFlowSpecReq.account")
xml_path_dict:set("querySubscribeFlowSpecV2_1","0_Vsbo_2_subscribeFlowSpecReq.userDomainId")
xml_path_dict:set("querySubscribeFlowSpecV3","0_Vsbo_0_querySubscribeFlowSpecReq.account")
xml_path_dict:set("querySubscribeFlowSpecV3_1","0_Vsbo_2_querySubscribeFlowSpecReq.userDomainId")
xml_path_dict:set("querySubscribeOperation","0_Vsbo_0_querySubscribeOperationReq.account")
xml_path_dict:set("querySubscribeOperationWithinOneYear","0_Vsbo_0_querySubscribeOperationReq.account")
xml_path_dict:set("querySubscribeOperationYearAgo","0_Vsbo_0_querySubscribeOperationReq.account")
xml_path_dict:set("querySubscribeRelation","0_Vsbo_0_querySubscribeRelationReq.userId")
xml_path_dict:set("querySubscribeRelation_1","0_Vsbo_2_querySubscribeRelationReq.userDomainId")
xml_path_dict:set("querySubscribeRelationV1","0_Vsbo_0_querySubscribeRelationReq.userId")
xml_path_dict:set("querySubscribeRelationV1_1","0_Vsbo_2_querySubscribeRelationReq.userDomainId")
xml_path_dict:set("querySubscribeRelationV2","0_Vsbo_0_querySubscribeRelationReq.userId")
xml_path_dict:set("querySubscribeRelationV2_1","0_Vsbo_2_querySubscribeRelationReq.userDomainId")
xml_path_dict:set("rollBackRight","0_Vsbo_0_rollBackRightReq.account")
xml_path_dict:set("rollBackRight_1","0_Vsbo_2_rollBackRightReq.userDomainId")
xml_path_dict:set("sendTemplateSms","0_Vsbo_0_sendTemplateSmsReq.userId")
xml_path_dict:set("subscribeFlowSpec","0_Vsbo_0_subscribeFlowSpecReq.account")
xml_path_dict:set("subscribeFlowSpec_1","0_Vsbo_2_subscribeFlowSpecReq.userDomainId")
xml_path_dict:set("subscribeFlowSpecV2","0_Vsbo_0_subscribeFlowSpecReq.account")
xml_path_dict:set("subscribeFlowSpecV2_1","0_Vsbo_2_subscribeFlowSpecReq.userDomainId")
xml_path_dict:set("syncAppOrder","0_Vsbo_0_SyncAppOrderReq.MSISDN")
xml_path_dict:set("thirdRefund","0_Vsbo_0_TPCTRefundReqVo.account")
xml_path_dict:set("unSubscribe","1_Vsbo_1_unSubscribeReq.orderID")
xml_path_dict:set("queryConditionSubScription","0_Vsbo_0_queryConditionSubScriptionReq.account")

xml_path_dict:set("groupBuyNotify","7_Vsbo_old")

xml_path_dict:set("flowCardOrderResult","3_Ad_1_OPFlowCardOrderFdbkReq.returnId")
xml_path_dict:set("notifyPaymentResultOfThirdParty","0_Ad_0_msgReq.featureData")
xml_path_dict:set("notifyTpctPayResult","1_Ad_1_msgReq.outOrderId")
xml_path_dict:set("refundNotify","2_Ad_1_msgReq.outOrderID")
xml_path_dict:set("salesorderResult","3_Ad_1_OPFlowPkgSubsFdbkReq.orderId")
xml_path_dict:set("subscribeFlowSpecNotify","6_Ad_1_subscribeFlowSpecNotifyReq.sourceOrderNo")


xml_path_dict:set("queryOrdersMock","0_Mock_0_queryOrdersReq.orderQueryCond.orderUserID")
xml_path_dict:set("createOrderMock","0_Mock_0_createOrderReq.userID")
xml_path_dict:set("queryContractListSubScriptionV4Mock","0_Mock_0_queryContractListSubScriptionReq.account")

local file = require "aspire/file"
local globalSwitch=file.get_value_by_key("globalSwitch")
conf_dict:set("globalSwitch",globalSwitch)

local flow =  require "aspire/flowByService"
ngx.log(ngx.ERR,"init success,current globalSwitch is ",globalSwitch)
