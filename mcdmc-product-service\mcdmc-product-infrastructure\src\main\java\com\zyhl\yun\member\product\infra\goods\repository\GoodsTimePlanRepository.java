package com.zyhl.yun.member.product.infra.goods.repository;

import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.cache.EntityCacheManager;
import com.zyhl.yun.member.product.infra.config.CacheConfig;
import com.zyhl.yun.member.product.infra.goods.mapper.GoodsTimePlanMapper;
import com.zyhl.yun.member.product.infra.goods.po.GoodsTimePlanPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

import static com.zyhl.yun.member.common.ResultCodeEnum.DATA_UPDATE_ERROR;
import static com.zyhl.yun.member.product.infra.config.CacheConfig.CacheNames.GOODS_TIME_PLAN;

/**
 * <AUTHOR>
 * @date 2023/07/27 15:29
 */
@Slf4j
@Component
public class GoodsTimePlanRepository {


    @Resource
    private GoodsTimePlanMapper goodsTimePlanMapper;

    @Resource
    private CacheConfig cacheConfig;

    /**
     * 根据时间计划Id查询
     *
     * @param goodsId
     * @return
     */
    public GoodsTimePlanPo queryGoodsTimePlanByGoodsId(String goodsId) {

        log.debug("[INFRA] start to queryGoodsTimePlanByGoodsId. goodsId: {}", goodsId);

        return EntityCacheManager
                .getCache(GOODS_TIME_PLAN, cacheConfig.getCacheType())
                .getByKey(goodsId, GoodsTimePlanPo.class,
                        id -> goodsTimePlanMapper.selectById(id));
    }


    /**
     * 插入时间计划
     *
     * @param goodsTimePlanPo
     * @return
     */
    public int insertGoodsTimePlan(GoodsTimePlanPo goodsTimePlanPo) {
        if (log.isDebugEnabled()) {
            log.debug("[INFRA] start to insertGoodsTimePlan. goodsTimePlanPo: {}", goodsTimePlanPo);
        }
        int count = goodsTimePlanMapper.insert(goodsTimePlanPo);
        if (count <= 0) {
            log.error("[INFRA] insertGoodsTimePlan failed. goodsTimePlanPo: {}", goodsTimePlanPo);
            throw new ServiceException(DATA_UPDATE_ERROR);
        }
        if (log.isDebugEnabled()) {
            log.debug("[INFRA] insertGoodsTimePlan successfully. goodsTimePlanPo: {}", goodsTimePlanPo);
        }

        EntityCacheManager
                .getCache(GOODS_TIME_PLAN, cacheConfig.getCacheType())
                .deleteByKeyList(Arrays.asList(goodsTimePlanPo.getGoodsId()), null);

        return count;
    }


    /**
     * 修改时间计划
     *
     * @param goodsTimePlanPo
     * @return
     */
    public int updateGoodsTimePlan(GoodsTimePlanPo goodsTimePlanPo) {
        if (log.isDebugEnabled()) {
            log.debug("[INFRA] start to updateGoodsTimePlan. goodsTimePlanPo: {}", goodsTimePlanPo);
        }
        int count = goodsTimePlanMapper.updateById(goodsTimePlanPo);
        if (log.isDebugEnabled()) {
            log.debug("[INFRA] updateGoodsTimePlan successfully. goodsTimePlanPo: {}", goodsTimePlanPo);
        }

        EntityCacheManager
                .getCache(GOODS_TIME_PLAN, cacheConfig.getCacheType())
                .deleteByKeyList(Arrays.asList(goodsTimePlanPo.getGoodsId()), null);
        return count;
    }


    /**
     * 删除时间计划
     *
     * @param goodsId
     * @return
     */
    public int deleteGoodsTimePlanByGoodsId(String goodsId) {

        log.debug("[INFRA] start to deleteGoodsTimePlanByGoodsId. goodsId: {}", goodsId);

        int count = goodsTimePlanMapper.deleteById(goodsId);

        EntityCacheManager
                .getCache(GOODS_TIME_PLAN, cacheConfig.getCacheType())
                .deleteByKeyList(Arrays.asList(goodsId), null);

        return count;
    }

}
