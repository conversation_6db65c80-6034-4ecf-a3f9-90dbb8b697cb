package com.zyhl.yun.member.mcdmc.activation.domain.enums.local;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 工单接口日志状态枚举
 *
 * <AUTHOR>
 * @since 2024/06/21 14:15
 */
@Getter
@AllArgsConstructor
public enum FlowLogStateEnum {
    PARAM_HANDLE(0, "参数处理中"),
    PARAM_HANDLE_EXCEPTION(1, "参数处理异常"),
    SHIPMEN_REMOTE_EXCEPTION(2, "远程调用异常"),
    SHIPMENT_REMOTE_SC(3, "远程调用成功"),
    /**
     * 远程调用成功后处理结果发生异常
     */
    SHIPMENT_EXCEPTION(4, "发货异常"),
    /**
     * 接口响应业务码的含义为失败
     */
    SHIPMENT_FAILED(5, "发货失败"),
    SHIPMENT_SUCCESS(6, "发货成功"),

    RECEIVED_CALLBACK(11, "收到回调"),
    CALLBACK_SUCCESS(12, "回调成功"),
    CALLBACK_FAILED(13, "回调失败");

    private final int state;
    private final String msg;

    public boolean stateEquals(Integer anotherState) {
        return anotherState != null && state == anotherState;
    }

    public static FlowLogStateEnum getByState(Integer state) {
        for (FlowLogStateEnum value : FlowLogStateEnum.values()) {
            if (value.stateEquals(state)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 需要调用远程接口的状态
     */
    private static final List<FlowLogStateEnum> NEED_REMOTE_STATE_LIST = Arrays.asList(
            PARAM_HANDLE, PARAM_HANDLE_EXCEPTION, SHIPMEN_REMOTE_EXCEPTION, SHIPMENT_FAILED);

    /**
     * 不需要任何发货处理的状态
     */
    private static final List<FlowLogStateEnum> NOT_NEED_SHIPMENT_STATE_LIST = Arrays.asList(
            SHIPMENT_SUCCESS, RECEIVED_CALLBACK, CALLBACK_SUCCESS, CALLBACK_FAILED);

    /**
     * 判断是否需要调用远程接口
     */
    public static boolean isNeedRemote(FlowLogStateEnum stateEnum) {
        return NEED_REMOTE_STATE_LIST.contains(stateEnum);
    }

    /**
     * 判断状态是否需要处理发货流程
     */
    public static boolean isNeedShipment(FlowLogStateEnum stateEnum) {
        return !NOT_NEED_SHIPMENT_STATE_LIST.contains(stateEnum);
    }
}
