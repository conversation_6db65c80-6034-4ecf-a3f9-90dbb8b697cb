package com.zyhl.yun.member.mcdmc.activation.domain.enums.rai;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 权益平台标记
 *
 * <AUTHOR>
 * @since 2024/06/28 16:13
 */
@Getter
@AllArgsConstructor
public enum RaiPlatformFlagEnum {
    NORMAL_RAI("1", "权益中心"),

    MOVIE("2", "影视资源（明威）");

    private final String code;

    private final String des;

    public static final String RAI_PLATFORM_FLAG = "RAI_PLATFORM_FLAG";

    public boolean codeEquals(String anotherCode) {
        return this.code.equals(anotherCode);
    }
}
