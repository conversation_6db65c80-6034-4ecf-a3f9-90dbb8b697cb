package com.zyhl.yun.member.common.cache.wrapper;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zyhl.yun.member.common.JsonUtil;
import com.zyhl.yun.member.common.cache.ConditionCacheable;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;


/**
 * <AUTHOR>
 */
@Slf4j
@Data
public class ConditionCountWrapper implements CacheValueWrapper {


    private String conditionDigest;

    private Long count;

    public ConditionCountWrapper(ConditionCacheable condition, Number count) {
        this(ConditionWrapper.getConditionHashKey(condition), count);
    }

    public ConditionCountWrapper() {
    }

    public ConditionCountWrapper(String conditionDigest, Number count) {
        this.conditionDigest = conditionDigest;
        this.count = count == null ? 0L : count.longValue();
    }

    public static ConditionCountWrapper fromJson(String jsonString) {
        try {
            return JsonUtil.fromJson(jsonString, ConditionCountWrapper.class);
        } catch (Exception e) {
            log.warn("[CACHE] parse json error: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] parse json error.", e);
            }
            return null;
        }
    }

    @Override
    @JsonIgnore
    public Serializable getKey() {
        return conditionDigest;
    }

    @Override
    @JsonIgnore
    public Serializable getValue() {
        return count;
    }


    public Long getCount() {
        return count;
    }
}
