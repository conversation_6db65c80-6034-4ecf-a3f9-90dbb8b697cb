package com.zyhl.yun.member.mcdmc.activation.callback.notify;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.framework.service.MdcLogInterceptor;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.facade.GoodsInstanceServiceFacade;
import com.zyhl.yun.member.mcdmc.activation.callback.req.RaiComNotifyReq;
import com.zyhl.yun.member.mcdmc.activation.constants.BusinessConstant;
import com.zyhl.yun.member.mcdmc.activation.constants.SymbolConstant;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowDo;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowLogDo;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.FlowLogStateEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.FlowStaticConfig;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.CustomDidGenerator;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.enums.WorkOrderStateEnum;
import com.zyhl.yun.member.mcdmc.activation.flow.rai.sub.RaiSubNormalFlow;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiNormalProperties;
import com.zyhl.yun.member.mcdmc.activation.serviceid.LocalServiceId;
import com.zyhl.yun.member.mcdmc.activation.util.RaiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 权益中心回调
 * com.huawei.jaguar.vsbo.service.action.RaiEquityNotifyResultAction#raiNotifyEquityResult
 *
 * <AUTHOR>
 * @since 2024/07/09 14:02
 */
@Slf4j
@Component
public class RaiNormalSubCallback extends RaiComSubCallback {

    @Resource
    private RaiUtil raiUtil;

    @Resource
    private RaiNormalProperties raiNormalProperties;

    @Override
    protected String getDefaultEquityVersion() {
        return raiNormalProperties.getEquityVersion();
    }


    @Override
    public WorkServiceFlowLogDo initSendFlowLogWhileNotFound(RaiComNotifyReq req) {
        log.info("work flow log is not found,will generate new work order and callback log,req={}", req);
        String channelNo = req.getChannelNo();
        String[] split = channelNo.split(SymbolConstant.UNDERLINE);
        String userId = null;
        String orderNo = channelNo;
        if (split.length == 2) {
            userId = split[0];
            orderNo = channelNo + BusinessConstant.ORDER_SUFFIX;
        }
        QueryGoodsInstanceCondition instanceCondition = new QueryGoodsInstanceCondition();
        instanceCondition.setUserId(userId);
        instanceCondition.setOrderId(orderNo);
        GoodsInstanceDo instanceDo = GoodsInstanceServiceFacade.queryGoodsInstanceByCondition(instanceCondition);
        if (instanceDo == null) {
            log.error("can not found goodsInstance,orderNo={}", orderNo);
            return null;
        }
        // 初始化工单并写入库
        WorkOrderDo workOrderDo = initWorkOrder(instanceDo);
        return initFlowLogDo(workOrderDo);
    }

    public WorkOrderDo initWorkOrder(GoodsInstanceDo goodsInstanceDo) {
        WorkOrderDo workOrderDo = new WorkOrderDo();
        String workId = CustomDidGenerator.generateId();
        workOrderDo.setWorkId(workId);
        workOrderDo.setOrderId(goodsInstanceDo.getOrderId());
        workOrderDo.setUserId(goodsInstanceDo.getUserId());
        workOrderDo.setState(WorkOrderStateEnum.BE_CALLBACK);
        workOrderDo.setTransactionId(MdcLogInterceptor.getCurrentTraceId() + "_fix");
        workOrderDo.setFlowProgress(0);
        workOrderDo.setFailCount(0);
        String raiSubNormalFlowName = CharSequenceUtil.lowerFirst(RaiSubNormalFlow.class.getSimpleName());
        String serviceCode = FlowStaticConfig.getFirstServiceCodeByIFlow(raiSubNormalFlowName);
        workOrderDo.setServiceCode(serviceCode);
        // 构建ComSendInterfaceReq
        ComSendInterfaceReq comSendInterfaceReq = ComSendInterfaceReq.builder()
                .goodsInstanceId(goodsInstanceDo.getGoodsInstanceId())
                .goodsId(goodsInstanceDo.getGoodsId())
                .productId(goodsInstanceDo.getProductId())
                .orderID(goodsInstanceDo.getOrderId())
                .userId(goodsInstanceDo.getUserId())
                .subTime(DateUtil.format(goodsInstanceDo.getSubTime(), DatePattern.PURE_DATETIME_PATTERN))
                .effectiveStartTime(DateUtil.format(goodsInstanceDo.getEffectiveStartTime(), DatePattern.PURE_DATETIME_PATTERN))
                .effectiveEndTime(DateUtil.format(goodsInstanceDo.getEffectiveEndTime(), DatePattern.PURE_DATETIME_PATTERN))
                .payWay(String.valueOf(goodsInstanceDo.getPayWay()))
                .channelId(goodsInstanceDo.getChannelId())
                .subChannelId(goodsInstanceDo.getSubChannelId())
                .totalAmount((long) goodsInstanceDo.getDealPrice())
                .chargeType(goodsInstanceDo.getChargeType())
                .parentOrderId(goodsInstanceDo.getParentOrderId())
                .build();
        // 将ComSendInterfaceReq转为JSON字符串并设置到workAttrs
        workOrderDo.setWorkAttrs(JSONUtil.toJsonStr(comSendInterfaceReq));
        // 写入工单
        DomainServiceContext activationContext = new DomainServiceContext(LocalServiceId.INSERT_OPERATION);
        activationContext.putInstance(workOrderDo);
        activationContext.writeAndFlush();
        return workOrderDo;
    }

    private WorkServiceFlowLogDo initFlowLogDo(WorkOrderDo workOrderDo) {
        WorkServiceFlowDo serviceFlowDo = FlowStaticConfig.getServiceFlowDo(workOrderDo.getServiceCode());
        WorkServiceFlowLogDo workServiceFlowLogDo = new WorkServiceFlowLogDo();
        workServiceFlowLogDo.setLogId(CustomDidGenerator.generateId());
        workServiceFlowLogDo.setWorkId(workOrderDo.getWorkId());
        workServiceFlowLogDo.setUserId(workOrderDo.getUserId());
        workServiceFlowLogDo.setOrderId(workOrderDo.getOrderId());
        workServiceFlowLogDo.setServiceCode(workOrderDo.getServiceCode());
        workServiceFlowLogDo.setWorkServiceFlowId(serviceFlowDo.getWorkServiceFlowId());
        workServiceFlowLogDo.setState(FlowLogStateEnum.SHIPMENT_SUCCESS);
        workServiceFlowLogDo.setExtParams(workOrderDo.getWorkAttrs());
        return workServiceFlowLogDo;
    }

}
