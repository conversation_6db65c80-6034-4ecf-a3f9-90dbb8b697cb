package com.zyhl.yun.member.mcdmc.activation.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 返回实体
 *
 * <AUTHOR>
 * @since 2023/01/06 21:30
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class Result<T> extends BaseResult {

    private T data;


    public Result(String resultCode, String message, T data) {
        super(resultCode, message);
        this.data = data;
    }

    public Result(boolean success, String resultCode, String message, T data) {
        super(success, resultCode, message);
        this.data = data;
    }

    public static <T> Result<T> ok() {
        return success(null);
    }

    public static <T> Result<T> success(T data) {
        return success(ResultCode.SUCCESS.getMsg(), data);
    }

    public static <T> Result<T> success(String message, T data) {
        return success(ResultCode.SUCCESS.getCode(), message, data);
    }

    public static <T> Result<T> success(ResultCode resultCode, T data) {
        return success(resultCode.getCode(), resultCode.getMsg(), data);
    }

    public static <T> Result<T> success(String code, String message, T data) {
        return new Result<>(true, code, message, data);
    }

    public static <T> Result<T> error(ResultCode resultCode) {
        return Result.error(resultCode.getCode(), resultCode.getMsg(), null);
    }

    public static <T> Result<T> error(ResultCode resultCode, T data) {
        return Result.error(resultCode.getCode(), resultCode.getMsg(), data);
    }

    public static Result<Object> error(String errorCode, String message) {
        return Result.error(errorCode, message, null);
    }

    public static <T> Result<T> error(String errorCode, String message, T data) {
        return new Result<>(false, errorCode, message, data);
    }

}
