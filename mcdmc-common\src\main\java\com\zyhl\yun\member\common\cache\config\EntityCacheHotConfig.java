package com.zyhl.yun.member.common.cache.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/11/25 11:32
 */
@Configuration
@RefreshScope
@Getter
public class EntityCacheHotConfig {


    @Value("${entityCache.redis.ttl:3600}")
    private Long redisTtl;

    @Value("${entityCache.redis.delayDeleteSeconds:0}")
    private Long delayDeleteSeconds;

    @Value("${entityCache.redis.connectHaftOpenSeconds:60}")
    private Long connectHaftOpenSeconds;

    @Value("${entityCache.enable:true}")
    private boolean cacheEnable;
}
