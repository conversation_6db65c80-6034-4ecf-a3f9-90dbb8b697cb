package com.zyhl.yun.member.mcdmc.activation.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/06/04 14:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("activation_work_order_attr")
@Accessors(chain = true)
public class WorkOrderAttrPo extends BasePo {

    /**
     * 属性id
     */
    @TableField(value = "attr_id")
    private Long attrId;


    /**
     * 工单id
     */
    @TableField(value = "work_id")
    private String workId;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private String userId;

    /**
     * 属性编码
     */

    @TableField(value = "attr_code")
    private String attrCode;

    /**
     * 属性值
     */
    @TableField(value = "attr_val")
    private String attrVal;

    /**
     * 状态
     */
    @TableField(value = "state")
    private String state;

}
