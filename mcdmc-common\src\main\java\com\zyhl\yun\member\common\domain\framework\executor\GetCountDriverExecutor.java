package com.zyhl.yun.member.common.domain.framework.executor;

/**
 * <AUTHOR>
 * @date 2024/05/29 16:00
 */

import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.common.domain.framework.BaseDriver;
import com.zyhl.yun.member.common.domain.framework.DomainEntityPersistenceWrapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

@Data
@AllArgsConstructor
@Slf4j
public class GetCountDriverExecutor implements DriverExecutor {


    private static final long serialVersionUID = 7468292755587593905L;


    private BaseDriver driver;


    @Override
    public Serializable exec(DomainEntityPersistenceWrapper wrapper) {
        return driver.getDelegateDriver(wrapper).doGetCount((BaseCondition) wrapper.getData());
    }
}
