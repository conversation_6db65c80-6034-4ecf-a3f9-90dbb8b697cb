package com.zyhl.yun.member.product.adapter.vsbo.product;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zyhl.hcy.plugin.logger.enums.LogType;
import com.zyhl.yun.member.common.*;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.util.ParameterUtil;
import com.zyhl.yun.member.product.ProductApplication;
import com.zyhl.yun.member.product.application.vsbo.common.vo.PageInfo;
import com.zyhl.yun.member.product.application.vsbo.product.vo.*;
import com.zyhl.yun.member.product.application.vsbo.product.vo.request.*;
import com.zyhl.yun.member.product.application.vsbo.product.vo.response.*;
import com.zyhl.yun.member.product.common.enums.GoodsSalesTypeEnum;
import com.zyhl.yun.member.product.common.enums.TimePlanCycleTypeEnum;
import com.zyhl.yun.member.product.common.enums.ValidateRuleObjectTypeEnum;
import com.zyhl.yun.member.product.domain.goods.BenefitGoodsDo;
import com.zyhl.yun.member.product.domain.goods.GoodsDo;
import com.zyhl.yun.member.product.domain.goods.pack.GoodsPackageDo;
import com.zyhl.yun.member.product.domain.validaterule.ValidateRuleDo;
import com.zyhl.yun.member.product.infra.goods.po.GoodsGroupPo;
import com.zyhl.yun.member.product.infra.goods.po.GoodsPackageDetailPo;
import com.zyhl.yun.member.product.infra.goods.po.GoodsPo;
import com.zyhl.yun.member.product.infra.goods.po.GoodsTimePlanPo;
import com.zyhl.yun.member.product.infra.validaterule.po.ValidateRulePo;
import com.zyhl.yun.member.product.test.SpringBaseTester;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.MDC;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;
import reactor.test.StepVerifier;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.zyhl.yun.member.common.domain.serviceid.GoodsServiceId.QUERY_GOODS_INFO;
import static com.zyhl.yun.member.product.common.constants.ProductInfoConstant.ConflictRule.EXCLUSION_103;
import static com.zyhl.yun.member.product.common.constants.ProductInfoConstant.ConflictRule.NOT_EXCLUSION;
import static com.zyhl.yun.member.product.common.constants.ProductInfoConstant.NamedParameterKey.*;
import static com.zyhl.yun.member.product.common.constants.ProductInfoConstant.PACKAGE_CATEGORY;
import static com.zyhl.yun.member.product.common.enums.GoodsGroupTypeEnum.BENEFIT_GROUP;
import static com.zyhl.yun.member.product.common.enums.GoodsGroupTypeEnum.CHILD_GOODS_GROUP;
import static com.zyhl.yun.member.product.common.enums.SubTimePlanPolicyEnum.INDEPENDENCE;
import static com.zyhl.yun.member.product.common.enums.SubTimePlanPolicyEnum.PUT_OFF;
import static com.zyhl.yun.member.product.domain.goods.BenefitGoodsDo.GOODS_ID_PREFIX;
import static com.zyhl.yun.member.product.domain.goods.BenefitGoodsDo.GOODS_ID_SPLIT;
import static com.zyhl.yun.member.product.domain.product.ProductDo.ProductId.MEMBER;


/**
 * <AUTHOR>
 * @date 2023/08/11 11:30
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ProductApplication.class)
@Slf4j
@AutoConfigureMockMvc
public class ProductsControllerTest extends SpringBaseTester {

    @Resource
    private ProductsController productsController;


    private static final String MEMBER_PRODUCT_ID = "General_QGuo_12Month";

    private static final String MEMBER_OBJECT_ID = "lx_00001";


    private static final String RIGHTS_PRODUCT_ID = "TEST_Rights_Back_SSH_Monthly030";

    private static final String RIGHTS_OBJECT_ID = "TEST_Rights_ObjectID_030";


    private static final String PACKAGE_CATEGORY_PRODUCT_ID = "TEST_PackageCategory_Monthly030";

    private static final String PACKAGE_CATEGORY_OBJECT_ID = "TEST_PackageCategory_ObjectID_030";


    private static final String PACKAGE_CATEGORY_CHILD_PRODUCT_ID1 = "TEST_PackageCategory_Monthly030_child_1";

    private static final String PACKAGE_CATEGORY_CHILD_OBJECT_ID1 = "TEST_PackageCategory_ObjectID_030_child_1";


    private static final String PACKAGE_CATEGORY_CHILD_PRODUCT_ID2 = "TEST_PackageCategory_Monthly030_child_2";

    private static final String PACKAGE_CATEGORY_CHILD_OBJECT_ID2 = "TEST_PackageCategory_ObjectID_030_child_2";

    private static final String CHILD_GOODS_GROUP_ID_1 = "TEST_Child_Group_Id_1";

    private static final String CHILD_GOODS_GROUP_ID_2 = "TEST_Child_Group_Id_2";

    private static final String DISPLAY_PRODUCT_ID = "TEST_Display_Product";
    private static final String DISPLAY_OBJECT_ID = "TEST_Display_Object";
    private static final String NON_DISPLAY_PRODUCT_ID = "TEST_NonDisplay_Product";
    private static final String NON_DISPLAY_OBJECT_ID = "TEST_NonDisplay_Object";
    private static final String NO_DISPLAY_FIELD_PRODUCT_ID = "TEST_NoDisplayField_Product";
    private static final String NO_DISPLAY_FIELD_OBJECT_ID = "TEST_NoDisplayField_Object";

    @Before
    public void init() {

        MDC.put("log_type", LogType.INNER_CLIENT.name());

        super.deleteGoods(MEMBER_PRODUCT_ID);
        super.deleteGoods(RIGHTS_PRODUCT_ID);
        super.deleteGoods(PACKAGE_CATEGORY_PRODUCT_ID);
        super.deleteGoods(PACKAGE_CATEGORY_CHILD_PRODUCT_ID1);
        super.deleteGoods(PACKAGE_CATEGORY_CHILD_PRODUCT_ID2);
        super.deleteValidate(PACKAGE_CATEGORY_PRODUCT_ID + ":" + CHILD_GOODS_GROUP_ID_1,
                ValidateRuleObjectTypeEnum.GOODS_GROUP);
        super.deleteValidate(PACKAGE_CATEGORY_PRODUCT_ID + ":" + CHILD_GOODS_GROUP_ID_2,
                ValidateRuleObjectTypeEnum.GOODS_GROUP);

        // Clean up products created by testProductDisplayFunctionality
        try {
            super.deleteGoods(DISPLAY_PRODUCT_ID);
        } catch (Exception e) {
            log.warn("Failed to delete display product: {}", e.getMessage());
        }
        try {
            super.deleteGoods(NON_DISPLAY_PRODUCT_ID);
        } catch (Exception e) {
            log.warn("Failed to delete non-display product: {}", e.getMessage());
        }
        try {
            super.deleteGoods(NO_DISPLAY_FIELD_PRODUCT_ID);
        } catch (Exception e) {
            log.warn("Failed to delete no-display-field product: {}", e.getMessage());
        }
    }

    @Test
    public void delete() {
        //       super.deleteGoods("oneplusone_proprietary_vip_qq_music_1year");
//        super.deleteGoods("opop_qm_silver_vip_1month_sub");
//        super.deleteGoods("proprietary_qq_music_1month_sub");
//        super.deleteGoods("proprietary_t3_travel_1month_sub");
//        super.deleteGoods("proprietary_meituan_1month_sub");
        //       super.deleteGoods("lxiang01");
    }

    /**
     * 会员产品的创建和查询
     * 包月
     */
    @Test
    public void testCreateAndQueryMemberProduct() {

        //测试创建
        CreateProductReq createProductReq = new CreateProductReq();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setProductID(MEMBER_PRODUCT_ID);
        productInfo.setProductName("测试会员产品");
        productInfo.setCycleType(TimePlanCycleTypeEnum.RELATIVE_DAY.getType());
        productInfo.setPeriodNum(31);
        productInfo.setChargeType(1);
        productInfo.setProductType(101);
        productInfo.setStatus(3);
        productInfo.setPeriodNum(31);
        productInfo.setOriginalPrice("250");
        productInfo.setObjectID(MEMBER_OBJECT_ID);
        productInfo.setCreateTime("20230809120000");
        productInfo.setEffectiveTime("20230909120000");
        productInfo.setExpiryTime("20230909180000");
        productInfo.setStatus(3);
        productInfo.setUnsubPolicy(2);

        ReserveFields reserveFields = new ReserveFields();
        //展示给用户
        reserveFields.setReserve4("1");
        //全球通赠送产品
        reserveFields.setReserve7("giftGoodsId");
        //独立空间ownerId
        reserveFields.setReserve8("testOwnerId");
        //到期提前提醒天数
        reserveFields.setReserve14("5");
        //限购6次
        reserveFields.setReserve15("6");
        //用途
        reserveFields.setReserve17("purpose_test");
        //申请人
        reserveFields.setReserve18("applicant_test");
        productInfo.setReserveFields(reserveFields);

        //话费支付
        PayPolicy payPolicy = new PayPolicy();
        payPolicy.setChannelType(2);
        payPolicy.setReturnUrl("test_returnUrl");
        payPolicy.setPayCode("test_payCode");
        payPolicy.setApID("test_apId");
        payPolicy.setAppID("test_appId");

        //微信支付
        PayPolicy weixinPayPolicy = new PayPolicy();
        weixinPayPolicy.setChannelType(66);

        //支付宝支付
        PayPolicy aliPayPolicy = new PayPolicy();
        aliPayPolicy.setChannelType(77);

        productInfo.setPayPolicyList(Arrays.asList(payPolicy, weixinPayPolicy, aliPayPolicy));

        CapacityPolicy capacityPolicy = new CapacityPolicy();
        capacityPolicy.setCapacitySwitch(0);
        capacityPolicy.setCapacity(100);
        capacityPolicy.setCapacityType(2);
        capacityPolicy.setMcsServiceID("McsServiceID_test");
        productInfo.setCapacityPolicy(capacityPolicy);


        SmsPolicy smsPolicy = new SmsPolicy();
        productInfo.setSmsPolicy(smsPolicy);
        smsPolicy.setSmsSwitch(1);
        smsPolicy.setSubscribeTemplateID("SubscribeTemplateID_test");
        smsPolicy.setRightsSubscribeTemplateID("RightsSubscribeTemplateID_test");
        smsPolicy.setUnsubscribeTemplateID("UnsubscribeTemplateID_test");
        smsPolicy.setInnerSubMessageTemplateID("InnerSubMessageTemplateID_test");
        smsPolicy.setOuterSubMessageTemplateID("OuterSubMessageTemplateID_test");
        smsPolicy.setInnerRightsReceivedMessageTemplateID("InnerRightsReceivedMessageTemplateID_test");
        smsPolicy.setOuterRightsReceivedMessageTemplateID("OuterRightsReceivedMessageTemplateID_test");
        smsPolicy.setExpiredRemindTemplateID("ExpiredRemindTemplateID_test");
        smsPolicy.setInviteMessageTemplateID("InviteMessageTemplateID_test");


        List<NotifyService> notifyServiceList = new ArrayList<>();
        NotifyService notifyService1 = new NotifyService();
        notifyServiceList.add(notifyService1);
//        notifyService1.setServiceAddress("http://test");
        notifyService1.setServiceProtocol(1);
        notifyService1.setServiceID("serviceId_test");
        notifyService1.setServiceType(0);

        NotifyService notifyService2 = new NotifyService();
        notifyServiceList.add(notifyService2);
//        notifyService2.setServiceAddress("http://test2");
        notifyService2.setServiceProtocol(0);
        notifyService2.setServiceID("serviceId_test2");
        notifyService2.setServiceType(1);

        productInfo.setNotifyServiceList(notifyServiceList);

        List<NamedParameter> extInfo = new ArrayList<>();
        ParameterUtil.addParameter(extInfo, APPLE_PRODUCT_ID, "silverVIP_1year");
        ParameterUtil.addParameter(extInfo, ORDER_DAY_QUOTA, "3");
        ParameterUtil.addParameter(extInfo, TIPS_V4, "testTipsV4");
        ParameterUtil.addParameter(extInfo, PROV_CUSTOM, "1");
        ParameterUtil.addParameter(extInfo, SUPPORT_OTHER_OPERATOR, "1");
        ParameterUtil.addParameter(extInfo, IS_UN_SUBSCRIBE, "0");
        ParameterUtil.addParameter(extInfo, ALI_PAY_TEMPLATE_ID, "ali_pay_template_id_test");
        ParameterUtil.addParameter(extInfo, ALI_PAY_CONTENT_ID, "ali_pay_content_id_test");
        ParameterUtil.addParameter(extInfo, WEIXIN_TEMPLATE_ID, "weixin_template_id_test");
        ParameterUtil.addParameter(extInfo, WEIXIN_CONTENT_ID, "weixin_content_id_test");
        ParameterUtil.addParameter(extInfo, WEIXIN_PURE_TEMPLATE_ID, "weixin_pure_template_id_test");
        ParameterUtil.addParameter(extInfo, AVG_AMOUNT, "150");
        ParameterUtil.addParameter(extInfo, ACCUMULATIVE_TIME, "99");
        ParameterUtil.addParameter(extInfo, MONTH_QUOTAL_TIME, "15");
        ParameterUtil.addParameter(extInfo, LIMITED_WITHIN_DAYS, "{\"frequency\":1,\"days\":30}");
        ParameterUtil.addParameter(extInfo, DELAY_DELIVERY, "1500");
        ParameterUtil.addParameter(extInfo, TOP_SPEED_TRANSFER, "topspeedTransfer_test");
        ParameterUtil.addParameter(extInfo, RECYCLE_BIN_VALIDITY_EXPAND, "recycleBinValidityExpand_test");
        ParameterUtil.addParameter(extInfo, TRANSFER_NUM, "transferNum_test");
        ParameterUtil.addParameter(extInfo, UP_DOWN_NO_FLOW, "upDownNoFlow_test");
        ParameterUtil.addParameter(extInfo, SINGLE_FILE_UPLOAD_LIMIT, "singleFileUploadLimit_test");
        ParameterUtil.addParameter(extInfo, FAMILY_EXPERIENCE, "familyExperience_test");
        ParameterUtil.addParameter(extInfo, EXCLUSIVE_SPACE, "exclusiveSpace_test");
        ParameterUtil.addParameter(extInfo, IS_DIS_UN_SUB_BUTTON, "isDisUnsubButton_test");
        ParameterUtil.addParameter(extInfo, TIME_OF_EFFECT, "timeOfEffect_test");
        ParameterUtil.addParameter(extInfo, MEMBER_LEVEL, "memberLevel_test");
        ParameterUtil.addParameter(extInfo, DISCOUNT, "discount_test");
        ParameterUtil.addParameter(extInfo, PRODUCT_SRC, "productSrc_test");
        ParameterUtil.addParameter(extInfo, IOT_LIMIT, "0");

        productInfo.setExtInfo(extInfo);
        createProductReq.setProductInfo(productInfo);

        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(MEMBER_PRODUCT_ID, resp.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();

        //校验product插入情况
        GoodsPo goodsPo = goodsMapper.selectById(MEMBER_PRODUCT_ID);
        Assert.assertNotNull(goodsPo);
        Assert.assertEquals(MEMBER, goodsPo.getProductId());


        //校验Do是否正确
        DomainServiceContext domainServiceContext = new DomainServiceContext(GoodsDo.class,
                QUERY_GOODS_INFO);
        GoodsDo goodsDo = domainServiceContext.read(MEMBER_PRODUCT_ID, GoodsDo.class);
        Assert.assertNotNull(goodsDo);
        Assert.assertTrue(goodsDo instanceof GoodsPackageDo);
        List<GoodsDo> autoActiveChildGoods = goodsDo.findAutoActiveChildGoods();
        Assert.assertFalse(CollectionUtils.isEmpty(autoActiveChildGoods));
        Assert.assertEquals(1, autoActiveChildGoods.size());
        Assert.assertEquals(BenefitGoodsDo.BenefitNo.BENEFIT_NO_FREE_FLOW_PKG, autoActiveChildGoods.get(0).getProductId());
        Assert.assertEquals(GoodsSalesTypeEnum.BENEFIT, autoActiveChildGoods.get(0).getGoodsSalesType());
        Assert.assertEquals(GOODS_ID_PREFIX + GOODS_ID_SPLIT + goodsDo.getGoodsId() + GOODS_ID_SPLIT +
                BenefitGoodsDo.BenefitNo.BENEFIT_NO_FREE_FLOW_PKG, autoActiveChildGoods.get(0).getGoodsId());


        GoodsDo benfitGoodsDo = domainServiceContext.read(autoActiveChildGoods.get(0).getGoodsId(), GoodsDo.class);
        Assert.assertNotNull(benfitGoodsDo);
        Assert.assertTrue(benfitGoodsDo instanceof BenefitGoodsDo);


        //校验group插入情况
        LambdaQueryWrapper<GoodsGroupPo> queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, MEMBER_PRODUCT_ID);
        List<GoodsGroupPo> goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(MEMBER_OBJECT_ID, goodsGroupPoList.get(0).getGoodsGroupId());
        Assert.assertEquals(BENEFIT_GROUP.getType(), goodsGroupPoList.get(0).getGoodsGroupType());


        //校验校验规则插入情况:包月产品排斥所有同权益组商品实例
        LambdaQueryWrapper<ValidateRulePo> queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, MEMBER_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.EXCLUDE_IN_BENEFIT_GROUP);
        List<ValidateRulePo> validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());

        //校验校验规则插入情况:限购
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, MEMBER_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.SUB_QUOTA);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());
        Assert.assertEquals(Integer.valueOf(6), validateRulePoList.get(0).getReferDataSetCount());

        //校验校验规则插入情况:每日限购
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, MEMBER_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.ORDER_DAY_QUOTA);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());
        Assert.assertEquals(Integer.valueOf(3), validateRulePoList.get(0).getReferDataSetCount());

        //校验校验规则插入情况:累积限购
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, MEMBER_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.ACCUMULATIVE_SUB_QUOTA);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());
        Assert.assertEquals(Integer.valueOf(99), validateRulePoList.get(0).getReferDataSetCount());


        //校验校验规则插入情况: 当月限购
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, MEMBER_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.MONTHLY_SUB_QUOTA);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());
        Assert.assertEquals(Integer.valueOf(15), validateRulePoList.get(0).getReferDataSetCount());


        //校验校验规则插入情况: n天内限购
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, MEMBER_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.SUB_QUOTA_WITHIN_DAYS);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());
        Assert.assertEquals(Integer.valueOf(1), validateRulePoList.get(0).getReferDataSetCount());


        //校验校验规则插入情况:全球通规则校验
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, MEMBER_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.GOTONE_CHECK);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());


        //校验校验规则插入情况:分省资格校验
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, MEMBER_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.PROV_CODE_CHECK);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());


        //校验校验规则插入情况:支付方式资格校验
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, MEMBER_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.PAY_WAY_CHECK);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());


        //校验校验规则插入情况:接入来源校验
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, MEMBER_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.ACCESS_SOURCE_CHECK);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());


        //校验校验规则插入情况:运营商校验
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, MEMBER_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.OPERATOR_CHECK);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());


        //校验校验规则插入情况:渠道校验
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, MEMBER_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.CHANNEL_CHECK);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());


        //测试查询
        QueryProductsReq queryProductsReq = new QueryProductsReq();
        queryProductsReq.setProductIDList(Arrays.asList(MEMBER_PRODUCT_ID));
        StepVerifier.create(productsController.queryProducts(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsResp resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());

                    ProductInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(MEMBER_PRODUCT_ID, productInfoResult.getProductID());
                    Assert.assertEquals(Integer.valueOf(101), productInfoResult.getProductType());
                    Assert.assertEquals(Integer.valueOf(TimePlanCycleTypeEnum.RELATIVE_DAY.getType()), productInfoResult.getCycleType());
                    Assert.assertEquals(Integer.valueOf(31), productInfoResult.getPeriodNum());
                    Assert.assertEquals(MEMBER_OBJECT_ID, productInfoResult.getObjectID());
                    Assert.assertEquals(Integer.valueOf(1), productInfoResult.getChargeType());
                    Assert.assertEquals(Integer.valueOf(2), productInfoResult.getUnsubPolicy());

                    Assert.assertEquals("20230909120000", productInfoResult.getEffectiveTime());
                    Assert.assertEquals("20230909180000", productInfoResult.getExpiryTime());

                    List<NamedParameter> resultExtInfo = productInfoResult.getExtInfo();
                    Assert.assertFalse(CollectionUtils.isEmpty(resultExtInfo));
                    Assert.assertEquals("silverVIP_1year", ParameterUtil.getParameter(resultExtInfo, APPLE_PRODUCT_ID));
                    Assert.assertEquals("3", ParameterUtil.getParameter(resultExtInfo, ORDER_DAY_QUOTA));
                    Assert.assertEquals("testTipsV4", ParameterUtil.getParameter(resultExtInfo, TIPS_V4));
                    Assert.assertEquals("1", ParameterUtil.getParameter(resultExtInfo, PROV_CUSTOM));
                    Assert.assertEquals("1", ParameterUtil.getParameter(resultExtInfo, SUPPORT_OTHER_OPERATOR));
                    Assert.assertEquals("0", ParameterUtil.getParameter(resultExtInfo, IS_UN_SUBSCRIBE));
                    Assert.assertEquals("ali_pay_template_id_test", ParameterUtil.getParameter(resultExtInfo, ALI_PAY_TEMPLATE_ID));
                    Assert.assertEquals("ali_pay_content_id_test", ParameterUtil.getParameter(resultExtInfo, ALI_PAY_CONTENT_ID));
                    Assert.assertEquals("weixin_template_id_test", ParameterUtil.getParameter(resultExtInfo, WEIXIN_TEMPLATE_ID));
                    Assert.assertEquals("weixin_content_id_test", ParameterUtil.getParameter(resultExtInfo, WEIXIN_CONTENT_ID));
                    Assert.assertEquals("weixin_pure_template_id_test", ParameterUtil.getParameter(resultExtInfo, WEIXIN_PURE_TEMPLATE_ID));
                    Assert.assertEquals("150", ParameterUtil.getParameter(resultExtInfo, AVG_AMOUNT));
                    Assert.assertEquals("99", ParameterUtil.getParameter(resultExtInfo, ACCUMULATIVE_TIME));
                    Assert.assertEquals("15", ParameterUtil.getParameter(resultExtInfo, MONTH_QUOTAL_TIME));
                    Assert.assertEquals("{\"frequency\":1,\"days\":30}", ParameterUtil.getParameter(resultExtInfo, LIMITED_WITHIN_DAYS));
                    Assert.assertEquals("1500", ParameterUtil.getParameter(resultExtInfo, DELAY_DELIVERY));
                    Assert.assertEquals("topspeedTransfer_test", ParameterUtil.getParameter(resultExtInfo, TOP_SPEED_TRANSFER));
                    Assert.assertEquals("recycleBinValidityExpand_test", ParameterUtil.getParameter(resultExtInfo, RECYCLE_BIN_VALIDITY_EXPAND));
                    Assert.assertEquals("transferNum_test", ParameterUtil.getParameter(resultExtInfo, TRANSFER_NUM));
                    Assert.assertEquals("upDownNoFlow_test", ParameterUtil.getParameter(resultExtInfo, UP_DOWN_NO_FLOW));
                    Assert.assertEquals("singleFileUploadLimit_test", ParameterUtil.getParameter(resultExtInfo, SINGLE_FILE_UPLOAD_LIMIT));
                    Assert.assertEquals("familyExperience_test", ParameterUtil.getParameter(resultExtInfo, FAMILY_EXPERIENCE));
                    Assert.assertEquals("exclusiveSpace_test", ParameterUtil.getParameter(resultExtInfo, EXCLUSIVE_SPACE));
                    Assert.assertEquals("isDisUnsubButton_test", ParameterUtil.getParameter(resultExtInfo, IS_DIS_UN_SUB_BUTTON));
                    Assert.assertEquals("timeOfEffect_test", ParameterUtil.getParameter(resultExtInfo, TIME_OF_EFFECT));
                    Assert.assertEquals("memberLevel_test", ParameterUtil.getParameter(resultExtInfo, MEMBER_LEVEL));
                    Assert.assertEquals("discount_test", ParameterUtil.getParameter(resultExtInfo, DISCOUNT));
                    Assert.assertEquals("productSrc_test", ParameterUtil.getParameter(resultExtInfo, PRODUCT_SRC));
                    Assert.assertEquals("0", ParameterUtil.getParameter(resultExtInfo, IOT_LIMIT));


                    ReserveFields resultResultFields = productInfoResult.getReserveFields();
                    Assert.assertNotNull(resultResultFields);
                    Assert.assertEquals("3", resultResultFields.getReserve1());
                    Assert.assertEquals("McsServiceID_test", resultResultFields.getReserve2());
                    Assert.assertEquals("1", resultResultFields.getReserve3());
                    Assert.assertEquals("1", resultResultFields.getReserve4());
                    Assert.assertEquals("giftGoodsId", resultResultFields.getReserve7());
                    Assert.assertEquals("testOwnerId", resultResultFields.getReserve8());
                    Assert.assertEquals("5", resultResultFields.getReserve14());
                    Assert.assertEquals("6", resultResultFields.getReserve15());
                    Assert.assertEquals("purpose_test", resultResultFields.getReserve17());
                    Assert.assertEquals("applicant_test", resultResultFields.getReserve18());

                    //短信策略
                    SmsPolicy resultSmsPolicy = productInfoResult.getSmsPolicy();
                    Assert.assertNotNull(resultSmsPolicy);
                    Assert.assertEquals(Integer.valueOf(1), resultSmsPolicy.getSmsSwitch());
                    Assert.assertEquals("SubscribeTemplateID_test", resultSmsPolicy.getSubscribeTemplateID());
                    Assert.assertEquals("RightsSubscribeTemplateID_test", resultSmsPolicy.getRightsSubscribeTemplateID());
                    Assert.assertEquals("UnsubscribeTemplateID_test", resultSmsPolicy.getUnsubscribeTemplateID());
                    Assert.assertEquals("InnerSubMessageTemplateID_test", resultSmsPolicy.getInnerSubMessageTemplateID());
                    Assert.assertEquals("OuterSubMessageTemplateID_test", resultSmsPolicy.getOuterSubMessageTemplateID());
                    Assert.assertEquals("InnerRightsReceivedMessageTemplateID_test", resultSmsPolicy.getInnerRightsReceivedMessageTemplateID());
                    Assert.assertEquals("OuterRightsReceivedMessageTemplateID_test", resultSmsPolicy.getOuterRightsReceivedMessageTemplateID());
                    Assert.assertEquals("ExpiredRemindTemplateID_test", resultSmsPolicy.getExpiredRemindTemplateID());
                    Assert.assertEquals("InviteMessageTemplateID_test", resultSmsPolicy.getInviteMessageTemplateID());


                    //支付策略
                    List<PayPolicy> payPolicyList = productInfoResult.getPayPolicyList();
                    Assert.assertFalse(CollectionUtils.isEmpty(payPolicyList));
                    Assert.assertTrue(payPolicyList.size() == 3);
                    PayPolicy resultPayPolicy = payPolicyList.get(0);
                    Assert.assertNotNull(resultPayPolicy);
                    Assert.assertEquals(Integer.valueOf(2), resultPayPolicy.getChannelType());
                    Assert.assertEquals("test_returnUrl", resultPayPolicy.getReturnUrl());
                    Assert.assertEquals("test_payCode", resultPayPolicy.getPayCode());
                    Assert.assertEquals("test_apId", resultPayPolicy.getApID());
                    Assert.assertEquals("test_appId", resultPayPolicy.getAppID());


                    //空间策略
                    CapacityPolicy resultCapacityPolicy = productInfoResult.getCapacityPolicy();
                    Assert.assertNotNull(resultCapacityPolicy);
                    Assert.assertEquals(capacityPolicy.getCapacity(), resultCapacityPolicy.getCapacity());
                    Assert.assertEquals(capacityPolicy.getCapacitySwitch(), resultCapacityPolicy.getCapacitySwitch());
                    Assert.assertEquals(capacityPolicy.getCapacityType(), resultCapacityPolicy.getCapacityType());
                    Assert.assertEquals(capacityPolicy.getMcsServiceID(), resultCapacityPolicy.getMcsServiceID());

                    //通知地址列表
                    List<NotifyService> resultNotifyServiceList = productInfoResult.getNotifyServiceList();
                    Assert.assertFalse(CollectionUtils.isEmpty(resultNotifyServiceList));
                    Assert.assertTrue(resultNotifyServiceList.size() == 2);
                    NotifyService resultNotifyService = resultNotifyServiceList.get(0);
                    Assert.assertEquals(notifyService1.getServiceID(), resultNotifyService.getServiceID());
                    Assert.assertEquals(notifyService1.getServiceAddress(), resultNotifyService.getServiceAddress());
                    Assert.assertEquals(notifyService1.getServiceType(), resultNotifyService.getServiceType());
                    Assert.assertEquals(notifyService1.getServiceProtocol(), resultNotifyService.getServiceProtocol());

                    NotifyService resultNotifyService2 = resultNotifyServiceList.get(1);
                    Assert.assertEquals(notifyService2.getServiceID(), resultNotifyService2.getServiceID());
                    Assert.assertEquals(notifyService2.getServiceAddress(), resultNotifyService2.getServiceAddress());
                    Assert.assertEquals(notifyService2.getServiceType(), resultNotifyService2.getServiceType());
                    Assert.assertEquals(notifyService2.getServiceProtocol(), resultNotifyService2.getServiceProtocol());


                    return true;
                })
                .expectComplete()
                .verify();

        //重复新增一样id的时候会报错
        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectErrorMatches(error -> {
                    log.info("[TEST] create error:{}", error);
                    if (error instanceof ServiceException) {
                        Assert.assertEquals(ResultCodeEnum.GOODS_ALREADY_EXISTS.getResultCode(), ((ServiceException) error).getErrorCode());

                    } else {
                        Assert.fail("exception type no match");
                    }

                    return true;
                })
                .verify();

    }


    /**
     * 权益产品的创建和查询
     */
    @Test
    public void testCreateAndQueryRightsProduct() {

        //测试创建
        CreateProductReq createProductReq = new CreateProductReq();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setProductID(RIGHTS_PRODUCT_ID);
        productInfo.setProductName("测试权益产品");
        productInfo.setCycleType(TimePlanCycleTypeEnum.RELATIVE_MONTH.getType());
        productInfo.setChargeType(1);
        productInfo.setProductType(103);
        productInfo.setStatus(3);
        productInfo.setPeriodNum(1);
        productInfo.setOriginalPrice("250");
        productInfo.setObjectID(RIGHTS_OBJECT_ID);
        productInfo.setCreateTime("20230809120000");
        productInfo.setEffectiveTime("20230909120000");
        productInfo.setExpiryTime("20230909120000");
        productInfo.setStatus(3);

        ReserveFields reserveFields = new ReserveFields();
        reserveFields.setReserve16(NOT_EXCLUSION);
        productInfo.setReserveFields(reserveFields);

        PayPolicy payPolicy = new PayPolicy();
        payPolicy.setChannelType(44);
        productInfo.setPayPolicyList(Arrays.asList(payPolicy));

        createProductReq.setProductInfo(productInfo);

        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(RIGHTS_PRODUCT_ID, resp.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();


        //校验group插入情况
        LambdaQueryWrapper<GoodsGroupPo> queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, RIGHTS_PRODUCT_ID);
        List<GoodsGroupPo> goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(RIGHTS_OBJECT_ID, goodsGroupPoList.get(0).getGoodsGroupId());

        //校验时间计划插入情况
        GoodsTimePlanPo goodsTimePlanPO = goodsTimePlanMapper.selectById(RIGHTS_PRODUCT_ID);
        Assert.assertNotNull(goodsTimePlanPO);
        Assert.assertEquals(new Integer(INDEPENDENCE.getPolicy()), goodsTimePlanPO.getSubTimePlanPolicy());

        //测试查询
        QueryProductsReq queryProductsReq = new QueryProductsReq();
        queryProductsReq.setProductIDList(Arrays.asList(RIGHTS_PRODUCT_ID));
        StepVerifier.create(productsController.queryProducts(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsResp resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());

                    ProductInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(RIGHTS_PRODUCT_ID, productInfoResult.getProductID());
                    Assert.assertEquals(Integer.valueOf(103), productInfoResult.getProductType());
                    Assert.assertEquals(Integer.valueOf(TimePlanCycleTypeEnum.RELATIVE_MONTH.getType()), productInfoResult.getCycleType());
                    Assert.assertEquals(Integer.valueOf(1), productInfoResult.getPeriodNum());
                    Assert.assertEquals(RIGHTS_OBJECT_ID, productInfoResult.getObjectID());

                    return true;
                })
                .expectComplete()
                .verify();


        //重复新增一样id的时候会报错
        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectErrorMatches(error -> {
                    log.info("[TEST] create error:{}", error);
                    if (error instanceof ServiceException) {
                        Assert.assertEquals(ResultCodeEnum.GOODS_ALREADY_EXISTS.getResultCode(), ((ServiceException) error).getErrorCode());

                    } else {
                        Assert.fail("exception type no match");
                    }

                    return true;
                })
                .verify();

    }


    /**
     * 权益产品的创建和查询
     */
    @Test
    public void testCreateAndQueryRightsProductV2() {

        //测试创建
        CreateProductReq createProductReq = new CreateProductReq();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setProductID(RIGHTS_PRODUCT_ID);
        productInfo.setProductName("测试权益产品");
        productInfo.setCycleType(1);
        productInfo.setChargeType(1);
        productInfo.setProductType(103);
        productInfo.setStatus(3);
        productInfo.setPeriodNum(30);
        productInfo.setOriginalPrice("250");
        productInfo.setObjectID(RIGHTS_OBJECT_ID);
        productInfo.setCreateTime("20230809120000");
        productInfo.setEffectiveTime("20230909120000");
        productInfo.setExpiryTime("20230909120000");
        productInfo.setStatus(3);

        ReserveFields reserveFields = new ReserveFields();
        reserveFields.setReserve16(EXCLUSION_103);
        productInfo.setReserveFields(reserveFields);

        PayPolicy payPolicy = new PayPolicy();
        payPolicy.setChannelType(44);
        productInfo.setPayPolicyList(Arrays.asList(payPolicy));

        createProductReq.setProductInfo(productInfo);

        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(RIGHTS_PRODUCT_ID, resp.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();


        //校验group插入情况
        LambdaQueryWrapper<GoodsGroupPo> queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, RIGHTS_PRODUCT_ID);
        List<GoodsGroupPo> goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(RIGHTS_OBJECT_ID, goodsGroupPoList.get(0).getGoodsGroupId());


        //校验时间计划插入情况
        GoodsTimePlanPo goodsTimePlanPO = goodsTimePlanMapper.selectById(RIGHTS_PRODUCT_ID);
        Assert.assertNotNull(goodsTimePlanPO);
        Assert.assertEquals(new Integer(PUT_OFF.getPolicy()), goodsTimePlanPO.getSubTimePlanPolicy());

        //测试查询
        QueryProductsReq queryProductsReq = new QueryProductsReq();
        queryProductsReq.setProductIDList(Arrays.asList(RIGHTS_PRODUCT_ID));
        StepVerifier.create(productsController.queryProductsV2(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsRespV2 resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());

                    ProductInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(RIGHTS_PRODUCT_ID, productInfoResult.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();

    }


    /**
     * 组合产品产品的创建和查询
     */
    @Test
    public void testCreateAndQueryPackageCategoryProduct() {

        createChildProductAndCheck(PACKAGE_CATEGORY_CHILD_PRODUCT_ID1, PACKAGE_CATEGORY_CHILD_OBJECT_ID1, "测试子产品1");
        createChildProductAndCheck(PACKAGE_CATEGORY_CHILD_PRODUCT_ID2, PACKAGE_CATEGORY_CHILD_OBJECT_ID2, "测试子产品2");

        //测试创建
        CreateProductReq createProductReq = new CreateProductReq();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setProductID(PACKAGE_CATEGORY_PRODUCT_ID);
        productInfo.setProductName("测试组合产品");
        productInfo.setCycleType(1);
        productInfo.setChargeType(1);
        productInfo.setProductType(103);
        productInfo.setStatus(3);
        productInfo.setPeriodNum(30);
        productInfo.setOriginalPrice("250");
        productInfo.setObjectID(PACKAGE_CATEGORY_OBJECT_ID);
        productInfo.setCreateTime("20230809120000");
        productInfo.setEffectiveTime("20230909120000");
        productInfo.setExpiryTime("20230909120000");
        productInfo.setStatus(3);

        ReserveFields reserveFields = new ReserveFields();
        reserveFields.setReserve16(NOT_EXCLUSION);
        reserveFields.setReserve19(PACKAGE_CATEGORY);
        productInfo.setReserveFields(reserveFields);

        PayPolicy payPolicy = new PayPolicy();
        payPolicy.setChannelType(44);
        productInfo.setPayPolicyList(Arrays.asList(payPolicy));

        List<NamedParameter> extInfo = new ArrayList<>();
        ParameterUtil.addParameter(extInfo, CHILD_PRODUCTS,
                "[{\"productId\":\"" + PACKAGE_CATEGORY_CHILD_PRODUCT_ID1 + "\",\"activeType\":\"0\",\"activeGroupId\":\"" + CHILD_GOODS_GROUP_ID_1 + "\"}," +
                        "{\"productId\":\"" + PACKAGE_CATEGORY_CHILD_PRODUCT_ID2 + "\",\"activeType\":\"0\",\"activeStrategy\":\"3\", \"activeGroupId\":\"" + CHILD_GOODS_GROUP_ID_2 + "\",\"activeCount\":\"2\"}]");
        productInfo.setExtInfo(extInfo);
        createProductReq.setProductInfo(productInfo);


        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID, resp.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();


        //校验group插入情况
        LambdaQueryWrapper<GoodsGroupPo> queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, PACKAGE_CATEGORY_PRODUCT_ID)
                .eq(GoodsGroupPo::getGoodsGroupType, BENEFIT_GROUP.getType());
        List<GoodsGroupPo> goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(PACKAGE_CATEGORY_OBJECT_ID, goodsGroupPoList.get(0).getGoodsGroupId());


        queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, PACKAGE_CATEGORY_CHILD_PRODUCT_ID1)
                .eq(GoodsGroupPo::getGoodsGroupType, CHILD_GOODS_GROUP.getType());
        goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID + ":" + CHILD_GOODS_GROUP_ID_1, goodsGroupPoList.get(0).getGoodsGroupId());


        queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, PACKAGE_CATEGORY_CHILD_PRODUCT_ID2)
                .eq(GoodsGroupPo::getGoodsGroupType, CHILD_GOODS_GROUP.getType());
        goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID + ":" + CHILD_GOODS_GROUP_ID_2, goodsGroupPoList.get(0).getGoodsGroupId());


        //校验校验规则插入情况:取消点播包月互斥只排斥同权益组包月产品
        LambdaQueryWrapper<ValidateRulePo> queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, PACKAGE_CATEGORY_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.EXCLUDE_MONTHLY_CHARGE_TYPE_IN_BENEFIT_GROUP);
        List<ValidateRulePo> validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());


        //校验校验规则插入情况:子商品互斥校验
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, PACKAGE_CATEGORY_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.CHILD_GOODS_EXCLUDE_OPTION);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());


        //校验校验规则插入情况:子商品领取校验
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS_GROUP.getType())
                .eq(ValidateRulePo::getObjectId, PACKAGE_CATEGORY_PRODUCT_ID + ":" + CHILD_GOODS_GROUP_ID_2)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.CHILD_GOODS_ACTIVE_CHECK);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());

        //校验校验规则插入情况:每月领取一次
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS_GROUP.getType())
                .eq(ValidateRulePo::getObjectId, PACKAGE_CATEGORY_PRODUCT_ID + ":" + CHILD_GOODS_GROUP_ID_2)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.N_CHOOSE_ALL_AS_CHIlD_GOODS);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());
        Assert.assertEquals(Integer.valueOf(2), validateRulePoList.get(0).getReferDataSetCount());

        //商品包校验
        List<GoodsPackageDetailPo> goodsPackageDetailPoList = goodsPackageDetailMapper.selectList(new LambdaQueryWrapper<GoodsPackageDetailPo>()
                .eq(GoodsPackageDetailPo::getGoodsPackageId, PACKAGE_CATEGORY_PRODUCT_ID));

        Assert.assertNotNull(goodsPackageDetailPoList);
        Assert.assertTrue(goodsPackageDetailPoList.size() == 2);
        Assert.assertTrue(goodsPackageDetailPoList.stream().map(goodsPackageDetailPO -> goodsPackageDetailPO.getGoodsId()).collect(Collectors.toList()).contains(PACKAGE_CATEGORY_CHILD_PRODUCT_ID1));
        Assert.assertTrue(goodsPackageDetailPoList.stream().map(goodsPackageDetailPO -> goodsPackageDetailPO.getGoodsId()).collect(Collectors.toList()).contains(PACKAGE_CATEGORY_CHILD_PRODUCT_ID2));


        //测试查询
        QueryProductsReq queryProductsReq = new QueryProductsReq();
        queryProductsReq.setProductIDList(Arrays.asList(PACKAGE_CATEGORY_PRODUCT_ID));
        StepVerifier.create(productsController.queryProducts(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsResp resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());

                    ProductInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID, productInfoResult.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();


        //重复新增一样id的时候会报错
        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectErrorMatches(error -> {
                    log.info("[TEST] create error:{}", error);
                    if (error instanceof ServiceException) {
                        Assert.assertEquals(ResultCodeEnum.GOODS_ALREADY_EXISTS.getResultCode(), ((ServiceException) error).getErrorCode());

                    } else {
                        Assert.fail("exception type no match");
                    }

                    return true;
                })
                .verify();

    }


    /**
     * 组合产品产品的创建与删除
     * <p>
     * 创建->删除->创建
     */
    @Test
    public void testCreateAndDeletePackageCategoryProduct() {

        createChildProductAndCheck(PACKAGE_CATEGORY_CHILD_PRODUCT_ID1, PACKAGE_CATEGORY_CHILD_OBJECT_ID1, "测试子产品1");
        createChildProductAndCheck(PACKAGE_CATEGORY_CHILD_PRODUCT_ID2, PACKAGE_CATEGORY_CHILD_OBJECT_ID2, "测试子产品2");

        //测试创建
        CreateProductReq createProductReq = new CreateProductReq();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setProductID(PACKAGE_CATEGORY_PRODUCT_ID);
        productInfo.setProductName("测试组合产品");
        productInfo.setCycleType(1);
        productInfo.setChargeType(1);
        productInfo.setProductType(103);
        productInfo.setStatus(3);
        productInfo.setPeriodNum(30);
        productInfo.setOriginalPrice("250");
        productInfo.setObjectID(PACKAGE_CATEGORY_OBJECT_ID);
        productInfo.setCreateTime("20230809120000");
        productInfo.setEffectiveTime("20230909120000");
        productInfo.setExpiryTime("20230909120000");
        productInfo.setStatus(3);

        ReserveFields reserveFields = new ReserveFields();
        reserveFields.setReserve16(NOT_EXCLUSION);
        reserveFields.setReserve19(PACKAGE_CATEGORY);
        productInfo.setReserveFields(reserveFields);

        PayPolicy payPolicy = new PayPolicy();
        payPolicy.setChannelType(44);
        productInfo.setPayPolicyList(Arrays.asList(payPolicy));

        List<NamedParameter> extInfo = new ArrayList<>();
        ParameterUtil.addParameter(extInfo, CHILD_PRODUCTS,
                "[{\"productId\":\"" + PACKAGE_CATEGORY_CHILD_PRODUCT_ID1 + "\",\"activeType\":\"0\",\"activeGroupId\":\"" + CHILD_GOODS_GROUP_ID_1 + "\"}," +
                        "{\"productId\":\"" + PACKAGE_CATEGORY_CHILD_PRODUCT_ID2 + "\",\"activeType\":\"0\",\"activeStrategy\":\"3\", \"activeGroupId\":\"" + CHILD_GOODS_GROUP_ID_2 + "\",\"activeCount\":\"2\"}]");
        productInfo.setExtInfo(extInfo);
        createProductReq.setProductInfo(productInfo);


        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID, resp.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();


        //校验group插入情况
        LambdaQueryWrapper<GoodsGroupPo> queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, PACKAGE_CATEGORY_PRODUCT_ID)
                .eq(GoodsGroupPo::getGoodsGroupType, BENEFIT_GROUP.getType());
        List<GoodsGroupPo> goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(PACKAGE_CATEGORY_OBJECT_ID, goodsGroupPoList.get(0).getGoodsGroupId());


        queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, PACKAGE_CATEGORY_CHILD_PRODUCT_ID1)
                .eq(GoodsGroupPo::getGoodsGroupType, CHILD_GOODS_GROUP.getType());
        goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID + ":" + CHILD_GOODS_GROUP_ID_1, goodsGroupPoList.get(0).getGoodsGroupId());


        queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, PACKAGE_CATEGORY_CHILD_PRODUCT_ID2)
                .eq(GoodsGroupPo::getGoodsGroupType, CHILD_GOODS_GROUP.getType());
        goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID + ":" + CHILD_GOODS_GROUP_ID_2, goodsGroupPoList.get(0).getGoodsGroupId());


        //校验校验规则插入情况:取消点播包月互斥只排斥同权益组包月产品
        LambdaQueryWrapper<ValidateRulePo> queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, PACKAGE_CATEGORY_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.EXCLUDE_MONTHLY_CHARGE_TYPE_IN_BENEFIT_GROUP);
        List<ValidateRulePo> validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());


        //校验校验规则插入情况:子商品互斥校验
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, PACKAGE_CATEGORY_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.CHILD_GOODS_EXCLUDE_OPTION);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());

        //校验校验规则插入情况:子商品领取校验
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS_GROUP.getType())
                .eq(ValidateRulePo::getObjectId, PACKAGE_CATEGORY_PRODUCT_ID + ":" + CHILD_GOODS_GROUP_ID_2)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.CHILD_GOODS_ACTIVE_CHECK);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());

        //校验校验规则插入情况:每月领取一次
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS_GROUP.getType())
                .eq(ValidateRulePo::getObjectId, PACKAGE_CATEGORY_PRODUCT_ID + ":" + CHILD_GOODS_GROUP_ID_2)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.N_CHOOSE_ALL_AS_CHIlD_GOODS);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());
        Assert.assertEquals(Integer.valueOf(2), validateRulePoList.get(0).getReferDataSetCount());

        //商品包校验
        List<GoodsPackageDetailPo> goodsPackageDetailPoList = goodsPackageDetailMapper.selectList(new LambdaQueryWrapper<GoodsPackageDetailPo>()
                .eq(GoodsPackageDetailPo::getGoodsPackageId, PACKAGE_CATEGORY_PRODUCT_ID));

        Assert.assertNotNull(goodsPackageDetailPoList);
        Assert.assertTrue(goodsPackageDetailPoList.size() == 2);
        Assert.assertTrue(goodsPackageDetailPoList.stream().map(goodsPackageDetailPO -> goodsPackageDetailPO.getGoodsId()).collect(Collectors.toList()).contains(PACKAGE_CATEGORY_CHILD_PRODUCT_ID1));
        Assert.assertTrue(goodsPackageDetailPoList.stream().map(goodsPackageDetailPO -> goodsPackageDetailPO.getGoodsId()).collect(Collectors.toList()).contains(PACKAGE_CATEGORY_CHILD_PRODUCT_ID2));


        //测试查询
        QueryProductsReq queryProductsReq = new QueryProductsReq();
        queryProductsReq.setProductIDList(Arrays.asList(PACKAGE_CATEGORY_PRODUCT_ID));
        StepVerifier.create(productsController.queryProducts(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsResp resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());

                    ProductInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID, productInfoResult.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();


        //重复新增一样id的时候会报错
        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectErrorMatches(error -> {
                    log.info("[TEST] create error:{}", error);
                    if (error instanceof ServiceException) {
                        Assert.assertEquals(ResultCodeEnum.GOODS_ALREADY_EXISTS.getResultCode(), ((ServiceException) error).getErrorCode());

                    } else {
                        Assert.fail("exception type no match");
                    }

                    return true;
                })
                .verify();


        //测试删除
        DeleteProductReq deleteProductReq = new DeleteProductReq();
        deleteProductReq.setProductID(PACKAGE_CATEGORY_PRODUCT_ID);

        StepVerifier.create(productsController.deleteProduct(deleteProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] delete result:{}", result);
                    DeleteProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID, resp.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();

        //商品包校验
        goodsPackageDetailPoList = goodsPackageDetailMapper.selectList(new LambdaQueryWrapper<GoodsPackageDetailPo>()
                .eq(GoodsPackageDetailPo::getGoodsPackageId, PACKAGE_CATEGORY_PRODUCT_ID));

        Assert.assertNotNull(goodsPackageDetailPoList);
        Assert.assertTrue(CollectionUtils.isEmpty(goodsPackageDetailPoList));


        //再次创建
        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID, resp.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();

        // 再次查询
        StepVerifier.create(productsController.queryProducts(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsResp resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());

                    ProductInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID, productInfoResult.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();

    }


    @Test
    public void testCreateAndUpdatePackageCategoryProduct() {

        createChildProductAndCheck(PACKAGE_CATEGORY_CHILD_PRODUCT_ID1, PACKAGE_CATEGORY_CHILD_OBJECT_ID1, "测试子产品1");
        createChildProductAndCheck(PACKAGE_CATEGORY_CHILD_PRODUCT_ID2, PACKAGE_CATEGORY_CHILD_OBJECT_ID2, "测试子产品2");

        //测试创建
        CreateProductReq createProductReq = new CreateProductReq();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setProductID(PACKAGE_CATEGORY_PRODUCT_ID);
        productInfo.setProductName("测试组合产品");
        productInfo.setCycleType(1);
        productInfo.setChargeType(1);
        productInfo.setProductType(103);
        productInfo.setStatus(3);
        productInfo.setPeriodNum(30);
        productInfo.setOriginalPrice("250");
        productInfo.setObjectID(PACKAGE_CATEGORY_OBJECT_ID);
        productInfo.setCreateTime("20230809120000");
        productInfo.setEffectiveTime("20230909120000");
        productInfo.setExpiryTime("20230909120000");
        productInfo.setStatus(3);
        productInfo.setUnsubPolicy(1);

        ReserveFields reserveFields = new ReserveFields();
        reserveFields.setReserve16(NOT_EXCLUSION);
        reserveFields.setReserve19(PACKAGE_CATEGORY);
        productInfo.setReserveFields(reserveFields);

        PayPolicy payPolicy = new PayPolicy();
        payPolicy.setChannelType(44);
        productInfo.setPayPolicyList(Arrays.asList(payPolicy));

        List<NamedParameter> extInfo = new ArrayList<>();
        ParameterUtil.addParameter(extInfo, CHILD_PRODUCTS,
                "[{\"productId\":\"" + PACKAGE_CATEGORY_CHILD_PRODUCT_ID1 + "\",\"activeType\":\"0\",\"activeGroupId\":\"" + CHILD_GOODS_GROUP_ID_1 + "\"}," +
                        "{\"productId\":\"" + PACKAGE_CATEGORY_CHILD_PRODUCT_ID2 + "\",\"activeType\":\"0\",\"activeStrategy\":\"3\", \"activeGroupId\":\"" + CHILD_GOODS_GROUP_ID_2 + "\",\"activeCount\":\"2\"}]");
        productInfo.setExtInfo(extInfo);
        createProductReq.setProductInfo(productInfo);


        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID, resp.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();


        //校验group插入情况
        LambdaQueryWrapper<GoodsGroupPo> queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, PACKAGE_CATEGORY_PRODUCT_ID)
                .eq(GoodsGroupPo::getGoodsGroupType, BENEFIT_GROUP.getType());
        List<GoodsGroupPo> goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(PACKAGE_CATEGORY_OBJECT_ID, goodsGroupPoList.get(0).getGoodsGroupId());


        queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, PACKAGE_CATEGORY_CHILD_PRODUCT_ID1)
                .eq(GoodsGroupPo::getGoodsGroupType, CHILD_GOODS_GROUP.getType());
        goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID + ":" + CHILD_GOODS_GROUP_ID_1, goodsGroupPoList.get(0).getGoodsGroupId());


        queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, PACKAGE_CATEGORY_CHILD_PRODUCT_ID2)
                .eq(GoodsGroupPo::getGoodsGroupType, CHILD_GOODS_GROUP.getType());
        goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID + ":" + CHILD_GOODS_GROUP_ID_2, goodsGroupPoList.get(0).getGoodsGroupId());


        //校验校验规则插入情况:取消点播包月互斥只排斥同权益组包月产品
        LambdaQueryWrapper<ValidateRulePo> queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, PACKAGE_CATEGORY_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.EXCLUDE_MONTHLY_CHARGE_TYPE_IN_BENEFIT_GROUP);
        List<ValidateRulePo> validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());


        //校验校验规则插入情况:子商品互斥校验
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, PACKAGE_CATEGORY_PRODUCT_ID)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.CHILD_GOODS_EXCLUDE_OPTION);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());


        //校验校验规则插入情况:每月领取一次
        queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS_GROUP.getType())
                .eq(ValidateRulePo::getObjectId, PACKAGE_CATEGORY_PRODUCT_ID + ":" + CHILD_GOODS_GROUP_ID_2)
                .eq(ValidateRulePo::getPreDefineValidateRule, ValidateRuleDo.PreDefineValidateRule.N_CHOOSE_ALL_AS_CHIlD_GOODS);
        validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertNotNull(validateRulePoList);
        Assert.assertEquals(1, validateRulePoList.size());
        Assert.assertEquals(Integer.valueOf(2), validateRulePoList.get(0).getReferDataSetCount());

        //商品包校验
        List<GoodsPackageDetailPo> goodsPackageDetailPoList = goodsPackageDetailMapper.selectList(new LambdaQueryWrapper<GoodsPackageDetailPo>()
                .eq(GoodsPackageDetailPo::getGoodsPackageId, PACKAGE_CATEGORY_PRODUCT_ID));

        Assert.assertNotNull(goodsPackageDetailPoList);
        Assert.assertTrue(goodsPackageDetailPoList.size() == 2);
        Assert.assertTrue(goodsPackageDetailPoList.stream().map(goodsPackageDetailPO -> goodsPackageDetailPO.getGoodsId()).collect(Collectors.toList()).contains(PACKAGE_CATEGORY_CHILD_PRODUCT_ID1));
        Assert.assertTrue(goodsPackageDetailPoList.stream().map(goodsPackageDetailPO -> goodsPackageDetailPO.getGoodsId()).collect(Collectors.toList()).contains(PACKAGE_CATEGORY_CHILD_PRODUCT_ID2));


        //测试查询
        QueryProductsReq queryProductsReq = new QueryProductsReq();
        queryProductsReq.setProductIDList(Arrays.asList(PACKAGE_CATEGORY_PRODUCT_ID));
        StepVerifier.create(productsController.queryProducts(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsResp resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());

                    ProductInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID, productInfoResult.getProductID());
                    Assert.assertEquals(Integer.valueOf(1), productInfoResult.getUnsubPolicy());

                    return true;
                })
                .expectComplete()
                .verify();

        UpdateProductReq updateProductReq = new UpdateProductReq();
        productInfo.setProductDesc("test_update_desc");
        productInfo.setUnsubPolicy(3);
        updateProductReq.setProductInfo(productInfo);

        //修改
        StepVerifier.create(productsController.updateProduct(updateProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    UpdateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);

                    return true;
                })
                .expectComplete()
                .verify();


        StepVerifier.create(productsController.queryProducts(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsResp resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());

                    ProductInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(PACKAGE_CATEGORY_PRODUCT_ID, productInfoResult.getProductID());
                    Assert.assertEquals("test_update_desc", productInfoResult.getProductDesc());
                    Assert.assertEquals(Integer.valueOf(3), productInfoResult.getUnsubPolicy());
                    return true;
                })
                .expectComplete()
                .verify();


    }

    /**
     * 创建组合产品的子产品并校验
     *
     * @param productId
     * @param objectId
     * @param productName
     */
    private void createChildProductAndCheck(String productId, String objectId, String productName) {

        //测试创建
        CreateProductReq createProductReq = new CreateProductReq();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setProductID(productId);
        productInfo.setProductName(productName);
        productInfo.setCycleType(1);
        productInfo.setChargeType(1);
        productInfo.setProductType(103);
        productInfo.setStatus(3);
        productInfo.setPeriodNum(30);
        productInfo.setOriginalPrice("250");
        productInfo.setObjectID(objectId);
        productInfo.setCreateTime("20230809120000");
        productInfo.setEffectiveTime("20230909120000");
        productInfo.setExpiryTime("20230909120000");
        productInfo.setStatus(3);

        ReserveFields reserveFields = new ReserveFields();
        reserveFields.setReserve16(NOT_EXCLUSION);
        productInfo.setReserveFields(reserveFields);

        PayPolicy payPolicy = new PayPolicy();
        payPolicy.setChannelType(44);
        productInfo.setPayPolicyList(Arrays.asList(payPolicy));

        createProductReq.setProductInfo(productInfo);

        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(productId, resp.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();


        //校验group插入情况
        LambdaQueryWrapper<GoodsGroupPo> queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, productId);
        List<GoodsGroupPo> goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(objectId, goodsGroupPoList.get(0).getGoodsGroupId());


        //测试查询
        QueryProductsReq queryProductsReq = new QueryProductsReq();
        queryProductsReq.setProductIDList(Arrays.asList(productId));
        StepVerifier.create(productsController.queryProducts(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsResp resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());

                    ProductInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(productId, productInfoResult.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();


        //重复新增一样id的时候会报错
        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectErrorMatches(error -> {
                    log.info("[TEST] create error:{}", error);
                    if (error instanceof ServiceException) {
                        Assert.assertEquals(ResultCodeEnum.GOODS_ALREADY_EXISTS.getResultCode(), ((ServiceException) error).getErrorCode());

                    } else {
                        Assert.fail("exception type no match");
                    }

                    return true;
                })
                .verify();
    }

    /**
     * 会员产品的创建和查询V2
     */
    @Test
    public void testCreateAndQueryMemberProductV2() {

        //测试创建
        CreateProductReq createProductReq = new CreateProductReq();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setProductID(MEMBER_PRODUCT_ID);
        productInfo.setProductName("测试会员产品");
        productInfo.setCycleType(1);
        productInfo.setChargeType(1);
        productInfo.setProductType(101);
        productInfo.setStatus(3);
        productInfo.setPeriodNum(30);
        productInfo.setOriginalPrice("250");
        productInfo.setObjectID(MEMBER_OBJECT_ID);
        productInfo.setCreateTime("20230809120000");
        productInfo.setEffectiveTime("20230909120000");
        productInfo.setExpiryTime("20230909120000");
        productInfo.setStatus(3);

        PayPolicy payPolicy = new PayPolicy();
        payPolicy.setChannelType(44);
        productInfo.setPayPolicyList(Arrays.asList(payPolicy));

        CapacityPolicy capacityPolicy = new CapacityPolicy();
        capacityPolicy.setCapacitySwitch(0);
        capacityPolicy.setCapacity(100);
        capacityPolicy.setCapacityType(2);
        capacityPolicy.setMcsServiceID("McsServiceID_test");
        productInfo.setCapacityPolicy(capacityPolicy);

        List<NotifyService> notifyServiceList = new ArrayList<>();
        NotifyService notifyService1 = new NotifyService();
        notifyServiceList.add(notifyService1);
//        notifyService1.setServiceAddress("http://test");
        notifyService1.setServiceProtocol(1);
        notifyService1.setServiceID("serviceId_test");
        notifyService1.setServiceType(0);

        NotifyService notifyService2 = new NotifyService();
        notifyServiceList.add(notifyService2);
//        notifyService2.setServiceAddress("http://test2");
        notifyService2.setServiceProtocol(0);
        notifyService2.setServiceID("serviceId_test2");
        notifyService2.setServiceType(1);

        productInfo.setNotifyServiceList(notifyServiceList);

        List<NamedParameter> extInfo = new ArrayList<>();
        ParameterUtil.addParameter(extInfo, APPLE_PRODUCT_ID, "silverVIP_1year");
        productInfo.setExtInfo(extInfo);
        createProductReq.setProductInfo(productInfo);

        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(MEMBER_PRODUCT_ID, resp.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();


        //校验group插入情况
        LambdaQueryWrapper<GoodsGroupPo> queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, MEMBER_PRODUCT_ID);
        List<GoodsGroupPo> goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(MEMBER_OBJECT_ID, goodsGroupPoList.get(0).getGoodsGroupId());

        //测试查询
        QueryProductsReq queryProductsReq = new QueryProductsReq();
        queryProductsReq.setProductIDList(Arrays.asList(MEMBER_PRODUCT_ID));

        StepVerifier.create(productsController.queryProductsV2(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsRespV2 resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());

                    ProductInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(MEMBER_PRODUCT_ID, productInfoResult.getProductID());
                    Assert.assertEquals(Integer.valueOf(101), productInfoResult.getProductType());


                    List<NamedParameter> resultExtInfo = productInfoResult.getExtInfo();
                    Assert.assertFalse(CollectionUtils.isEmpty(resultExtInfo));
                    Assert.assertEquals("silverVIP_1year", ParameterUtil.getParameter(resultExtInfo, APPLE_PRODUCT_ID));

                    //支付策略
                    List<PayPolicy> payPolicyList = productInfoResult.getPayPolicyList();
                    Assert.assertFalse(CollectionUtils.isEmpty(payPolicyList));
                    PayPolicy resultPayPolicy = payPolicyList.get(0);
                    Assert.assertNotNull(resultPayPolicy);
                    Assert.assertEquals(payPolicy.getChannelType(), resultPayPolicy.getChannelType());


                    //空间策略
                    CapacityPolicy resultCapacityPolicy = productInfoResult.getCapacityPolicy();
                    Assert.assertNotNull(resultCapacityPolicy);
                    Assert.assertEquals(capacityPolicy.getCapacity(), resultCapacityPolicy.getCapacity());
                    Assert.assertEquals(capacityPolicy.getCapacitySwitch(), resultCapacityPolicy.getCapacitySwitch());
                    Assert.assertEquals(capacityPolicy.getCapacityType(), resultCapacityPolicy.getCapacityType());
                    Assert.assertEquals(capacityPolicy.getMcsServiceID(), resultCapacityPolicy.getMcsServiceID());

                    //通知地址列表
                    List<NotifyService> resultNotifyServiceList = productInfoResult.getNotifyServiceList();
                    Assert.assertFalse(CollectionUtils.isEmpty(resultNotifyServiceList));
                    Assert.assertTrue(resultNotifyServiceList.size() == 2);
                    NotifyService resultNotifyService = resultNotifyServiceList.get(0);
                    Assert.assertEquals(notifyService1.getServiceID(), resultNotifyService.getServiceID());
                    Assert.assertEquals(notifyService1.getServiceAddress(), resultNotifyService.getServiceAddress());
                    Assert.assertEquals(notifyService1.getServiceType(), resultNotifyService.getServiceType());
                    Assert.assertEquals(notifyService1.getServiceProtocol(), resultNotifyService.getServiceProtocol());

                    NotifyService resultNotifyService2 = resultNotifyServiceList.get(1);
                    Assert.assertEquals(notifyService2.getServiceID(), resultNotifyService2.getServiceID());
                    Assert.assertEquals(notifyService2.getServiceAddress(), resultNotifyService2.getServiceAddress());
                    Assert.assertEquals(notifyService2.getServiceType(), resultNotifyService2.getServiceType());
                    Assert.assertEquals(notifyService2.getServiceProtocol(), resultNotifyService2.getServiceProtocol());


                    return true;
                })
                .expectComplete()
                .verify();

        //重复新增一样id的时候会报错
        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectErrorMatches(error -> {
                    log.info("[TEST] create error:{}", error);
                    if (error instanceof ServiceException) {
                        Assert.assertEquals(ResultCodeEnum.GOODS_ALREADY_EXISTS.getResultCode(), ((ServiceException) error).getErrorCode());

                    } else {
                        Assert.fail("exception type no match");
                    }

                    return true;
                })
                .verify();

    }


    /**
     * 会员产品的创建和修改
     */
    @Test
    public void testCreateAndUpdateMemberProduct() {

        //测试创建
        CreateProductReq createProductReq = new CreateProductReq();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setProductID(MEMBER_PRODUCT_ID);
        productInfo.setProductName("测试会员产品");
        productInfo.setCycleType(1);
        productInfo.setChargeType(1);
        productInfo.setProductType(101);
        productInfo.setStatus(3);
        productInfo.setPeriodNum(30);
        productInfo.setOriginalPrice("250");
        productInfo.setObjectID(MEMBER_OBJECT_ID);
        productInfo.setCreateTime("20230809120000");
        productInfo.setEffectiveTime("20230909120000");
        productInfo.setExpiryTime("20230909120000");
        productInfo.setStatus(3);

        PayPolicy payPolicy = new PayPolicy();
        payPolicy.setChannelType(44);
        productInfo.setPayPolicyList(Arrays.asList(payPolicy));

        CapacityPolicy capacityPolicy = new CapacityPolicy();
        capacityPolicy.setCapacitySwitch(0);
        capacityPolicy.setCapacity(100);
        capacityPolicy.setCapacityType(2);
        capacityPolicy.setMcsServiceID("McsServiceID_test");
        productInfo.setCapacityPolicy(capacityPolicy);

        List<NotifyService> notifyServiceList = new ArrayList<>();
        NotifyService notifyService1 = new NotifyService();
        notifyServiceList.add(notifyService1);
        notifyService1.setServiceAddress("http://test");
        notifyService1.setServiceProtocol(1);
        notifyService1.setServiceID("serviceId_test");
        notifyService1.setServiceType(0);

        NotifyService notifyService2 = new NotifyService();
        notifyServiceList.add(notifyService2);
        notifyService2.setServiceAddress("http://test2");
        notifyService2.setServiceProtocol(0);
        notifyService2.setServiceID("serviceId_test2");
        notifyService2.setServiceType(1);

        productInfo.setNotifyServiceList(notifyServiceList);

        List<NamedParameter> extInfo = new ArrayList<>();
        ParameterUtil.addParameter(extInfo, APPLE_PRODUCT_ID, "silverVIP_1year");
        productInfo.setExtInfo(extInfo);
        createProductReq.setProductInfo(productInfo);

        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(MEMBER_PRODUCT_ID, resp.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();


        //校验group插入情况
        LambdaQueryWrapper<GoodsGroupPo> queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, MEMBER_PRODUCT_ID);
        List<GoodsGroupPo> goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(MEMBER_OBJECT_ID, goodsGroupPoList.get(0).getGoodsGroupId());

        //测试查询
        QueryProductsReq queryProductsReq = new QueryProductsReq();
        queryProductsReq.setProductIDList(Arrays.asList(MEMBER_PRODUCT_ID));

        StepVerifier.create(productsController.queryProductsV2(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsRespV2 resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());

                    ProductInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(MEMBER_PRODUCT_ID, productInfoResult.getProductID());
                    Assert.assertEquals(Integer.valueOf(101), productInfoResult.getProductType());
                    Assert.assertEquals("250", productInfoResult.getOriginalPrice());

                    List<NamedParameter> resultExtInfo = productInfoResult.getExtInfo();
                    Assert.assertFalse(CollectionUtils.isEmpty(resultExtInfo));
                    Assert.assertEquals("silverVIP_1year", ParameterUtil.getParameter(resultExtInfo, APPLE_PRODUCT_ID));

                    //支付策略
                    List<PayPolicy> payPolicyList = productInfoResult.getPayPolicyList();
                    Assert.assertFalse(CollectionUtils.isEmpty(payPolicyList));
                    PayPolicy resultPayPolicy = payPolicyList.get(0);
                    Assert.assertNotNull(resultPayPolicy);
                    Assert.assertEquals(payPolicy.getChannelType(), resultPayPolicy.getChannelType());


                    //空间策略
                    CapacityPolicy resultCapacityPolicy = productInfoResult.getCapacityPolicy();
                    Assert.assertNotNull(resultCapacityPolicy);
                    Assert.assertEquals(capacityPolicy.getCapacity(), resultCapacityPolicy.getCapacity());
                    Assert.assertEquals(capacityPolicy.getCapacitySwitch(), resultCapacityPolicy.getCapacitySwitch());
                    Assert.assertEquals(capacityPolicy.getCapacityType(), resultCapacityPolicy.getCapacityType());
                    Assert.assertEquals(capacityPolicy.getMcsServiceID(), resultCapacityPolicy.getMcsServiceID());

                    //通知地址列表
                    List<NotifyService> resultNotifyServiceList = productInfoResult.getNotifyServiceList();
                    Assert.assertFalse(CollectionUtils.isEmpty(resultNotifyServiceList));
                    Assert.assertTrue(resultNotifyServiceList.size() == 2);
                    NotifyService resultNotifyService = resultNotifyServiceList.get(0);
                    Assert.assertEquals(notifyService1.getServiceID(), resultNotifyService.getServiceID());
                    Assert.assertEquals(notifyService1.getServiceAddress(), resultNotifyService.getServiceAddress());
                    Assert.assertEquals(notifyService1.getServiceType(), resultNotifyService.getServiceType());
                    Assert.assertEquals(notifyService1.getServiceProtocol(), resultNotifyService.getServiceProtocol());

                    NotifyService resultNotifyService2 = resultNotifyServiceList.get(1);
                    Assert.assertEquals(notifyService2.getServiceID(), resultNotifyService2.getServiceID());
                    Assert.assertEquals(notifyService2.getServiceAddress(), resultNotifyService2.getServiceAddress());
                    Assert.assertEquals(notifyService2.getServiceType(), resultNotifyService2.getServiceType());
                    Assert.assertEquals(notifyService2.getServiceProtocol(), resultNotifyService2.getServiceProtocol());


                    return true;
                })
                .expectComplete()
                .verify();


        //测试修改
        UpdateProductReq updateProductReq = new UpdateProductReq();
        productInfo = new ProductInfo();
        productInfo.setProductID(MEMBER_PRODUCT_ID);
        productInfo.setProductName("测试会员产品");
        productInfo.setCycleType(1);
        productInfo.setChargeType(1);
        productInfo.setProductType(101);
        productInfo.setStatus(3);
        productInfo.setPeriodNum(31);
        productInfo.setOriginalPrice("200");
        productInfo.setObjectID(MEMBER_OBJECT_ID);
        productInfo.setCreateTime("20230809120000");
        productInfo.setEffectiveTime("20230909120000");
        productInfo.setExpiryTime("20230909120000");
        updateProductReq.setProductInfo(productInfo);


        payPolicy.setChannelType(44);
        productInfo.setPayPolicyList(Arrays.asList(payPolicy));


        capacityPolicy.setCapacitySwitch(0);
        capacityPolicy.setCapacity(200);
        capacityPolicy.setCapacityType(2);
        capacityPolicy.setMcsServiceID("McsServiceID_test");
        productInfo.setCapacityPolicy(capacityPolicy);


//        notifyService1.setServiceAddress("http://test_update");
        notifyService1.setServiceProtocol(2);
        notifyService1.setServiceID("serviceId_test_update");
        notifyService1.setServiceType(0);

//        notifyService2.setServiceAddress("http://test2_update");
        notifyService2.setServiceProtocol(3);
        notifyService2.setServiceID("serviceId_test2_update");
        notifyService2.setServiceType(1);

        productInfo.setNotifyServiceList(notifyServiceList);

        ParameterUtil.addParameter(extInfo, APPLE_PRODUCT_ID, "silverVIP_1year_update");
        productInfo.setExtInfo(extInfo);
        updateProductReq.setProductInfo(productInfo);


        StepVerifier.create(productsController.updateProduct(updateProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    UpdateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);

                    return true;
                })
                .expectComplete()
                .verify();


        StepVerifier.create(productsController.queryProductsV2(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsRespV2 resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());

                    ProductInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(MEMBER_PRODUCT_ID, productInfoResult.getProductID());
                    Assert.assertEquals(Integer.valueOf(3), productInfoResult.getStatus());
                    Assert.assertEquals(Integer.valueOf(101), productInfoResult.getProductType());
                    Assert.assertEquals("200", productInfoResult.getOriginalPrice());

                    List<NamedParameter> resultExtInfo = productInfoResult.getExtInfo();
                    Assert.assertFalse(CollectionUtils.isEmpty(resultExtInfo));
                    Assert.assertEquals("silverVIP_1year", ParameterUtil.getParameter(resultExtInfo, APPLE_PRODUCT_ID));

                    //支付策略
                    List<PayPolicy> payPolicyList = productInfoResult.getPayPolicyList();
                    Assert.assertFalse(CollectionUtils.isEmpty(payPolicyList));
                    PayPolicy resultPayPolicy = payPolicyList.get(0);
                    Assert.assertNotNull(resultPayPolicy);
                    Assert.assertEquals(payPolicy.getChannelType(), resultPayPolicy.getChannelType());


                    //空间策略
                    CapacityPolicy resultCapacityPolicy = productInfoResult.getCapacityPolicy();
                    Assert.assertNotNull(resultCapacityPolicy);
                    Assert.assertEquals(capacityPolicy.getCapacity(), resultCapacityPolicy.getCapacity());
                    Assert.assertEquals(capacityPolicy.getCapacitySwitch(), resultCapacityPolicy.getCapacitySwitch());
                    Assert.assertEquals(capacityPolicy.getCapacityType(), resultCapacityPolicy.getCapacityType());
                    Assert.assertEquals(capacityPolicy.getMcsServiceID(), resultCapacityPolicy.getMcsServiceID());

                    //通知地址列表
                    List<NotifyService> resultNotifyServiceList = productInfoResult.getNotifyServiceList();
                    Assert.assertFalse(CollectionUtils.isEmpty(resultNotifyServiceList));
                    Assert.assertTrue(resultNotifyServiceList.size() == 2);
                    NotifyService resultNotifyService = resultNotifyServiceList.get(0);
                    Assert.assertEquals(notifyService1.getServiceID(), resultNotifyService.getServiceID());
                    Assert.assertEquals(notifyService1.getServiceAddress(), resultNotifyService.getServiceAddress());
                    Assert.assertEquals(notifyService1.getServiceType(), resultNotifyService.getServiceType());
                    Assert.assertEquals(notifyService1.getServiceProtocol(), resultNotifyService.getServiceProtocol());

                    NotifyService resultNotifyService2 = resultNotifyServiceList.get(1);
                    Assert.assertEquals(notifyService2.getServiceID(), resultNotifyService2.getServiceID());
                    Assert.assertEquals(notifyService2.getServiceAddress(), resultNotifyService2.getServiceAddress());
                    Assert.assertEquals(notifyService2.getServiceType(), resultNotifyService2.getServiceType());
                    Assert.assertEquals(notifyService2.getServiceProtocol(), resultNotifyService2.getServiceProtocol());


                    return true;
                })
                .expectComplete()
                .verify();

        //测试修改下线
        productInfo.setStatus(4);

        StepVerifier.create(productsController.updateProduct(updateProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    UpdateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);

                    return true;
                })
                .expectComplete()
                .verify();

        // 直接用产品id依然能查出来
        StepVerifier.create(productsController.queryProductsV2(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsRespV2 resp = result.getData();
                    Assert.assertNotNull(resp);
                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());
                    ProductInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(MEMBER_PRODUCT_ID, productInfoResult.getProductID());
                    Assert.assertEquals(Integer.valueOf(4), productInfoResult.getStatus());
                    Assert.assertEquals(Integer.valueOf(101), productInfoResult.getProductType());
                    Assert.assertEquals("200", productInfoResult.getOriginalPrice());
                    return true;
                })
                .expectComplete()
                .verify();


    }

    /**
     * queryProductsV2
     */
    @Test
    public void queryProductsV2() {

        // 入参为goodsSalesType
        QueryProductsReq queryProductsReq = new QueryProductsReq();
//        queryProductsReq.setProductType(GoodsSalesTypeEnum.RIGHTS);

        // 测试入参为provCode
        List<NamedParameter> namedParameterList = new ArrayList<>();
        NamedParameter namedParameter = new NamedParameter();
        // namedParameter.setKey("provCode");
        // namedParameter.setValue("999");

        namedParameter.setKey("account");
        namedParameter.setValue("***********");
        namedParameterList.add(namedParameter);
        NamedParameterList namedParameterList1 = new NamedParameterList();
        namedParameterList1.setNamedParameters(namedParameterList);
        // QueryProductsReq queryProductsReq = new QueryProductsReq();
        queryProductsReq.setExtensionInfo(namedParameterList1);

        // 测试入参为pageInfo
        // QueryProductsReq queryProductsReq = new QueryProductsReq();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNo(1);
        pageInfo.setPageSize(1);
        queryProductsReq.setPageInfo(pageInfo);

        // 测试入参完全为空
        // QueryProductsReq queryProductsReq = new QueryProductsReq();
        //设置idList
        // QueryProductsReq queryProductsReq = new QueryProductsReq();
        // queryProductsReq.setProductIDList(Arrays.asList("test_zzf"));
        queryProductsReq.setProductIDList(Arrays.asList("pp_park_vip_8_monthly_space1"));
        StepVerifier.create(productsController.queryProductsV2(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsRespV2 resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(0, productInfoList.size());

                    return true;
                })
                .expectComplete()
                .verify();
    }

    /**
     * queryProductsByType
     */
    @Test
    public void queryProductsByType() {


        //测试创建
        CreateProductReq createProductReq = new CreateProductReq();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setProductID(MEMBER_PRODUCT_ID);
        productInfo.setProductName("测试会员产品");
        productInfo.setCycleType(1);
        productInfo.setChargeType(1);
        productInfo.setProductType(101);
        productInfo.setStatus(3);
        productInfo.setPeriodNum(30);
        productInfo.setOriginalPrice("250");
        productInfo.setObjectID(MEMBER_OBJECT_ID);
        productInfo.setCreateTime("20230809120000");
        productInfo.setEffectiveTime("20230909120000");
        productInfo.setExpiryTime("20230909120000");
        productInfo.setStatus(3);

        PayPolicy payPolicy = new PayPolicy();
        payPolicy.setChannelType(44);
        productInfo.setPayPolicyList(Arrays.asList(payPolicy));

        CapacityPolicy capacityPolicy = new CapacityPolicy();
        capacityPolicy.setCapacitySwitch(0);
        capacityPolicy.setCapacity(100);
        capacityPolicy.setCapacityType(2);
        capacityPolicy.setMcsServiceID("McsServiceID_test");
        productInfo.setCapacityPolicy(capacityPolicy);

        List<NotifyService> notifyServiceList = new ArrayList<>();
        NotifyService notifyService1 = new NotifyService();
        notifyServiceList.add(notifyService1);
//        notifyService1.setServiceAddress("http://test");
        notifyService1.setServiceProtocol(1);
        notifyService1.setServiceID("serviceId_test");
        notifyService1.setServiceType(0);

        NotifyService notifyService2 = new NotifyService();
        notifyServiceList.add(notifyService2);
//        notifyService2.setServiceAddress("http://test2");
        notifyService2.setServiceProtocol(0);
        notifyService2.setServiceID("serviceId_test2");
        notifyService2.setServiceType(1);

        productInfo.setNotifyServiceList(notifyServiceList);

        List<NamedParameter> extInfo = new ArrayList<>();
        ParameterUtil.addParameter(extInfo, APPLE_PRODUCT_ID, "silverVIP_1year");
        productInfo.setExtInfo(extInfo);
        createProductReq.setProductInfo(productInfo);

        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(MEMBER_PRODUCT_ID, resp.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();


        //校验group插入情况
        LambdaQueryWrapper<GoodsGroupPo> queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, MEMBER_PRODUCT_ID);
        List<GoodsGroupPo> goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(MEMBER_OBJECT_ID, goodsGroupPoList.get(0).getGoodsGroupId());


        // 入参为goodsSalesType
        QueryProductsByTypeReq queryProductsByTypeReq = new QueryProductsByTypeReq();
        queryProductsByTypeReq.setSaleType(101);
        queryProductsByTypeReq.setContractId(MEMBER_PRODUCT_ID);

        // 设置是否需要返回总数
        queryProductsByTypeReq.setIsReturnCnt(1);

        StepVerifier.create(productsController.queryProductsByType(queryProductsByTypeReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProductsByType result:{}", result);
                    QueryProductsByTypeResp resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ContractAllInfo> productInfoList = resp.getContractAllInfoList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());

                    ContractAllInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(MEMBER_PRODUCT_ID, productInfoResult.getContractBasicInfo().getContractID());

                    return true;
                })
                .expectComplete()
                .verify();
    }


    @Test
    public void testCreateAndDeleteMemberProduct() {

        //测试创建
        CreateProductReq createProductReq = new CreateProductReq();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setProductID(MEMBER_PRODUCT_ID);
        productInfo.setProductName("测试会员产品");
        productInfo.setCycleType(1);
        productInfo.setChargeType(1);
        productInfo.setProductType(101);
        productInfo.setStatus(3);
        productInfo.setPeriodNum(30);
        productInfo.setOriginalPrice("250");
        productInfo.setObjectID(MEMBER_OBJECT_ID);
        productInfo.setCreateTime("20230809120000");
        productInfo.setEffectiveTime("20230909120000");
        productInfo.setExpiryTime("20230909120000");
        productInfo.setStatus(3);

        PayPolicy payPolicy = new PayPolicy();
        payPolicy.setChannelType(44);
        productInfo.setPayPolicyList(Arrays.asList(payPolicy));

        CapacityPolicy capacityPolicy = new CapacityPolicy();
        capacityPolicy.setCapacitySwitch(0);
        capacityPolicy.setCapacity(100);
        capacityPolicy.setCapacityType(2);
        capacityPolicy.setMcsServiceID("McsServiceID_test");
        productInfo.setCapacityPolicy(capacityPolicy);

        List<NotifyService> notifyServiceList = new ArrayList<>();
        NotifyService notifyService1 = new NotifyService();
        notifyServiceList.add(notifyService1);
//        notifyService1.setServiceAddress("http://test");
        notifyService1.setServiceProtocol(1);
        notifyService1.setServiceID("serviceId_test");
        notifyService1.setServiceType(0);

        NotifyService notifyService2 = new NotifyService();
        notifyServiceList.add(notifyService2);
//        notifyService2.setServiceAddress("http://test2");
        notifyService2.setServiceProtocol(0);
        notifyService2.setServiceID("serviceId_test2");
        notifyService2.setServiceType(1);

        productInfo.setNotifyServiceList(notifyServiceList);

        List<NamedParameter> extInfo = new ArrayList<>();
        ParameterUtil.addParameter(extInfo, APPLE_PRODUCT_ID, "silverVIP_1year");
        productInfo.setExtInfo(extInfo);
        createProductReq.setProductInfo(productInfo);

        StepVerifier.create(productsController.createProduct(createProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(MEMBER_PRODUCT_ID, resp.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();


        //校验group插入情况
        LambdaQueryWrapper<GoodsGroupPo> queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, MEMBER_PRODUCT_ID);
        List<GoodsGroupPo> goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertNotNull(goodsGroupPoList);
        Assert.assertEquals(1, goodsGroupPoList.size());
        Assert.assertEquals(MEMBER_OBJECT_ID, goodsGroupPoList.get(0).getGoodsGroupId());

        //测试查询
        QueryProductsReq queryProductsReq = new QueryProductsReq();
        queryProductsReq.setProductIDList(Arrays.asList(MEMBER_PRODUCT_ID));

        StepVerifier.create(productsController.queryProductsV2(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsRespV2 resp = result.getData();
                    Assert.assertNotNull(resp);

                    List<ProductInfo> productInfoList = resp.getProductList();
                    Assert.assertNotNull(productInfoList);
                    Assert.assertEquals(1, productInfoList.size());

                    ProductInfo productInfoResult = productInfoList.get(0);
                    Assert.assertEquals(MEMBER_PRODUCT_ID, productInfoResult.getProductID());
                    Assert.assertEquals(Integer.valueOf(101), productInfoResult.getProductType());
                    Assert.assertEquals("250", productInfoResult.getOriginalPrice());

                    List<NamedParameter> resultExtInfo = productInfoResult.getExtInfo();
                    Assert.assertFalse(CollectionUtils.isEmpty(resultExtInfo));
                    Assert.assertEquals("silverVIP_1year", ParameterUtil.getParameter(resultExtInfo, APPLE_PRODUCT_ID));

                    //支付策略
                    List<PayPolicy> payPolicyList = productInfoResult.getPayPolicyList();
                    Assert.assertFalse(CollectionUtils.isEmpty(payPolicyList));
                    PayPolicy resultPayPolicy = payPolicyList.get(0);
                    Assert.assertNotNull(resultPayPolicy);
                    Assert.assertEquals(payPolicy.getChannelType(), resultPayPolicy.getChannelType());


                    //空间策略
                    CapacityPolicy resultCapacityPolicy = productInfoResult.getCapacityPolicy();
                    Assert.assertNotNull(resultCapacityPolicy);
                    Assert.assertEquals(capacityPolicy.getCapacity(), resultCapacityPolicy.getCapacity());
                    Assert.assertEquals(capacityPolicy.getCapacitySwitch(), resultCapacityPolicy.getCapacitySwitch());
                    Assert.assertEquals(capacityPolicy.getCapacityType(), resultCapacityPolicy.getCapacityType());
                    Assert.assertEquals(capacityPolicy.getMcsServiceID(), resultCapacityPolicy.getMcsServiceID());

                    //通知地址列表
                    List<NotifyService> resultNotifyServiceList = productInfoResult.getNotifyServiceList();
                    Assert.assertFalse(CollectionUtils.isEmpty(resultNotifyServiceList));
                    Assert.assertTrue(resultNotifyServiceList.size() == 2);
                    NotifyService resultNotifyService = resultNotifyServiceList.get(0);
                    Assert.assertEquals(notifyService1.getServiceID(), resultNotifyService.getServiceID());
                    Assert.assertEquals(notifyService1.getServiceAddress(), resultNotifyService.getServiceAddress());
                    Assert.assertEquals(notifyService1.getServiceType(), resultNotifyService.getServiceType());
                    Assert.assertEquals(notifyService1.getServiceProtocol(), resultNotifyService.getServiceProtocol());

                    NotifyService resultNotifyService2 = resultNotifyServiceList.get(1);
                    Assert.assertEquals(notifyService2.getServiceID(), resultNotifyService2.getServiceID());
                    Assert.assertEquals(notifyService2.getServiceAddress(), resultNotifyService2.getServiceAddress());
                    Assert.assertEquals(notifyService2.getServiceType(), resultNotifyService2.getServiceType());
                    Assert.assertEquals(notifyService2.getServiceProtocol(), resultNotifyService2.getServiceProtocol());


                    return true;
                })
                .expectComplete()
                .verify();

        //测试删除
        DeleteProductReq deleteProductReq = new DeleteProductReq();
        deleteProductReq.setProductID(MEMBER_PRODUCT_ID);

        StepVerifier.create(productsController.deleteProduct(deleteProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] delete result:{}", result);
                    DeleteProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(MEMBER_PRODUCT_ID, resp.getProductID());

                    return true;
                })
                .expectComplete()
                .verify();


        //校验group删除情况
        queryWrapper = new LambdaQueryWrapper<GoodsGroupPo>()
                .eq(GoodsGroupPo::getGoodsId, MEMBER_PRODUCT_ID);
        goodsGroupPoList = goodsGroupMapper.selectList(queryWrapper);
        Assert.assertTrue(CollectionUtils.isEmpty(goodsGroupPoList));


        //校验校验规则删除情况
        LambdaQueryWrapper<ValidateRulePo> queryValidateRuleWrapper = new LambdaQueryWrapper<ValidateRulePo>()
                .eq(ValidateRulePo::getObjectType, ValidateRuleObjectTypeEnum.GOODS.getType())
                .eq(ValidateRulePo::getObjectId, MEMBER_PRODUCT_ID);
        List<ValidateRulePo> validateRulePoList = validateRuleMapper.selectList(queryValidateRuleWrapper);
        Assert.assertTrue(CollectionUtils.isEmpty(validateRulePoList));


        StepVerifier.create(productsController.queryProductsV2(queryProductsReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] queryProducts result:{}", result);
                    QueryProductsRespV2 resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertTrue(CollectionUtils.isEmpty(resp.getProductList()));

                    return true;
                })
                .expectComplete()
                .verify();


    }

    //@Test
    public void testProductDisplayFunctionality() {
        // 1. Create a product that should be displayed to users
        CreateProductReq displayProductReq = new CreateProductReq();
        ProductInfo displayProductInfo = new ProductInfo();
        displayProductInfo.setProductID(DISPLAY_PRODUCT_ID);
        displayProductInfo.setProductName("Display Product");
        displayProductInfo.setProductType(101);
        displayProductInfo.setStatus(3);
        displayProductInfo.setObjectID(DISPLAY_OBJECT_ID);
        displayProductInfo.setCycleType(TimePlanCycleTypeEnum.RELATIVE_DAY.getType());
        displayProductInfo.setPeriodNum(31);
        displayProductInfo.setChargeType(1);
        displayProductInfo.setOriginalPrice("250");
        displayProductInfo.setCreateTime("20230809120000");
        displayProductInfo.setEffectiveTime("20230909120000");
        displayProductInfo.setExpiryTime("20230909180000");
        displayProductInfo.setUnsubPolicy(2);
        
        ReserveFields displayReserveFields = new ReserveFields();
        displayReserveFields.setReserve4("1"); // Set to display
        displayProductInfo.setReserveFields(displayReserveFields);
        
        displayProductReq.setProductInfo(displayProductInfo);

        // Create the display product
        StepVerifier.create(productsController.createProduct(displayProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create display product result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(DISPLAY_PRODUCT_ID, resp.getProductID());
                    return true;
                })
                .expectComplete()
                .verify();

        // 2. Create a product that should not be displayed to users
        CreateProductReq nonDisplayProductReq = new CreateProductReq();
        ProductInfo nonDisplayProductInfo = new ProductInfo();
        nonDisplayProductInfo.setProductID(NON_DISPLAY_PRODUCT_ID);
        nonDisplayProductInfo.setProductName("Non-Display Product");
        nonDisplayProductInfo.setProductType(101);
        nonDisplayProductInfo.setStatus(3);
        nonDisplayProductInfo.setObjectID(NON_DISPLAY_OBJECT_ID);
        nonDisplayProductInfo.setCycleType(TimePlanCycleTypeEnum.RELATIVE_DAY.getType());
        nonDisplayProductInfo.setPeriodNum(31);
        nonDisplayProductInfo.setChargeType(1);
        nonDisplayProductInfo.setOriginalPrice("250");
        nonDisplayProductInfo.setCreateTime("20230809120000");
        nonDisplayProductInfo.setEffectiveTime("20230909120000");
        nonDisplayProductInfo.setExpiryTime("20230909180000");
        nonDisplayProductInfo.setUnsubPolicy(2);
        
        ReserveFields nonDisplayReserveFields = new ReserveFields();
        nonDisplayReserveFields.setReserve4("0"); // Set to not display
        nonDisplayProductInfo.setReserveFields(nonDisplayReserveFields);
        
        nonDisplayProductReq.setProductInfo(nonDisplayProductInfo);

        // Create the non-display product
        StepVerifier.create(productsController.createProduct(nonDisplayProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create non-display product result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(NON_DISPLAY_PRODUCT_ID, resp.getProductID());
                    return true;
                })
                .expectComplete()
                .verify();

        // 3. Create a product without display field
        CreateProductReq noDisplayFieldProductReq = new CreateProductReq();
        ProductInfo noDisplayFieldProductInfo = new ProductInfo();
        noDisplayFieldProductInfo.setProductID(NO_DISPLAY_FIELD_PRODUCT_ID);
        noDisplayFieldProductInfo.setProductName("No Display Field Product");
        noDisplayFieldProductInfo.setProductType(101);
        noDisplayFieldProductInfo.setStatus(3);
        noDisplayFieldProductInfo.setObjectID(NO_DISPLAY_FIELD_OBJECT_ID);
        noDisplayFieldProductInfo.setCycleType(TimePlanCycleTypeEnum.RELATIVE_DAY.getType());
        noDisplayFieldProductInfo.setPeriodNum(31);
        noDisplayFieldProductInfo.setChargeType(1);
        noDisplayFieldProductInfo.setOriginalPrice("250");
        noDisplayFieldProductInfo.setCreateTime("20230809120000");
        noDisplayFieldProductInfo.setEffectiveTime("20230909120000");
        noDisplayFieldProductInfo.setExpiryTime("20230909180000");
        noDisplayFieldProductInfo.setUnsubPolicy(2);
        
        // No reserve4 field set, which means not display
        ReserveFields noDisplayFieldReserveFields = new ReserveFields();
        noDisplayFieldProductInfo.setReserveFields(noDisplayFieldReserveFields);
        
        noDisplayFieldProductReq.setProductInfo(noDisplayFieldProductInfo);

        // Create the product without display field
        StepVerifier.create(productsController.createProduct(noDisplayFieldProductReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] create no display field product result:{}", result);
                    CreateProductResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertEquals(NO_DISPLAY_FIELD_PRODUCT_ID, resp.getProductID());
                    return true;
                })
                .expectComplete()
                .verify();

        // 4. Query products that should be displayed to users
        QueryProductsReq displayQueryReq = new QueryProductsReq();
        List<NamedParameter> displayQueryExtInfo = new ArrayList<>();
        ParameterUtil.addParameter(displayQueryExtInfo, "isDisplayToUser", "1");
        NamedParameterList namedParameterList = new NamedParameterList();
        namedParameterList.setNamedParameters(displayQueryExtInfo);
        displayQueryReq.setExtensionInfo(namedParameterList);

        StepVerifier.create(productsController.queryProducts(displayQueryReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] query display products result:{}", result);
                    QueryProductsResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertNotNull(resp.getProductList());
                    boolean foundDisplayProduct = resp.getProductList().stream()
                            .anyMatch(p -> DISPLAY_PRODUCT_ID.equals(p.getProductID()));
                    Assert.assertTrue("Should find the display product", foundDisplayProduct);
                    boolean foundNonDisplayProduct = resp.getProductList().stream()
                            .anyMatch(p -> NON_DISPLAY_PRODUCT_ID.equals(p.getProductID()));
                    Assert.assertFalse("Should not find the non-display product", foundNonDisplayProduct);
                    boolean foundNoDisplayFieldProduct = resp.getProductList().stream()
                            .anyMatch(p -> NO_DISPLAY_FIELD_PRODUCT_ID.equals(p.getProductID()));
                    Assert.assertFalse("Should not find the product without display field", foundNoDisplayFieldProduct);
                    return true;
                })
                .expectComplete()
                .verify();

        // 5. Query products that should not be displayed to users
        QueryProductsReq nonDisplayQueryReq = new QueryProductsReq();
        List<NamedParameter> nonDisplayQueryExtInfo = new ArrayList<>();
        ParameterUtil.addParameter(nonDisplayQueryExtInfo, "isDisplayToUser", "0");
        namedParameterList = new NamedParameterList();
        namedParameterList.setNamedParameters(nonDisplayQueryExtInfo);
        nonDisplayQueryReq.setExtensionInfo(namedParameterList);

        StepVerifier.create(productsController.queryProducts(nonDisplayQueryReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] query non-display products result:{}", result);
                    QueryProductsResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertNotNull(resp.getProductList());
                    boolean foundDisplayProduct = resp.getProductList().stream()
                            .anyMatch(p -> DISPLAY_PRODUCT_ID.equals(p.getProductID()));
                    Assert.assertFalse("Should not find the display product", foundDisplayProduct);
                    boolean foundNonDisplayProduct = resp.getProductList().stream()
                            .anyMatch(p -> NON_DISPLAY_PRODUCT_ID.equals(p.getProductID()));
                    Assert.assertTrue("Should find the non-display product", foundNonDisplayProduct);
                    boolean foundNoDisplayFieldProduct = resp.getProductList().stream()
                            .anyMatch(p -> NO_DISPLAY_FIELD_PRODUCT_ID.equals(p.getProductID()));
                    Assert.assertTrue("Should find the product without display field", foundNoDisplayFieldProduct);
                    return true;
                })
                .expectComplete()
                .verify();

        // 6. Query products with invalid display value
        QueryProductsReq invalidDisplayQueryReq = new QueryProductsReq();
        List<NamedParameter> invalidDisplayQueryExtInfo = new ArrayList<>();
        ParameterUtil.addParameter(invalidDisplayQueryExtInfo, "isDisplayToUser", "3");
        NamedParameterList invalidNamedParameterList = new NamedParameterList();
        invalidNamedParameterList.setNamedParameters(invalidDisplayQueryExtInfo);
        invalidDisplayQueryReq.setExtensionInfo(invalidNamedParameterList);

        StepVerifier.create(productsController.queryProducts(invalidDisplayQueryReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] query products with invalid display value result:{}", result);
                    QueryProductsResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertNotNull(resp.getProductList());
                    boolean foundDisplayProduct = resp.getProductList().stream()
                            .anyMatch(p -> DISPLAY_PRODUCT_ID.equals(p.getProductID()));
                    Assert.assertTrue("Should find the display product when querying all products", foundDisplayProduct);
                    boolean foundNonDisplayProduct = resp.getProductList().stream()
                            .anyMatch(p -> NON_DISPLAY_PRODUCT_ID.equals(p.getProductID()));
                    Assert.assertTrue("Should find the non-display product when querying all products", foundNonDisplayProduct);
                    boolean foundNoDisplayFieldProduct = resp.getProductList().stream()
                            .anyMatch(p -> NO_DISPLAY_FIELD_PRODUCT_ID.equals(p.getProductID()));
                    Assert.assertTrue("Should find the product without display field when querying all products", foundNoDisplayFieldProduct);
                    return true;
                })
                .expectComplete()
                .verify();

        // 7. Query all products without extension fields
        QueryProductsReq allProductsQueryReq = new QueryProductsReq();
        // No extension info set, which means query all products

        StepVerifier.create(productsController.queryProducts(allProductsQueryReq))
                .expectNextMatches(result -> {
                    log.info("[TEST] query all products result:{}", result);
                    QueryProductsResp resp = result.getData();
                    Assert.assertNotNull(resp);
                    Assert.assertNotNull(resp.getProductList());
                    boolean foundDisplayProduct = resp.getProductList().stream()
                            .anyMatch(p -> DISPLAY_PRODUCT_ID.equals(p.getProductID()));
                    Assert.assertTrue("Should find the display product when querying all products", foundDisplayProduct);
                    boolean foundNonDisplayProduct = resp.getProductList().stream()
                            .anyMatch(p -> NON_DISPLAY_PRODUCT_ID.equals(p.getProductID()));
                    Assert.assertTrue("Should find the non-display product when querying all products", foundNonDisplayProduct);
                    boolean foundNoDisplayFieldProduct = resp.getProductList().stream()
                            .anyMatch(p -> NO_DISPLAY_FIELD_PRODUCT_ID.equals(p.getProductID()));
                    Assert.assertTrue("Should find the product without display field when querying all products", foundNoDisplayFieldProduct);
                    return true;
                })
                .expectComplete()
                .verify();
    }
}

