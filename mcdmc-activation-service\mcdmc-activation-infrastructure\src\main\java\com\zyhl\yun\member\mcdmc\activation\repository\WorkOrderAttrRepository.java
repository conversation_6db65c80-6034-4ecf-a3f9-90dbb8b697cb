package com.zyhl.yun.member.mcdmc.activation.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.yun.member.mcdmc.activation.mapper.WorkOrderAttrMapper;
import com.zyhl.yun.member.mcdmc.activation.po.WorkOrderAttrPo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/06/04 19:20
 */
@Repository
public class WorkOrderAttrRepository extends ServiceImpl<WorkOrderAttrMapper, WorkOrderAttrPo> {

    public void insertBatch(List<WorkOrderAttrPo> list) {
        super.saveBatch(list);
    }
}
