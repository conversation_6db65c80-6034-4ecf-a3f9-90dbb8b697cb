package com.zyhl.yun.member.mcdmc.activation.remote.send.req;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 能力开放平台号码校验接口请求对象
 *
 * <AUTHOR>
 * @since 2019-12-12
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "NumberStateCheckReq")
public class OpenNumberStateCheckReq {
    /**
     * 消息类型，默认NumberStateCheckReq
     */
    @XmlElement(name = "MsgType")
    private String msgType = "NumberStateCheckReq";

    /**
     * 业务号码类型(1-手机号；2-宽带号)
     */
    @XmlElement(name = "NumType")
    private String numType;

    /**
     * 业务号码(手机号长度为11位)
     */
    @XmlElement(name = "Number")
    private String number;

    /**
     * 该接口消息的版本号
     */
    @XmlElement(name = "Version")
    private String version;

    @Override
    public String toString() {
        return "NumberStateCheckReq [msgType=" +
                msgType +
                ", version=" +
                version +
                ", numType=" +
                numType +
                ", number=" +
                number +
                "]";
    }
}