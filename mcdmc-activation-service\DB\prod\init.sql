-- bmsuite订购流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(1, 'bmsuiteSubServiceCode', 'bmsuite订购流程', 'bmsuiteSubFlow', 0, 3, 3, '10', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(1, 1, 'bmsuiteSubServiceCode', 'bmsuiteSubIFace', 'bmsuiteSubIFace', '0', 'text/xml', 'http://10.19.4.70:18089/bmsuite/richlifeApp/devapp/bmsuite.IUser', NULL, '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- bmsuite退订流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(2, 'bmsuiteUnSubServiceCode', 'bmsuite退订流程', 'bmsuiteUnSubFlow', 0, 3, 3, '10', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(2, 2, 'bmsuiteUnSubServiceCode', 'bmsuiteUnSubIFace', 'bmsuiteUnSubIFace', '0', 'text/xml', 'http://10.19.4.70:18089/bmsuite/richlifeApp/devapp/bmsuite.IUser', NULL, '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- bmsuite续订流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(3, 'bmsuiteRenewServiceCode', 'bmsuite续订流程', 'bmsuiteRenewFlow', 0, 3, 3, '10', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(3, 3, 'bmsuiteRenewServiceCode', 'bmsuiteRenewIFace', 'bmsuiteRenewIFace', '0', 'text/xml', 'http://10.19.4.70:18089/bmsuite/richlifeApp/devapp/bmsuite.IUser', NULL, '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- bmsuite退费流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(4, 'bmsuiteRefundServiceCode', 'bmsuite退费流程', 'bmsuiteRefundFlow', 0, 3, 3, '10', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(4, 4, 'bmsuiteRefundServiceCode', 'bmsuiteRenewIFace', 'bmsuiteRenewIFace', '0', 'text/xml', 'http://10.19.4.70:18089/bmsuite/richlifeApp/devapp/bmsuite.IUser', NULL, '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- 普通权益订购流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(5, 'normalRaiSubServiceCode', '普通权益订购流程', 'raiSubNormalFlow', 0, 3, 4, '5', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(5, 5, 'normalRaiSubServiceCode', 'raiSubNormalIFace', 'raiSubNormalIFace', '0', 'application/json', 'http://10.50.5.14:80/cmpp-api/externalPlus/v4/orderAccept', '/cloudSEE/openApi/raiNotifyEquityResult', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- 畅享权益订购流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(6, 'changXRaiSubServiceCode', '畅享权益订购流程', 'raiSubChangXFlow', 0, 3, 4, '5', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(6, 6, 'changXRaiSubServiceCode', 'raiSubChangXIFace', 'raiSubChangXIFace', '0', 'application/json', 'http://10.50.5.14:80/cmpp-api/externalPlus/v4/orderAccept', '/cloudSEE/openApi/raiNotifyEquityResult', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- 畅影权益订购流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(7, 'movieRaiSubServiceCode', '畅影权益订购流程', 'raiSubMovieFlow', 0, 3, 4, '5', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(7, 7, 'movieRaiSubServiceCode', 'raiSubMovieIFace', 'raiSubMovieIFace', '0', 'application/json', 'http://123.206.60.179:8082/cloudmovieapi/sub/order_accept', '/cloudSEE/openApi/notifyMovieEquityRightsResult', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- t3权益订购流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(8, 't3RaiSubServiceCode', 't3权益订购流程', 'raiSubT3Flow', 0, 3, 4, '5', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(8, 8, 't3RaiSubServiceCode', 'raiSubT3IFace', 'raiSubT3IFace', '0', 'application/json', 'http://10.50.5.14:80/cmpp-api/externalPlus/v4/orderAccept', '/cloudSEE/openApi/raiNotifyEquityResult', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- 普通权益主动查询状态流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(9, 'raiQueryNormalServiceCode', '普通权益主动查询状态流程', 'raiQueryNormalFlow', 0, 3, 1, '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(9, 9, 'raiQueryNormalServiceCode', 'raiQueryNormalIFace', 'raiQueryNormalIFace', '0', 'application/json', 'http://10.50.5.14:80/cmpp-api/externalPlus/v4/queryOrders', '', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');


-- 明威/畅影权益主动查询状态流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(10, 'raiQueryMovieServiceCode', '明威/畅影权益主动查询状态流程', 'raiQueryMovieFlow', 0, 3, 1, '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(10, 10, 'raiQueryMovieServiceCode', 'raiQueryMovieIFace', 'raiQueryMovieIFace', '0', 'application/json', 'http://123.206.60.179:8082/cloudmovieapi/sub/query_order', '', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- 普通权益退费流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(11, 'raiNormalRefundServiceCode', '普通权益退费流程', 'raiRefundNormalFlow', 0, 0, -1, '', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(11, 11, 'raiNormalRefundServiceCode', 'raiRefundNormalIFace', 'raiRefundNormalIFace', '0', 'application/json', 'http://10.50.5.14:80/cmpp-api/external/rightsSalesReturn', '/cloudSEE/openApi/notifyRightsSalesReturnResult', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- 内容相关权益退费流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(12, 'raiChanYinRefundServiceCode', '内容相关权益退费流程(给内容发送mq）', 'raiRefundChangYingFlow', 0, 0, -1, '', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(12, 12, 'raiChanYinRefundServiceCode', 'raiRefundChangYingIFace', 'raiRefundChangYingIFace', '0', 'application/json', 'http://10.50.5.14:80/cmpp-api/external/rightsSalesReturn', '/cloudSEE/openApi/notifyRightsSalesReturnResult', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');


-- 权益订单核减流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(13, 'raiOrderReductionServiceCode', '权益订单核减流程', 'raiOrderReductionFlow', 0, 3, 4, '1', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(13, 13, 'raiOrderReductionServiceCode', 'raiOrderReductionIFace', 'raiOrderReductionIFace', '0', 'application/json', 'http://10.50.5.14:80/cmpp-api/external/rightsSalesReturn', '/cloudSEE/openApi/notifyRightsSalesReturnResult', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- 权益补退流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(14, 'raiOrderReUnSubServiceCode', '权益补退流程', 'raiOrderReUnSubFlow', 0, 3, 4, '1', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(14, 14, 'raiOrderReUnSubServiceCode', 'raiOrderReUnSubIFace', 'raiOrderIncreaseIFace', '0', 'application/json', 'http://10.50.5.14:80/cmpp-api/external/rightsSalesReturn', '/cloudSEE/openApi/notifyRightsSalesReturnResult', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- 权益核增流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(15, 'raiOrderIncreaseServiceCode', '权益核增流程', 'raiOrderIncreaseFlow', 0, 3, 4, '5', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(15, 15, 'raiOrderIncreaseServiceCode', 'raiOrderIncreaseIFace', 'raiOrderIncreaseIFace', '0', 'application/json', 'http://10.50.5.14:80/cmpp-api/externalPlus/v4/orderAccept', '/cloudSEE/openApi/raiNotifyEquityResult', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- 权益核增流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(16, 'raiOrderRePushServiceCode', '权益重推流程', 'raiOrderRePushFlow', 0, 3, 4, '1', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(16, 16, 'raiOrderRePushServiceCode', 'raiOrderRePushIFace', 'raiOrderRePushIFace', '0', 'application/json', 'http://10.50.5.14:80/cmpp-api/externalPlus/v4/orderAccept', '/cloudSEE/openApi/raiNotifyEquityResult', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- 停车退订流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(17, 'parkUnSubServiceCode', '停车退订流程', 'parkUnSubFlow', 0, 0, -1, '', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(17, 17, 'parkUnSubServiceCode', 'parkUnSubIFace', 'parkUnSubIFace', '0', 'application/json', 'https://39.108.161.49:9019/bus/quanyi/refundYpOrder', NULL, '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- 一级能开免流订购流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(18, 'openSyncServiceCode', '一级能开免流订购流程', 'openSyncFlow', 0, 3, 6, '1', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(18, 18, 'openSyncServiceCode', 'openNumberCheckIFace', 'openNumberCheckIFace', '0', 'application/xml', 'https://cust.h5cmpassport.com/ifp/flowCheckAction/numberStateCheck.service', NULL, '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(19, 18, 'openSyncServiceCode', 'openFreeFlowCheckIFace', 'openFreeFlowCheckIFace', '0', 'application/xml', 'https://cust.h5cmpassport.com/ifp/flowCheckAction/bizQualification.service', NULL, '1', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(20, 18, 'openSyncServiceCode', 'openFreeFlowSyncIFace', 'openFreeFlowSyncIFace', '0', 'application/json', 'https://cust.h5cmpassport.com/ifp/flowOrderAction/intergratedSalesOrderSync.service', '/cloudSEE/openApi/salesorderResult', '2', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- 一级能开免流退订流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(19, 'openReturnServiceCode', '一级能开免流退订流程', 'openReturnFlow', 0, 3, 4, '10', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(21, 19, 'openReturnServiceCode', 'openReturnIFace', 'openReturnIFace', '0', 'application/json', 'https://cust.h5cmpassport.com/ifp/ReturnsyncAction/returnsyncForAll.service', '/cloudSEE/openApi/flowCardOrderResult', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- 一级能开免流退费流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(20, 'openRefundServiceCode', '一级能开免流退费流程', 'openRefundFlow', 0, 3, 4, '10', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(22, 20, 'openRefundServiceCode', 'openReturnIFace', 'openReturnIFace', '0', 'application/json', 'https://cust.h5cmpassport.com/ifp/ReturnsyncAction/returnsyncForAll.service', '/cloudSEE/openApi/flowCardOrderResult', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');

-- 集运免流流程
INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(21, 'jiyunFlowSpecServiceCode', '集运免流流程', 'jiyunFlowSpecFlow', 0, 3, 4, '5', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
INSERT INTO activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_flag)
VALUES(23, 21, 'jiyunFlowSpecServiceCode', 'jiyunFlowSpecIFace', 'jiyunFlowSpecIFace', '0', 'application/json', 'https://dev.coc.10086.cn/coc/gateway/api/common/activity/order/addOrder', '/cloudSEE/openApi/subscribeFlowSpecNotify', '0', 1, now(), now(), 'hmq', 'tenantA', b'0000000000000000000000000000000000000000000000000000000000000001');
