package com.zyhl.yun.member.mcdmc.activation.domain.mq.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 流程重试mq配置
 *
 * <AUTHOR>
 * @since 2024/07/24 10:40
 */
@Data
@Configuration
@ConfigurationProperties("rocketmq.consumer.flow-retry")
public class FlowRetryMqProperties {

    /**
     * 流程重试topic
     */
    private String retryTopic;
    /**
     * 流程重试tag
     */
    private String retryTag;

}
