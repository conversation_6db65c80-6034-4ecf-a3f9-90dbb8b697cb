package com.zyhl.yun.member.application.jiyun.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.zyhl.yun.member.application.jiyun.JiyunFlowSpecApplication;
import com.zyhl.yun.member.common.OperatorEnum;
import com.zyhl.yun.member.common.ResultCodeEnum;
import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.constants.ErrorCode;
import com.zyhl.yun.member.common.constants.JiyunConstants;
import com.zyhl.yun.member.common.constants.NumberConstant;
import com.zyhl.yun.member.common.enums.JiyunSwitchEnum;
import com.zyhl.yun.member.common.enums.UserStatusEnum;
import com.zyhl.yun.member.domain.jiyun.domain.FlowSpecSubscriptionDo;
import com.zyhl.yun.member.domain.user.domain.QueryUserCondition;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.dto.GivenPrdOrderRelQueryResp;
import com.zyhl.yun.member.facade.UserServiceFacade;
import com.zyhl.yun.member.jiyun.service.JiyunFlowSpecDomainService;
import com.zyhl.yun.member.jiyun.service.convertor.JiyunFlowToDoConvertor;
import com.zyhl.yun.member.jiyun.service.gateway.JiyunFlowSpecGateWay;
import com.zyhl.yun.member.jiyun.service.property.JiyunProperties;
import com.zyhl.yun.member.utils.RedisUtils;
import com.zyhl.yun.member.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/4 11:53
 * @descrition 0元30G免流需求开发
 */
@Service
@Slf4j
public class JiyunFlowSpecApplicationImpl implements JiyunFlowSpecApplication {

    @Resource
    private JiyunProperties jiyunProperties;

    @Resource
    private JiyunFlowSpecDomainService jiyunFlowSpecDomainService;

    @Resource
    private RedisUtils redisUtils;
//    @Resource
//    private RedisTemplate<String,Object> redisTemplate;

    @Resource
    private JiyunFlowSpecGateWay jiyunFlowSpecGateWay;

    @Resource
    private JiyunFlowToDoConvertor jiyunFlowToDoConvertor;


    @Override
    public SubscribeFlowSpecResp subscribeFlowSpec(SubscribeFlowSpecReqVo subscribeFlowSpecReqVo) {
        //log.info("method subscribeFlowSpec begin!");
        if (log.isDebugEnabled()) {
            log.debug("[app] subscribeFlowSpec receive request body:{}", subscribeFlowSpecReqVo);
        }
        String userId = null;
        String userDomainId = null;
        //1、查询用户手机号运营商
        if (NumberConstant.ONE.equals(jiyunProperties.getCmccSwitch())) {
            UserDo userDo = jiyunFlowSpecDomainService.queryUserIDByAccount(subscribeFlowSpecReqVo.getAccount());
            userId = userDo.getUserId();
            userDomainId = userDo.getUserDomainId();
        }
        subscribeFlowSpecReqVo.setUserId(userId);
        //2、查询免流状态是否已存在或者限流等
        SubscribeFlowSpecResp subscribeFlowSpecResp = this.querySubscribeFlowCache(subscribeFlowSpecReqVo);

        //3、判断免流状态，如果已经开通，则直接返回；否则往下调用集运订购接口开通
        if (ObjectUtils.isNotNull(subscribeFlowSpecResp) &&
                StringUtils.isNotEmpty(subscribeFlowSpecResp.getResultCode()) &&
                CharSequenceUtil.isBlank(subscribeFlowSpecResp.getSubscribeFlowSpecRespList().get(0).getSourceOrderNo())) {
            return subscribeFlowSpecResp;
        }

        //3、调用服开集运平台接口，进行免流订购addorder、 使用mq新模型调用服开就开通免流部分
        jiyunFlowSpecDomainService.addOrderServiceOpen(subscribeFlowSpecReqVo, subscribeFlowSpecResp, userDomainId);
        //4、查询数据库关系订购关系
        FlowSpecSubscriptionDo flowSpecSubscriptionDo = jiyunFlowSpecDomainService.queryJiyunFlowSpecInfo
                (subscribeFlowSpecReqVo.getUserId(), subscribeFlowSpecReqVo.getSourceGoodsId());

        //5、构造响应
        SubscribeFlowSpecResp subscribeFlowSpecRsp = jiyunFlowSpecDomainService.buildSubscribeFlowSpecRespVo(flowSpecSubscriptionDo);

        if (log.isDebugEnabled()) {
            log.debug("subscribeFlowSpec receive response body:" + subscribeFlowSpecRsp);
        }
        //log.info("method subscribeFlowSpec end!");
        return subscribeFlowSpecRsp;
    }

    @Override
    public SubscribeFlowSpecResp subscribeFlowSpecV2(SubscribeFlowSpecReqVo subscribeFlowSpecReqVo) {
        //log.info("method subscribeFlowSpecV2 begin!");
        if (log.isDebugEnabled()) {
            log.info("subscribeFlowSpecV2 receive request body:" + subscribeFlowSpecReqVo);
        }

        String userId = null;
        String userDomainId = null;
        //1、查询用户手机号运营商
        if (NumberConstant.ONE.equals(jiyunProperties.getCmccSwitch())) {
            UserDo userDo = jiyunFlowSpecDomainService.queryUserIDByAccount(subscribeFlowSpecReqVo.getAccount());
            userId = userDo.getUserId();
            userDomainId = userDo.getUserDomainId();
        }
        subscribeFlowSpecReqVo.setUserId(userId);
        //2、查询免流状态是否已存在或者限流等
        SubscribeFlowSpecResp subscribeFlowSpecResp = this.querySubscribeFlowCache(subscribeFlowSpecReqVo);
        if (ObjectUtils.isNotNull(subscribeFlowSpecResp) &&
                StringUtils.isNotEmpty(subscribeFlowSpecResp.getResultCode()) &&
                CharSequenceUtil.isBlank(subscribeFlowSpecResp.getSubscribeFlowSpecRespList().get(0).getSourceOrderNo())) {
            return subscribeFlowSpecResp;
        }

        //3、调用集运平台下单接口  使用mq新模型调用服开就开通免流部分 +重试功能
        // 如果开启重试开关&&集运接口限流&&没有超过重试次数，则进行订购重试
        jiyunFlowSpecDomainService.addOrderServiceOpen(subscribeFlowSpecReqVo, subscribeFlowSpecResp, userDomainId);
        //4、查询数据库订购关系
        FlowSpecSubscriptionDo flowSpecSubscriptionDo = jiyunFlowSpecDomainService.queryJiyunFlowSpecInfo(subscribeFlowSpecReqVo.getUserId(), subscribeFlowSpecReqVo.getSourceGoodsId());

        //5、构造响应
        SubscribeFlowSpecResp subscribeFlowSpecRsp = jiyunFlowSpecDomainService.buildSubscribeFlowSpecRespVo(flowSpecSubscriptionDo);

        if (log.isDebugEnabled()) {
            log.debug("subscribeFlowSpec receive response body:" + subscribeFlowSpecRsp);
        }
        //log.info("method subscribeFlowSpecV2 end!");
        return subscribeFlowSpecRsp;
    }

    @Override
    public QuerySubscribeFlowSpecResp querySubscribeFlowSpec(QuerySubscribeFlowSpecReqVO querySubscribeFlowSpecReqVO) {
        //log.info("method querySubscribeFlowSpec begin!");
        if (log.isDebugEnabled()) {
            log.debug("querySubscribeFlowSpec receive request body:" + querySubscribeFlowSpecReqVO);
        }
        String userId = querySubscribeFlowSpecReqVO.getUserId();
        String userDomainId = null;
        //1、查询用户手机号运营商
        if (CharSequenceUtil.isEmpty(userId) && NumberConstant.ONE.equals(jiyunProperties.getCmccSwitch())) {
            UserDo userDo = jiyunFlowSpecDomainService.queryUserIDByAccount(querySubscribeFlowSpecReqVO.getAccount());
            userId = userDo.getUserId();
            userDomainId = userDo.getUserDomainId();
        }
        querySubscribeFlowSpecReqVO.setUserId(userId);
        //2、查询集运接口可配置信息
        Map<String, String> jiyunMap = getActivityIdBySkuId(querySubscribeFlowSpecReqVO.getSourceGoodsId());

        //3、调用集运平台查询订购关系
        GivenPrdOrderRelQueryResp givenPrdOrderRelQueryResp =
                jiyunFlowSpecGateWay.givenPrdOrderRelQuery(jiyunFlowSpecDomainService.buildGivenPrdOrderRelQueryReq(querySubscribeFlowSpecReqVO, jiyunProperties.getJiyunGoodsID()));

        //4、组装构造集运响应报文
        givenPrdOrderRelQueryResp = jiyunFlowSpecDomainService.analyGivenPrdOrderRel(givenPrdOrderRelQueryResp);

        //5、查询数据库免流信息
        FlowSpecSubscriptionDo flowSpecSubscriptionDo = jiyunFlowSpecDomainService.queryJiyunFlowSpecInfo(querySubscribeFlowSpecReqVO.getUserId(), querySubscribeFlowSpecReqVO.getSourceGoodsId());

        //6、刷新数据库更新免流信息
        flowSpecSubscriptionDo = jiyunFlowSpecDomainService.refreshJiyunFlowSpecInfo(flowSpecSubscriptionDo, givenPrdOrderRelQueryResp, jiyunMap, querySubscribeFlowSpecReqVO);

        //7、构建接口响应报文
        QuerySubscribeFlowSpecResp querySubscribeFlowSpecResp = jiyunFlowSpecDomainService.buildProductRelationResp(flowSpecSubscriptionDo, givenPrdOrderRelQueryResp, jiyunMap);

        //log.info("querySubscribeFlowSpec receive response " + querySubscribeFlowSpecResp);
        if (log.isDebugEnabled()) {
            log.debug("querySubscribeFlowSpec receive response body:" + querySubscribeFlowSpecResp);
        }
        //log.info("method querySubscribeFlowSpec end!");
        return querySubscribeFlowSpecResp;
    }

    @Override
    public QuerySubscribeFlowSpecResp querySubscribeFlowSpecV2(QuerySubscribeFlowSpecReqVO querySubscribeFlowSpecReqVO) {
        //log.info("method querySubscribeFlowSpecV2 begin!");
        if (log.isDebugEnabled()) {
            log.debug("querySubscribeFlowSpecV2 receive request body:" + querySubscribeFlowSpecReqVO);
        }
        String userId = querySubscribeFlowSpecReqVO.getUserId();
        String userDomainId = null;

        //1、查询用户手机号运营商
        if (CharSequenceUtil.isEmpty(userId) && NumberConstant.ONE.equals(jiyunProperties.getCmccSwitch())) {
            UserDo userDo = jiyunFlowSpecDomainService.queryUserIDByAccount(querySubscribeFlowSpecReqVO.getAccount());
            userId = userDo.getUserId();
            userDomainId = userDo.getUserDomainId();
        }
        querySubscribeFlowSpecReqVO.setUserId(userId);

        //2、查询集运接口可配置信息
        Map<String, String> jiyunMap = getActivityIdBySkuId(querySubscribeFlowSpecReqVO.getSourceGoodsId());

        //3、调用集运平台查询订购关系
        GivenPrdOrderRelQueryResp givenPrdOrderRelQueryResp = jiyunFlowSpecGateWay.givenPrdOrderRelQuery(jiyunFlowSpecDomainService.buildGivenPrdOrderRelQueryReq(querySubscribeFlowSpecReqVO, jiyunProperties.getJiyunGoodsID()));

        //4、组装构造集运响应报文
        givenPrdOrderRelQueryResp = jiyunFlowSpecDomainService.analyGivenPrdOrderRel(givenPrdOrderRelQueryResp);

        //5、查询数据库免流信息
        FlowSpecSubscriptionDo flowSpecSubscriptionDo = jiyunFlowSpecDomainService.queryJiyunFlowSpecInfo(querySubscribeFlowSpecReqVO.getUserId(), querySubscribeFlowSpecReqVO.getSourceGoodsId());

        //6、刷新数据库更新免流信息
        flowSpecSubscriptionDo = jiyunFlowSpecDomainService.refreshJiyunFlowSpecInfo(flowSpecSubscriptionDo, givenPrdOrderRelQueryResp, jiyunMap, querySubscribeFlowSpecReqVO);

        //7、构建接口响应报文
        QuerySubscribeFlowSpecResp querySubscribeFlowSpecResp = jiyunFlowSpecDomainService.buildProductRelationRespV2(givenPrdOrderRelQueryResp, flowSpecSubscriptionDo, jiyunMap);

        //log.info("querySubscribeFlowSpecV2 receive response " + querySubscribeFlowSpecResp);
        if (log.isDebugEnabled()) {
            log.debug("querySubscribeFlowSpecV2 receive response body:" + querySubscribeFlowSpecResp);
        }
        //log.info("method querySubscribeFlowSpecV2 end!");
        return querySubscribeFlowSpecResp;
    }

    @Override
    public QuerySubscribeFlowSpecV3Resp querySubscribeFlowSpecV3(QuerySubscribeFlowSpecReqVO querySubscribeFlowSpecReqVO) {
        //log.info("method querySubscribeFlowSpecV3 begin!");
        if (log.isDebugEnabled()) {
            log.debug("querySubscribeFlowSpecV3 receive request body:" + querySubscribeFlowSpecReqVO);
        }
        String userId = querySubscribeFlowSpecReqVO.getUserId();
//        String userDomainId = null;
        //1、查询用户手机号运营商
        if (CharSequenceUtil.isEmpty(userId) && JiyunSwitchEnum.ON.getCode().equals(jiyunProperties.getCmccSwitch())) {
            QueryUserCondition queryUserCondition = new QueryUserCondition();
            queryUserCondition.setAccount(querySubscribeFlowSpecReqVO.getAccount());
            queryUserCondition.setStatusList(Collections.singletonList(UserStatusEnum.NORMAL));
            UserDo userDo = UserServiceFacade.queryUser(queryUserCondition);
            if (ObjectUtils.isEmpty(userDo)) {
                log.error("[APP] querySubscribeFlowSpecV3 failed. user not found. account: {}", querySubscribeFlowSpecReqVO.getAccount());
                throw new ServiceException(ResultCodeEnum.USER_NOT_FOUND.getResultCode(),
                        ResultCodeEnum.USER_NOT_FOUND.getResultDesc());
            }
            OperatorEnum operator = userDo.getOperator();
            if (!OperatorEnum.CMCC.equals(operator)) {
                log.error("[APP] not a cmcc user. user not found. account: {}, operator: {}", querySubscribeFlowSpecReqVO.getAccount(), operator);
                throw new ServiceException(ResultCodeEnum.USER_NOT_CMCC_USER.getResultCode(),
                        ResultCodeEnum.USER_NOT_CMCC_USER.getResultDesc());
            }
            userId = userDo.getUserId();
//            userDomainId = userDo.getUserDomainId();
        }
        querySubscribeFlowSpecReqVO.setUserId(userId);

        //2、查询集运接口可配置信息
        Map<String, String> jiyunMap = getActivityIdBySkuId(querySubscribeFlowSpecReqVO.getSourceGoodsId());


        //3、调用集运平台查询订购关系
        GivenPrdOrderRelQueryResp givenPrdOrderRelQueryResp = jiyunFlowSpecGateWay.givenPrdOrderRelQuery(jiyunFlowSpecDomainService.buildGivenPrdOrderRelQueryReq(querySubscribeFlowSpecReqVO, jiyunProperties.getJiyunGoodsID()));

        //4、组装构造集运响应报文
        givenPrdOrderRelQueryResp = jiyunFlowSpecDomainService.analyGivenPrdOrderRel(givenPrdOrderRelQueryResp);

        //5、查询数据库免流信息
        FlowSpecSubscriptionDo flowSpecSubscriptionDo = jiyunFlowSpecDomainService.queryJiyunFlowSpecInfo(querySubscribeFlowSpecReqVO.getUserId(), querySubscribeFlowSpecReqVO.getSourceGoodsId());

        //6、刷新数据库更新免流信息
        flowSpecSubscriptionDo = jiyunFlowSpecDomainService.refreshJiyunFlowSpecInfo(flowSpecSubscriptionDo, givenPrdOrderRelQueryResp, jiyunMap, querySubscribeFlowSpecReqVO);

        //7、构建接口响应报文
        QuerySubscribeFlowSpecV3Resp querySubscribeFlowSpecV3Resp = jiyunFlowSpecDomainService.buildProductRelationRespV3(givenPrdOrderRelQueryResp, flowSpecSubscriptionDo, jiyunMap);

        //log.info("querySubscribeFlowSpecV3 receive response " + querySubscribeFlowSpecV3Resp);
        if (log.isDebugEnabled()) {
            log.debug("querySubscribeFlowSpecV3 receive response body:" + querySubscribeFlowSpecV3Resp);
        }
        //log.info("method querySubscribeFlowSpecV3 end!");
        return querySubscribeFlowSpecV3Resp;
    }

    /**
     * 查询免流缓存
     *
     * @param subscribeFlowSpecReqVo
     * @return
     */
    private SubscribeFlowSpecResp querySubscribeFlowCache(SubscribeFlowSpecReqVo subscribeFlowSpecReqVo) {
        QuerySubscribeFlowSpecReqVO querySubscribeFlowSpecReqVO = jiyunFlowToDoConvertor.sub2Query(subscribeFlowSpecReqVo);
        QuerySubscribeFlowSpecResp querySubscribeFlowSpecResp = querySubscribeFlowSpec(querySubscribeFlowSpecReqVO);
        //判断是否已经开通过
        if (ObjectUtils.isNotNull(querySubscribeFlowSpecResp) &&
                CollectionUtils.isNotEmpty(querySubscribeFlowSpecResp.getProductRelations()) &&
                JiyunConstants.ALREADY_ORDER.equals(querySubscribeFlowSpecResp.getProductRelations().get(0).getIsOrder())) {
            return jiyunFlowSpecDomainService.buildSubscribeFlowSpecRespV2(subscribeFlowSpecReqVo.getAccount());
        }
        //2023-2-7 新增免流限制请求频率需求
        subscribeFlowlimitCount(subscribeFlowSpecReqVo, jiyunProperties.getFlowSpecRedisKey(), jiyunProperties.getFlowSpecRedisExpirationTime());
        return jiyunFlowSpecDomainService.buildSubscribeFlowSpecResp(querySubscribeFlowSpecResp.getProductRelations().get(0).getSourceOrderNo());
    }


    /**
     * 免流接口优化需求新增限制点击免流错误次数 缓存1h
     *
     * @param subscribeFlowSpecReqVo
     */
    private void subscribeFlowlimitCount(SubscribeFlowSpecReqVo subscribeFlowSpecReqVo, String flowSpecRedisKey, String flowSpecRedisExpirationTime) {
        //log.info("method subscribeFlowlimitCount begin!");
        //1.查询redis是否存在限制
        String subscribeFlowSpecKey = String.format(flowSpecRedisKey, subscribeFlowSpecReqVo.getAccount(), subscribeFlowSpecReqVo.getSourceGoodsId());
        String subscribeFlowSpecRedisValue = (String) redisUtils.get(subscribeFlowSpecKey);
//        String subscribeFlowSpecRedisValue = redisClusterUtil.getString(subscribeFlowSpecKey);
        if (CharSequenceUtil.isEmpty(subscribeFlowSpecRedisValue)) {
            //2.为空处理写入redis 1h
            int lockExpireTimes = Integer.parseInt(flowSpecRedisExpirationTime) * 3600;
            redisUtils.set(subscribeFlowSpecKey, "keys is exist !", lockExpireTimes);
//            redisClusterUtil.setStringWithExpiry(subscribeFlowSpecKey, "keys is exist !", lockExpireTimes);

            return;
        }
        //log.info("method subscribeFlowlimitCount end!");
        //3.存在redis限制内,返回特定错误码
        throw new ServiceException(ErrorCode.SUBSCRIBE_FLOW_INTERFACE_LIMIT_CODE, ErrorCode.SUBSCRIBE_FLOW_INTERFACE_LIMIT_CODE_ERROR_MESSAGE);
    }

    /**
     * 根据sourceGoodsId获得集运活动id等信息
     *
     * @param sourceGoodsId
     * @return
     */
    private Map<String, String> getActivityIdBySkuId(String sourceGoodsId) {
        //log.info("method getActivityIdBySkuId begin!");
        Map<String, String> jiyunMap = new HashMap<>();
        String skuIdActivityIds = jiyunProperties.getSkuIdActivityIdMapConfig();
        String activityIdActivityName = jiyunProperties.getActivityIdActivityNameMapConfig();
        String skuIdSkuName = jiyunProperties.getSkuIdSkuNameMapConfig();
        String activityIds = Arrays.stream(skuIdActivityIds.split("\\|"))
                .collect(Collectors.toMap(v -> v.split(":")[0], v -> v.split(":")[1])).get(sourceGoodsId);
        if (Objects.isNull(skuIdActivityIds) || Objects.isNull(activityIds)) {
            throw new ServiceException(ErrorCode.SUBSCRIBE_FLOWNOTIFY_SOURCEGOODSID_NOT_EXIST, ErrorCode.SUBSCRIBE_FLOWNOTIFY_SOURCEGOODSID_NOT_EXIST_MESSAGE);
        }
        jiyunMap.put(JiyunConstants.JIYUN_SOURCE_APP_STR, jiyunProperties.getJiyunSourceApp());
        jiyunMap.put(JiyunConstants.ACTIVITY_ID_STR, activityIds);
        jiyunMap.put(JiyunConstants.ACTIVITYNAME_STR, Arrays.asList(activityIdActivityName.split("\\|")).stream().collect(Collectors.toMap(v -> v.split(":")[0], v -> v.split(":")[1])).get(activityIds));
        jiyunMap.put(JiyunConstants.SKUNAME_STR, Arrays.asList(skuIdSkuName.split("\\|")).stream().collect(Collectors.toMap(v -> v.split(":")[0], v -> v.split(":")[1])).get(sourceGoodsId));
        jiyunMap.put(JiyunConstants.SKUCODE_STR, jiyunProperties.getJiyunSkuCode());
        jiyunMap.put(JiyunConstants.SKUID_STR, sourceGoodsId);
        jiyunMap.put(JiyunConstants.JIYUN_GOODSID, jiyunProperties.getJiyunGoodsID());
        log.info("jiyunMap is ,: {}", jiyunMap);
        //log.info("method getActivityIdBySkuId end!");
        return jiyunMap;
    }
}