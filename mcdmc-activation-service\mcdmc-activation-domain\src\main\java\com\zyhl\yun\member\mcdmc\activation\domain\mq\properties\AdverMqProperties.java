package com.zyhl.yun.member.mcdmc.activation.domain.mq.properties;

import com.zyhl.yun.member.consumer.common.producer.MqFlowStateEnum;
import com.zyhl.yun.member.consumer.common.producer.properties.IMqFlow;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 权益领取发送广告平台的mq配置
 *
 * <AUTHOR>
 * @since 2024/09/09 10:06
 */

@Data
@Configuration
@ConfigurationProperties("rocketmq.consumer.rai-adver")
public class AdverMqProperties implements IMqFlow {
    /**
     * 发送领取外部权益到广告平台topic
     */
    private String adverTopic = "adverPlatform";
    /**
     * 发送领取外部权益到广告平台tag
     */
    private String userActionTag = "receive";
    /**
     * 发送领取外部权益到广告平台topic
     */
    private String oldAdverTopic = "adverPlatform";
    /**
     * 发送领取外部权益到广告平台tag
     */
    private String oldUserActionTag = "receive";

    /**
     * mq流转状态
     *
     * @see MqFlowStateEnum
     */
    private String flowStatus = MqFlowStateEnum.OLD.getState();

    @Override
    public String getNewTopic() {
        return adverTopic;
    }

    @Override
    public String getOldTopic() {
        return oldAdverTopic;
    }

    @Override
    public String getNewTag() {
        return userActionTag;
    }

    @Override
    public String getOldTag() {
        return oldUserActionTag;
    }
}
