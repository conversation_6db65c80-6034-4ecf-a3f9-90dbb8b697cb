package com.zyhl.yun.member.mcdmc.activation.remote.send.req;


import lombok.Data;

import java.util.List;

/**
 * 一级能力开放免流综合退订接口
 *
 * <AUTHOR>
 * @since 2019-12-26
 */
@Data
public class OpenReturnReq {
    /**
     * 消息类型，OPReturnSyncForAllReq
     */
    private String msgType="OPReturnSyncForAllReq";

    /**
     * 消息版本号
     */
    private String version;

    /**
     * 渠道
     */
    private String channelCode;

    /**
     * 退订类型
     */
    private String returnType;

    /**
     * 渠道退单编码
     */
    private String extReturnId;

    /**
     * 原订单编码
     */
    private String orderId;

    /**
     * 原子订单编码
     */
    private String subOrderId;

    /**
     * 发起退订的店铺编码
     */
    private String shopCode;

    /**
     * 发起退订的店铺名称
     */
    private String shopName;

    /**
     * 买家发起退订申请时间
     */
    private String buyerReturnTime;

    /**
     * 合作渠道发起退订时间
     */
    private String extChannelReturnTime;

    /**
     * 买家名称
     */
    private String buyerNickname;

    /**
     * 业务号码
     */
    private String serviceNo;

    /**
     * 业务号码类型
     */
    private String serviceNoType;

    /**
     * 退订商品信息
     */
    private List<ReturnGoodsInfo> returnGoodsInfo;

    /**
     * 原订单总金额
     */
    private Integer totalFee;

    /**
     * 原订单实际支付金额
     */
    private Integer payment;

    /**
     * 是否需要退款
     */
    private String needRefund;

    /**
     * 申请退款金额
     */
    private Integer refundAmount;

    /**
     * 退订联系人号码
     */
    private String contactPhone;

    /**
     * 退订原因
     */
    private String refundReason;

    /**
     * 买家说明
     */
    private String buyerComments;

    /**
     * 卖家备注
     */
    private String sellerRemark;

    /**
     * 退单反馈地址
     */
    private String feedbackUrl;

    @Override
    public String toString() {
        return "OpenReturnReq{" + "msgType='" + msgType + '\'' +
                ", version='" + version + '\'' +
                ", channelCode='" + channelCode + '\'' +
                ", returnType='" + returnType + '\'' +
                ", extReturnId='" + extReturnId + '\'' +
                ", orderId='" + orderId + '\'' +
                ", subOrderId='" + subOrderId + '\'' +
                ", shopCode='" + shopCode + '\'' +
                ", shopName='" + shopName + '\'' +
                ", buyerReturnTime='" + buyerReturnTime + '\'' +
                ", extChannelReturnTime='" + extChannelReturnTime + '\'' +
                ", buyerNickname='" + buyerNickname + '\'' +
                ", serviceNo='" + serviceNo + '\'' +
                ", serviceNoType='" + serviceNoType + '\'' +
                ", returnGoodsInfo=" + returnGoodsInfo +
                ", totalFee=" + totalFee +
                ", payment=" + payment +
                ", needRefund='" + needRefund + '\'' +
                ", refundAmount=" + refundAmount +
                ", contactPhone='" + contactPhone + '\'' +
                ", refundReason='" + refundReason + '\'' +
                ", buyerComments='" + buyerComments + '\'' +
                ", sellerRemark='" + sellerRemark + '\'' +
                ", feedbackUrl='" + feedbackUrl + '\'' +
                '}';
    }

    /**
     * 退订商品信息
     *
     * <AUTHOR>
     * @since 2019-12-26
     */
    @Data
    public static class ReturnGoodsInfo {
        /**
         * 商品编码
         */
        private String goodsId;

        /**
         * 商品标题
         */
        private String goodsTitle;

        /**
         * 退订数量，单位：个
         */
        private Integer returnQuantity;

        /**
         * 原商品价格，单位：分
         */
        private Integer price;

        /**
         * AP服务列表
         */
        private String serviceIdList;

        @Override
        public String toString() {
            return "ReturnGoodsInfo [goodsId=" +
                    goodsId +
                    ", goodsTitle=" +
                    goodsTitle +
                    ", returnQuantity=" +
                    returnQuantity +
                    ", price=" +
                    price +
                    ", serviceIdList=" +
                    serviceIdList +
                    "]";
        }
    }
}
