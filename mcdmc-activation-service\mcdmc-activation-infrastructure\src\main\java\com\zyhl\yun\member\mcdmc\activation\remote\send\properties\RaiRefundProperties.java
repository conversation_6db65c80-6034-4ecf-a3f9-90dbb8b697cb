package com.zyhl.yun.member.mcdmc.activation.remote.send.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/06/17 19:45
 */
@Data
@Configuration
@ConfigurationProperties("platform.rai.refund")
public class RaiRefundProperties {

    /**
     * 权益退费业务编码
     * rai.rightsSalesReturnApiId
     */
    private String rightsSalesReturnApiId;
    /**
     * 权益退费渠道编码
     * rai.rightsSalesReturnChannelCode
     */
    private String rightsSalesReturnChannelCode;
    /**
     * 权益退费密钥
     * rai.rightsSalesReturnKey
     */
    private String rightsSalesReturnKey;

}
