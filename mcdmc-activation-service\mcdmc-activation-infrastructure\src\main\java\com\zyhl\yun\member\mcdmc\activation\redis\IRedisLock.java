package com.zyhl.yun.member.mcdmc.activation.redis;

import com.zyhl.yun.member.mcdmc.activation.domain.lock.ILock;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/06/28 19:17
 */
@Component
public class IRedisLock implements ILock {
    @Resource
    private RedisLockOperator redisLockOperator;
    private final static String ACTIVATION_LOCK_KEY_PREFIX = "ACTIVATION_LOCK_KEY_%s";

    @Override
    public boolean lock(String lockKey, long ttlSecond) {
        return StringUtils.hasText(redisLockOperator.lock(getActivationLockKey(lockKey), ttlSecond));
    }

    @Override
    public String distributedLock(String lockKey, long ttlSecond) {
        return redisLockOperator.lock(getActivationLockKey(lockKey), ttlSecond);
    }

    @Override
    public boolean continueLock(String lockKey, String lockId, long ttlSecond) {
        return redisLockOperator.continueLock(getActivationLockKey(lockKey), lockId, ttlSecond);
    }

    @Override
    public boolean unlock(String lockKey, String lockId) {
        return redisLockOperator.unlock(getActivationLockKey(lockKey), lockId);
    }

    private String getActivationLockKey(String lockKey) {
        return String.format(ACTIVATION_LOCK_KEY_PREFIX, lockKey);
    }
}
