package com.zyhl.yun.member.common.domain.framework.executor;

import com.zyhl.yun.member.common.domain.framework.DomainEntityPersistenceWrapper;

import java.io.Serializable;

/**
 * 驱动器的执行器
 *
 * <AUTHOR>
 * @date 2024/05/24 17:35
 */
public interface DriverExecutor extends Serializable {

    /**
     * 执行
     *
     * @param wrapper
     * @return
     */
    Serializable exec(DomainEntityPersistenceWrapper wrapper);
}
