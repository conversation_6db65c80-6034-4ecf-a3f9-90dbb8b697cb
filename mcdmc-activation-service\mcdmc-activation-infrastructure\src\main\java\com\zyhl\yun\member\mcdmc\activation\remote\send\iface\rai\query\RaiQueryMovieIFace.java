package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.query;

import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiBaseProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiMovieProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.RaiSubQueryReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.RaiSubQueryRsp;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 影视资源主动查询接口
 *
 * <AUTHOR>
 * @since 2024/07/30 10:33
 */
@Component
public class RaiQueryMovieIFace extends RaiQueryBaseIFace {

    @Resource
    private RaiMovieProperties raiMovieProperties;

    @Override
    protected void doRaiReceiveSuccess(InterfaceContext<RaiSubQueryReq, RaiSubQueryRsp> interfaceContext, GoodsInstanceDo goodsInstanceDo) {
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        // 订购 或 非订购且没回调 则更新领取表状态
        MemberContextUtil.updateGoodsInstance(comSendReq,
                getResourceId(interfaceContext), true, true, SendOperation.SUB);
    }

    @Override
    protected RaiBaseProperties getRaiProperties() {
        return raiMovieProperties;
    }

}
