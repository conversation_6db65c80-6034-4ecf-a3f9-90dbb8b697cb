package com.zyhl.yun.member.mcdmc.activation.flow.rai.sub;

import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import com.zyhl.yun.member.mcdmc.activation.util.RaiUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 畅影转存券发货流程（直接发货，不需要iFace接口）
 *
 * <AUTHOR>
 * @since 2025/01/01 09:47
 */
@Component
public class RaiConversionCouponsFlow extends RaiSubNormalFlow {
    @Resource
    private RaiUtil raiUtil;

    @Override
    protected void doFlowSuccess(WorkOrderDo workOrderDo, boolean isFinally, IFlowResult lastFaceResult) {
        ComSendInterfaceReq comSendReq = JSONUtil.toBean(workOrderDo.getWorkAttrs(), ComSendInterfaceReq.class);
        GoodsInstanceDo goodsInstanceDo = MemberContextUtil.qryGoodsInstanceDo(
                comSendReq.getUserId(), comSendReq.getGoodsInstanceId());
        // 先发送畅影转存券的mq通知
        raiUtil.sendContentMq(comSendReq, goodsInstanceDo);
        // 结束流程
        super.doFlowSuccess(workOrderDo, isFinally, lastFaceResult);
    }
}
