package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.query;

import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiBaseProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiNormalProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.RaiSubQueryReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.RaiSubQueryRsp;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import com.zyhl.yun.member.mcdmc.activation.util.RaiUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 常规权益主动查询接口
 * <a href="https://docs.qq.com/doc/DRWFCTE95anRoZEdt">权益中心接口文档</a>
 *
 * <AUTHOR>
 * @since 2024/07/30 10:33
 */
@Component
public class RaiQueryNormalIFace extends RaiQueryBaseIFace {

    @Resource
    private RaiNormalProperties raiNormalProperties;

    @Override
    protected void doBusiFail(InterfaceContext<RaiSubQueryReq, RaiSubQueryRsp> interfaceContext) {
        // 处理业务失败逻辑
        RaiSubQueryRsp rsp = interfaceContext.getInterfaceRspObj();
        if (rsp != null && rsp.getContractRoot() != null && rsp.getContractRoot().getBody() != null
                && RaiUtil.RECEIVE_SUCCESS_RESULT_CODE.equals(rsp.getContractRoot().getBody().getResultCode()) &&
                CollectionUtils.isNotEmpty(rsp.getContractRoot().getBody().getOrderList())) {
            // 订单正在处理中则需要流程重试
            List<RaiSubQueryRsp.OrderListEntity> orderList = rsp.getContractRoot().getBody().getOrderList();
            Integer mainStatus = orderList.get(0).getMainStatus();
            if (RaiSubQueryRsp.MainStatus.PENDING_ACTIVATION.codeEquals(mainStatus)) {
                throw new FlowTerminationException(this.getClass(), interfaceContext.getWorkOrderDo(), NextHintEnum.RESTART,
                        "rai order is processing, rai query response is %s, work order detail is %s", rsp, interfaceContext.getWorkOrderDo().toSimpleLogStr());
            }
        }
    }

    @Override
    protected void doRaiReceiveSuccess(InterfaceContext<RaiSubQueryReq, RaiSubQueryRsp> interfaceContext, GoodsInstanceDo goodsInstanceDo) {
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        // 订购 或 非订购且没回调 则更新领取表状态
        String resourceId = getResourceId(interfaceContext);
        MemberContextUtil.updateGoodsInstance(comSendReq, resourceId, true, true, SendOperation.SUB);
    }

    @Override
    protected RaiBaseProperties getRaiProperties() {
        return raiNormalProperties;
    }

}
