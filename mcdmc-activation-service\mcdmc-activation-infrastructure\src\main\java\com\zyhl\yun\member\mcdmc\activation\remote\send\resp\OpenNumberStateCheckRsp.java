package com.zyhl.yun.member.mcdmc.activation.remote.send.resp;


import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.Objects;

/**
 * 能力开放平台号码校验接口响应对象
 *
 * <AUTHOR>
 * @since 2019-12-12
 */
@Data
@XmlRootElement(name = "NumberStateCheckResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class OpenNumberStateCheckRsp {
    /**
     * 消息类型，默认NumberStateCheckResp
     */
    @XmlElement(name = "MsgType")
    private String msgType;

    /**
     * 该接口消息的版本号
     */
    @XmlElement(name = "Version")
    private String version;

    /**
     * 开放平台返回值
     */
    private Integer hRet;

    /**
     * 结果码
     */
    private String bizCode;

    /**
     * 一级能力开放平台结果描述
     */
    private String bizDesc;

    @Override
    public String toString() {
        return "NumberStateCheckResp [msgType=" +
                msgType +
                ", version=" +
                version +
                ", hRet=" +
                hRet +
                ", bizCode=" +
                bizCode +
                ", bizDesc=" +
                bizDesc +
                "]";
    }

    public static boolean isSuccess(OpenNumberStateCheckRsp rsp) {
        return rsp != null && Objects.equals(0, rsp.getHRet()) && Objects.equals("0000", rsp.getBizCode());
    }
}
