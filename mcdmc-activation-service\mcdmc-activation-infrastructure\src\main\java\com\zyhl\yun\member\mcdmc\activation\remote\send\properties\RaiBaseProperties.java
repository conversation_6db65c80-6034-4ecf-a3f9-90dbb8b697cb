package com.zyhl.yun.member.mcdmc.activation.remote.send.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/06/17 08:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RaiBaseProperties {

    /**
     * 权益订单发货渠道编码(默认渠道编码）
     */
    private String equityChannelCode;

    private String equityOrderApiId;

    private String equityQueryApiId;

    private String equityVersion;

    /**
     * rai.equityQuerySecretKey
     * 查询接口的密钥
     */
    private String equityQuerySecretKey;

    /**
     * rai.equityQuerySecretKey
     * 下单接口的密钥
     */
    private String equityOrderSecretKey;

    /**
     * 延时查询时间,单位min
     */
    private int delayQueryMinTime = 5;

    /**
     * 获取订单发货渠道编码
     *
     * @param orderSettlementType 订单结算类型
     */
    public String getOrderChannelCode(String orderSettlementType) {
        return equityChannelCode;
    }

}
