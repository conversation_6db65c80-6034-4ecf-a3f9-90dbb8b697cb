package com.zyhl.yun.member.mcdmc.activation.flow.free_flow;

import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.domain.resource.domain.ResourceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.flow.base.BaseDefaultServiceFlow;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import com.zyhl.yun.member.mcdmc.activation.util.OrderContextUtil;
import com.zyhl.yun.member.order.domain.OrderDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;


/**
 * 一级能开免流退费流程（和退订一样，但是状态不一致）
 *
 * <AUTHOR>
 * @since 2024/07/03 11:58
 */
@Slf4j
@Component
public class OpenPauseFlow extends BaseDefaultServiceFlow {
    @Resource
    private MemberContextUtil memberContextUtil;

    /**
     * 需要添加order表判断flowOrderNo是否有值，有值则表示已经订购过
     */

    @Override
    protected IFlowResult beforeFlow(WorkOrderDo workOrderDo) {
        ComSendInterfaceReq comSendReq = JSONUtil.toBean(workOrderDo.getWorkAttrs(), ComSendInterfaceReq.class);
        OrderDo parentOrderDo;
        if (StringUtils.hasText(comSendReq.getParentOrderId())) {
            parentOrderDo = OrderContextUtil.queryOrderDo(comSendReq.getParentOrderId(), workOrderDo.getUserId(), false);
        } else {
            // 兼容暂停时未传父订单id情况
            parentOrderDo = OrderContextUtil.queryParentOrderDo(comSendReq.getGoodsInstanceId(), workOrderDo.getUserId(), false);
        }
        // 判断商品实例是否有发起过退费
        if (Objects.isNull(parentOrderDo)) {
            log.error("open pause error, order is null,workOrder detail is {}", workOrderDo.toSimpleLogStr());
            // 查不到商品实例，则直接终止流程
            throw new FlowTerminationException(this.getClass(), workOrderDo,
                    "open pause error, order is null,workOrder detail is %s", workOrderDo.toSimpleLogStr());
        }
        // 判断是否发起订购，若没有订购就退费则为延迟发货（发货前退费）
        if (!StringUtils.hasText(parentOrderDo.getFlowOrderNo())) {
            log.error("open pause error, flowOrderNo is empty,parentOrderId is {}, so is not sub,workOrder detail is {}", parentOrderDo.getOrderNo(), workOrderDo.toSimpleLogStr());
            return IFlowResult.finish(null, "open pause error, flowOrderNo is empty,parentOrderId is %s, so is not sub,workOrder detail is %s",
                    parentOrderDo.getOrderNo(), workOrderDo.toSimpleLogStr());
        }
        // 判断是否开通免流
        ResourceDo currentResourceDo = memberContextUtil.updateResourceEndTime(workOrderDo, false);
        // 如果存在生效的资产，则不能走流程
        if (null != currentResourceDo && null != currentResourceDo.getEffectiveEndTime()
                && currentResourceDo.getEffectiveEndTime().after(new Date())) {
            log.info("userId={} contain valid resource,skip {}. orderId={}", workOrderDo.getUserId(), this.getClass().getSimpleName(), workOrderDo.getOrderId());
            return IFlowResult.finish(currentResourceDo.getResourceId(),
                    "early skip flow,because it contain valid resource,workOrder detail is ", workOrderDo.toSimpleLogStr());
        }
        return IFlowResult.result(NextHintEnum.NEXT, null);
    }

}
