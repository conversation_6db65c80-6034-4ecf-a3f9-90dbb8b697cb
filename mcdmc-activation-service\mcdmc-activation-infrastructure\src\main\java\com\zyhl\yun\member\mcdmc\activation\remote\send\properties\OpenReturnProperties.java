package com.zyhl.yun.member.mcdmc.activation.remote.send.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 一级能开免流退订配置
 *
 * <AUTHOR>
 * @since 2024/07/05 17:50
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "platform.open.return")
public class OpenReturnProperties{
    /**
     * 综合退订同步消息退订类型
     * /config/vsbo/goToneConfig/opReturnSyncReturnType:1
     */
    private String opReturnSyncReturnType = "1";
    /**
     * 综合退订同步消息版本号
     * /config/vsbo/goToneConfig/opReturnSyncVersion:1.0.0
     */
    private String opReturnSyncVersion = "1.0.0";

    /**
     * 综合退订同步消息渠道
     * /config/vsbo/goToneConfig/opReturnSyncChannelCode:100006
     */
    private String opReturnSyncChannelCode = "100006";
    /**
     * 综合退订同步接口回调vsbo接口url
     * /config/vsbo/goToneConfig/opReturnSyncFeedbackUrl
     */
    private String opReturnSyncFeedbackUrl;
}
