package com.zyhl.yun.member.consumer;

import com.zyhl.yun.member.consumer.common.MemberVipSaveRetry;
import com.zyhl.yun.member.domain.activity.domain.ActivityInstanceDO;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.notify.domain.PerformOrderDo;

import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

/**
 * 功能描述。
 * 包名称:  com.zyhl.yun.member.order.converter
 * 类名称:  GoodsInstance2NotifyConverter
 * 类描述:  。
 *
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/7/19 19:17
 */
@Mapper(componentModel = "spring")
public interface GoodsInstance2NotifyConverter {
    void goodsInstance2Notify(GoodsInstanceDo goodsInstanceExtendDo, @MappingTarget PerformOrderDo performOrderDo);



    void activityVo2Do(MemberVipSaveRetry memberVipSaveRetry, @MappingTarget ActivityInstanceDO activityInstanceDO);
}
