package com.zyhl.yun.member.mcdmc.activation.mapping;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zyhl.yun.member.mcdmc.activation.po.WorkOrderPo;
import com.zyhl.yun.member.mcdmc.activation.repository.WorkOrderRepository;
import com.zyhl.yun.member.mcdmc.activation.serviceid.LocalServiceId;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/07/01 14:11
 */
public class WorkOrderMethodMapping {
    @Getter
    @AllArgsConstructor
    public enum WRITE {
        INSERT(LocalServiceId.INSERT_OPERATION, "插入工单") {
            @Override
            public int exec(WorkOrderRepository workOrderRepository, WorkOrderPo workOrderPo) {
                workOrderPo.setCreatedTime(null);
                workOrderPo.setUpdatedTime(null);
                boolean isSave = workOrderRepository.save(workOrderPo);
                return isSave ? 1 : 0;
            }
        },
        INSERT_IGNORE(LocalServiceId.INSERT_IGNORE_OPERATION, "插入工单(主键存在则不插入）") {
            @Override
            public int exec(WorkOrderRepository workOrderRepository, WorkOrderPo workOrderPo) {
                workOrderPo.setCreatedTime(null);
                workOrderPo.setUpdatedTime(null);
                return workOrderRepository.insertIgnoreWorkOrder(workOrderPo);
            }
        },
        UPSERT(LocalServiceId.UPSERT_OPERATION, "插入或更新工单（存在插入，不存在更新）") {
            @Override
            public int exec(WorkOrderRepository workOrderRepository, WorkOrderPo workOrderPo) {
                workOrderPo.setUpdatedTime(null);
                LambdaQueryWrapper<WorkOrderPo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(WorkOrderPo::getWorkId, workOrderPo.getWorkId())
                        .eq(StringUtils.isNotEmpty(workOrderPo.getUserId()), WorkOrderPo::getUserId, workOrderPo.getUserId());
                boolean isUpdate = workOrderRepository.update(workOrderPo, queryWrapper);
                boolean isSave = false;
                if (Boolean.FALSE.equals(isUpdate)) {
                    workOrderPo.setCreatedTime(null);
                    isSave = workOrderRepository.save(workOrderPo);
                }
                return isUpdate || isSave ? 1 : 0;
            }
        },

        UPDATE_BY_ID(LocalServiceId.UPDATE_BY_ID_OPERATION, "根据主键更新工单") {
            @Override
            public int exec(WorkOrderRepository workOrderRepository, WorkOrderPo workOrderPo) {
                workOrderPo.setUpdatedTime(null);
                LambdaQueryWrapper<WorkOrderPo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(WorkOrderPo::getWorkId, workOrderPo.getWorkId())
                        .eq(StringUtils.isNotEmpty(workOrderPo.getUserId()),
                                WorkOrderPo::getUserId, workOrderPo.getUserId());
                boolean isUpdate = workOrderRepository.update(workOrderPo, queryWrapper);
                return isUpdate ? 1 : 0;
            }
        };

        private final String serviceId;
        private final String msg;

        public abstract int exec(WorkOrderRepository workOrderRepository,
                                 WorkOrderPo workOrderPo);
    }

    public static WorkOrderMethodMapping.WRITE getWriterByServiceId(String serviceId) {
        for (WorkOrderMethodMapping.WRITE write : WorkOrderMethodMapping.WRITE.values()) {
            if (write.serviceId.equals(serviceId)) {
                return write;
            }
        }
        return null;
    }
}
