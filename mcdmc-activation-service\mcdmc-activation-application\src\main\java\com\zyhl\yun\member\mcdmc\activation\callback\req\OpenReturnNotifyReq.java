package com.zyhl.yun.member.mcdmc.activation.callback.req;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 流量卡订单状态变更通知接口请求对象
 *
 * <AUTHOR>
 * @apiNote 一级能开退订回调接口请求体
 * @since 2019-12-26
 */
@Data
@XmlRootElement(name = "OPFlowCardOrderFdbkReq")
@XmlAccessorType(XmlAccessType.FIELD)
public class OpenReturnNotifyReq {

    /**
     * 消息类型
     */
    @XmlElement(name = "MsgType", required = true)
    private String msgType;

    /**
     * 接口消息版本号, 如：1.0.0
     */
    @XmlElement(name = "Version", required = true)
    private String version;

    /**
     * 原订单ID
     *
     * @apiNote 免流订单号
     */
    @XmlElement(required = true)
    private String orderId;

    /**
     * 退单编号
     */
    private String returnId;

    /**
     * 原子订单ID
     */
    @XmlElement(required = true)
    private String subOrderId;

    /**
     * 业务订单时间
     */
    @XmlElement(required = true)
    private String orderTime;

    /**
     * 订单状态
     * 退订状态：RC标识成功
     */
    @XmlElement(required = true)
    private String status;

    /**
     * 订单状态描述
     */
    private String statusDesc;

    /**
     * 物流单号/运单编码
     */
    private String shipmentNo;

    /**
     * 物流公司编码
     */
    private String shipmentCompanyCode;

    /**
     * 物流公司名称
     */
    private String shipmentCompany;

    /**
     * 订单状态更新时间
     */
    @XmlElement(required = true)
    private String updateTime;

    private String opContactName;

    private String opContactNo;

    private String opContactPhone;

    private String opComments;

    private String opContactOrganization;

    private String isResourceBundle;

    private String resourceCode;

    private String receiveTime;

    private String effTime;

    private String expTime;
}
