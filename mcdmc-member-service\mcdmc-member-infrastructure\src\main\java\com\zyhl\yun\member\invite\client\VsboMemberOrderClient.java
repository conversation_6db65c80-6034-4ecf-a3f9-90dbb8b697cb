package com.zyhl.yun.member.invite.client;

import com.zyhl.yun.member.activity.remote.config.FeignConfig;
import com.zyhl.yun.member.domain.goodsinstance.MemberQueryConditionSubScriptionResp;
import com.zyhl.yun.member.domain.goodsinstance.QueryConditionSubScriptionResp;
import com.zyhl.yun.member.domain.invite.MemberManageSubscribeRelationResp;
import com.zyhl.yun.member.order.dto.ManageSubscribeRelationReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;

@FeignClient(name = "VsboMemberOrderClient", url = "${vsbo-url}", configuration = FeignConfig.class)
public interface VsboMemberOrderClient {
    @PostMapping(value = "/cloudSEE/openApi/manageSubscribeRelation",
            consumes = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE},
            produces = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE})
    MemberManageSubscribeRelationResp manageSubscribeRelation(@RequestHeader Map<String, String> headers, @RequestBody String manageSubscribeRelationReq);

    @PostMapping(value = "/cloudSEE/openApi/rollBackRight",
            consumes = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE},
            produces = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE})
    String rollBackRight(@RequestHeader Map<String, String> headers, @RequestBody String xmlReqStr);

    @PostMapping(value = "/cloudSEE/openApi/manageSubscribeRelationV2",
            consumes = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE},
            produces = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE})
    MemberManageSubscribeRelationResp manageSubscribeRelationV2(@RequestHeader Map<String, String> headers, @RequestBody String xmlReqStr);

    @PostMapping(value = "/cloudSEE/openApi/queryConditionSubScription",
            consumes = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE},
            produces = {MediaType.APPLICATION_XML_VALUE, MediaType.TEXT_XML_VALUE})
    MemberQueryConditionSubScriptionResp queryConditionSubScription(@RequestHeader Map<String, String> headers, @RequestBody String xmlReqStr);

}
