spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    cachePrepStmts: true
    prepStmtCacheSize: 300
    prepStmtCacheSqlLimit: 2048
    useServerPrepStmts: true
    useLocalSessionState: true
    rewriteBatchedStatements: true
    cacheResultSetMetadata: true
    cacheServerConfiguration: true
    elideSetAutoCommits: true
    maintainTimeStats: false
    url: ******************************************************************************************************************************************************************************
    username: vip_refactor_4_user@yun_member_refactor#ypcg_member_prod
    password: ENC(3AWBbewChGiObgjH+NFpR85OjPjRvr/6GJvAtIE1EiA=)
  cache:
    type: redis
    redis:
      time-to-live: '3000'
  redis:
    host: ************
    password: ENC(w6V7yZOWZpUY/lwBHTocvrkznf/GAyWbDSDEePmFuvI=)
    port: 32066
    database: 0
    max-redirects: 3
    timeout: 3000
    jedis:
      pool:
        # 最大连接，单位：个。当前tomcat配置线程数为200，考虑每秒内有一半线程在操作redis，且每个线程操作不超过100ms，故线程数设置为50
        maxTotal: 200
        #最大空闲连接，单位：个
        maxIdle: 200
        # 最小空闲连接，单位：个
        minIdle: 20
        # 最大获取连接等待时间，单位：毫秒
        maxWaitMillis: 3000
        #空闲连接逐出时间，大于该值的空闲连接一直未被使用则会被释放，单位：毫秒
        minEvictableIdleTimeMillis: 30000
        #空闲连接探测时间间隔，单位：毫秒。 例如系统的空闲连接探测时间配置为30s，则代表每隔30s会对连接进行探测，如果30s内发生异常的连接，
        #经过探测后会进行连接排除。根据连接数的多少进行配置，如果连接数太大，配置时间太短，会造成请求资源浪费。
        timeBetweenEvictionRunsMillis: 30000
        #向资源池借用连接时是否做连接有效性检测（ping），检测到的无效连接将会被移除。对于业务连接极端敏感的，并且性能可以接受的情况下，
        #可以配置为True，一般来说建议配置为False，启用连接空闲检测。
        testOnBorrow: true
        # 是否在空闲资源监测时通过ping命令监测连接有效性，无效连接将被销毁。
        testWhileIdle: true
        # 向资源池归还连接时是否做连接有效性检测（ping），检测到无效连接将会被移除。耗费性能
        testOnReturn: false
        # 连接空闲检测的时间间隔，单位：毫秒
        timeout: 3000
        # 连接建立超时时间，单位：毫秒
        connectTimeout: 3000
        # 空闲连接检测时，每次检测的连接数
        numTestsPerEvictionRun: 3

mybatis-plus:
  # 启动时是否检查MyBatis XML文件是否存在
  check-config-location: true
  mapper-locations: classpath:mapper/**.xml
  #开启sql日志
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl



inner-service:
  list:
    - serviceIp: 127.0.0.1
      serviceName: member-vip
      servicePort: 8085
      servicePath: /member-vip
    - serviceIp: 127.0.0.1
      serviceName: member-order
      servicePort: 8189
      servicePath: /member-order
    - serviceIp: 127.0.0.1
      serviceName: member-product
      servicePort: 8080
      servicePath: /member-product
    - serviceIp: 127.0.0.1
      serviceName: member-payment
      servicePort: 8083
      servicePath: ''
    - serviceIp: 127.0.0.1
      serviceName: member-activation
      servicePort: 18084
      servicePath: /member-activation
    - serviceIp: 127.0.0.1
      serviceName: member-support
      servicePort: 18078
      servicePath: /member-support

xxl:
  job:
    admin:
      addresses: http://yun-xxl-common-10-svc.prod.svc.cluster.local:8080/xxl-job-admin
    accessToken: zvGYZqpUdkk%xwQrA8yg
    executor:
      appname: member-task
      ### 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
      logpath: /opt/aspire/product/hlwljyy/hcy_task
      ### 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
      logretentiondays: 30
      port: -1

push:
  config:
    pushTitle: 会员中心测试推送
    pushContent: VIP权益独享，您的会员身份即将到期！别错失独家特权，立即续费，继续嚣张生活，尽情享受尊贵服务！
    templateType: 0
    expectedInsertions: 1500000
    probability: 0.001
    mergeCount: 200

flow-switch:
  monthlyRetry: 'false'
  dailyRetry: 'false'
  unSubRetry: 'false'

# VSBO服务地址
platform:
  vsbo:
    url: 'http://***************:8080'
  member:
    url: 'http://[fd11:1111:1111:23::2a56]:18085/member-vip'

server:
  tomcat:
    threads:
      max: 400
      min-spare: 50
    #等待队列长度，当可分配的线程数全部用完之后，后续的请求将进入等待队列等待，等待队列满后则拒绝处理，默认100
    accept-count: 20000
    #最大可被连接数
    max-connections: 10000
    #连接超时时间，该值需要大于nginx的keepalive_timeout，否则nginx会主动断开连接，默认60000
    connection-timeout: 70000

manage:
  neAuth: TWpBeU5UQXhNRFF3TkRFME1qZzFNN0xlTXEwMXdSakJkak9DeWJZSTN4OTlWVnZpVzBiQWhyTDlXNFJtbzZTag==

# 报文压缩
server.compressFlag: true
# http2开启
http2.flag: true