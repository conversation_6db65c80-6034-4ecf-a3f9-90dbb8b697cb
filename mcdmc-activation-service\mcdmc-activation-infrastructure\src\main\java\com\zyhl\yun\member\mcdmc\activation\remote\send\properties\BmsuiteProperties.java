package com.zyhl.yun.member.mcdmc.activation.remote.send.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/06/21 17:18
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "platform.bmsuite")
public class BmsuiteProperties {

    /**
     * 网元名
     */
    private String neName;
    /**
     * 网元密码
     */
    private String nePasswd;

    /**
     * aes加密密钥
     */
    private String aesEncryptPasswd;

    /**
     * 第三方鉴权appKey
     */
    private String appKey;

    /**
     * 第三方鉴权appSecretId
     */
    private String appSecretId;

    /**
     * 第三方鉴权appSecret
     */
    private String appSecret;

    /**
     * 第三方鉴权版本号
     */
    private String algorithmVersion = "1.0";
}
