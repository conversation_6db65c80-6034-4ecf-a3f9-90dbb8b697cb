---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    # 修改应用名,若跟随节点部署要加上节点标识，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的D列或H列，应用模块的英文名
    app.name: mcdmc-openresty-service-starter
  # 修改应用名,若跟随节点部署要加上节点标识，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的D列或H列，应用模块的英文名
  name: mcdmc-openresty-service-starter
  namespace: prod
spec:
  progressDeadlineSeconds: 600
  # 修改副本数,对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的【内网规划】sheet K列
  replicas: 6
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      # 修改应用名,若跟随节点部署要加上节点标识，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的D列或H列，应用模块的英文名
      app.name: mcdmc-openresty-service-starter
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        # 修改应用名,若跟随节点部署要加上节点标识，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的D列或H列，应用模块的英文名
        app.name: mcdmc-openresty-service-starter
    spec:
      containers:
        # 修改环境变量
      - env:
        - name: jasypt.encryptor.password
          value: Vip2024_zwDev
        image: b08ypcg01-53ab9ddf.ecis.guangzhou-2.cmecloud.cn/prod/mcdmc-openresty-service-starter:202410111643
        imagePullPolicy: Always
        # 修改应用名,若跟随节点部署要加上节点标识，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的D列或H列，应用模块的英文名
        name: mcdmc-openresty-service-starter
        # 针对CPU，建议是按实际使用情况进行分配；
        # 针对内存，参考腾讯云给的建议（https://cloud.tencent.com/document/product/649/74310），request配置为JVM最大堆内存的1.25倍，limit配置为JVM最大堆内存的2倍；
        # 确认要修改后，请下方修改需求写在上线方案中，在变更窗口让运维同事修改【敏捷平台】中生产环境对应模块的【模板集】，然后再用CD流水线发布更新；
        # 下方为JVM最大最小堆都是8G的建议样例，请根据实际情况修改，请勿照抄！
        # 下方为JVM最大最小堆都是8G的建议样例，请根据实际情况修改，请勿照抄！
        # 下方为JVM最大最小堆都是8G的建议样例，请根据实际情况修改，请勿照抄！
        resources:
          # 修改资源限制
          limits:
            cpu: '4'
            memory: 8Gi
          # 修改资源需求
          requests:
            cpu: '2'
            memory: 4Gi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
          - mountPath: /logs
            name: logs
          - mountPath: /etc/localtime
            name: time-localtime
            readOnly: true
          ########### 有共享存储需求的模块需修改，具体修改内容请沟通确认，如已知不需要可移除
          ########### mountPath为挂载到容器中的目录；name为存储卷名称，需要与90行的对应；subPath为文件存储中的目录，如果是两个不同应用需共享相同存储卷，要求两个模块的subPath配置的值需要一样，如果没有此需求的可以移除此行
          ##- mountPath: /data/xxx/yyy
          ##  name: vol-pvc-example
          ##  subPath: xxx/yyy
          ###########
          ###########
      dnsPolicy: ClusterFirst
      # 部署到对应标签的主机上，标签在【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的【内网规划】sheet M列
      nodeSelector:
        mcdmc: "true"
      imagePullSecrets:
        - name: cis-auth
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 60
      volumes:
        - hostPath:
            path: /var/lib/docker/logs
            type: DirectoryOrCreate
          name: logs
        - hostPath:
            path: /etc/localtime
            type: ''
          name: time-localtime
        ########### 有共享存储需求的模块需修改，具体修改内容请沟通确认，如已知不需要可移除
        ########### name为存储卷名称，需要与65行的对应；persistentVolumeClaim为pvc的名字，请沟通确认
        ##- name: vol-pvc-example
        ##  persistentVolumeClaim:
        ##    claimName: pvc-nas-gz3-ypcg-p00X-XXX
        ###########
        ###########
      dnsConfig:
        options:
          - name: single-request-reopen
          - name: timeout
            value: '1'
          - name: attempts
            value: '3'
          - name: ndots
            value: '2'

---
apiVersion: v1
kind: Service
metadata:
  annotations: {}
  labels:
    # 修改应用名,若跟随节点部署要加上节点标识，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的D列或H列，应用模块的英文名
    app.name: mcdmc-openresty-service-starter
  # 修改为【应用名-svc】,若跟随节点部署应用名后要加上节点标识
  name: mcdmc-openresty-service-starter-svc
  namespace: prod
spec:
  ipFamilies:
    - IPv6
  ports:
    # 修改为应用名-port
    - name: mcdmc-openresty-service-starter-port
      # 修改为应用端口，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的BB列
      port: 16888
      protocol: TCP
      # 修改为应用端口，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的BB列
      targetPort: 80
  selector:
    # 修改应用名,若跟随节点部署要加上节点标识，对应【南基】https://www.kdocs.cn/l/ccjjXxdSB9gt 或 【凤凰】https://www.kdocs.cn/l/cc7KvTQt4bUL 的D列或H列，应用模块的英文名
    app.name: mcdmc-openresty-service-starter
  sessionAffinity: None
  type: ClusterIP

