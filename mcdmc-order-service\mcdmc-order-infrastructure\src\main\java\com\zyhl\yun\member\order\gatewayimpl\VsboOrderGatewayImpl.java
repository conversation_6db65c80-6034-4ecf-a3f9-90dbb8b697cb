package com.zyhl.yun.member.order.gatewayimpl;

import cn.hutool.core.util.JAXBUtil;
import com.zyhl.yun.member.order.dto.ManageSubscribeRelationReq;
import com.zyhl.yun.member.order.dto.RollBackRightReq;
import com.zyhl.yun.member.order.gateway.VsboMemberOrderGateway;
import com.zyhl.yun.member.order.remote.VsboClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.zyhl.yun.member.common.Constant.HttpHeader.X_NE_AUTH_DSDP;

/**
 * @Description:
 * @Author: zouzefeng
 * @Date: 2024/12/28
 */
@Slf4j
@Component
public class VsboOrderGatewayImpl implements VsboMemberOrderGateway {

    @Resource
    private VsboClient vsboClient;

    @Value("${manage.neAuth}")
    private String neAuth;

    @Override
    public String manageSubscribeRelation(ManageSubscribeRelationReq manageSubscribeRelationReq) {
        String xmlReqStr = JAXBUtil.beanToXml(manageSubscribeRelationReq);
        Map<String, String> headers = new HashMap<>();
        headers.put(X_NE_AUTH_DSDP, neAuth);
        return vsboClient.manageSubscribeRelationString(headers, xmlReqStr);
    }

    @Override
    public String rollBackRight(RollBackRightReq rollBackRightReq) {
        String xmlReqStr = JAXBUtil.beanToXml(rollBackRightReq);
        Map<String, String> headers = new HashMap<>();
        headers.put(X_NE_AUTH_DSDP, neAuth);
        return vsboClient.rollBackRight(headers, xmlReqStr);
    }

    @Override
    public String manageSubscribeRelationV2(ManageSubscribeRelationReq manageSubscribeRelationReq) {
        String xmlReqStr = JAXBUtil.beanToXml(manageSubscribeRelationReq);
        Map<String, String> headers = new HashMap<>();
        headers.put(X_NE_AUTH_DSDP, neAuth);
        return vsboClient.manageSubscribeRelationV2(headers, xmlReqStr);
    }

}
