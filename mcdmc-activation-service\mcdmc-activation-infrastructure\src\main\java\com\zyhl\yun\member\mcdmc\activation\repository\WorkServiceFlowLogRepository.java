package com.zyhl.yun.member.mcdmc.activation.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.mcdmc.activation.domains.conditions.QueryWorkServiceFlowLogCondition;
import com.zyhl.yun.member.mcdmc.activation.mapper.WorkServiceFlowLogMapper;
import com.zyhl.yun.member.mcdmc.activation.po.WorkServiceFlowLogPo;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/06/05 11:01
 */
@Repository
public class WorkServiceFlowLogRepository extends ServiceImpl<WorkServiceFlowLogMapper, WorkServiceFlowLogPo> {

    public List<WorkServiceFlowLogPo> searchByCondition(BaseCondition condition) {
        QueryWrapper<WorkServiceFlowLogPo> queryWrapper = new QueryWrapper<>();
        condition.getConditions().forEach(queryWrapper::eq);
        return super.list(queryWrapper);
    }


    /**
     * 根据查询条件返回对应数据
     *
     * @param condition
     * @return
     */
    public List<WorkServiceFlowLogPo> searchByCondition(QueryWorkServiceFlowLogCondition condition) {

        LambdaQueryWrapper<WorkServiceFlowLogPo> wrapper = Wrappers.lambdaQuery(WorkServiceFlowLogPo.class)
                .eq(StringUtils.hasLength(condition.getUserId()), WorkServiceFlowLogPo::getUserId, condition.getUserId())
                .in(!CollectionUtils.isEmpty(condition.getServiceCodeList()), WorkServiceFlowLogPo::getServiceCode, condition.getServiceCodeList())
                .in(!CollectionUtils.isEmpty(condition.getWorkIdList()), WorkServiceFlowLogPo::getWorkId, condition.getWorkIdList());

        return super.list(wrapper);
    }
}
