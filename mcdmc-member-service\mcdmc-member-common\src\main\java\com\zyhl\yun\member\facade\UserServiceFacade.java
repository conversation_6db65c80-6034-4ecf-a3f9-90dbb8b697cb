package com.zyhl.yun.member.facade;

import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.UserServiceId;
import com.zyhl.yun.member.common.enums.UserStatusEnum;
import com.zyhl.yun.member.domain.user.domain.QueryUserCondition;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

import static com.zyhl.yun.member.common.domain.serviceid.DefaultServiceId.DEFAULT;
import static com.zyhl.yun.member.common.domain.serviceid.UserServiceId.INSERT_USER_INFO;
import static com.zyhl.yun.member.common.domain.serviceid.UserServiceId.UPDATE_USER_INFO;
import static com.zyhl.yun.member.common.domain.serviceid.UserServiceId.USER_LSB_SERVICE;

/**
 * <AUTHOR>
 * @date 2024/10/08 17:49
 */
@Slf4j
public class UserServiceFacade {


    /**
     * 获取用户信息
     *
     * @param phoneNumber
     * @param userDomainId
     * @return
     */
    public static UserDo getAndCreateUser(String phoneNumber, String userDomainId) {

        log.debug("[APP] getAndCheckUser start. phoneNumber: {}, userDomainId: {}",
                phoneNumber, userDomainId);

        if (!StringUtils.hasLength(phoneNumber) && !StringUtils.hasLength(userDomainId)) {
            log.error("[APP] getAndCheckUser error. phoneNumber and userDomainId is null");
            throw new ServiceException();
        }


        DomainServiceContext userServiceContext = new DomainServiceContext(UserDo.class, UserServiceId.QUERY_AND_CREATE_USER_INFO);
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setAccount(phoneNumber);
        queryUserCondition.setUserDomainId(userDomainId);
        queryUserCondition.setStatusList(Collections.singletonList(UserStatusEnum.NORMAL));

        UserDo userDo = userServiceContext.readFirst(queryUserCondition, UserDo.class);
        if (null == userDo) {
            log.error("[APP] getAndCheckUser error. the user is not exist,msisdn: {}, userDomainId: {}",
                    phoneNumber, userDomainId);
            throw new ServiceException();
        }
        return userDo;
    }

    public static UserDo queryAndCreateUser(String account, boolean isQueryGotone) {
        DomainServiceContext userServiceContext = new DomainServiceContext(UserServiceId.QUERY_AND_CREATE_USER_INFO);
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setAccount(account);
        queryUserCondition.setQueryGotoneParams(isQueryGotone);
        return userServiceContext.readFirst(queryUserCondition, UserDo.class);
    }

    /**
     * @param account
     * @param userDomainId
     * @param isQueryGotone
     * @return
     */
    public static UserDo queryAndCreateUser(String account, String userDomainId, boolean isQueryGotone) {
        DomainServiceContext userServiceContext = new DomainServiceContext(UserServiceId.QUERY_AND_CREATE_USER_INFO);
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setAccount(account);
        queryUserCondition.setUserDomainId(userDomainId);
        queryUserCondition.setQueryGotoneParams(isQueryGotone);
        return userServiceContext.readFirst(queryUserCondition, UserDo.class);
    }

    public static UserDo queryAndCreateUser(String account, boolean isQueryGotone, boolean isCheckUser) {
        DomainServiceContext userServiceContext = new DomainServiceContext(UserServiceId.QUERY_AND_CREATE_USER_INFO);
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setAccount(account);
        queryUserCondition.setQueryGotoneParams(isQueryGotone);
        UserDo userDo = userServiceContext.readFirst(queryUserCondition, UserDo.class);
        if (Boolean.TRUE.equals(isCheckUser) && Objects.isNull(userDo)) {
            log.error("[APP] getAndCheckUser error. the user is not exist,account: {}", account);
            throw new ServiceException("queryAndCreateUser check user error. the user is not exist.account = [" + account + "]");
        }
        return userDo;
    }

    public static UserDo getUserByUserId(String userId) {
        return getUserByUserId(userId, false);
    }

    public static UserDo getUserByUserId(String userId, boolean isCheckUserExist) {
        DomainServiceContext userServiceContext = new DomainServiceContext(UserServiceId.QUERY_USER_INFO);
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setUserId(userId);
        UserDo userDo = userServiceContext.readFirst(queryUserCondition, UserDo.class);
        if (Objects.isNull(userDo) && Boolean.TRUE.equals(isCheckUserExist)) {
            log.error("[APP] getAndCheckUser error. the user is not exist,userId: {}", userId);
            throw new ServiceException("getUserByUserId check user error. the user is not exist.userId = [" + userId + "]");
        }
        return userDo;
    }

    /**
     * 仅从数据库获取正常状态的用户，不查询用户域等其它信息
     *
     * @param msisdn 手机号码
     * @return 用户
     */
    public static UserDo getNormalDbUserDo(String msisdn) {
        if (!StringUtils.hasText(msisdn)) {
            return null;
        }
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setAccount(msisdn);
        queryUserCondition.setStatusList(Collections.singletonList(UserStatusEnum.NORMAL));
        DomainServiceContext context = new DomainServiceContext(DEFAULT);
        return context.readFirst(queryUserCondition, UserDo.class);
    }


    /**
     * 仅从数据库获取正常状态的和销户的用户，不查询用户域等其它信息
     *
     * @param msisdn 手机号码
     * @return 用户
     */
    public static UserDo getUserFromDbByMsisdn(String msisdn) {
        if (!StringUtils.hasText(msisdn)) {
            return null;
        }
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setAccount(msisdn);
        List<UserStatusEnum> statusList = new ArrayList<>();
        statusList.add(UserStatusEnum.NORMAL);
        statusList.add(UserStatusEnum.CANCELLED);
        queryUserCondition.setStatusList(statusList);
        DomainServiceContext context = new DomainServiceContext(DEFAULT);
        List<UserDo> userDoList = context.read(queryUserCondition, UserDo.class);
        if (CollectionUtils.isEmpty(userDoList)) {
            return null;
        }
        return userDoList.stream()
                .filter(Objects::nonNull)
                .min(Comparator.comparing(
                        UserDo::getCreatedTime, // 根据 createTime 排序
                        Comparator.nullsLast(Comparator.naturalOrder()) // 处理 null 值，null 放到最后
                ))
                .orElse(null);
    }



    public static UserDo getUserFromDbByUserId(String userId) {
        if (!StringUtils.hasLength(userId)) {
            return null;
        }
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setUserId(userId);
        List<UserStatusEnum> statusList = new ArrayList<>();
        statusList.add(UserStatusEnum.NORMAL);
        statusList.add(UserStatusEnum.CANCELLED);
        queryUserCondition.setStatusList(statusList);
        DomainServiceContext context = new DomainServiceContext(DEFAULT);
        List<UserDo> userDoList = context.read(queryUserCondition, UserDo.class);
        if (CollectionUtils.isEmpty(userDoList)) {
            return null;
        }
        return userDoList.stream()
                .filter(Objects::nonNull)
                .min(Comparator.comparing(
                        UserDo::getCreatedTime, // 根据 createTime 排序
                        Comparator.nullsLast(Comparator.naturalOrder()) // 处理 null 值，null 放到最后
                ))
                .orElse(null);
    }

    public static void insertUser(UserDo userDo) {
        DomainServiceContext userWriteContext = new DomainServiceContext(INSERT_USER_INFO);
        userWriteContext.putInstance(userDo);
        userWriteContext.writeAndFlush();
    }

    /**
     * 更新用户信息
     *
     * @param userDo 待更新的用户
     */
    public static void updateUser(UserDo userDo) {
        DomainServiceContext userWriteContext = new DomainServiceContext(UPDATE_USER_INFO);
        userWriteContext.putInstance(userDo);
        userWriteContext.writeAndFlush();
    }

    public static UserDo queryUser(String masterUserPhoneNumber, String masterUserDomainId) {
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setAccount(masterUserPhoneNumber);
        queryUserCondition.setUserDomainId(masterUserDomainId);
        DomainServiceContext userWriteContext = new DomainServiceContext(USER_LSB_SERVICE);
        return userWriteContext.readFirst(queryUserCondition, UserDo.class);
    }

    public static UserDo queryUser(QueryUserCondition queryUserCondition) {
        DomainServiceContext userWriteContext = new DomainServiceContext(UserServiceId.QUERY_USER_INFO);
        return userWriteContext.readFirst(queryUserCondition, UserDo.class);
    }

    public static void fillGotoneInfo(UserDo userDo) {
        if (!UserStatusEnum.NORMAL.equals(userDo.getStatus())) {
            log.error("User status is not normal, cannot fill gotone info. userDo = [{}]", userDo);
            return;
        }
        DomainServiceContext userContext = new DomainServiceContext(UserServiceId.QUERY_USER_GOTONE_INFO);
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setAccount(userDo.getMsisdn());
        UserDo gotoneUserDo = userContext.readFirst(queryUserCondition, UserDo.class);
        if (gotoneUserDo != null) {
            userDo.setGotoneUserLevel(gotoneUserDo.getGotoneUserLevel());
            userDo.setGotoneCustType(gotoneUserDo.getGotoneCustType());
            userDo.setFilledGotone(true);
        }
    }
}
