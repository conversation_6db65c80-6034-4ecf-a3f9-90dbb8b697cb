package com.zyhl.yun.member.mcdmc.activation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.activation.WorkOrderServiceId;
import com.zyhl.yun.member.common.util.StringUtil;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.mcdmc.activation.constants.ServiceCodeConstants;
import com.zyhl.yun.member.mcdmc.activation.constants.WorkAttrCodeConstant;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowLogDo;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.FlowLogStateEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.ActivationContextUtil;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderAttrDo;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.conditions.QueryWorkOrderCondition;
import com.zyhl.yun.member.mcdmc.activation.repository.WorkServiceFlowLogRepository;
import com.zyhl.yun.member.mcdmc.activation.serviceid.LocalServiceId;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 工单driver测试类
 *
 * <AUTHOR>
 * @since 2024/07/28 14:32
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ActivationApplication.class)
@AutoConfigureMockMvc
public class WorkDriverTest {

    @Resource
    private WorkServiceFlowLogRepository workServiceFlowLogRepository;

    @Test
    public void testWorkAttrDriver() {
        QueryWorkOrderCondition qryWorkOrderCondition = new QueryWorkOrderCondition();
        qryWorkOrderCondition.setUserId("***************");
        qryWorkOrderCondition.setOrderIdList(Collections.singletonList("***************_3c1dd4bc2f7743488fa5af65d1368fcd_CloudSpaceContractSubscribeOrder"));
        qryWorkOrderCondition.setServiceCodeList(Collections.singletonList(ServiceCodeConstants.NORMAL_RAI_SUB_SERVICE_CODE));
        qryWorkOrderCondition.setAttrKeyList(Arrays.asList(WorkAttrCodeConstant.RAI_SUB_FAIL_BODY, WorkAttrCodeConstant.RIGHTS_OUT_ACCOUNT_ID));
        DomainServiceContext activationContext = new DomainServiceContext(WorkOrderServiceId.QUERY_ACTIVATION_ATTR);
        List<WorkOrderDo> workOrderDoList = activationContext.read(qryWorkOrderCondition, WorkOrderDo.class);
        String rightOutAccountId = null;
        String extension3 = null;
        for (WorkOrderDo workOrderDo : workOrderDoList) {
            rightOutAccountId = CollUtil.emptyIfNull(workOrderDo.getWorkOrderAttrDoList()).stream()
                    .filter(attr -> WorkAttrCodeConstant.RIGHTS_OUT_ACCOUNT_ID.equals(attr.getAttrCode()))
                    .map(WorkOrderAttrDo::getAttrVal)
                    .filter(StringUtil::isNotEmpty)
                    .findFirst().orElse(null);
            extension3 = CollUtil.emptyIfNull(workOrderDo.getWorkOrderAttrDoList()).stream()
                    .filter(attr -> WorkAttrCodeConstant.RAI_SUB_FAIL_BODY.equals(attr.getAttrCode()))
                    .map(WorkOrderAttrDo::getAttrVal)
                    .findFirst().orElse(null);
        }
        log.info("rightOutAccountId={},extension3={}", rightOutAccountId, extension3);
        Assert.assertNotNull(extension3);
    }

    @Test
    public void testGoodInstance() {
        GoodsInstanceDo goodsInstanceDo = MemberContextUtil.qryGoodsInstanceDo("************", "1867043749781409792");
        log.info("goodsInstanceDo={}", goodsInstanceDo);
    }

    @Test
    public void testUpsertAttr() {
        boolean isInsertSuccess = true;
        try {
            String randomWorkId = RandomUtil.randomString(10);
            ActivationContextUtil.upsertWorkAttr("testUserId1", randomWorkId, "testInsert", "testInsert");
        } catch (Exception e) {
            log.error("error", e);
            isInsertSuccess = false;
        }
        Assert.assertTrue("插入日志操作应该成功", isInsertSuccess);
        boolean isUpdateSuccess = true;
        try {
            String randomString = RandomUtil.randomString(10);
//            ActivationContextUtil.upsertWorkAttr("************", "607817243403427843", "CANCEL_CAUSE", "testUpdate" + randomString);
        } catch (Exception e) {
            log.error("error", e);
            isUpdateSuccess = false;
        }
        Assert.assertTrue("更新日志操作应该成功", isUpdateSuccess);
    }

    @Test
    public void testFlowLogUpsert() {
        // 测试插入操作
        boolean isInsertSuccess = true;
        WorkServiceFlowLogDo flowLogDo = new WorkServiceFlowLogDo();
        try {
            String randomLogId = RandomUtil.randomString(10);
            flowLogDo.setLogId(randomLogId);
            flowLogDo.setUserId("testUserId1");
            flowLogDo.setServiceCode(LocalServiceId.UPSERT_OPERATION);
            flowLogDo.setWorkId(RandomUtil.randomString(8));
            flowLogDo.setRequestBody("测试请求参数");
            flowLogDo.setResponseBody("测试响应参数");
            flowLogDo.setState(FlowLogStateEnum.PARAM_HANDLE);
            DomainServiceContext context = new DomainServiceContext(LocalServiceId.UPSERT_OPERATION);
            context.putInstance(flowLogDo);
            context.writeAndFlush();
        } catch (Exception e) {
            log.error("插入日志失败", e);
            isInsertSuccess = false;
        }
        Assert.assertTrue("插入日志操作应该成功", isInsertSuccess);
        boolean isUpdateSuccess = true;
        try {
            String randomString = RandomUtil.randomString(10);
            flowLogDo.setRequestBody(randomString);
            DomainServiceContext context = new DomainServiceContext(LocalServiceId.UPSERT_OPERATION);
            context.putInstance(flowLogDo);
            context.writeAndFlush();
        } catch (Exception e) {
            log.error("更新日志失败", e);
            isUpdateSuccess = false;
        }
        Assert.assertTrue("更新日志操作应该成功", isUpdateSuccess);
    }

}
