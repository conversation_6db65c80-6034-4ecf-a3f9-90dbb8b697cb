package com.zyhl.yun.member.mcdmc.activation.remote.send.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/06/11 14:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement(name = "notifySubChange")
@XmlAccessorType(XmlAccessType.FIELD)
public class BmsNscReq implements Serializable {
    @XmlElement(name = "NotifySubChangeReq")
    private NotifySubChangeReq req;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class NotifySubChangeReq implements Serializable {
        /**
         * 事件类型
         */
        private int eventType;

        /**
         * 交易流水号，调用方保证唯一
         */
        private String transactionId;

        /**
         * MCS 用户ID
         */
        private String userID;

        private String account;

        /**
         * 产品类型
         */
        private int productType;

        /**
         * 套餐Id
         */
        private String serviceId;

        /**
         * 套餐订购关系ID
         */
        private String seqId;

        /**
         * 套餐起始日期
         */
        private String beginDate;

        /**
         * 套餐终止日期
         */
        private String endDate;

        /**
         * 变更类型：1会员产品退订，2会员产品续订
         */
        private Integer changeType;

        /**
         * 腾讯独立空间的ownerId
         */
        private String ownerId;

        /**
         * 订购用户类型，1：云盘产品大陆用户；2：独立空间大陆用户；3：香港用户, 4：巴基斯坦用户    默认为1
         */
        private String subUserType;

        @Override
        public String toString() {
            return "NotifySubChangeReq [eventType=" + eventType + ", transactionId=" + transactionId + ", userID=" + userID
                    + ", account=" + account + ", productType=" + productType + ", serviceId="
                    + serviceId + ", seqId=" + seqId + ", beginDate=" + beginDate + ", endDate=" + endDate + "]";
        }
    }

}
