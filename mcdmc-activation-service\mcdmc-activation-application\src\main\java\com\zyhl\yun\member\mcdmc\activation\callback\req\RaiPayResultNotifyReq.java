package com.zyhl.yun.member.mcdmc.activation.callback.req;

import lombok.Data;

/**
 * 权益中心续订、退订结果通知请求类
 *
 * <AUTHOR>
 * @date 2023/02/22
 */
@Data
public class RaiPayResultNotifyReq {
    /**
     * 消息类型，填写“PayResultNotifyReq”
     */
    String msgType;

    /**
     * 该接口消息的版本号，版本默认为“1.0”
     */
    String msgVer;

    /**
     * 支付中心订单号
     */
    String orderId;

    /**
     * 商户订单ID(权益中心vip_order表payId字段)
     */
    String outOrderId;

    /**
     * 支付结果，取值：
     * 0：表示支付成功
     * 1：表示支付失败
     * 2：对账补单支付成功
     * 3：退订成功（解约成功）
     * 4：退订失败
     * 5：已签约
     * 6：标识续约扣款失败
     * 7：apple pay退款成功
     * 8：apple pay退款失败
     * 9：apple pay重启订阅
     */
    String payResult;

    /**
     * payResult取值为0/2/7时有效， 实际支付的金额，以分为单位
     */
    Integer amount;

    /**
     * payResult取值为0/2/7时有效， 支付机构，取值参考附录定义参照表。
     */
    String payOrganization;

    /**
     * payResult取值为0/2/7时有效, 用户完成支付的时间，yyyyMMddHHmmss
     */
    String payDate;

    /**
     * payResult取值为0/2时有效
     */
    String reserved1;

    /**
     * payResult取值为0/2/7时有效,支付通道，取值参考附录定义参照表。
     */
    String payWay;

    /**
     * payResult取值为0/2/7时有效
     */
    Integer payType;

    /**
     * 次月签约结果通知必填。
     */
    Integer wxPlan_id;

    /**
     * 次月周期扣款结果通知必填。次月周期扣款处理时间（YYYYMMDDHH24MISS）
     */
    String contractDate;

    /**
     * 次月周期扣款结果通知必填。次月周期扣款费用,整数，单位：分
     */
    Integer contractFee;

    /**
     * 订单标识,本次通知是测试订单值为1，否则通知报文不含该字段
     */
    String orderType;

    /**
     * 数字签名, 对以上所有字段按照定义的顺序取值再与网元密钥key拼接相连，然后进行SHA1摘要算法所得值，最后转换成16进制字符串（大写）
     */
    String hmac;

    /**
     * 消息类型:(1.首次订购,2.续订，3.退订，4签约,5单次订购,7 apple pay退款,8 apple pay 重启订阅)当版本号msgVer为非1.0时，需要加上该字段
     */
    String NotifyType;

    /**
     * 支付宝、微信等第三支付平台交易流水号
     */
    String tradeNo;

    /**
     * 苹果支付时传入
     */
    String appleAppId;
}
