package com.zyhl.yun.member.mcdmc.activation.domain.exception;

import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发货流程接口异常
 *
 * <AUTHOR>
 * @since 2024/07/31 10:32
 */
@Getter
@AllArgsConstructor
public class FlowIFaceException extends RuntimeException {
    /**
     * 流程接口类名
     */
    private final String iFaceName;

    /**
     * 工单id
     */
    private final String workId;

    /**
     * 工单参数/属性
     */
    private final String workAttrs;
    /**
     * 终止原因
     */
    private final String reason;
    /**
     * 原始异常
     */
    private final Exception originException;

    public FlowIFaceException(String iFaceClassName, WorkOrderDo workOrderDo, String errorMsg, Exception originException) {
        super(errorMsg, originException);
        this.iFaceName = iFaceClassName;
        this.workId = workOrderDo.getWorkId();
        this.workAttrs = workOrderDo.getWorkAttrs();
        this.reason = errorMsg;
        this.originException = originException;
    }

    @Override
    public String toString() {
        return "流程接口异常：" + iFaceName + "，工单id：" + workId +
                "，工单参数为：" + workAttrs + "，终止原因：" + reason +
                "，原始异常：" + originException.toString();
    }

    @Override
    public String getMessage() {
        return this.getClass().getSimpleName() + " message is :" + super.getMessage()
                + "originException message is :" + originException.getMessage();
    }
}
