package com.zyhl.yun.member.vip.domain.handler;


import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.domain.ose.resp.GetUserResp;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.user.domain.QueryUserCondition;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.goodsInstance.domain.handler.GoodsInstanceDoConditionHandler;
import com.zyhl.yun.member.vip.utils.UserInfoOperatorUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.*;



@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class UserQuerySingleHandlerTest {

    @InjectMocks
    private UserQueryOnlyHandler userQuerySingleHandler;

    @InjectMocks
    private GoodsInstanceDoConditionHandler goodsInstanceDoConditionHandler;

    @Mock
    private DomainServiceContext domainServiceContext;

    @Mock
    private Callable<Serializable> getter;

    @Mock
    private UserInfoOperatorUtil userDomainUtil;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void handleReadByConditionAction_StatusListIsNull_DefaultToNormal() throws Exception {
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setUserId("userId");
        when(getter.call()).thenReturn((Serializable) Collections.emptyList());

        Serializable result = userQuerySingleHandler.handleReadByConditionAction(domainServiceContext, queryUserCondition, getter);

        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void handleReadByConditionAction_UserIdQueryNoData_ReturnsNull() throws Exception {
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setUserId("userId");
        when(getter.call()).thenReturn((Serializable) Collections.emptyList());

        Serializable result = userQuerySingleHandler.handleReadByConditionAction(domainServiceContext, queryUserCondition, getter);

        assertNull(result);
    }

    @Test
    public void handleReadByConditionAction_NoUserData_ReturnsEmptyList() throws Exception {
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        when(getter.call()).thenReturn((Serializable) Collections.emptyList());

        Serializable result = userQuerySingleHandler.handleReadByConditionAction(domainServiceContext, queryUserCondition, getter);

        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void handleReadByConditionAction_UserDomainIdIsNull_UpdatesDomainId() throws Exception {
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        UserDo userDo = new UserDo();
        userDo.setMsisdn("1234567890");
        List<UserDo> userDos = new ArrayList<>();
        userDos.add(userDo);
        when(getter.call()).thenReturn((Serializable) userDos);
        GetUserResp userDomainUserMsg = new GetUserResp();
        userDomainUserMsg.setUserDomainId("userDomainId");
        when(userDomainUtil.getUserDomainUserInfo("1234567890", null)).thenReturn(userDomainUserMsg);

        Serializable result = userQuerySingleHandler.handleReadByConditionAction(domainServiceContext, queryUserCondition, getter);

        assertEquals(userDos, result);
        assertEquals("userDomainId", userDo.getUserDomainId());
    }

    @Test
    public void handleReadByConditionAction_UserDomainIdNotNull_UpdatesUserInfo() throws Exception {
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        UserDo userDo = new UserDo();
        userDo.setMsisdn("1234567890");
        userDo.setUserDomainId("userDomainId");
        List<UserDo> userDos = new ArrayList<>();
        userDos.add(userDo);
        when(getter.call()).thenReturn((Serializable) userDos);
        GetUserResp userByPhone = new GetUserResp();
        userByPhone.setNationCode("nationCode");
        when(userDomainUtil.getUserByPhone(userDo.getMsisdn())).thenReturn(userByPhone);

        Serializable result = userQuerySingleHandler.handleReadByConditionAction(domainServiceContext, queryUserCondition, getter);

        assertEquals(userDos, result);
        assertEquals("nationCode", userDo.getNationCode());
    }


    @Test
    public void handleReadByConditionAction() throws Exception {
        QueryGoodsInstanceCondition queryGoodsInstanceCondition = new QueryGoodsInstanceCondition();
        // 设置商品实例ID列表（模拟3个实例ID）
        queryGoodsInstanceCondition.setGoodsInstanceIdList(
                Arrays.asList("inst_001", "inst_002", "inst_003"));

        // 设置商品ID列表（模拟2个商品ID）
        queryGoodsInstanceCondition.setGoodsIdList(
                Arrays.asList("goods_001", "goods_002"));

        // 设置订单ID列表（JSON格式）
        queryGoodsInstanceCondition.setOrderIdList(
                Arrays.asList("orders1", "orders2"));

        // 设置用户ID（模拟单个用户查询）
        queryGoodsInstanceCondition.setPhoneNumber("15820178757");

        Serializable result =  goodsInstanceDoConditionHandler.handleReadByConditionAction(domainServiceContext, queryGoodsInstanceCondition, getter);

        List<GoodsInstanceDo> list = (List<GoodsInstanceDo>) result;
        System.out.println(list);

    }
}
