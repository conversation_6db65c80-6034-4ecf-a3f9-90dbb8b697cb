package com.zyhl.yun.member.mcdmc.activation.convertor;

import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowIFaceDo;
import com.zyhl.yun.member.mcdmc.activation.po.WorkServiceFlowIFacePo;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/08/02 18:59
 */
@Mapper(componentModel = "spring")
public interface WorkServiceFlowIFaceConvertor {
    List<WorkServiceFlowIFaceDo> toDoList(List<WorkServiceFlowIFacePo> poList);
}
