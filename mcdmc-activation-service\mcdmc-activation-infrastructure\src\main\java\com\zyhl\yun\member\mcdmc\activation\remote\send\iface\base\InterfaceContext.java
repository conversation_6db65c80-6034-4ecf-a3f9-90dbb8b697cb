package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base;

import cn.hutool.core.util.JAXBUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.activation.WorkOrderServiceId;
import com.zyhl.yun.member.mcdmc.activation.constants.WorkAttrCodeConstant;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.DbFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowIFaceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowLogDo;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.FlowLogStateEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowIFaceException;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.ActivationContextUtil;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.CustomDidGenerator;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.XMLUtil;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.facade.ActivationServiceFacade;
import com.zyhl.yun.member.mcdmc.activation.remote.client.RemoteClient;
import com.zyhl.yun.member.mcdmc.activation.remote.client.RemoteException;
import com.zyhl.yun.member.mcdmc.activation.remote.client.RemoteRequest;
import com.zyhl.yun.member.mcdmc.activation.remote.client.RemoteResponse;
import com.zyhl.yun.member.mcdmc.activation.serviceid.LocalServiceId;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.TransientDataAccessResourceException;
import org.springframework.http.MediaType;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * 接口上下文，用于存储执行某个外部接口的上下文
 *
 * <AUTHOR>
 * @since 2024/06/28 16:21
 */
@Slf4j
@Data
public class InterfaceContext<T, R> {

    /**
     * 接口对应工单
     */
    private WorkOrderDo workOrderDo;

    /**
     * 流程接口配置
     */
    private WorkServiceFlowIFaceDo workServiceFlowIFaceDo;

    /**
     * 外部传入的公共请求入参
     */
    private ComSendInterfaceReq comSendReq;

    /**
     * 接口调用请求入参类
     */
    private Class<T> reqClass;
    /**
     * 接口调用请求入参
     */
    private T interfaceReqObj;

    /**
     * 接口调用请求入参字符串
     */
    private String interfaceReqObjStr;

    /**
     * 接口请求头
     */
    private Map<String, String> headerMap;
    /**
     * 接口响应类
     */
    private Class<R> rspClass;

    /**
     * 接口调用请求响应
     */
    private R interfaceRspObj;

    /**
     * 接口调用请求响应字符串
     */
    private String interfaceRspObjStr;

    /**
     * 回调查询的条件
     */
    private String callbackCondition;

    /**
     * 接口日志
     */
    private WorkServiceFlowLogDo workServiceFlowLog;

    /**
     * 上一个iface的返回结果，可能会为null
     */
    private IFlowResult lastFaceResult;

    /**
     * 当前接口执行的流程状态
     */
    FlowLogStateEnum curFlowLogStateEnum = FlowLogStateEnum.PARAM_HANDLE;
    /**
     * 表示当前执行流程的下一步
     */
    private NextHintEnum nextHintEnum = NextHintEnum.NEXT;

    /**
     * 更新异常时的流程状态
     */
    private FlowLogStateEnum errorFlowLogStateEnum;

    private InterfaceContext() {
    }

    public MediaType getMediaType() {
        return MediaType.parseMediaType(workServiceFlowIFaceDo.getMediaType());
    }

    public R getInterfaceRspObj() {
        if (this.interfaceRspObj == null) {
            // 防止空指针异常
            this.fillInterfaceContext();
        }
        return this.interfaceRspObj;
    }

    public T getInterfaceReqObj() {
        if (this.interfaceReqObj == null) {
            // 防止空指针异常
            this.fillInterfaceContext();
        }
        return this.interfaceReqObj;
    }

    /**
     * 获取接口上下文
     *
     * @param workOrderDo            工单信息
     * @param workServiceFlowIFaceDo 流程接口配置信息
     * @param <T>                    接口请求体的类型
     * @param <R>                    接口响应体的类型
     * @return 接口上下文
     */
    public static <T, R> InterfaceContext<T, R> newInstance(WorkOrderDo workOrderDo,
                                                            WorkServiceFlowIFaceDo workServiceFlowIFaceDo,
                                                            Class<T> reqClass, Class<R> rspClass,
                                                            IFlowResult lastFaceResult) {
        InterfaceContext<T, R> interfaceContext = new InterfaceContext<>();
        interfaceContext.workOrderDo = workOrderDo;
        interfaceContext.workServiceFlowIFaceDo = workServiceFlowIFaceDo;
        interfaceContext.comSendReq = JSONUtil.toBean(workOrderDo.getWorkAttrs(), ComSendInterfaceReq.class);
        interfaceContext.reqClass = reqClass;
        interfaceContext.rspClass = rspClass;
        // 填充接口日志
        // TODO-2024/10/26: 考虑接口日志每次新增一条（如有调用）
        WorkServiceFlowLogDo workServiceFlowLogDo = searchOldFlowLog(workOrderDo, workServiceFlowIFaceDo, interfaceContext);
        interfaceContext.workServiceFlowLog = workServiceFlowLogDo;
        interfaceContext.interfaceReqObjStr = workServiceFlowLogDo.getRequestBody();
        interfaceContext.interfaceRspObjStr = workServiceFlowLogDo.getResponseBody();
        interfaceContext.callbackCondition = workServiceFlowLogDo.getCallbackCondition();
        interfaceContext.headerMap = JSONUtil.toBean(workServiceFlowLogDo.getRequestHeader(), HashMap.class);
        // 上一个Iface的接口，可能为空
        interfaceContext.lastFaceResult = lastFaceResult;
        return interfaceContext;
    }

    /**
     * 查询原来的接口日志
     */
    private static <T, R> WorkServiceFlowLogDo searchOldFlowLog(WorkOrderDo workOrderDo,
                                                                WorkServiceFlowIFaceDo iFaceDo,
                                                                InterfaceContext<T, R> interfaceContext) {
        //初始化工单日志
        DomainServiceContext context = new DomainServiceContext(WorkOrderServiceId.QUERY_ACTIVATION);
        BaseCondition baseCondition = new BaseCondition();
        baseCondition.putCondition(DbFieldConstants.SERVICE_CODE, workOrderDo.getServiceCode());
        baseCondition.putCondition(DbFieldConstants.WORK_ID, workOrderDo.getWorkId());
        baseCondition.putCondition(DbFieldConstants.ORDER_ID, workOrderDo.getOrderId());
        baseCondition.putCondition(DbFieldConstants.USER_ID, workOrderDo.getUserId());
        baseCondition.putCondition(DbFieldConstants.IFACE_ID, String.valueOf(iFaceDo.getIFaceId()));
        WorkServiceFlowLogDo workServiceFlowLogDo = context.readLast(baseCondition, WorkServiceFlowLogDo.class);
        if (workServiceFlowLogDo != null && FlowLogStateEnum.isNeedShipment(workServiceFlowLogDo.getState())) {
            //如果不是成功状态，则查询日志属性是否有备份
            String flowLogBackupStr = ActivationServiceFacade.getSingleWorkAttrValue(workOrderDo.getUserId(), workOrderDo.getWorkId(), WorkAttrCodeConstant.FLOW_LOG_BACKUP);
            if (StringUtils.isNotBlank(flowLogBackupStr)) {
                WorkServiceFlowLogDo flowLogBackUpDo = JSONUtil.toBean(flowLogBackupStr, WorkServiceFlowLogDo.class);
                if (flowLogBackUpDo != null && flowLogBackUpDo.getState().getState() > workServiceFlowLogDo.getState().getState()) {
                    // 备份日志的流程比流程日志表的要往后，则优先用备份日志流程
                    workServiceFlowLogDo = flowLogBackUpDo;
                }
            }
        }
        if (null == workServiceFlowLogDo) {
            // 第一次执行流程时查出来都是为空，初始化日志
            workServiceFlowLogDo = initServiceFlowLog(workOrderDo, iFaceDo, interfaceContext.getComSendReq());
        }
        return workServiceFlowLogDo;
    }

    /**
     * 初始化接口日志
     *
     * @param workOrderDo 工单实体
     * @param iFaceDo     接口流程
     * @param comSendReq  公共请求入参
     * @return 接口日志
     */
    private static WorkServiceFlowLogDo initServiceFlowLog(WorkOrderDo workOrderDo, WorkServiceFlowIFaceDo
            iFaceDo, ComSendInterfaceReq comSendReq) {
        // 填充日志
        WorkServiceFlowLogDo workServiceFlowLog = new WorkServiceFlowLogDo();
        workServiceFlowLog.setLogId(CustomDidGenerator.generateId());
        workServiceFlowLog.setWorkId(workOrderDo.getWorkId());
        workServiceFlowLog.setUserId(workOrderDo.getUserId());
        workServiceFlowLog.setOrderId(workOrderDo.getOrderId());
        workServiceFlowLog.setServiceCode(workOrderDo.getServiceCode());
        workServiceFlowLog.setIFaceId(iFaceDo.getIFaceId());
        workServiceFlowLog.setWorkServiceFlowId(iFaceDo.getWorkServiceFlowId());
        workServiceFlowLog.setUrl(iFaceDo.getUrl());
        workServiceFlowLog.setCallbackUrl(iFaceDo.getNotifyUri());
        workServiceFlowLog.setState(FlowLogStateEnum.PARAM_HANDLE);
        workServiceFlowLog.setRetryCount(0);
        workServiceFlowLog.setExtParams(JSONUtil.toJsonStr(comSendReq));
        return workServiceFlowLog;
    }


    /**
     * 处理请求参数
     *
     * @param paramChecker 请求参数校验器
     * @param reqObjGetter 请求体构造器
     * @param headerGetter 请求头构造器
     */
    public void doHandleParam(Consumer<InterfaceContext<T, R>> paramChecker,
                              Function<InterfaceContext<T, R>, T> reqObjGetter,
                              Function<InterfaceContext<T, R>, Map<String, String>> headerGetter) {
        this.doExecute(() -> {
            // 请求参数校验
            paramChecker.accept(this);
            // 获取接口请求入参
            T requestBody = reqObjGetter.apply(this);
            this.interfaceReqObj = requestBody;
            // 请求入参转字符串
            this.interfaceReqObjStr = convertBean2Str(requestBody, reqClass);
            // 获取请求头
            this.headerMap = headerGetter.apply(this);
        }, FlowLogStateEnum.PARAM_HANDLE_EXCEPTION, "参数处理异常", true);
    }

    /**
     * 调用远程
     */
    public void doHandleRemote(RemoteClient remoteClient,
                               Function<InterfaceContext<T, R>, String> callbackConditionGetter) {
        this.doExecute(() -> {
            // 调用第三方接口
            // TODO-2024/11/6: 接口超时时间考虑放置接口配置
            RemoteRequest<String> remoteRequest = RemoteRequest.getPostInstance(
                    workServiceFlowIFaceDo.getUrl(), interfaceReqObjStr, headerMap,
                    getMediaType(), String.class);
            RemoteResponse<String> remoteResponse = remoteClient.postSync(remoteRequest);
            // 更新接口日志的响应状态码
            this.workServiceFlowLog.setResponseHttpCode(remoteResponse.getResponseHttpCode());
            this.workServiceFlowLog.setResponseHeader(JSONUtil.toJsonStr(remoteResponse.getResponseHeader()));
            if (remoteResponse.getRemoteException() != null) {
                this.interfaceRspObjStr = remoteResponse.getRemoteException().getMessage();
                // 远程调用异常
                throw new RemoteException(remoteResponse.getRemoteException());
            }
            this.interfaceRspObjStr = remoteResponse.getResponseData();
            this.interfaceRspObj = convertStr2Bean(interfaceRspObjStr, rspClass);
            // 设置回调查询条件
            this.callbackCondition = callbackConditionGetter.apply(this);
            // 修改当前状态为远程调用成功
            this.curFlowLogStateEnum = FlowLogStateEnum.SHIPMENT_REMOTE_SC;
        }, FlowLogStateEnum.SHIPMEN_REMOTE_EXCEPTION, "远程调用异常", false);
    }

    /**
     * 处理结果
     *
     * @param businessSuccessChecker   业务是否成功的校验器
     * @param businessSuccessExecutor  业务成功执行器
     * @param businessFailExecutor     业务失败执行器
     * @param businessCompleteExecutor 业务完成执行器
     */
    public void doHandleResult(Predicate<R> businessSuccessChecker,
                               Consumer<InterfaceContext<T, R>> businessSuccessExecutor,
                               Consumer<InterfaceContext<T, R>> businessFailExecutor,
                               Consumer<InterfaceContext<T, R>> businessCompleteExecutor) {
        this.doExecute(() -> {
            // 填充上下文的请求出入参
            this.fillInterfaceContext();
            // 判断执行结果
            boolean isSuccess = businessSuccessChecker.test(this.interfaceRspObj);
            // 根据响应结果执行不同流程
            if (isSuccess) {
                // 成功响应执行
                businessSuccessExecutor.accept(this);
                // 设置为发货成功
                this.curFlowLogStateEnum = FlowLogStateEnum.SHIPMENT_SUCCESS;
            } else {
                // 失败相应执行
                businessFailExecutor.accept(this);
                // 设置为发货失败
                this.curFlowLogStateEnum = FlowLogStateEnum.SHIPMENT_FAILED;
            }
            // 业务完成前执行流程
            businessCompleteExecutor.accept(this);
        }, FlowLogStateEnum.SHIPMENT_EXCEPTION, "处理结果异常", false);
    }

    /**
     * 统一执行器
     *
     * @param runnable        执行流程方法
     * @param exceptionStatus 异常状态
     * @param errorMsg        异常信息
     * @param isUpsertFlowLog 是否更新或插入流程日志
     */
    private void doExecute(Runnable runnable, FlowLogStateEnum exceptionStatus, String errorMsg, boolean isUpsertFlowLog) {
        try {
            //执行流程
            runnable.run();
            // 更新状态
            this.upsertFlowLogDo(isUpsertFlowLog, true);
        } catch (Exception e) {
            if (e instanceof FlowTerminationException) {
                // 流程终止异常则直接抛出
                FlowTerminationException terminationException = (FlowTerminationException) e;
                NextHintEnum nextHint = terminationException.getNextHint();
                if (NextHintEnum.FINISH.equals(nextHint)) {
                    // 如果提前成功，则直接更新为发货成功
                    this.curFlowLogStateEnum = FlowLogStateEnum.SHIPMENT_SUCCESS;
                } else if (NextHintEnum.RESTART.equals(nextHint)) {
                    // 如果需要重试，则直接更新为发货失败
                    this.curFlowLogStateEnum = FlowLogStateEnum.SHIPMENT_FAILED;
                }
            } else {
                // 流程失败时更新为异常日志状态
                this.curFlowLogStateEnum = exceptionStatus;
            }
            // 更新状态
            this.upsertFlowLogDo(isUpsertFlowLog, true);
            if (e instanceof FlowTerminationException) {
                // 流程终止异常则直接抛出
                throw e;
            }
            // 抛出自定义异常
            throw new FlowIFaceException(workServiceFlowIFaceDo.getIFaceClassName(), workOrderDo, errorMsg, e);
        }
    }

    /**
     * 将请求体转化成字符串，默认用json工具类转换
     */
    private String convertBean2Str(Object obj, Class<T> objClass) {
        if (MediaType.APPLICATION_XML.equals(getMediaType()) || MediaType.TEXT_XML.equals(getMediaType())) {
            return XMLUtil.beanToXml(obj, objClass);
        }
        return JSONUtil.toJsonStr(obj);
    }

    private R convertStr2Bean(String str, Class<R> rspClass) {
        MediaType curMediaType = getMediaType();
        if (MediaType.APPLICATION_XML.equals(curMediaType) || MediaType.TEXT_XML.equals(curMediaType)) {
            return XMLUtil.xmlToBean(str, rspClass);
        }
        return JSONUtil.toBean(str, rspClass);
    }

    /**
     * 更新接口调用日志状态状态
     *
     * @param isUpsertFlowLog 是否更新或插入流程日志
     */
    private void upsertFlowLogDo(boolean isUpsertFlowLog, boolean isUpsertAttrWhileUpdateErr) {
        workServiceFlowLog.setCallbackCondition(callbackCondition);
        workServiceFlowLog.setRequestBody(interfaceReqObjStr);
        workServiceFlowLog.setRequestHeader(JSONUtil.toJsonStr(headerMap));
        workServiceFlowLog.setResponseBody(interfaceRspObjStr);
        workServiceFlowLog.setState(curFlowLogStateEnum);

        String serviceId = LocalServiceId.UPDATE_BY_ID_OPERATION;
        if (Boolean.TRUE.equals(isUpsertFlowLog)) {
            serviceId = LocalServiceId.UPSERT_OPERATION;
        }
        try {
            DomainServiceContext context = new DomainServiceContext(serviceId);
            context.putInstance(workServiceFlowLog);
            // 持久化接口日志
            context.writeAndFlush();
        } catch (TransientDataAccessResourceException e) {
            log.error("upsertFlowLog error,isUpsertFlowLog={},isUpsertAttrWhileUpdateErr={}，detail exception is ",
                    isUpsertFlowLog, isUpsertAttrWhileUpdateErr, e);
            // 记录流程日志备份
            if (Boolean.TRUE.equals(isUpsertAttrWhileUpdateErr)) {
                // 记录此时异常的状态
                errorFlowLogStateEnum = workServiceFlowLog.getState();
                ActivationContextUtil.upsertWorkAttr(workServiceFlowLog.getUserId(), workServiceFlowLog.getWorkId(),
                        WorkAttrCodeConstant.FLOW_LOG_BACKUP, JSONUtil.toJsonStr(workServiceFlowLog));
            }
        }
    }

    /**
     * 填充接口上下文：请求出入参
     */
    private void fillInterfaceContext() {
        MediaType mediaType = this.getMediaType();
        if (MediaType.APPLICATION_XML.equals(mediaType) || MediaType.TEXT_XML.equals(mediaType)) {
            if (this.interfaceReqObj == null && StringUtils.isNotEmpty(this.interfaceReqObjStr)) {
                this.interfaceReqObj = JAXBUtil.xmlToBean(this.interfaceReqObjStr, reqClass);
            }
            if (this.interfaceRspObj == null && StringUtils.isNotEmpty(this.interfaceRspObjStr)) {
                this.interfaceRspObj = XMLUtil.xmlToBean(this.interfaceRspObjStr, rspClass);
            }
        } else {
            if (this.interfaceReqObj == null && StringUtils.isNotEmpty(this.interfaceReqObjStr) && JSONUtil.isTypeJSON(this.interfaceReqObjStr)) {
                this.interfaceReqObj = JSONUtil.toBean(this.interfaceReqObjStr, reqClass);
            }
            if (this.interfaceRspObj == null && StringUtils.isNotEmpty(this.interfaceRspObjStr) && JSONUtil.isTypeJSON(this.interfaceRspObjStr)) {
                this.interfaceRspObj = JSONUtil.toBean(this.interfaceRspObjStr, rspClass);
            }
        }
    }

    /**
     * 检查是否有过更新流程日志异常，有则最后再尝试更新一次流程日志表
     */
    public void checkAndFillFLowLog() {
        if (errorFlowLogStateEnum != null && errorFlowLogStateEnum.getState() == curFlowLogStateEnum.getState()) {
            // 仅在出现更新异常且为最后一次更新也失败时才去在尝试更新一次流程日志表
            this.upsertFlowLogDo(false, false);
        }
    }
}
