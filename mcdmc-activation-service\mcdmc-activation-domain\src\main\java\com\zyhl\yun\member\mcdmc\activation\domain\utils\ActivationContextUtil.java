package com.zyhl.yun.member.mcdmc.activation.domain.utils;

import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.mcdmc.activation.constants.WorkAttrCodeConstant;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderAttrDo;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.enums.WorkOrderStateEnum;
import com.zyhl.yun.member.mcdmc.activation.serviceid.LocalServiceId;
import lombok.extern.slf4j.Slf4j;


/**
 * 工单相关工具类
 *
 * <AUTHOR>
 * @since 2025/01/01 10:08
 */
@Slf4j
public class ActivationContextUtil {
    private ActivationContextUtil() {
    }

    /**
     * 流程成功后最终执行的动作
     *
     * @param workOrderDo 工单
     * @param isFinally   是否是最终流程（是否有回调）
     */
    public static void updateFinallyWorkOrder(WorkOrderDo workOrderDo, boolean isFinally) {
        DomainServiceContext context = new DomainServiceContext(LocalServiceId.UPDATE_BY_ID_OPERATION);
        if (Boolean.TRUE.equals(isFinally)) {
            workOrderDo.setState(WorkOrderStateEnum.COMPLETED);
        } else {
            workOrderDo.setState(WorkOrderStateEnum.BE_CALLBACK);
        }
        context.putInstance(workOrderDo);
        context.writeAndFlush();
    }

    public static void updateFailureStatusAndRecordReason(WorkOrderDo workOrderDo, WorkOrderStateEnum status, String reason) {
        String attrCode;
        if (WorkOrderStateEnum.CANCELED.equals(status)) {
            attrCode = WorkAttrCodeConstant.CANCEL_CAUSE;
        } else if (WorkOrderStateEnum.FAILED.equals(status)) {
            attrCode = WorkAttrCodeConstant.ERROR_MSG;
        } else {
            log.error("更新工单状态失败，状态：{}", status);
            return;
        }
        workOrderDo.updateStateAndSaveDb(status);
        // 插入工单属性
        insertWorkAttr(workOrderDo.getUserId(), workOrderDo.getWorkId(), attrCode, reason);
    }

    /**
     * 插入工单属性
     *
     * @param userId   用户id
     * @param workId   工单id
     * @param attrCode 属性code
     * @param attrVal  属性值
     */
    public static void insertWorkAttr(String userId, String workId, String attrCode, String attrVal) {
        DomainServiceContext attrContext = new DomainServiceContext(LocalServiceId.INSERT_OPERATION);
        WorkOrderAttrDo workOrderAttrDo = attrContext.newInstance(WorkOrderAttrDo.class);
        workOrderAttrDo.setUserId(userId);
        workOrderAttrDo.setWorkId(workId);
        workOrderAttrDo.setAttrCode(attrCode);
        workOrderAttrDo.setAttrVal(attrVal);
        attrContext.writeAndFlush();
    }

    /**
     * 存在则更新，不存在则插入工单属性
     *
     * @param workId   工单id
     * @param attrCode 属性code
     * @param attrVal  属性值
     */
    public static void upsertWorkAttr(String userId, String workId, String attrCode, String attrVal) {
        DomainServiceContext attrContext = new DomainServiceContext(LocalServiceId.UPSERT_OPERATION);
        WorkOrderAttrDo workOrderAttrDo = attrContext.newInstance(WorkOrderAttrDo.class);
        workOrderAttrDo.setUserId(userId);
        workOrderAttrDo.setWorkId(workId);
        workOrderAttrDo.setAttrCode(attrCode);
        workOrderAttrDo.setAttrVal(attrVal);
        attrContext.writeAndFlush();
    }

}
