/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.zyhl.yun.member.mcdmc.activation.domain.utils;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;

/**
 * 和MCS通信的加密工具类
 *
 * <AUTHOR>
 * @version [版本号, 2017年3月1日]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Slf4j
public class NEAuthUtil {
    private static final int AES_BLOCK_SIZE = 16;
    public static final String CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding";
    public static String getCipherAlgorithm(){
        return CIPHER_ALGORITHM;
    }

    /**
     * <一句话功能简述>
     * <功能详细描述>
     *
     * @param neName           neName
     * @param nePasswd         nePasswd
     * @param aesEncryptPasswd aesEncryptPasswd
     * @return String String
     * @throws Exception Exception
     * @see [类、类#方法、类#成员]
     */
    public static String getNeAuthHeader(String neName, String nePasswd, String aesEncryptPasswd) throws Exception {
        return Base64.encode(encrypt((neName + ":" + nePasswd).getBytes("UTF-8"), aesEncryptPasswd.getBytes("UTF-8")));
    }

    /**
     * <一句话功能简述>
     * <功能详细描述>
     *
     * @param content content
     * @param key     key
     * @return byte[] byte
     * @throws Exception Exception
     * @see [类、类#方法、类#成员]
     */
    public static byte[] encrypt(byte[] content, byte[] key) throws Exception {
        if (key == null || key.length != 16) {
            return new byte[0];
        }
        byte[] iv = new byte[AES_BLOCK_SIZE];
        SecureRandom rand = new SecureRandom();
        rand.nextBytes(iv);
        SecretKeySpec skeySpec = new SecretKeySpec(key, "AES");
        Cipher cipher = Cipher.getInstance(getCipherAlgorithm());
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec, ivSpec);
        byte[] cipherText = cipher.doFinal(content);
        byte[] cipherTextWithIv = new byte[cipherText.length + AES_BLOCK_SIZE];
        System.arraycopy(iv, 0, cipherTextWithIv, 0, AES_BLOCK_SIZE);
        System.arraycopy(cipherText, 0, cipherTextWithIv, AES_BLOCK_SIZE, cipherText.length);
        return cipherTextWithIv;
    }

}
