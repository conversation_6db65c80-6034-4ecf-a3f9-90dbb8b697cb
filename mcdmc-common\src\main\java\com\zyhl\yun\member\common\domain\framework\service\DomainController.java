package com.zyhl.yun.member.common.domain.framework.service;

import com.zyhl.yun.member.common.annotation.NoAccountTransform;
import com.zyhl.yun.member.common.domain.framework.ResultWrapper;
import com.zyhl.yun.member.common.domain.framework.constants.DomainHeaderConstant;
import com.zyhl.yun.member.common.domain.framework.executor.DriverExecutorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/07/17 15:22
 */
@RestController
@Slf4j
public class DomainController {

    @PostMapping(value = "/v2/gsdomain/asyncDomainEvent")
    @NoAccountTransform
    public Mono<byte[]> asyncDomainEventV2(@RequestBody byte[] compressData, @RequestHeader Map<String, String> headers) {
        return Mono.defer(() -> Mono.just(DriverExecutorService.execRemoteRequest(compressData)));
    }

    @PostMapping(value = "/v1/gsdomain/asyncDomainEvent")
    @NoAccountTransform
    public Mono<ResultWrapper> asyncDomainEvent(@RequestBody String reqBody, @RequestHeader Map<String, String> headers) {
        return Mono.defer(() -> Mono.just(DriverExecutorService.execRemoteRequest(reqBody)));
    }
}
