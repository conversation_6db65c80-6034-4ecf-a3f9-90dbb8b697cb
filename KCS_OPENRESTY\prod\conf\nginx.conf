# nginx.conf  --  docker-openresty
#
# This file is installed to:
#   `/usr/local/openresty/nginx/conf/nginx.conf`
# and is the file loaded by nginx at startup,
# unless the user specifies otherwise.
#
# It tracks the upstream OpenResty's `nginx.conf`, but removes the `server`
# section and adds this directive:
#     `include /etc/nginx/conf.d/*.conf;`
#
# The `docker-openresty` file `nginx.vh.default.conf` is copied to
# `/etc/nginx/conf.d/default.conf`.  It contains the `server section
# of the upstream `nginx.conf`.
#
# See https://github.com/openresty/docker-openresty/blob/master/README.md#nginx-config-files
#

#user  nobody;
user  hcaiyun hcaiyun;
worker_processes 5;

# Enables the use of JIT for regular expressions to speed-up their processing.
pcre_jit on;



#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    use epoll;
    worker_connections  65535;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    # Enables or disables the use of underscores in client request header fields.
    # When the use of underscores is disabled, request header fields whose names contain underscores are marked as invalid and become subject to the ignore_invalid_headers directive.
    # underscores_in_headers off;


    log_format  main  '$time_iso8601|User_request_status:$status|$request_time|$body_bytes_sent|$remote_addr|$http_x_forwarded_for|$request_method|[$host]|$request_uri| '
                          '[$http_user_agent]|[$http_accept_encoding]|[$http_referer]|$proxy_add_x_forwarded_for| '
                          '$upstream_addr|Server_status:$upstream_status|$upstream_response_time|$request_length';

    access_log  LOG_DIR/access.log  main;
    error_log  LOG_DIR/error.log;
        # Log in JSON Format
        # log_format nginxlog_json escape=json '{ "timestamp": "$time_iso8601", '
        # '"remote_addr": "$remote_addr", '
        #  '"body_bytes_sent": $body_bytes_sent, '
        #  '"request_time": $request_time, '
        #  '"response_status": $status, '
        #  '"request": "$request", '
        #  '"request_method": "$request_method", '
        #  '"host": "$host",'
        #  '"upstream_addr": "$upstream_addr",'
        #  '"http_x_forwarded_for": "$http_x_forwarded_for",'
        #  '"http_referrer": "$http_referer", '
        #  '"http_user_agent": "$http_user_agent", '
        #  '"http_version": "$server_protocol", '
        #  '"nginx_access": true }';
        # access_log /dev/stdout nginxlog_json;

    # See Move default writable paths to a dedicated directory (#119)
    # https://github.com/openresty/docker-openresty/issues/119
    client_body_temp_path /usr/local/openresty/temp/nginx-client-body;
    proxy_temp_path       /usr/local/openresty/temp/nginx-proxy;
    fastcgi_temp_path     /usr/local/openresty/temp/nginx-fastcgi;
    uwsgi_temp_path       /usr/local/openresty/temp/nginx-uwsgi;
    scgi_temp_path        /usr/local/openresty/temp/nginx-scgi;

    sendfile       on;
    #tcp_nopush     on;
    #tcp_nodelay	   on;
    server_tokens  off;

    #压缩配置
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 4;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";
    gzip_http_version 1.1;

    include /etc/nginx/conf.d/*.conf;

    # Don't reveal OpenResty version to clients.
    # server_tokens off;
    upstream myserver1 {
       least_conn;
       balancer_by_lua_block {
            local balancer = require "ngx.balancer"
            balancer.set_current_peer(ngx.ctx.ip_addr,ngx.ctx.port)
       }
       server [fd11:1111:1111:23::6b]:18188;
       #长连接保持配置
       keepalive 200; #保持与每个后端服务器的长连接数量，试情况修改
       keepalive_requests 10000; #每个后端服务器的长连接的最大请求数量，试情况修改
    }

    upstream new_vsbo_upstream {
       server [fd11:1111:1111:23::6b]:18188;
       #长连接保持配置
       keepalive 200; #保持与每个后端服务器的长连接数量，试情况修改
       keepalive_requests 10000; #每个后端服务器的长连接的最大请求数量，试情况修改
    }
    upstream old_vsbo_upstream {
       server ***************:8080;
       #长连接保持配置
       keepalive 200; #保持与每个后端服务器的长连接数量，试情况修改
       keepalive_requests 10000; #每个后端服务器的长连接的最大请求数量，试情况修改
    }
    #防DDOS攻击配置
    client_header_timeout 10s;
    client_max_body_size 20m;
    client_body_timeout 15s;
    send_timeout 15s;
    keepalive_timeout 30s;
    client_header_buffer_size 4k;
    client_body_buffer_size 16k;    # 8k for 32-bit or 16k for 64-bit platform
    large_client_header_buffers 8 16k;

    underscores_in_headers on;
    lua_need_request_body on;

    # 标签路径映射信息
    lua_shared_dict xml_path_dict 1m;
    lua_shared_dict json_path_dict 1m;
    # 白名单列表信息
    lua_shared_dict white_dict 1m;
    # 配置信息
    lua_shared_dict conf_dict 1m;
    init_by_lua_file /usr/local/openresty/lualib/aspire/init.lua;
    server {
        #如果该端口已被使用，改用9090
        listen       80;
        listen      443 ssl;
        listen     [::]:80;
        listen     [::]:443 ssl;
        server_name ose1.caiyun.feixin.10086.cn;
        ssl_certificate /usr/local/openresty/ssl/_.caiyun.feixin.10086.cn.pem;
        ssl_certificate_key /usr/local/openresty/ssl/_.caiyun.feixin.10086.cn.key;
        ssl_protocols TLSv1.1 TLSv1.2;
        ssl_ciphers "ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA:ECDHE-RSA-DES-CBC3-SHA:EDH-RSA-DES-CBC3-SHA:AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA256:AES128-SHA256:AES256-SHA:AES128-SHA:DES-CBC3-SHA:HIGH:!aNULL:!eNULL:!EXPORT:!DES:!MD5:!PSK:!RC4";
        ssl_prefer_server_ciphers on;
        error_page  404  /404.html;

        set $remote_host $http_x_forwarded_for;
        if ( $remote_host ~ "" ) {
            set $remote_host $remote_addr;
        }

        location / {
            access_by_lua_block {
                -- local flow =  require "aspire/flowByCache"
                -- 跳转地址
                flow.turnTo()
            }
            proxy_pass http://myserver1;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $http_x_real_ip;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Cookie $http_cookie;
            proxy_set_header X-Custom-Header "mcdmc.openresty.com";
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }

        location /cloudSEE/openApi/acceptClearedDetail {
            proxy_pass http://new_vsbo_upstream;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Cookie $http_cookie;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        location /cloudSEE/openApi/bindFamilyNumber {
            proxy_pass http://new_vsbo_upstream;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Cookie $http_cookie;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        location /cloudSEE/openApi/preCheckFamilyNumber {
            proxy_pass http://new_vsbo_upstream;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Cookie $http_cookie;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        location /cloudSEE/openApi/modifyFamilyNumber {
            proxy_pass http://new_vsbo_upstream;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Cookie $http_cookie;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        location /cloudSEE/openApi/queryFamilyNumber {
            proxy_pass http://new_vsbo_upstream;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Cookie $http_cookie;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }

         location /cloudSEE/openApi/createFamilyPackageGroup {
             proxy_pass http://new_vsbo_upstream;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header X-Forwarded-Proto $scheme;
             proxy_set_header Cookie $http_cookie;
             proxy_http_version 1.1;
             proxy_set_header Connection "";
         }
          location /cloudSEE/openApi/queryFamilyPackageBind {
              proxy_pass http://new_vsbo_upstream;
              proxy_set_header Host $http_host;
              proxy_set_header X-Real-IP $remote_addr;
              proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
              proxy_set_header X-Forwarded-Proto $scheme;
              proxy_set_header Cookie $http_cookie;
              proxy_http_version 1.1;
              proxy_set_header Connection "";
          }
          location /cloudSEE/openApi/bindFamilyPackage {
             proxy_pass http://new_vsbo_upstream;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header X-Forwarded-Proto $scheme;
             proxy_set_header Cookie $http_cookie;
             proxy_http_version 1.1;
             proxy_set_header Connection "";
        }
        location /cloudSEE/openApi/preBindFamilyPackageCheck {
             proxy_pass http://new_vsbo_upstream;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header X-Forwarded-Proto $scheme;
             proxy_set_header Cookie $http_cookie;
             proxy_http_version 1.1;
             proxy_set_header Connection "";
        }

        location /cloudSEE/openApi/modifyFamilyPackageBind {
             proxy_pass http://new_vsbo_upstream;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header X-Forwarded-Proto $scheme;
             proxy_set_header Cookie $http_cookie;
             proxy_http_version 1.1;
             proxy_set_header Connection "";
        }

        location /cloudSEE/openApi/afterCancelAccountProcess {
             proxy_pass http://new_vsbo_upstream;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header X-Forwarded-Proto $scheme;
             proxy_set_header Cookie $http_cookie;
             proxy_http_version 1.1;
             proxy_set_header Connection "";
        }
        location /cloudSEE/openApi/queryFamilyPackageGroup {
             proxy_pass http://new_vsbo_upstream;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header X-Forwarded-Proto $scheme;
             proxy_set_header Cookie $http_cookie;
             proxy_http_version 1.1;
             proxy_set_header Connection "";
        }

        location /cloudSEE/openApi/queryChannelAndGoodsShelveInfo {
             proxy_pass http://new_vsbo_upstream;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header X-Forwarded-Proto $scheme;
             proxy_set_header Cookie $http_cookie;
             proxy_http_version 1.1;
             proxy_set_header Connection "";
        }

        location /cloudSEE/openApi/querySpaceSubscribeRelationV2 {
             proxy_pass http://old_vsbo_upstream;
             proxy_set_header Host $http_host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header X-Forwarded-Proto $scheme;
             proxy_set_header Cookie $http_cookie;
             proxy_http_version 1.1;
             proxy_set_header Connection "";
        }
        # 内部刷新白名单列表接口
        location /inner/dynamic/refreshWhiteList {
            content_by_lua_block {
                local dynamic = require "aspire/dynamic"
                local res = dynamic.refresh()
                if res == 1 then
                    ngx.exit(200)
                else
                    ngx.exit(401)
                end
            }
        }
        # 内部刷新映射配置接口
        location /inner/dynamic/refreshConfig {
            content_by_lua_block {
                local dynamic = require "aspire/dynamic"
                local res = dynamic.refreshConfig()
                if res == 1 then
                    ngx.exit(200)
                else
                    ngx.exit(401)
                end
            }
        }

        location = /404.html {
            root /local/webserver;
            index 404.html;
        }
    }

}