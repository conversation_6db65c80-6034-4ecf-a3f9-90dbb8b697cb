package com.zyhl.yun.member.goodsInstance.domain.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.yun.member.common.Constant;
import com.zyhl.yun.member.common.ResultCodeEnum;
import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.domain.framework.BaseAroundPersistenceHandler;
import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.common.domain.framework.DomainEntityPersistenceWrapper;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.util.ParameterUtil;
import com.zyhl.yun.member.domain.goodsinstance.MemberQueryConditionSubScriptionResp;
import com.zyhl.yun.member.domain.goodsinstance.QueryConditionSubScriptionReq;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.facade.UserServiceFacade;
import com.zyhl.yun.member.goodsinstance.dto.MemberSubscription;
import com.zyhl.yun.member.goodsinstance.enums.GoodsInstanceStateEnum;
import com.zyhl.yun.member.invite.gateway.VsboOrderGateway;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

import static com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceExtendServiceId.QUERY_CONDITION_GOODS_INSTANCE;

/**
 * <AUTHOR>
 * @date 2025/3/3
 * @description 环绕UpdateGoodsInstance的处理器，更新商品实例则需要判断是否要发送mq
 */
@Component
@Slf4j
public class GoodsInstanceDoConditionHandler extends BaseAroundPersistenceHandler<GoodsInstanceDo> {

    @Resource
    private VsboOrderGateway vsboOrderGateway;

    /**
     * 获取支持的类列表
     *
     * @return 返回类列表
     */
    @Override
    protected List<Class> getSupportedClassList() {
        return Collections.singletonList(GoodsInstanceDo.class);
    }

    /**
     * 获取支持的服务id列表
     *
     * @return 服务id列表
     */
    @Override
    protected List<String> getSupportedServiceList() {
        return Collections.singletonList(QUERY_CONDITION_GOODS_INSTANCE);
    }

    /**
     * 持久化前置逻辑
     *
     * @param wrapper ReceivedRecDo类
     */
    @Override
    public void preHandle(DomainEntityPersistenceWrapper wrapper) {
        super.preHandle(wrapper);
    }

    @Override
    public Serializable handleReadByConditionAction(DomainServiceContext serviceContext, BaseCondition condition, Callable<Serializable> getter) {
        QueryGoodsInstanceCondition queryGoodsInstanceCondition = (QueryGoodsInstanceCondition) condition;
        String userId = queryGoodsInstanceCondition.getUserId();
        QueryConditionSubScriptionReq queryConditionSubScriptionReq = new QueryConditionSubScriptionReq();
        if (StringUtils.isNotEmpty(queryGoodsInstanceCondition.getPhoneNumber())) {
            queryConditionSubScriptionReq.setAccount(queryGoodsInstanceCondition.getPhoneNumber());
        } else {
            UserDo userDo = UserServiceFacade.getUserFromDbByUserId(userId);
            queryConditionSubScriptionReq.setAccount(userDo.getMsisdn());
        }
        queryConditionSubScriptionReq.setSubIdList(queryGoodsInstanceCondition.getGoodsInstanceIdList());
        queryConditionSubScriptionReq.setOrderIdList(queryGoodsInstanceCondition.getOrderIdList());
        queryConditionSubScriptionReq.setGoodsIdList(queryGoodsInstanceCondition.getGoodsIdList());
        // 如果queryGoodsInstanceCondition的orderId不为空，添加进queryConditionSubScriptionReq.orderIdList
        if (StringUtils.isNotEmpty(queryGoodsInstanceCondition.getOrderId())) {
            // orderIdList判断空
            if (CollUtil.isEmpty(queryConditionSubScriptionReq.getOrderIdList())) {
                queryConditionSubScriptionReq.setOrderIdList(new ArrayList<>());
            }
            queryConditionSubScriptionReq.getOrderIdList().add(queryGoodsInstanceCondition.getOrderId());
        }
        //log.info("[GoodsInstanceDoConditionHandler] queryConditionSubScriptionReq is {}", queryConditionSubScriptionReq);
        MemberQueryConditionSubScriptionResp response = vsboOrderGateway.queryConditionSubScription(queryConditionSubScriptionReq);
        //log.info("[GoodsInstanceDoConditionHandler] queryConditionSubScriptionResp is {}", response);

        // 商品实例转换
        List<GoodsInstanceDo> goodsInstanceDoList = new ArrayList<>();
        if (response.getQueryConditionSubScriptionResp() != null
                && ResultCodeEnum.SUCCESS.getResultCode().equals(String.valueOf(response.getResultCode()))) {
            List<MemberSubscription> memberSubscriptionList = response.getQueryConditionSubScriptionResp().getMemberSubscriptionList();
            if (CollUtil.isNotEmpty(memberSubscriptionList)) {
                goodsInstanceDoList = convert(memberSubscriptionList);
            }
        } else {
            throw new ServiceException("vsbo queryConditionSubScription failed!!");
        }
        return (Serializable) goodsInstanceDoList;
    }


    private List<GoodsInstanceDo> convert(List<MemberSubscription> memberSubscriptionList) {
        return memberSubscriptionList.stream()
                .map(this::convertSingle)
                .collect(Collectors.toList());
    }

    private GoodsInstanceDo convertSingle(MemberSubscription ms) {
        return GoodsInstanceDo.builder()
                .goodsInstanceId(ms.getSubscriptionId())
                .payWay(ms.getPayWay())
                .orderId(ms.getOrderID())
                .userId(ParameterUtil.getParameter(ms.getExtensionInfo(), Constant.USER_ID))
                .subTime(ms.getSubTime())
                .unsubTime(ms.getUnSubTime())
                .updateTime(ms.getUpdateTime())
                .subWay(checkNotNull(ms.getSubWay()) ? Integer.valueOf(ms.getSubWay()) : null)
                .stateEnum(GoodsInstanceStateEnum.fromState(Integer.parseInt(ms.getStatus())))
                .saleType(checkNotNull(ms.getProductType()) ? Integer.valueOf(ms.getProductType()) : null)
                .goodsPackageInstanceId(ms.getParentSubscriptionId())
                .effectiveEndTime(ms.getEndTime())
                .goodsPackageId(ms.getParentProductId())
                .goodsId(ms.getContractID())
                .effectiveStartTime(ms.getStartTime())
                .effectiveEndTime(ms.getEndTime())
                .chargeType(ParameterUtil.getParameter(ms.getExtensionInfo(), Constant.CHARGEING_TYPE))
                .build();
    }

    private boolean checkNotNull(String value) {
        return CharSequenceUtil.isNotEmpty(value) && !"null".equals(value);
    }

//    /**
//     * 持久化后置逻辑
//     *
//     * @param wrapper ReceivedRecDo类
//     * @param result  持久化返回结果
//     */
//    @Override
//    public void afterHandle(DomainEntityPersistenceWrapper wrapper, Serializable result) {
//        super.afterHandle(wrapper, result);
//        log.info("UpdateGoodsInstanceHandler.afterHandle start!");
//        GoodsInstanceDo goodsInstanceDo = (GoodsInstanceDo) wrapper.getData();
//        // 如果是订购期间发货失败(实例)，需要发送mq给m4c订阅，用于m4c回滚权益
//        if (GoodsInstanceStateEnum.NORMAL.equals(goodsInstanceDo.getStateEnum()) &&
//                (RightsStatusEnum.INITIALIZATION_FAIL.equals(goodsInstanceDo.getRightsStatusEnum())
//                || RightsStatusEnum.OPEN_FAIL.equals(goodsInstanceDo.getRightsStatusEnum()))
//        ) {
//            DomainServiceContext goodsInstanceContext = new DomainServiceContext(GoodsInstanceExtendServiceId.QUERY_GOODS_INSTANCE_EXTEND);
//            QueryGoodsInstanceExtendCondition queryCondition = new QueryGoodsInstanceExtendCondition();
//            queryCondition.setUserId(goodsInstanceDo.getUserId());
//            queryCondition.setOrderId(goodsInstanceDo.getOrderId());
//            queryCondition.setIsExpired(ExpireEnum.ALL.getState());
//            List<GoodsInstanceDo> goodsInstanceExtendDos = goodsInstanceContext.read(queryCondition, GoodsInstanceDo.class);
//            if (CollectionUtils.isEmpty(goodsInstanceExtendDos)) {
//                log.warn("[DOMAIN] UpdateGoodsInstanceHandler.afterHandle get goodsInstance not found, orderId is {}", goodsInstanceDo.getOrderId());
//                return;
//            }
//            // 获取产品实例
//            GoodsInstanceDo goodsInstanceExtendDo = goodsInstanceExtendDos.get(0);
//            String userId = goodsInstanceDo.getUserId();
//            DomainServiceContext queryUserContext = new DomainServiceContext(QUERY_USER_INFO);
//            UserDo userFromDb = queryUserContext.read(userId, UserDo.class);
//            try {
//                // 发送发货失败结果Mq
//                GoodsInstanceEventMqMessage message = new GoodsInstanceEventMqMessage();
//                message.setMsisdn(userFromDb.getMsisdn());
//                message.setUserId(userFromDb.getUserId());
//                message.setGoodsInstanceId(goodsInstanceExtendDo.getGoodsInstanceId());
//                message.setStatus(String.valueOf(goodsInstanceExtendDo.getStateEnum().getState()));
//                message.setRightsStatus(String.valueOf(goodsInstanceExtendDo.getRightsStatusEnum().getStatus()));
//                message.setEffectiveStartTime(goodsInstanceExtendDo.getEffectiveStartTime());
//                message.setEffectiveEndTime(goodsInstanceExtendDo.getEffectiveEndTime());
//                message.setSubTime(goodsInstanceExtendDo.getSubTime());
//                message.setExtInfo(goodsInstanceExtendDo.getExtInfo());
//                message.setEventType(GoodsInstanceEventEnum.DELIVERY_FAILED.getCode());
//                mqProducer.sendOuterMessage(vsboProperty, JsonUtil.toJson(message));
//            }
//            catch (Exception e) {
//                log.error("sendGoodsInstanceEventMq is error:{}",e);
//            }
//        }
//    }

    /**
     * 默认环绕处理
     *
     * @param wrapper
     * @return
     */
    @Override
    public Serializable handle(DomainEntityPersistenceWrapper wrapper) {
        return super.handle(wrapper);
    }
}
