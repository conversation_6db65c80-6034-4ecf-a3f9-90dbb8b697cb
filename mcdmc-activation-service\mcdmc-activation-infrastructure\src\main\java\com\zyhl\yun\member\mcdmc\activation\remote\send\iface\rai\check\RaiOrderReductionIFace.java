package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.check;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.mono.ProcessByThrowExceptionUtil;
import com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceExtendServiceId;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.mcdmc.activation.constants.SymbolConstant;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.rai.CheckActionTypeEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.rai.ReturnStatusEnum;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.refund.RaiRefundNormalIFace;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.RaiReturnReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.RaiReturnRsp;
import com.zyhl.yun.member.order.domain.OrderDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceExtendServiceId.UPDATE_GOODS_INSTANCE_EXTEND;
import static com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.sub.RaiSubBaseIFace.updateParentInstanceByRightReduction;

/**
 * 订单核减流程
 * com.huawei.jaguar.vsbo.service.serviceimpl.OrderServiceImpl#orderReduction
 *
 * <AUTHOR>
 * @since 2024/07/28 11:49
 */
@Slf4j
@Component
public class RaiOrderReductionIFace extends RaiRefundNormalIFace {

    @Override
    protected String buildExtField1(ComSendInterfaceReq comSendReq) {
        Map<String, String> map = new HashMap<>(2);
        map.put(OtherFieldConstants.PAY_WAY, comSendReq.getPayWay());
        map.put(OtherFieldConstants.RETURN, ReturnStatusEnum.RIGHT_REDUCTION.getReturnStatus());
        if (StrUtil.isNotEmpty(comSendReq.getParentCampaignId())) {
            map.put(OrderDo.ExtInfoKeys.CAMPAIGN_ID, comSendReq.getParentCampaignId());
        }
        return JSONUtil.toJsonStr(map);
    }

    @Override
    protected void doBusiComplete(InterfaceContext<RaiReturnReq, RaiReturnRsp> interfaceContext) {
        Object resultCode = ProcessByThrowExceptionUtil.runNotThrowExceptionProcessByReturn(() ->
                interfaceContext.getInterfaceRspObj().getContractRoot().getBody().getResultCode());
        this.updateGoodsInstance(interfaceContext, String.valueOf(resultCode));
    }

    @Override
    protected void doException(InterfaceContext<RaiReturnReq, RaiReturnRsp> interfaceContext, Exception e) throws Exception {
        this.updateGoodsInstance(interfaceContext, "innerException");
        throw e;
    }

    /**
     * 更新商品实例的核减标识
     *
     * @param interfaceContext 接口上下文
     * @param resultCode       结果码
     */
    private void updateGoodsInstance(InterfaceContext<RaiReturnReq, RaiReturnRsp> interfaceContext, String resultCode) {
        String userId = interfaceContext.getWorkOrderDo().getUserId();
        String goodsInstanceId = interfaceContext.getComSendReq().getGoodsInstanceId();
        GoodsInstanceDo goodsInstanceDo = MemberContextUtil.qryGoodsInstanceDo(userId, goodsInstanceId);
        if (goodsInstanceDo == null) {
            log.error("根据userId={}和goodInstanceId={}查询不到商品实例", userId, goodsInstanceId);
            return;
        }
        // 更新核对标识
        String rightReduction = String.join(SymbolConstant.PIPE, this.getCheckActionTypeEnum().getType(),
                DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN), resultCode);
        String oriRightReduction = goodsInstanceDo.getRightReduction();
        if (StringUtils.hasText(oriRightReduction)) {
            goodsInstanceDo.setRightReduction(oriRightReduction + "," + rightReduction);
        } else {
            goodsInstanceDo.setRightReduction(rightReduction);
        }
        String orderId = interfaceContext.getWorkOrderDo().getOrderId();
        log.info("更新商品实例核减/补退标识,userId={},orderId={},goodsInstanceId={},oriRightReduction={},rightReduction={}",
                userId, orderId, goodsInstanceId, oriRightReduction, rightReduction);
        DomainServiceContext memberContext = new DomainServiceContext(UPDATE_GOODS_INSTANCE_EXTEND);
        memberContext.putInstance(goodsInstanceDo);
        memberContext.writeAndFlush();
        updateParentInstanceByRightReduction(goodsInstanceDo);
    }

    protected CheckActionTypeEnum getCheckActionTypeEnum() {
        // 核减标识
        return CheckActionTypeEnum.ORDER_REDUCTION;
    }
}
