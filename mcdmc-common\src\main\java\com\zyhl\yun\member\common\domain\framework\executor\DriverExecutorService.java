package com.zyhl.yun.member.common.domain.framework.executor;

import com.zyhl.yun.member.common.domain.framework.*;
import com.zyhl.yun.member.common.domain.mono.ClsObjectInputStream;
import com.zyhl.yun.member.common.util.GzipUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.Serializable;
import java.util.HashMap;

/**
 * 驱动执行器服务
 *
 * <AUTHOR>
 * @date 2024/05/31 17:31
 */
@Slf4j
public class DriverExecutorService {


    /**
     * 处理本地请求
     *
     * @param wrapper
     * @return
     */
    public static Serializable execLocalRequest(DomainEntityPersistenceWrapper wrapper) {

        if (wrapper.getExecutor() == null) {
            log.warn("[FRAMEWORK] executor is null");
            return null;
        }

        if (wrapper.getAroundPersistenceHandler() != null) {
            return wrapper.getAroundPersistenceHandler().handle(wrapper);
        } else {
            return wrapper.getExecutor().exec(wrapper);
        }

    }

    public static ResultWrapper execRemoteRequest(String requestBody) {
        HashMap<String, String> reqObj;
        try {
            reqObj = (HashMap<String, String>) ClsObjectInputStream.deserialObjByBytes(requestBody);
            String inst = reqObj.get("inst");
            Object wrapper = ClsObjectInputStream.deserialObjByBytes(inst);
            if (wrapper instanceof DomainEntityPersistenceWrapper) {
                DomainEntityPersistenceWrapper persistenceWrapper = (DomainEntityPersistenceWrapper) wrapper;
                ResultWrapper resultWrapper = execRemoteRequest(persistenceWrapper);
                return resultWrapper;
            }
            throw new DomainRemoteException("not supported wrapper type");
        } catch (Exception e) {
            log.error("[FRAMEWORK] execRemoteRequest error.", e);
            return new ResultWrapper(new DomainRemoteException(e));
        }

    }

    /**
     * 处理远程请求
     *
     * @param compressData
     * @return
     * @throws IOException
     * @throws ClassNotFoundException
     */
    public static byte[] execRemoteRequest(byte[] compressData) {
        HashMap<String, String> reqObj;
        String serviceId = "";
        try {
            String requestBody = GzipUtils.decompress(compressData);
            reqObj = (HashMap<String, String>) ClsObjectInputStream.deserialObjByBytes(requestBody);
            String inst = reqObj.get("inst");
            Object wrapper = ClsObjectInputStream.deserialObjByBytes(inst);
            if (wrapper instanceof DomainEntityPersistenceWrapper) {
                DomainEntityPersistenceWrapper persistenceWrapper = (DomainEntityPersistenceWrapper) wrapper;
                ResultWrapper resultWrapper = execRemoteRequest(persistenceWrapper);
                serviceId = persistenceWrapper.getDomainServiceContext().getServiceId();
                return GzipUtils.compressObject(resultWrapper);
            }
            throw new DomainRemoteException("not supported wrapper type");
        } catch (Exception e) {
            log.error("[FRAMEWORK] execRemoteRequest error.serviceId ={},detail exception is", serviceId, e);
            ResultWrapper resultWrapper = new ResultWrapper(new DomainRemoteException(e));
            return GzipUtils.compressObject(resultWrapper);
        }

    }


    /**
     * 处理远程请求
     *
     * @param wrapper
     * @return
     */
    private static ResultWrapper execRemoteRequest(DomainEntityPersistenceWrapper wrapper) {
        if (wrapper.getExecutor() == null) {
            return new ResultWrapper(new DomainRemoteException("executor is null"));
        }
        AroundPersistenceHandler handler = wrapper.getAroundPersistenceHandler() != null ?
                wrapper.getAroundPersistenceHandler() : getDelegateHandler(wrapper);
        try {
            Serializable result;
            if (handler != null) {
                if (wrapper.getExecutor() instanceof RemoteDriverExecutor) {
                    wrapper.setExecutor(((RemoteDriverExecutor) wrapper.getExecutor()).getExecutor());
                }

                result = handler.handle(wrapper);
            } else {
                result = wrapper.getExecutor().exec(wrapper);
            }

            if (!(result instanceof ResultWrapper)) {
                return new ResultWrapper(result);
            } else {
                return (ResultWrapper) result;
            }
        } catch (Exception e) {
            log.error("[FRAMEWORK] execRemoteRequest error.", e);
            return new ResultWrapper(new DomainRemoteException(e));
        }
    }


    /**
     * 获取委托处理器
     *
     * @param domainEntityWrapper
     * @return
     */
    private static AroundPersistenceHandler getDelegateHandler(DomainEntityPersistenceWrapper domainEntityWrapper) {
        return AroundHandlerManager.getHandler(domainEntityWrapper.getDomainClassName(),
                domainEntityWrapper.getDomainServiceContext().getServiceId());
    }

}
