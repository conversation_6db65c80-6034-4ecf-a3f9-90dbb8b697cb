package com.zyhl.yun.member.mcdmc.activation.driver;

import cn.hutool.core.collection.CollUtil;
import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.common.domain.framework.BaseLocalDriver;
import com.zyhl.yun.member.common.domain.framework.DomainEntityPersistenceWrapper;
import com.zyhl.yun.member.mcdmc.activation.convertor.WorkServiceFlowConvertor;
import com.zyhl.yun.member.mcdmc.activation.convertor.WorkServiceFlowIFaceConvertor;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowDo;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowIFaceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.dto.WorkServiceCondition;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.ServiceTypeEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.ServiceFlowStateEnum;
import com.zyhl.yun.member.mcdmc.activation.po.WorkServiceFlowIFacePo;
import com.zyhl.yun.member.mcdmc.activation.po.WorkServiceFlowPo;
import com.zyhl.yun.member.mcdmc.activation.repository.WorkServiceFlowIFaceRepository;
import com.zyhl.yun.member.mcdmc.activation.repository.WorkServiceFlowRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/06/05 16:45
 */
@Slf4j
@Component
public class WorkServiceFlowDriver extends BaseLocalDriver<WorkServiceFlowDo> {

    @Resource
    private WorkServiceFlowRepository workServiceFlowRepository;

    @Resource
    private WorkServiceFlowIFaceRepository flowIFaceRepository;

    @Resource
    private WorkServiceFlowConvertor workServiceFlowConvertor;

    @Resource
    private WorkServiceFlowIFaceConvertor flowIFaceConvertor;

    @Override
    public int doWrite(DomainEntityPersistenceWrapper domainEntityWrapper) {
        return 0;
    }

    @Override
    public WorkServiceFlowDo doReadByPk(Serializable pk) {
        return null;
    }

    @Override
    public List<? extends WorkServiceFlowDo> doReadByCondition(BaseCondition condition) {
        if (condition instanceof WorkServiceCondition) {
            return readByCondition((WorkServiceCondition) condition);
        }
        return Collections.emptyList();
    }

    @Override
    public Long doGetCount(BaseCondition condition) {
        return 0L;
    }

    private List<WorkServiceFlowDo> readByCondition(WorkServiceCondition condition) {
        List<WorkServiceFlowPo> workServiceFlowPoList = Collections.emptyList();
        if (ServiceTypeEnum.QRY_BY_SERVICE_ID.equals(condition.getServiceType())) {
            workServiceFlowPoList = workServiceFlowRepository.qryByServiceId(condition.getServiceId());
        } else if (ServiceTypeEnum.QRY_ALL.equals(condition.getServiceType())) {
            workServiceFlowPoList = workServiceFlowRepository.qryAll();
        }
        List<WorkServiceFlowDo> flowDoList = workServiceFlowConvertor.toDoList(workServiceFlowPoList);
        this.fillFlowIFace(flowDoList);
        return flowDoList;
    }


    private void fillFlowIFace(List<WorkServiceFlowDo> workServiceFlowDoList) {
        List<WorkServiceFlowIFacePo> flowIFacePoList = flowIFaceRepository.list();
        // 只查出有效的
        Map<String, List<WorkServiceFlowIFacePo>> flowId2IFacesMap = CollUtil.emptyIfNull(flowIFacePoList).stream()
                .filter(iFace -> ServiceFlowStateEnum.VALID.codeEquals(iFace.getState()))
                .collect(Collectors.groupingBy(WorkServiceFlowIFacePo::getWorkServiceFlowId));
        workServiceFlowDoList.forEach(workServiceFlowDo -> {
            List<WorkServiceFlowIFacePo> iFacePoList = flowId2IFacesMap.getOrDefault(workServiceFlowDo.getWorkServiceFlowId(), Collections.emptyList());
            List<WorkServiceFlowIFaceDo> iFaceList = flowIFaceConvertor.toDoList(iFacePoList);
            workServiceFlowDo.setIFaceDoList(iFaceList);
        });
    }


    @Override
    protected List<Class> getSupportedClass() {
        return Collections.singletonList(WorkServiceFlowDo.class);
    }
}
