/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2019-2019. All rights reserved.
 */

package com.zyhl.yun.member.mcdmc.activation.domain.utils;


import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 〈功能描述〉
 *
 * <AUTHOR>
 * @since 2019-12-11
 */
@Slf4j
public class Md5Util {

    /**
     * Md5需要加密的因子
     */
    private static final String[] HEXDIGITS =
            {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};

    private static final int SIXTEEN = 16;
    public static final String MD5_ALGORITHM = "MD5";

    public static String getMd5Algorithm() {
        return MD5_ALGORITHM;
    }
    /**
     * 构造函数
     */
    private Md5Util() {
    }

    /**
     * 调用MD5进行加密
     *
     * @param origin 原来的字符
     * @return 加密后的字符
     */
    public static String md5Encode(String origin) {
        String resultString = null;
        try {
            resultString = origin;
            MessageDigest md = MessageDigest.getInstance(getMd5Algorithm());
            md.update(resultString.getBytes("UTF-8"));
            resultString = byteArrayToHexString(md.digest());
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException localException) {
            log.error("md5 encode is error", localException);
        }
        return resultString;
    }

    /**
     * 〈功能描述〉
     *
     * @param bb 入参
     * @return hexString
     */
    public static String byteArrayToHexString(byte[] bb) {
        StringBuilder resultSb = new StringBuilder();
        byte[] arr = bb;
        int len = arr.length;
        for (int index = 0; index < len; index++) {
            byte ab = arr[index];
            resultSb.append(byteToHexString(ab));
        }
        return resultSb.toString();
    }

    private static String byteToHexString(byte bb) {
        int nn = bb;
        if (nn < 0) {
            nn += SIXTEEN * SIXTEEN;
        }
        int d1 = nn / SIXTEEN;
        int d2 = nn % SIXTEEN;
        return HEXDIGITS[d1] + HEXDIGITS[d2];
    }
}
