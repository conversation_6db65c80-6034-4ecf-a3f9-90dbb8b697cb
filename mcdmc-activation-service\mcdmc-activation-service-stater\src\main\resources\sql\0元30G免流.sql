INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, service_flow_class, interface_name, condition_template, request_header_template, request_body_template, request_type, url, notify_url, response_condition_template, callback_condition_template, sync_flag, prior_sort, state, created_time, updated_time, operator, tenant_id, retry_count, version_tag, retry_policy)
VALUES('12', 'jiyunFlowSpecServiceCode', 'jiyunFlowSpecFlow', 'jiyunFlowSpecIFace', NULL, NULL, NULL, 0, 'http://*********:18000/mock/gateway/api/common/activity/order/addOrder', 'http://localhost:18084/member-activation/cloudSEE/openApi/subscribeFlowSpecNotify', NULL, NULL, 0, 1, 0, sysdate(),  sysdate(), 'gxq', 'tenantA', 3, '1', 0);

INSERT INTO activation_work_service_flow
(work_service_flow_id, service_code, service_flow_class, interface_name, condition_template, request_header_template, request_body_template, request_type, url, notify_url, response_condition_template, callback_condition_template, sync_flag, prior_sort, state, created_time, updated_time, operator, tenant_id, retry_count, version_tag, retry_policy)
VALUES('13', 'jiyunFlowSpecServiceCode', 'jiyunFlowSpecFlow', 'jiyunFlowSpecQueryIFace', NULL, NULL, NULL, 0, 'http://*********:18000/mock/gateway/api/common/gw/cmosp/givenPrdOrderRelQuery', NULL, NULL, NULL, 0, 0, 0, sysdate(),  sysdate(), 'gxq', 'tenantA', 3, '1', 0);