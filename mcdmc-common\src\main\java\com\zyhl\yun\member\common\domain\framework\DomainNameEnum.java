package com.zyhl.yun.member.common.domain.framework;

import com.zyhl.yun.member.common.domain.framework.constants.DomainConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 领域名称
 *
 * <AUTHOR>
 * @date 2024/05/27 9:13
 */
@Getter
@AllArgsConstructor
public enum DomainNameEnum {

    /**
     * 会员域
     */
    MEMBER_VIP("member-vip", "UserDo"),
    MEMBER_REVICE("member-vip", "ReceivedRecDo"),
    MEMBER_REVICE_GOODSEXT("member-vip", "GoodsInstanceExtendDo"),
    MEMBER_GOODS_INSTANCE("member-vip", "GoodsInstanceDo"),
    FLOWSPEC_SUBSCRIPTION_INSTANCE("member-vip", "FlowSpecSubscriptionDo"),
    RESOURCE_INSTANCE("member-vip", "ResourceDo"),
    MARKETPASS_INSTANCE("member-vip", "MarketPassDo"),

    ACTIVITY_GOODS("member-vip", "MultipleActivityDO"),
    MULTIPLE_ACTIVITY("member-vip", "ActivityInstanceDO"),
    STOCK("member-vip", "StockDo"),
    GOODS_INSTANCE_REC("member-vip", "GoodsInstanceRecDo"),
    MEMBER_T_GOTONE_CONFIG("member-vip", "MemberTGotoneConfigDO"),
    FAMILY_PACKAGE_BIND("member-vip", "FamilyPackageBindDo"),
    FAMILY_PACKAGE_BIND_HISTORY("member-vip", "FamilyPackageBindHistoryDo"),
    FAMILY_PACKAGE_GROUP_RELATION("member-vip", "FamilyPackageGroupRelationDo"),
    GOTONE_PERFORM_ORDER("member-vip", "GotonePerformOrderDo"),


    /**
     * 商品域
     */
    MEMBER_GOODS("member-product", "GoodsDo"),
    MEMBER_PRODUCT("member-product", "ProductDo"),
    VALIDATE_RULE("member-product", "ValidateRuleDo"),
    RULE_VALIDATE_RESULT("member-product", "RuleValidateResultDo"),
    SMS_TEMPLATE_ID("member-product", "SmsTemplateDo"),
    CHANNEL_AND_GOODS_SHELVE_INFO("member-product", "ChannelAndGoodsShelveInfoDo"),
    CHANNEL_ATTR("member-product", "ChannelAttrDo"),
    CHANNEL("member-product", "ChannelDo"),
    CHANNEL_GOODS_SHELVE("member-product", "ChannelGoodsShelveDo"),
    CHANNEL_GOODS_SHELVE_POLICY("member-product", "ChannelGoodsShelvePolicyDo"),
    VALID_CHANEL_SHELF_LIMIT(DomainConstant.MEMBER_PRODUCT, "ChannelValidResultDo"),
    /**
     * 订单域
     */
    MEMBER_ORDER("member-order", "OrderDo"),
    MEMBER_ORDERV2("member-order", "OrderDomainDo"),
    MEMBER_ORDERDETAIL("member-order", "OrderDetailDO"),
    MEMBER_ORDER_SYNC("member-order", "SyncAppOrderDo"),
    MEMBER_SUBSCRIBE("member-order", "SubscriptionRecDo"),
    GROUP_BUY_DELIVER("member-order", "GroupBuyDeliverDO"),
    MEMBER_PAYMENT("member-payment", "PaymentDo"),

    MEMBER_GOODS_INSTANCE_SUB("member-vip", "GoodsInstanceSubDo"),
    NOTIFY("member-order", "PerformOrderDo"),
    CONTRACT_ATTRIBUTE_ORDER("member-order", "ContractAttributeOrderDo"),
    GROUP_BUY("member-order", "OrderGroupBuyDo"),
    PERFORM_SLB_ORDER("member-order", "PerformSlbOrderDo"),
    TPCT("member-order", "TpctDo"),
    TPCT_ORDER_DELIVER("member-order", "TPCTOrderDeliverResp"),

    /**
     * 服开
     */
    WORK_ORDER_ATTR("member-activation", "WorkOrderAttrDo"),
    WORK_ORDER("member-activation", "WorkOrderDo"),
    WORK_SERVICE_FLOW_LOG("member-activation", "WorkServiceFlowLogDo"),

    /**
     * 任务域
     */
    MEMBER_TASK("member-task", "InstWorkOrderTaskDo"),
    MESSAGE_NOTICE_TASK("member-task", "MessageNoticeTaskDo"),
    TASK_SUB_EXPIRED_PAUSE("member-task", "TaskSubExpiredPauseDo"),
    TASK_SCHEDULE("member-task", "CommonInstanceTaskDo"),

    /**
     * 支撑域
     */
    MEMBER_SUPPORT_SMS("member-support", "SmsSendDo"),
    MEMBER_SUPPORT_APPPUSH("member-support", "AppPushDo"),
    CREATE_CIRCLE_GROUP("member-support", "GroupInfoDo"),
    QUERY_MY_GROUP_PAGE_LIST("member-support", "CircleDetailPageInfoDo"),
    QUERY_GROUP_DETAIL("member-support", "GroupDetailDo"),
    QUERY_MY_GROUP_LIST("member-support", "CircleDetailInfoDo"),
    INVITE_RELATION("member-vip", "InviteRelationDo"),

    /**
     * 事件领取记录域
     */
    EVENT_RECEIVED_REC("member-vip", "EventReceivedRecDo"),

    /**
     * 未知
     */
    UN_KNOW("unKnow", "Object");

    private final String domainName;

    private final String rootClassName;


    private static final Map<String, DomainNameEnum> map = new HashMap<>();

    static {
        for (DomainNameEnum domain : DomainNameEnum.values()) {
            map.put(domain.getRootClassName(), domain);
        }
    }

    public static DomainNameEnum fromClass(Class clazz) {

        Class fromClazz = clazz;
        DomainNameEnum domain = null;
        do {
            domain = map.get(fromClazz.getSimpleName());
            if (domain == null) {
                fromClazz = fromClazz.getSuperclass();
            } else {
                return domain;
            }
        } while (!fromClazz.equals(Object.class));

        return UN_KNOW;
    }
}
