package com.zyhl.yun.member.common.cache.impl;

import cn.hutool.core.util.RandomUtil;
import com.zyhl.yun.member.common.JsonUtil;
import com.zyhl.yun.member.common.cache.ConditionCacheable;
import com.zyhl.yun.member.common.cache.config.EntityCacheConfig;
import com.zyhl.yun.member.common.cache.config.EntityCacheHotConfig;
import com.zyhl.yun.member.common.cache.config.RedisClusterPropertiesConfig;
import com.zyhl.yun.member.common.cache.wrapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/20 20:42
 */
@Slf4j
public class RedisCacheImpl extends BaseDelegationCacheImpl {


    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private EntityCacheHotConfig entityCacheHotConfig;

    @Resource
    private EntityCacheConfig entityCacheConfig;

    @Resource
    private RedisClusterPropertiesConfig redisClusterPropertiesConfig;

    private RedisConnectionFactory redisConnectionFactory;

    private Date lastConnectFailTime;

    public RedisCacheImpl(RedisTemplate redisTemplate) {
        log.info("[CACHE] RedisCacheImpl init");
        this.redisTemplate = redisTemplate;
        redisConnectionFactory = redisTemplate.getConnectionFactory();
    }

    @Override
    public SimpleValueWrapper get(String cacheSpace, Serializable key, Class clazz) {
        log.debug("[CACHE] get value from redis. cacheSpace: {}, key: {}, redisKey: {}",
                cacheSpace, key, getKey(cacheSpace, key));

        if (!canConnectToRedis()) {
            return null;
        }

        try {
            String value = redisTemplate.opsForValue().get(getKey(cacheSpace, key));
            if (value == null) {
                log.debug("[CACHE] not value found in redis. cacheSpace: {}, key: {}, redisKey: {}",
                        cacheSpace, key, getKey(cacheSpace, key));
                return null;
            }

            return SimpleValueWrapper.fromJson(value, clazz);
        } catch (Exception e) {
            log.error("[CACHE] get value from redis error: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] get value from redis error", e);
            }

            lastConnectFailTime = new Date();
        }

        return null;
    }

    @Override
    public CollectionWrapper getList(String cacheSpace, List<Serializable> keyList, Class clazz) {

        log.debug("[CACHE] get value list from redis. cacheSpace: {}, keyList: {}",
                cacheSpace, keyList);

        if (!canConnectToRedis()) {
            return null;
        }

        if (CollectionUtils.isEmpty(keyList)) {
            return new CollectionWrapper(keyList, null);
        }


        RedisConnection redisConnection = getConnection();
        RedisSerializer redisSerializer = redisTemplate.getValueSerializer();
        try {
            List<byte[]> valueList = redisConnection
                    .mGet(keyList.stream()
                            .map(key -> getKey(cacheSpace, key).getBytes())
                            .collect(Collectors.toList())
                            .toArray(new byte[0][0]));

            List<CacheValueWrapper> cacheValueWrapperList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(valueList)) {
                for (byte[] value : valueList) {
                    if (value != null) {
                        cacheValueWrapperList.add(SimpleValueWrapper.fromJson((String) redisSerializer.deserialize(value), clazz));
                    }
                }
            }

            return new CollectionWrapper(keyList, cacheValueWrapperList);
        } catch (Exception e) {
            log.error("[CACHE] put list cache to redis error: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] put list cache to redis error", e);
            }
            lastConnectFailTime = new Date();
        } finally {
            redisConnection.close();
        }


        return null;
    }

    @Override
    public ConditionWrapper getKeyListByCondition(String cacheSpace, ConditionCacheable condition) {

        log.debug("[CACHE] get key list from redis. cacheSpace: {}, condition: {}",
                cacheSpace, condition);

        if (!canConnectToRedis()) {
            return null;
        }

        List<String> conditionKeyList = getConditionKeyList(cacheSpace, condition);
        if (CollectionUtils.isEmpty(conditionKeyList)) {
            // 当条件缓存key列表为空时，直接返回null，代表没查询到
            return null;
        }

        String conditionHashKey = getConditionHashKey(condition);
        if (!StringUtils.hasLength(conditionHashKey)) {
            return null;
        }

        try {
            for (String key : conditionKeyList) {
                if (StringUtils.hasLength(key)) {
                    log.debug("[CACHE] get key list from redis. cacheSpace: {},  conditionRedisKey: {}, conditionHashKey: {}",
                            cacheSpace, key, conditionHashKey);
                    String value = (String) redisTemplate.opsForHash().get(key, conditionHashKey);
                    if (value != null) {
                        return ConditionWrapper.fromJson(value);
                    }
                }
            }

        } catch (Exception e) {
            log.error("[CACHE] get key list from redis error: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] get key list from redis error", e);
            }
            lastConnectFailTime = new Date();
        }

        return null;
    }

    @Override
    public ConditionCountWrapper getTotalCountByCondition(String cacheSpace, ConditionCacheable condition) {
        log.debug("[CACHE] get total count from redis. cacheSpace: {}, condition: {}",
                cacheSpace, condition);

        if (!canConnectToRedis()) {
            return null;
        }

        List<String> conditionKeyList = getConditionKeyList(cacheSpace, condition);
        if (CollectionUtils.isEmpty(conditionKeyList)) {
            // 当条件缓存key列表为空时，直接返回null，代表没查询到
            return null;
        }

        String conditionCountHashKey = getConditionCountHashKey(condition);
        if (!StringUtils.hasLength(conditionCountHashKey)) {
            return null;
        }

        try {
            for (String key : conditionKeyList) {
                if (StringUtils.hasLength(key)) {
                    log.debug("[CACHE] get total count from redis cacheSpace. {},  conditionRedisKey: {}, conditionCountHashKey: {}",
                            cacheSpace, key, conditionCountHashKey);
                    String value = (String) redisTemplate.opsForHash().get(key, conditionCountHashKey);
                    if (value != null) {
                        return ConditionCountWrapper.fromJson(value);
                    }
                }
            }
        } catch (Exception e) {
            log.error("[CACHE] get total count from redis error: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] get total count from redis error", e);
            }
            lastConnectFailTime = new Date();
        }
        return null;
    }

    @Override
    public void put(String cacheSpace, Serializable key, SimpleValueWrapper value) {
        log.debug("[CACHE] put value to redis. cacheSpace: {}, key: {}, redisKey: {}",
                cacheSpace, key, getKey(cacheSpace, key));

        if (!canConnectToRedis()) {
            return;
        }

        try {

            redisTemplate.opsForValue().set(getKey(cacheSpace, key), JsonUtil.toJson(value),
                    Duration.ofSeconds(getTtl()));

        } catch (Exception e) {
            log.error("[CACHE] put value to redis error: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] put value to redis error", e);
            }
            lastConnectFailTime = new Date();
        }
    }

    @Override
    public void put(String cacheSpace, ConditionCacheable condition, ConditionWrapper conditionWrapper) {

        log.debug("[CACHE] put condition to redis. cacheSpace: {}, condition: {}",
                cacheSpace, condition);


        if (!canConnectToRedis()) {
            return;
        }

        List<String> conditionKeyList = getConditionKeyList(cacheSpace, condition);
        if (CollectionUtils.isEmpty(conditionKeyList)) {
            // 没有返回条件缓存key的不处理
            return;
        }

        if (!StringUtils.hasLength(conditionWrapper.getConditionDigest())) {
            return;
        }

        try {
            for (String key : conditionKeyList) {
                if (StringUtils.hasLength(key)) {

                    log.debug("[CACHE] put condition to redis. cacheSpace: {},  conditionRedisKey: {}, conditionHashKey: {}",
                            cacheSpace, key, conditionWrapper.getConditionDigest());
                    redisTemplate.opsForHash()
                            .put(key, conditionWrapper.getConditionDigest(), JsonUtil.toJson(conditionWrapper));
                    Long ttl = redisTemplate.getExpire(key);
                    if (ttl == -1) {
                        redisTemplate.expire(key, getTtl(), TimeUnit.SECONDS);
                    }
                }
            }

        } catch (Exception e) {
            log.error("[CACHE] put condition to redis error: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] put condition to redis error", e);
            }
            lastConnectFailTime = new Date();
        }
    }

    @Override
    public void put(String cacheSpace, ConditionCacheable condition, ConditionCountWrapper conditionCountWrapper) {
        log.debug("[CACHE] put condition count to redis. cacheSpace: {}, condition: {}",
                cacheSpace, condition);

        if (!canConnectToRedis()) {
            return;
        }

        List<String> conditionKeyList = getConditionKeyList(cacheSpace, condition);
        if (CollectionUtils.isEmpty(conditionKeyList)) {
            // 没有返回条件缓存key的不处理
            return;
        }

        String conditionCountHashKey = getConditionCountHashKey(condition);
        if (!StringUtils.hasLength(conditionCountHashKey)) {
            return;
        }

        try {
            for (String key : conditionKeyList) {
                if (StringUtils.hasLength(key)) {

                    log.debug("[CACHE] put condition count to redis. cacheSpace: {}, conditionRedisKey: {}, conditionCountHashKey: {}",
                            cacheSpace, key, conditionCountHashKey);
                    redisTemplate.opsForHash()
                            .put(key, conditionCountHashKey, JsonUtil.toJson(conditionCountWrapper));
                    Long ttl = redisTemplate.getExpire(key);
                    if (ttl == -1) {
                        redisTemplate.expire(key, getTtl(), TimeUnit.SECONDS);
                    }
                }
            }
        } catch (Exception e) {
            log.error("[CACHE] put condition count to redis error: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] put condition count to redis error", e);
            }
            lastConnectFailTime = new Date();
        }
    }

    @Override
    public void putList(String cacheSpace, CollectionWrapper collectionWrapper) {

        List<Serializable> keyList = collectionWrapper.getKeyList();

        log.debug("[CACHE] put value list to redis. cacheSpace: {}, keyList: {}",
                cacheSpace, keyList);

        if (!canConnectToRedis()) {
            return;
        }

        if (CollectionUtils.isEmpty(keyList)) {
            // 没有缓存key列表的不处理
            return;
        }

        try {

            for (Serializable key : keyList) {
                this.put(cacheSpace, key, new SimpleValueWrapper(key, collectionWrapper.getValueElement(key)));
            }

        } catch (Exception e) {
            log.error("[CACHE] put list cache to redis error: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] put list cache to redis error", e);
            }
            lastConnectFailTime = new Date();
        }
    }

    @Override
    public void deleteByKeyList(String cacheSpace, List<Serializable> keyList) {

        log.debug("[CACHE] delete value list from redis. cacheSpace: {}, keyList: {}",
                cacheSpace, keyList);

        if (!canConnectToRedis()) {
            return;
        }

        if (CollectionUtils.isEmpty(keyList)) {
            // 没有缓存key列表的不处理
            return;
        }


        RedisConnection redisConnection = getConnection();
        try {

            byte[][] keysBates = keyList.stream()
                    .map(key -> getKey(cacheSpace, key).getBytes())
                    .collect(Collectors.toList())
                    .toArray(new byte[0][0]);

            if (entityCacheHotConfig.getDelayDeleteSeconds() != null &&
                    entityCacheHotConfig.getDelayDeleteSeconds() > 0) {
                for (byte[] keyBates : keysBates) {
                    redisConnection.expire(keyBates, entityCacheHotConfig.getDelayDeleteSeconds());
                }
            } else {
                redisConnection.del(keysBates);
            }
        } catch (Exception e) {
            log.error("[CACHE] delete value list from redis error: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] delete value list from redis error", e);
            }
            lastConnectFailTime = new Date();
        } finally {
            redisConnection.close();
        }

    }

    @Override
    public void clearConditions(String cacheSpace, ConditionCacheable condition) {

        log.debug("[CACHE] delete condition from redis. cacheSpace: {}, condition: {}",
                cacheSpace, condition);

        if (!canConnectToRedis()) {
            return;
        }

        List<String> conditionKeyList = getConditionKeyList(cacheSpace, condition);
        if (CollectionUtils.isEmpty(conditionKeyList)) {
            // 没有返回条件缓存key的不处理
            return;
        }
        try {
            for (String key : conditionKeyList) {
                if (StringUtils.hasLength(key)) {
                    log.debug("[CACHE] delete condition from redis. cacheSpace: {}, conditionRedisKey: {}",
                            cacheSpace, key);
                    if (entityCacheHotConfig.getDelayDeleteSeconds() != null &&
                            entityCacheHotConfig.getDelayDeleteSeconds() > 0) {
                        redisTemplate.expire(key, Duration.ofSeconds(entityCacheHotConfig.getDelayDeleteSeconds()));
                    } else {
                        redisTemplate.delete(key);
                    }

                }
            }
        } catch (Exception e) {
            log.error("[CACHE] delete condition from redis error: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] delete condition from redis error", e);
            }
            lastConnectFailTime = new Date();
        }
    }

    private boolean useCluster() {
        return !CollectionUtils.isEmpty(redisClusterPropertiesConfig.getNodes());
    }


    private boolean canConnectToRedis() {
        if (lastConnectFailTime == null) {
            return true;
        }

        if (System.currentTimeMillis() - lastConnectFailTime.getTime() > entityCacheHotConfig.getConnectHaftOpenSeconds() * 1000) {
            lastConnectFailTime = null;
            return true;
        }

        log.warn("[CACHE] redis can not be connected now. lastConnectFailTime: {}", lastConnectFailTime);
        return false;
    }

    private RedisConnection getConnection() {
        if (useCluster()) {
            return redisConnectionFactory.getClusterConnection();
        } else {
            return redisConnectionFactory.getConnection();
        }

    }

    private long getTtl() {
        // return entityCacheHotConfig.getRedisTtl();
        return (long) (entityCacheHotConfig.getRedisTtl() * (RandomUtil.randomFloat() + 1f));
    }

    private String getKey(String cacheSpace, Serializable key) {
        return cacheSpace + ":Entity:" + key;
    }

}
