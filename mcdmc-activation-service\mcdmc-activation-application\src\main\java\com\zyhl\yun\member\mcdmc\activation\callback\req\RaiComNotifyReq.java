package com.zyhl.yun.member.mcdmc.activation.callback.req;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/06/21 10:07
 */
@Data
public class RaiComNotifyReq {
    private String msgType;
    private String msgVer;
    /**
     * 订单流水号（外部）
     */
    private String orderNo;
    /**
     * 会员中心订单号
     */
    private String channelNo;
    /**
     * 状态
     * 5.订购成功 其它为失败
     */
    private Integer status;
    private List<OrderListEntity> orderList;

    @Data
    public static class OrderListEntity {
        private String orderId;
        private String orderItemId;
        private Integer mainStatus;
        private Integer status;
        private Integer quantity;
        private String salesId;
        private String salesName;
        private String prodId;
        private String prodName;
        private String skuId;
        private String skuName;
        /**
         * 时间格式为：yyyy-MM-dd HH:mm:ss
         */
        private String updateTime;
        private String redeemCode;
        private String exchangePassword;
        private String mainRemark;
        private String itemRemark;
    }
}
