-- iface新增字段
ALTER TABLE member_sopen.activation_work_service_flow_iface ADD query_service_code varchar(100) NULL COMMENT '接口主动查询的流程编码';
ALTER TABLE member_sopen.activation_work_service_flow_iface ADD iface_config varchar(1000) NULL COMMENT '接口配置属性，仅支持json类型！';

-- 更新主动查询接口
UPDATE activation_work_service_flow_iface
SET query_service_code='raiQueryNormalServiceCode'
WHERE service_code='normalRaiSubServiceCode' and iface_class_name='raiSubNormalIFace';
UPDATE activation_work_service_flow_iface
SET query_service_code='raiQueryNormalServiceCode'
WHERE service_code='changXRaiSubServiceCode' and iface_class_name='raiSubChangXIFace';
UPDATE activation_work_service_flow_iface
SET query_service_code='raiQueryMovieServiceCode'
WHERE service_code='movieRaiSubServiceCode' and iface_class_name='raiSubMovieIFace';
UPDATE activation_work_service_flow_iface
SET query_service_code='raiQueryNormalServiceCode'
WHERE service_code='t3RaiSubServiceCode' and iface_class_name='raiSubT3IFace';

