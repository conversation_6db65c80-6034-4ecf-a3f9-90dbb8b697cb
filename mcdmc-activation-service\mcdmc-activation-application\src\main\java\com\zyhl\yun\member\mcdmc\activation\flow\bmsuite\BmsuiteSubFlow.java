package com.zyhl.yun.member.mcdmc.activation.flow.bmsuite;

import com.zyhl.yun.member.mcdmc.activation.flow.base.BaseSubServiceFlow;
import org.springframework.stereotype.Component;


/**
 * bmsuite订购流程
 *
 * <AUTHOR>
 * @since 2024/07/03 11:56
 */
@Component
public class BmsuiteSubFlow extends BaseSubServiceFlow {

    @Override
    protected boolean isJudgeSubGoodInstanceEffective() {
        // bmsuite不判断是否有效，防止订购->暂停->激活，订单刚好被暂停了故未订购，导致后面的暂停和激活均失败
        return false;
    }

    @Override
    protected boolean hasCallback() {
        return false;
    }


}
