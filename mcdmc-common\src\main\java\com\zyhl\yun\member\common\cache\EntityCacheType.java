package com.zyhl.yun.member.common.cache;

import com.zyhl.yun.member.common.cache.impl.CaffeineCacheImpl;
import com.zyhl.yun.member.common.cache.impl.NoCacheImpl;
import com.zyhl.yun.member.common.cache.impl.RedisCacheImpl;
import com.zyhl.yun.member.common.cache.impl.TwoLevelCacheImpl;

/**
 * <AUTHOR>
 * @date 2024/11/22 13:49
 */
public enum EntityCacheType {

    REDIS(RedisCacheImpl.class),

    NO_CACHE(NoCacheImpl.class),

    TWO_LEVEL(TwoLevelCacheImpl.class),

    CAFFEINE(CaffeineCacheImpl.class);


    private Class<? extends DelegationCache> imlClass;

    EntityCacheType(Class imlClass) {
        this.imlClass = imlClass;
    }

    public Class getImlClass() {
        return imlClass;
    }
}
