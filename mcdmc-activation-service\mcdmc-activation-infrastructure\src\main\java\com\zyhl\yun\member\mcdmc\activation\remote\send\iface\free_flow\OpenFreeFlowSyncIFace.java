package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.free_flow;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.zyhl.yun.member.common.constants.HeaderConstant;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.framework.service.MdcLogInterceptor;
import com.zyhl.yun.member.common.domain.serviceid.ResourceServiceId;
import com.zyhl.yun.member.common.enums.MemberResourceStateEnum;
import com.zyhl.yun.member.common.enums.ResourceTypeEnum;
import com.zyhl.yun.member.domain.resource.domain.ResourceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.CustomDidGenerator;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.SendTemplate;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.OpenCommonProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.OpenFreeFlowProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.OpenFreeFlowSyncReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.OpenFreeFlowSyncRsp;
import com.zyhl.yun.member.mcdmc.activation.util.OrderContextUtil;
import com.zyhl.yun.member.order.domain.OrderDo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * 一级能开免流订单同步逻辑
 * com.huawei.jaguar.vsbo.service.serviceimpl.asyncService.SubscribeNotifyFlowRetrySubThread#invokeOneLevelAbility
 * 配置地址：/config/vsbo/goToneConfig/oneLevelAbilityUrl
 *
 * <AUTHOR>
 * @apiNote 有回调<hr>OpenFreeFlowSyncNotify<hr>
 * @since 2024/06/19 16:17
 */
@Component
public class OpenFreeFlowSyncIFace extends SendTemplate<OpenFreeFlowSyncReq, OpenFreeFlowSyncRsp> {
    @Resource
    protected OpenFreeFlowProperties openFreeFlowProperties;
    @Resource
    private OpenCommonProperties openCommonProperties;

    public static final String FLOW_ORDER_PREFIX = "mpclouds";
    public static final String FLOW_ORDER_SUFFIX = "_rft";

    /**
     * 生成新的免流订单号
     */
    private String generateFlowId() {
        return FLOW_ORDER_PREFIX + CustomDidGenerator.generateId() + FLOW_ORDER_SUFFIX;
    }

    @Override
    protected Map<String, String> getRequestHeader(InterfaceContext<OpenFreeFlowSyncReq, OpenFreeFlowSyncRsp> interfaceContext) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.CONTENT_TYPE, interfaceContext.getMediaType().toString());
        headerMap.put(HeaderConstant.SRC_MOD_NAME, HeaderConstant.SRC_MOD_NAME_VALUE);
        headerMap.put(HeaderConstant.MCS_FLOW_ID, MdcLogInterceptor.getCurrentTraceId());
        return headerMap;
    }

    @Override
    protected OpenFreeFlowSyncReq getRequestBody(InterfaceContext<OpenFreeFlowSyncReq, OpenFreeFlowSyncRsp> interfaceContext) {
        OpenFreeFlowSyncReq req = new OpenFreeFlowSyncReq();
        req.setMsgType(openFreeFlowProperties.getMsgType());
        req.setVersion(openFreeFlowProperties.getVersion());
        req.setUniChannelId(openFreeFlowProperties.getUniChannelId());
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        // 生成新的免流订单号
        String flowOrderId = this.generateFlowId();
        req.setOrderId(flowOrderId);
        String createTime = comSendReq.getCreateTime();
        if (StringUtils.isEmpty(createTime)) {
            OrderDo orderDo = OrderContextUtil.queryOrderDo(comSendReq.getOrderID(), comSendReq.getUserId(), false);
            createTime = DateUtil.format(orderDo.getCreateTime(), DatePattern.PURE_DATETIME_PATTERN);
        }
        req.setCreateTime(createTime);
        req.setBuyerNickname(comSendReq.getMsisdn());
        req.setShopCode(openCommonProperties.getShopCode());
        req.setShopName(openCommonProperties.getShopName());
        req.setSellerId(openCommonProperties.getSellerId());
        OpenFreeFlowSyncReq.PaymentInfo paymentInfo = new OpenFreeFlowSyncReq.PaymentInfo();
        paymentInfo.setChargeType(openCommonProperties.getChargeType());
        paymentInfo.setPaymentTime(createTime);
        req.setPaymentInfo(paymentInfo);

        req.setNeedDistribution("2");
        req.setFeedbackUrl(openFreeFlowProperties.getFeedbackUrl());
        req.setChannelCode(openFreeFlowProperties.getChannelCode());
        OpenFreeFlowSyncReq.ConfirmInfor confirmInfor = new OpenFreeFlowSyncReq.ConfirmInfor();
        confirmInfor.setConfirmLog(openCommonProperties.getConfirmLog());
        confirmInfor.setConfirmWay(openCommonProperties.getConfirmWay());
        confirmInfor.setConfirmTime(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
        req.setConfirmInfor(confirmInfor);

        OpenFreeFlowSyncReq.SubIntegratedSalesOrder subIntegratedSalesOrder =
                buildSubIntegratedSalesOrder(comSendReq, interfaceContext.getWorkOrderDo(), comSendReq.getMsisdn());
        req.setSubOrderList(Collections.singletonList(subIntegratedSalesOrder));
        return req;
    }

    @Override
    protected boolean isSuccess(OpenFreeFlowSyncRsp rsp) {
        return OpenFreeFlowSyncRsp.isSuccess(rsp);
    }

    @Override
    protected void doBusiSuccess(InterfaceContext<OpenFreeFlowSyncReq, OpenFreeFlowSyncRsp> interfaceContext) {
        //  订购成功，需要插入一条待回调的会员资产
        DomainServiceContext memberContext = new DomainServiceContext(ResourceServiceId.CREATE_RESOURCE_INFO);
        ResourceDo resourceDo = memberContext.newInstance(ResourceDo.class);
        resourceDo.setResourceId(getResourceId(interfaceContext));
        resourceDo.setResourceType(ResourceTypeEnum.OPEN_FREE_FLOW);
        resourceDo.setUserId(interfaceContext.getWorkOrderDo().getUserId());
        // 20241127，更新一级能开更新订单id为父订单id
        resourceDo.setParentOrderId(interfaceContext.getComSendReq().getParentOrderId());
        resourceDo.setState(MemberResourceStateEnum.BE_CALLBACK.getState());
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        resourceDo.setEffectiveStartTime(DateUtil.parse(comSendReq.getEffectiveStartTime(), DatePattern.PURE_DATETIME_PATTERN));
        resourceDo.setEffectiveEndTime(DateUtil.parse(comSendReq.getEffectiveEndTime(), DatePattern.PURE_DATETIME_PATTERN));
        memberContext.writeAndFlush();
    }

    private OpenFreeFlowSyncReq.SubIntegratedSalesOrder buildSubIntegratedSalesOrder(
            ComSendInterfaceReq comSendReq, WorkOrderDo workOrderDo, String msisdn) {

        OpenFreeFlowSyncReq.SubIntegratedSalesOrder.GoodsInfo goodsInfo = new OpenFreeFlowSyncReq.SubIntegratedSalesOrder.GoodsInfo();
        goodsInfo.setGoodsId(openCommonProperties.getMonthlyGoodsID());
        goodsInfo.setGoodsTitle(openCommonProperties.getGoodsTitle());
        goodsInfo.setAmount(1);
        goodsInfo.setPrice(0);
        goodsInfo.setGoodsProvince("NA");
        goodsInfo.setGoodsCity("NA");

        OrderDo orderDo = OrderContextUtil.queryOrderDo(comSendReq.getParentOrderId(), workOrderDo.getUserId());
        if (Objects.isNull(orderDo) || CollectionUtils.isEmpty(orderDo.getOrderDetailDos())) {
            throw new FlowTerminationException(this.getClass(), workOrderDo, "query orderDo is null");
        }
        String orderItemId = orderDo.getOrderDetailDos().get(0).getOrderDetailId();
        orderItemId = orderItemId.replace("-", "");
        OpenFreeFlowSyncReq.SubIntegratedSalesOrder subIntegratedSalesOrder =
                new OpenFreeFlowSyncReq.SubIntegratedSalesOrder(orderItemId, msisdn);
        subIntegratedSalesOrder.setGoodsInfo(goodsInfo);
        subIntegratedSalesOrder.setOrderStatus(openCommonProperties.getOrderStatus());
        return subIntegratedSalesOrder;
    }

    @Override
    protected boolean isNeedCacheFlowLog() {
        // 缓存发货流水日志，加快回调处理速度
        return true;
    }

    @Override
    protected String getCallbackCondition(InterfaceContext<OpenFreeFlowSyncReq, OpenFreeFlowSyncRsp> context) {
        return context.getInterfaceReqObj().getOrderId();
    }

    @Override
    protected Class<OpenFreeFlowSyncReq> getReqClass() {
        return OpenFreeFlowSyncReq.class;
    }

    @Override
    protected Class<OpenFreeFlowSyncRsp> getRspClass() {
        return OpenFreeFlowSyncRsp.class;
    }

}
