package com.zyhl.yun.member.mcdmc.activation.flow.base;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.mcdmc.activation.constants.WorkAttrCodeConstant;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowIFaceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.FlowStaticConfig;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IOutInterface;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.ActivationContextUtil;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.serviceid.LocalServiceId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;

import static com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum.RESET;
import static com.zyhl.yun.member.mcdmc.activation.enums.WorkOrderStateEnum.CANCELED;

/**
 * 默认统一流程
 *
 * <AUTHOR>
 * @since 2024/12/13 15:58
 */
@Slf4j
public abstract class BaseDefaultServiceFlow extends BaseServiceFlow {

    @Override
    public void handle(WorkOrderDo workOrderDo, Callable<Boolean> continueLockFunc) {
        try {
            // 流程前置逻辑
            IFlowResult flowResult = beforeHandle(workOrderDo);
            // 流程处理
            if (flowResult.isContinue()) {
                this.realHandle(workOrderDo, continueLockFunc);
            } else {
                this.doHandleFlowTerminalResult(workOrderDo, flowResult);
            }
            // 流程后置逻辑
            afterHandle();
        } catch (FlowTerminationException e) {
            // 流程终止异常
            this.doHandleFlowTerminalResult(workOrderDo, e.toIFlowResult());
        } catch (Exception e) {
            log.error("业务流程={}发生异常，工单id={}，serviceCode={},异常信息为：", this.getClass().getSimpleName(),
                    workOrderDo.getWorkId(), workOrderDo.getServiceCode(), e);
            // 异常重试
            doRetry(workOrderDo, e.getMessage(), true);
        }
    }

    /**
     * 流程前置动作
     *
     * @param workOrderDo 工单
     * @return 流程前置结果
     */
    protected IFlowResult beforeHandle(WorkOrderDo workOrderDo) {
        // 流程前置处理
        return beforeFlow(workOrderDo);
    }

    protected void afterHandle() {
        // 后置处理
    }

    protected void realHandle(WorkOrderDo workOrderDo, Callable<Boolean> continueLockFunc) throws Exception {
        DomainServiceContext context = new DomainServiceContext(LocalServiceId.UPDATE_BY_ID_OPERATION);
        int index = 0;
        if (isAllowContinueFlow()) {
            // 流程允许继续执行之前执行的步骤
            index = workOrderDo.getFlowProgress();
        }
        IFlowResult flowResult = IFlowResult.next();
        // 获取接口对应地址列表
        List<IOutInterface> outInterfaceList = FlowStaticConfig.getOutInterfaceList(workOrderDo.getServiceCode());
        while (index < outInterfaceList.size()) {
            IOutInterface outInterface = outInterfaceList.get(index);
            flowResult = outInterface.exec(workOrderDo, flowResult);
            if (!flowResult.isContinue()) {
                // 流程不能继续执行，直接结束流程
                this.doHandleFlowTerminalResult(workOrderDo, flowResult);
                return;
            }
            index++;
            // 更新工单流程，表示走到第几个接口
            workOrderDo.setFlowProgress(index);
            context.putInstance(workOrderDo);
            context.writeAndFlush();
            // 续锁
            Boolean isContinueLock = continueLockFunc.call();
            if (Boolean.FALSE.equals(isContinueLock)) {
                // 续锁失败，表示有人在抢着做，直接退出循环
                log.error("续锁失败，无法获取工单锁，退出流程，工单详情={}", workOrderDo);
                return;
            }
        }

        boolean isFinally = false;
        if (CollectionUtils.isEmpty(outInterfaceList)) {
            // 没有接口，则直接结束流程
            isFinally = true;
        } else {
            IOutInterface lastOutInterface = outInterfaceList.get(Math.min(index - 1, outInterfaceList.size() - 1));
            WorkServiceFlowIFaceDo lastIFaceDo = FlowStaticConfig.getFaceDoByIFaceClass(workOrderDo.getServiceCode(), lastOutInterface.getClass());
            String notifyUri = Objects.isNull(lastIFaceDo) ? null : lastIFaceDo.getNotifyUri();
            if (index == outInterfaceList.size() && !StringUtils.hasText(notifyUri)) {
                // （最后一个接口，且没有回调）或（nextHintEnum==FINISH）则表示流程结束
                isFinally = true;
            }
        }

        // 流程成功结束动作
        this.doFlowSuccess(workOrderDo, isFinally, flowResult);
    }

    /**
     * 处理流程终止结果
     *
     * @param workOrderDo 工单实体
     * @param flowResult  流程结果
     */
    private void doHandleFlowTerminalResult(WorkOrderDo workOrderDo, IFlowResult flowResult) {
        if (flowResult.isSuccessFinish()) {
            log.debug("flow terminal:work order is success finish early，reason is {},work detail is :{}", flowResult.getMessage(), workOrderDo.toSimpleLogStr());
            // 流程提前结束
            this.doFlowSuccess(workOrderDo, true, flowResult);
        } else if (flowResult.isRestart()) {
            log.debug("flow terminal:work order is restart，reason is {},work detail is :{}", flowResult.getMessage(), workOrderDo.toSimpleLogStr());
            // 业务需要做重试
            String errorMsg = CharSequenceUtil.nullToDefault(flowResult.getMessage(), "调用接口业务返回失败重试错误码");
            doRetry(workOrderDo, errorMsg, true);
        } else if (flowResult.isFailFinish()) {
            log.debug("flow terminal:work order is fail finish，reason is {},work detail is :{}", flowResult.getMessage(), workOrderDo.toSimpleLogStr());
            // 流程失败结束
            String errorMsg = CharSequenceUtil.nullToDefault(flowResult.getMessage(), "调用接口业务返回失败不重试错误码");
            doRetry(workOrderDo, errorMsg, false);
        } else if (NextHintEnum.CANCEL.equals(flowResult.getNextHintEnum())) {
            // 流程取消
            log.debug("flow terminal:work order is cancel，reason is{},work detail is :{}", flowResult.getMessage(), workOrderDo.toSimpleLogStr());
            ActivationContextUtil.updateFailureStatusAndRecordReason(workOrderDo, CANCELED, flowResult.getMessage());
        } else if (RESET.equals(flowResult.getNextHintEnum())) {
            log.debug("flow terminal:work order is reset，reason is{},work detail is :{}", flowResult.getMessage(), workOrderDo.toSimpleLogStr());
            // 流程重置需发送延迟重试消息但不累加重试次数
            super.doSendRetryMessage(workOrderDo);
            // 记录重置原因
            ActivationContextUtil.upsertWorkAttr(workOrderDo.getUserId(), workOrderDo.getWorkId(), WorkAttrCodeConstant.RESET_MSG, flowResult.getMessage());
        } else {
            log.error("flow terminal:work order is unknown terminal，reason is {},work detail is :{}", flowResult, workOrderDo.toSimpleLogStr());
        }

    }

}
