package com.zyhl.yun.member.task.application.handler;

import com.zyhl.yun.member.common.JsonUtil;
import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.domain.framework.BaseAroundPersistenceHandler;
import com.zyhl.yun.member.common.domain.framework.DomainEntityPersistenceWrapper;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.GoodsServiceId;
import com.zyhl.yun.member.common.domain.serviceid.MessageNoticeServiceId;
import com.zyhl.yun.member.common.enums.SmsSubType;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.user.domain.QueryUserCondition;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.facade.GoodsInstanceServiceFacade;
import com.zyhl.yun.member.message.domain.SmsMessageContext;
import com.zyhl.yun.member.product.domain.goods.GoodsDo;
import com.zyhl.yun.member.product.domain.goods.dto.QueryGoodsCondition;
import com.zyhl.yun.member.product.domain.goods.pack.GoodsPackageDo;
import com.zyhl.yun.member.task.common.domain.MessageNoticeTaskDo;
import com.zyhl.yun.member.task.common.properties.NoticeTimeProperties;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.DatePattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Callable;

import static com.zyhl.yun.member.common.ResultCodeEnum.GOODS_NOT_FOUND;
import static com.zyhl.yun.member.common.domain.serviceid.GoodsServiceId.QUERY_GOODS_INFO;
import static com.zyhl.yun.member.common.domain.serviceid.UserServiceId.QUERY_USER_INFO;

/**
 * 消息通知任务环绕处理器
 * 用于处理消息通知任务保存前的时间校验和调整
 *
 * @Author: xiaojianbiao
 * @Date: 2025/6/17
 */
@Service
@Slf4j
public class MessageNoticeTaskHandler extends BaseAroundPersistenceHandler<MessageNoticeTaskDo> {

    @Resource
    private NoticeTimeProperties noticeTimeProperties;

    private final Random random = new Random();

    /**
     * 处理写入动作，检查并调整通知时间
     */
    @Override
    public Serializable handleWriteAction(DomainEntityPersistenceWrapper wrapper, Callable<Serializable> writer) {
        MessageNoticeTaskDo taskDo = (MessageNoticeTaskDo) wrapper.getData();

        // 如果不是短信发送通知，则直接插入通知记录
        if (!MessageNoticeTaskDo.NoticeWay.SMS.getCode().equals(taskDo.getNoticeWay())) {
            try {
                return writer.call();
            } catch (Exception e) {
                log.error("Failed to save MessageNoticeTaskDo", e);
                return -1;
            }
        }

        // 获取产品信息
        GoodsDo goodsDo = getGoodsDo(taskDo);

        Date noticeTime = taskDo.getNoticeTime();
        LocalDateTime noticeLdt;
        // 如果通知时间为空，默认设为当前时间
        if (noticeTime == null) {
            noticeLdt = LocalDateTime.now();
            noticeTime = Date.from(noticeLdt.atZone(ZoneId.systemDefault()).toInstant());
            taskDo.setNoticeTime(noticeTime);
        }
        // 如果是到期通知，则根据产品配置的到期提醒天数计算通知时间
        else if (MessageNoticeTaskDo.NoticeItem.EXPIRE.getCode().equals(taskDo.getNoticeItem())
                && null != goodsDo.getGoodsExt()
                && null != goodsDo.getGoodsExt().getExpiredRemindBeforeDays()) {
            SmsMessageContext smsMessageContext = JsonUtil.fromJson(taskDo.getContext().toString(), SmsMessageContext.class);
            if (null == smsMessageContext || null == smsMessageContext.getArgsMap()) {
                log.error("EXPIRE failed to get smsMessageContext");
                return -1;
            }
            String endTimeStr = smsMessageContext.getArgsMap().get("endTime");
            if (endTimeStr == null || endTimeStr.isEmpty()) {
                log.error("EXPIRE failed to get endTime from argsMap");
                return -1;
            }

            Date endTime;
            try {
                endTime = DateUtils.parseDate(endTimeStr, "yyyy-MM-dd HH:mm:ss");
            } catch (Exception e) {
                log.error("Failed to parse endTime: {}", endTimeStr, e);
                return -1;
            }

            noticeTime = DateUtils.addDays(endTime, -goodsDo.getGoodsExt().getExpiredRemindBeforeDays());
            noticeLdt = LocalDateTime.ofInstant(noticeTime.toInstant(), ZoneId.systemDefault());
            taskDo.setNoticeTime(noticeTime);
        }
        else {
            noticeLdt = LocalDateTime.ofInstant(noticeTime.toInstant(), ZoneId.systemDefault());
        }

        // 获取用户省份编码
        String provCode = getUserProvCode(taskDo.getUserId());

        // 检查是否开启通知免打扰，以及检查是否在允许的时间窗口内
        log.debug("goodsDo.getSmsPolicy() is {}",goodsDo.getSmsPolicy());
        if (goodsDo.getSmsPolicy() != null
                && goodsDo.getSmsPolicy().isDoNotDisturb(provCode, SmsSubType.fromCode(String.valueOf(taskDo.getNoticeItem())))
                && !isWithinAllowedTimeWindow(noticeLdt)) {
            LocalDateTime nextAllowedLdt = calculateNextAllowedTime(noticeLdt);
            LocalDateTime adjustedLdt = addRandomOffset(nextAllowedLdt);
            Date adjustedTime = Date.from(adjustedLdt.atZone(ZoneId.systemDefault()).toInstant());
            taskDo.setNoticeTime(adjustedTime);
            log.info("Adjusted noticeTime from {} to {} for sourceId: {}",
                    DateUtil.format(noticeTime, DatePattern.NORM_DATETIME_PATTERN),
                    DateUtil.format(adjustedTime, DatePattern.NORM_DATETIME_PATTERN),
                    taskDo.getSourceId());
        }

        try {
            return writer.call();
        } catch (Exception e) {
            log.error("Failed to save MessageNoticeTaskDo", e);
            return -1;
        }
    }

    private static GoodsDo getGoodsDo(MessageNoticeTaskDo messageNoticeTaskDo) {
        // 查询产品信息
        // 请求本地产品领域
        DomainServiceContext goodContext = new DomainServiceContext(QUERY_GOODS_INFO);
        GoodsDo goodsDo = goodContext.read(messageNoticeTaskDo.getGoodsIds(), GoodsDo.class);
        if (null == goodsDo) {
            log.error("submitToMessageSystem goodsDo is null");
            throw new ServiceException(GOODS_NOT_FOUND);
        }
        return goodsDo;
    }

    /**
     * 判断时间是否在允许的窗口内
     */
    private boolean isWithinAllowedTimeWindow(LocalDateTime dateTime) {
        log.info("isWithinAllowedTimeWindow get noticeTimeProperties windowStartHour is {}, windowEndHour is {}",
                noticeTimeProperties.getWindowStartHour(), noticeTimeProperties.getWindowEndHour());
        LocalDateTime startOfWindow = dateTime.withHour(noticeTimeProperties.getWindowStartHour())
                .withMinute(noticeTimeProperties.getWindowStartMinute())
                .withSecond(0)
                .withNano(0);
        LocalDateTime endOfWindow = dateTime.withHour(noticeTimeProperties.getWindowEndHour())
                .withMinute(noticeTimeProperties.getWindowEndMinute())
                .withSecond(0)
                .withNano(0);
        return dateTime.isAfter(startOfWindow) && dateTime.isBefore(endOfWindow);
    }

    /**
     * 计算下一个允许的时间点
     */
    private LocalDateTime calculateNextAllowedTime(LocalDateTime currentTime) {
        LocalDateTime startOfTodayWindow = currentTime.withHour(noticeTimeProperties.getWindowStartHour())
                .withMinute(noticeTimeProperties.getWindowStartMinute())
                .withSecond(0)
                .withNano(0);
        LocalDateTime endOfTodayWindow = currentTime.withHour(noticeTimeProperties.getWindowEndHour())
                .withMinute(noticeTimeProperties.getWindowEndMinute())
                .withSecond(0)
                .withNano(0);

        if (currentTime.isBefore(startOfTodayWindow)) {
            // 当前时间在今天窗口开始之前，使用今天开始时间
            return startOfTodayWindow;
        } else if (currentTime.isAfter(endOfTodayWindow)) {
            // 当前时间在今天窗口结束之后，使用明天开始时间
            return startOfTodayWindow.plusDays(1);
        } else {
            // 在窗口内，直接返回
            return currentTime;
        }
    }

    /**
     * 添加随机偏移，避免时间集中
     */
    private LocalDateTime addRandomOffset(LocalDateTime dateTime) {
        int randomSeconds = random.nextInt(noticeTimeProperties.getRandomOffsetMaxSeconds());
        return dateTime.plusSeconds(randomSeconds);
    }

    /**
     * 获取支持的实体类列表
     */
    @Override
    protected List<Class> getSupportedClassList() {
        return Collections.singletonList(MessageNoticeTaskDo.class);
    }

    /**
     * 获取支持的服务ID列表
     */
    @Override
    protected List<String> getSupportedServiceList() {
        return Collections.singletonList(MessageNoticeServiceId.SAVE);
    }

    /**
     * 获取用户省份编码
     *
     * @param  userId 用户id
     * @return 省份编码
     */
    private String getUserProvCode(String userId) {
        try {
            // 校验接受人用户信息
            DomainServiceContext userContext = new DomainServiceContext(UserDo.class, QUERY_USER_INFO);
            QueryUserCondition queryUserCondition = new QueryUserCondition();
            queryUserCondition.setUserId(userId);
            UserDo userDo = userContext.readFirst(queryUserCondition, UserDo.class);
            if (userDo != null) {
                // 从用户信息中获取省份编码
                return userDo.getProvCode();
            }
            return null;
        } catch (Exception e) {
            log.warn("Failed to get user prov code", e);
            return null;
        }
    }
}