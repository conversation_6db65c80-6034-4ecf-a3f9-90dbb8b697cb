package com.zyhl.yun.member.mcdmc.activation.domain.enums.bmsuite.rai;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 权益订单状态
 *
 * <AUTHOR>
 * @since 2024/06/19 09:41
 */
@Getter
@AllArgsConstructor
public enum RaiOrderStatusEnums {

    LAUNCH_OPEN_OK(1, "发起权益开通成功"),
    LAUNCH_OPEN_ERROR(2, "发起权益开通失败"),
    ORDER_OPEN_OK(3, "权益开通成功"),
    ORDER_OPEN_ERROR(4, "权益开通失败");


    private final int code;

    private final String msg;
}
