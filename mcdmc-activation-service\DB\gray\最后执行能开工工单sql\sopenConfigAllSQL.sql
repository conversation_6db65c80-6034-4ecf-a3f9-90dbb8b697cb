
-- 优酷会员权益产品服开流程配置
insert into `member_sopen`.`activation_work_service_flow` (`work_service_flow_id`, `service_code`, `flow_desc`,
                                                           `service_flow_class`, `sync_flag`, `retry_count`,
                                                           `retry_policy`, `retry_time`, `state`, `created_time`,
                                                           `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3001, 'youKuOuterUnSubServiceCode', '优酷会员退订外部通知流程', 'outerRightsUnSubFlow', 0, 0, -1, '0', 1,
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 通知能开的iface配置，默认配置2，在正常退订流程后
insert into `member_sopen`.`activation_work_service_flow_iface`
(`work_service_flow_id`, `service_code`, `query_service_code`, `iface_name`, `iface_class_name`,
`request_type`, `media_type`, `url`, `notify_uri`, `prior_sort`, `state`, `iface_config`,
`created_time`, `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3001, 'youKuOuterUnSubServiceCode', NULL, 'outerRightsUnSubIFace', 'outerRightsUnSubIFace', '0',
        'application/json', '', NULL, '1', 1,
        '{"channelId": "10006", "channelKey": "", "appId": "1729059384379700000", "notifyType": "1"}',
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 猫眼会员权益产品服开流程配置
insert into `member_sopen`.`activation_work_service_flow` (`work_service_flow_id`, `service_code`, `flow_desc`,
                                                           `service_flow_class`, `sync_flag`, `retry_count`,
                                                           `retry_policy`, `retry_time`, `state`, `created_time`,
                                                           `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3002, 'maoYanOuterUnSubServiceCode', '猫眼会员退订外部通知流程', 'outerRightsUnSubFlow', 0, 0, -1, '0', 1,
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 通知能开的iface配置，默认配置2，在正常退订流程后
insert into `member_sopen`.`activation_work_service_flow_iface`
(`work_service_flow_id`, `service_code`, `query_service_code`, `iface_name`, `iface_class_name`,
`request_type`, `media_type`, `url`, `notify_uri`, `prior_sort`, `state`, `iface_config`,
`created_time`, `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3002, 'maoYanOuterUnSubServiceCode', NULL, 'outerRightsUnSubIFace', 'outerRightsUnSubIFace', '0',
        'application/json', '', NULL, '1', 1,
        '{"channelId": "10007", "channelKey": "", "appId": "1746814031248887808", "notifyType": "1"}',
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 芒果TV会员权益产品服开流程配置
insert into `member_sopen`.`activation_work_service_flow` (`work_service_flow_id`, `service_code`, `flow_desc`,
                                                           `service_flow_class`, `sync_flag`, `retry_count`,
                                                           `retry_policy`, `retry_time`, `state`, `created_time`,
                                                           `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3003, 'mangGuoOuterUnSubServiceCode', '芒果TV会员退订外部通知流程', 'outerRightsUnSubFlow', 0, 0, -1, '0', 1,
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 通知能开的iface配置，默认配置2，在正常退订流程后
insert into `member_sopen`.`activation_work_service_flow_iface`
(`work_service_flow_id`, `service_code`, `query_service_code`, `iface_name`, `iface_class_name`,
`request_type`, `media_type`, `url`, `notify_uri`, `prior_sort`, `state`, `iface_config`,
`created_time`, `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3003, 'mangGuoOuterUnSubServiceCode', NULL, 'outerRightsUnSubIFace', 'outerRightsUnSubIFace', '0',
        'application/json', '', NULL, '1', 1,
        '{"channelId": "10008", "channelKey": "", "appId": "1729050972967276544", "notifyType": "1"}',
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 爱奇艺白金会员权益产品服开流程配置
insert into `member_sopen`.`activation_work_service_flow` (`work_service_flow_id`, `service_code`, `flow_desc`,
                                                           `service_flow_class`, `sync_flag`, `retry_count`,
                                                           `retry_policy`, `retry_time`, `state`, `created_time`,
                                                           `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3004, 'iqiyiBaijinOuterUnSubServiceCode', '爱奇艺白金会员退订外部通知流程', 'outerRightsUnSubFlow', 0, 0, -1, '0', 1,
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 通知能开的iface配置，默认配置2，在正常退订流程后
insert into `member_sopen`.`activation_work_service_flow_iface`
(`work_service_flow_id`, `service_code`, `query_service_code`, `iface_name`, `iface_class_name`,
`request_type`, `media_type`, `url`, `notify_uri`, `prior_sort`, `state`, `iface_config`,
`created_time`, `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3004, 'iqiyiBaijinOuterUnSubServiceCode', NULL, 'outerRightsUnSubIFace', 'outerRightsUnSubIFace', '0',
        'application/json', '', NULL, '1', 1,
        '{"channelId": "10009", "channelKey": "", "appId": "1750803215445790720", "notifyType": "1"}',
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 爱奇艺优选会员权益产品服开流程配置
insert into `member_sopen`.`activation_work_service_flow` (`work_service_flow_id`, `service_code`, `flow_desc`,
                                                           `service_flow_class`, `sync_flag`, `retry_count`,
                                                           `retry_policy`, `retry_time`, `state`, `created_time`,
                                                           `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3005, 'iqiyiYouxuanOuterUnSubServiceCode', '爱奇艺优选会员退订外部通知流程', 'outerRightsUnSubFlow', 0, 0, -1, '0', 1,
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 通知能开的iface配置，默认配置2，在正常退订流程后
insert into `member_sopen`.`activation_work_service_flow_iface`
(`work_service_flow_id`, `service_code`, `query_service_code`, `iface_name`, `iface_class_name`,
`request_type`, `media_type`, `url`, `notify_uri`, `prior_sort`, `state`, `iface_config`,
`created_time`, `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3005, 'iqiyiYouxuanOuterUnSubServiceCode', NULL, 'outerRightsUnSubIFace', 'outerRightsUnSubIFace', '0',
        'application/json', '', NULL, '1', 1,
        '{"channelId": "10010", "channelKey": "", "appId": "1750803215445790720", "notifyType": "1"}',
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 爱奇艺黄金会员权益产品服开流程配置
insert into `member_sopen`.`activation_work_service_flow` (`work_service_flow_id`, `service_code`, `flow_desc`,
                                                           `service_flow_class`, `sync_flag`, `retry_count`,
                                                           `retry_policy`, `retry_time`, `state`, `created_time`,
                                                           `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3006, 'iqiyiHuangjinOuterUnSubServiceCode', '爱奇艺黄金会员退订外部通知流程', 'outerRightsUnSubFlow', 0, 0, -1, '0', 1,
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 通知能开的iface配置，默认配置2，在正常退订流程后
insert into `member_sopen`.`activation_work_service_flow_iface`
(`work_service_flow_id`, `service_code`, `query_service_code`, `iface_name`, `iface_class_name`,
`request_type`, `media_type`, `url`, `notify_uri`, `prior_sort`, `state`, `iface_config`,
`created_time`, `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3006, 'iqiyiHuangjinOuterUnSubServiceCode', NULL, 'outerRightsUnSubIFace', 'outerRightsUnSubIFace', '0',
        'application/json', '', NULL, '1', 1,
        '{"channelId": "10011", "channelKey": "", "appId": "1750803215445790720", "notifyType": "1"}',
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 畅读会员权益产品服开流程配置
insert into `member_sopen`.`activation_work_service_flow` (`work_service_flow_id`, `service_code`, `flow_desc`,
                                                           `service_flow_class`, `sync_flag`, `retry_count`,
                                                           `retry_policy`, `retry_time`, `state`, `created_time`,
                                                           `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3007, 'changDuOuterUnSubServiceCode', '畅读会员退订外部通知流程', 'outerRightsUnSubFlow', 0, 0, -1, '0', 1,
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 通知能开的iface配置，默认配置2，在正常退订流程后
insert into `member_sopen`.`activation_work_service_flow_iface`
(`work_service_flow_id`, `service_code`, `query_service_code`, `iface_name`, `iface_class_name`,
`request_type`, `media_type`, `url`, `notify_uri`, `prior_sort`, `state`, `iface_config`,
`created_time`, `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3007, 'changDuOuterUnSubServiceCode', NULL, 'outerRightsUnSubIFace', 'outerRightsUnSubIFace', '0',
        'application/json', '', NULL, '1', 1,
        '{"channelId": "10012", "channelKey": "", "appId": "1774745942273167360", "notifyType": "1"}',
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 畅影会员权益产品服开流程配置
insert into `member_sopen`.`activation_work_service_flow` (`work_service_flow_id`, `service_code`, `flow_desc`,
                                                           `service_flow_class`, `sync_flag`, `retry_count`,
                                                           `retry_policy`, `retry_time`, `state`, `created_time`,
                                                           `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3008, 'changYingRaiOuterUnSubServiceCode', '畅影会员退订外部通知流程', 'outerRightsUnSubFlow', 0, 0, -1, '0', 1,
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 通知能开的iface配置，默认配置2，在正常退订流程后
insert into `member_sopen`.`activation_work_service_flow_iface`
(`work_service_flow_id`, `service_code`, `query_service_code`, `iface_name`, `iface_class_name`,
`request_type`, `media_type`, `url`, `notify_uri`, `prior_sort`, `state`, `iface_config`,
`created_time`, `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3008, 'changYingRaiOuterUnSubServiceCode', NULL, 'outerRightsUnSubIFace', 'outerRightsUnSubIFace', '0',
        'application/json', '', NULL, '1', 1,
        '{"channelId": "10001", "channelKey": "", "appId": "1776919734177100000", "notifyType": "1"}',
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 畅学会员权益产品服开流程配置
insert into `member_sopen`.`activation_work_service_flow` (`work_service_flow_id`, `service_code`, `flow_desc`,
                                                           `service_flow_class`, `sync_flag`, `retry_count`,
                                                           `retry_policy`, `retry_time`, `state`, `created_time`,
                                                           `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3009, 'changXueOuterUnSubServiceCode', '畅学会员退订外部通知流程', 'outerRightsUnSubFlow', 0, 0, -1, '0', 1,
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 通知能开的iface配置，默认配置2，在正常退订流程后
insert into `member_sopen`.`activation_work_service_flow_iface`
(`work_service_flow_id`, `service_code`, `query_service_code`, `iface_name`, `iface_class_name`,
`request_type`, `media_type`, `url`, `notify_uri`, `prior_sort`, `state`, `iface_config`,
`created_time`, `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3009, 'changXueOuterUnSubServiceCode', NULL, 'outerRightsUnSubIFace', 'outerRightsUnSubIFace', '0',
        'application/json', 'https://openapi.xkw.com/third/zyhlw/trunk/dispose', NULL, '1', 1,
        '{"channelId": "10002", "channelKey": "Tmt4YXpuNWQwVFZPdWNGZFE=", "appId": "1697431531934584832", "notifyType": "1"}',
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 畅活会员权益产品服开流程配置
insert into `member_sopen`.`activation_work_service_flow` (`work_service_flow_id`, `service_code`, `flow_desc`,
                                                           `service_flow_class`, `sync_flag`, `retry_count`,
                                                           `retry_policy`, `retry_time`, `state`, `created_time`,
                                                           `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3010, 'changLiveOuterUnSubServiceCode', '畅活会员退订外部通知流程', 'outerRightsUnSubFlow', 0, 0, -1, '0', 1,
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 通知能开的iface配置，默认配置2，在正常退订流程后
insert into `member_sopen`.`activation_work_service_flow_iface`
(`work_service_flow_id`, `service_code`, `query_service_code`, `iface_name`, `iface_class_name`,
`request_type`, `media_type`, `url`, `notify_uri`, `prior_sort`, `state`, `iface_config`,
`created_time`, `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3010, 'changLiveOuterUnSubServiceCode', NULL, 'outerRightsUnSubIFace', 'outerRightsUnSubIFace', '0',
        'application/json', 'https://apis.samhotele.com/samho/yunpan/unsubscribeNotice', NULL, '1', 1,
        '{"channelId": "10003", "channelKey": "UWh3ckFyekVhZTFQMXJsVg==", "appId": "1790989623632203776", "notifyType": "1"}',
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 畅行会员权益产品服开流程配置
insert into `member_sopen`.`activation_work_service_flow` (`work_service_flow_id`, `service_code`, `flow_desc`,
                                                           `service_flow_class`, `sync_flag`, `retry_count`,
                                                           `retry_policy`, `retry_time`, `state`, `created_time`,
                                                           `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3011, 'changXingOuterUnSubServiceCode', '畅行会员退订外部通知流程', 'outerRightsUnSubFlow', 0, 0, -1, '0', 1,
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 通知能开的iface配置，默认配置2，在正常退订流程后
insert into `member_sopen`.`activation_work_service_flow_iface`
(`work_service_flow_id`, `service_code`, `query_service_code`, `iface_name`, `iface_class_name`,
`request_type`, `media_type`, `url`, `notify_uri`, `prior_sort`, `state`, `iface_config`,
`created_time`, `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3011, 'changXingOuterUnSubServiceCode', NULL, 'outerRightsUnSubIFace', 'outerRightsUnSubIFace', '0',
        'application/json', 'https://apis.samhotele.com/samho/yunpan/unsubscribeNotice', NULL, '1', 1,
        '{"channelId": "10004", "channelKey": "UWh3ckFyekVhZTFQMXJsVg==", "appId": "1790989623632203776", "notifyType": "1"}',
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 畅停会员权益产品服开流程配置
insert into `member_sopen`.`activation_work_service_flow` (`work_service_flow_id`, `service_code`, `flow_desc`,
                                                           `service_flow_class`, `sync_flag`, `retry_count`,
                                                           `retry_policy`, `retry_time`, `state`, `created_time`,
                                                           `updated_time`, `operator`, `tenant_id`, `version_flag`)
VALUES (3012, 'parkOuterUnSubServiceCode', '畅停会员退订外部通知流程', 'outerRightsUnSubFlow', 0, 0, -1, '0', 1,
        NOW(), NOW(), 'lj', 'tenantA',
        b'0000000000000000000000000000000000000000000000000000000000000001');

-- 通知能开的iface配置，默认配置2，在正常退订流程后
update `member_sopen`.`activation_work_service_flow_iface` set iface_config = '{"channelId": "10005", "channelKey": "ajJNZkZNV1h0NDRDeHFiOQ==", "appId": "1803680926476144640|1801564884241092608|1802531700622888960", "notifyType": "1"}' where service_code = 'parkUnSubServiceCode';

