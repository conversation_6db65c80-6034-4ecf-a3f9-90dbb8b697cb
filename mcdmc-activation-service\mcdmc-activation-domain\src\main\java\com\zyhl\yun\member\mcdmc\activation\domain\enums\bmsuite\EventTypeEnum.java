package com.zyhl.yun.member.mcdmc.activation.domain.enums.bmsuite;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/06/13 10:48
 */
@Getter
@AllArgsConstructor
public enum EventTypeEnum {
    /**
     * 1： 建立订购关系
     */
    ESTABLISH(1),

    /**
     * 2： 取消订购关系
     */
    CANCEL(2),

    /**
     * 3:订购关系有效日期变化
     */
    UPDATE(3);

    private final int type;
}
