package com.zyhl.yun.member.mcdmc.activation.domain.mq.properties;

import com.zyhl.yun.member.consumer.common.producer.MqFlowStateEnum;
import com.zyhl.yun.member.consumer.common.producer.properties.IMqFlow;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 内容退费mq配置
 *
 * <AUTHOR>
 * @since 2024/07/08 10:12
 */
@Data
@Configuration
@ConfigurationProperties("rocketmq.consumer.content")
public class ContentMqProperties implements IMqFlow {
    /**
     * 内容退费同步tags
     */
    private String contentSquareTags;

    /**
     * 内容退费同步topic
     */
    private String contentSquareTopic;

    /**
     * 旧svbo的内容退费同步tags
     */
    private String oldContentSquareTags;

    /**
     * 旧vsbo的内容退费同步topic
     */
    private String oldContentSquareTopic;

    /**
     * mq流转状态
     *
     * @see MqFlowStateEnum
     */
    private String flowStatus = MqFlowStateEnum.OLD.getState();

    @Override
    public String getNewTopic() {
        return contentSquareTopic;
    }

    @Override
    public String getOldTopic() {
        return oldContentSquareTopic;
    }

    @Override
    public String getNewTag() {
        return contentSquareTags;
    }

    @Override
    public String getOldTag() {
        return oldContentSquareTags;
    }
}
