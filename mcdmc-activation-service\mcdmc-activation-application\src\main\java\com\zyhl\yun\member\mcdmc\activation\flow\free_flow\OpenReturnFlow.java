package com.zyhl.yun.member.mcdmc.activation.flow.free_flow;

import com.zyhl.yun.member.domain.resource.domain.ResourceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.flow.base.BaseUnSubServiceFlow;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;


/**
 * 一级能开免流退订流程
 *
 * <AUTHOR>
 * @since 2024/07/03 11:58
 */
@Slf4j
@Component
public class OpenReturnFlow extends BaseUnSubServiceFlow {
    @Resource
    private MemberContextUtil memberContextUtil;

    @Override
    protected IFlowResult beforeFlow(WorkOrderDo workOrderDo) {
        // 判断是否开通免流
        ResourceDo currentResourceDo = memberContextUtil.updateResourceEndTime(workOrderDo, false);
        // 如果存在生效的资产，则不能走流程
        if (null != currentResourceDo && null != currentResourceDo.getEffectiveEndTime()
                && currentResourceDo.getEffectiveEndTime().after(new Date())) {
            log.info("userId={} contain valid resource,skip {}. orderId={}", workOrderDo.getUserId(), this.getClass().getSimpleName(), workOrderDo.getOrderId());
            return IFlowResult.finish(currentResourceDo.getResourceId(),
                    "early skip flow,because it contain valid resource,workOrder detail is %s", workOrderDo.toSimpleLogStr());

        }
        // 将资源信息临时存放入工单表
        workOrderDo.putExtData(OtherFieldConstants.RESOURCE_DO, currentResourceDo);
        return IFlowResult.result(NextHintEnum.NEXT, null);
    }

}
