@startuml
'https://plantuml.com/activity-diagram-beta

start
:开始分流;
if (uri=syncAppOrder？) then (true)
  :解析报文，获取入参Subplace;
  if (Subplace=7？) then (yes)
    : 获取入参Action;
    if (Action=1？) then (yes)
      :获取用户信息;
      if (用户为白名单?) then (yes)
         : 转发新vsbo;
      else (no)
          : 转发旧vsbo;
      endif
    else (no)
      : 转发新vsbo(新vsbo需要改造orderId生成规则);
      : 新vsbo根据订单号判断是否新vsbo生成，\n若是旧vsbo生成规则，则调用旧vsbo\n若是新vsbo生成规则，则直接处理;
    endif
    stop
  else (false)
    :获取订单id;
    if (订单id为新vsbo生成规则?) then (yes)
      : 转发新vsbo;
    else
      : 转发旧vsbo;
    endif
      stop
  endif
endif
: 根据对应接口映射规则处理转发;
stop
@enduml
