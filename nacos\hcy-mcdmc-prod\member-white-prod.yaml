server:
  tomcat:
    threads:
      max: 400
      min-spare: 50
    #等待队列长度，当可分配的线程数全部用完之后，后续的请求将进入等待队列等待，等待队列满后则拒绝处理，默认100
    accept-count: 20000
    #最大可被连接数
    max-connections: 10000
    #连接超时时间，该值需要大于nginx的keepalive_timeout，否则nginx会主动断开连接，默认60000
    connection-timeout: 70000
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    cachePrepStmts: true
    prepStmtCacheSize: 300
    prepStmtCacheSqlLimit: 2048
    useServerPrepStmts: true
    useLocalSessionState: true
    rewriteBatchedStatements: true
    cacheResultSetMetadata: true
    cacheServerConfiguration: true
    elideSetAutoCommits: true
    maintainTimeStats: false
    url: ****************************************************************************************************************************************************************************
    #url: *********************************************************************************************************************************************************************************
    username: vip_refactor_4_user@yun_member_refactor#ypcg_member_prod

    password: ENC(oV1WCvoO/VFYQyqUb3GDCep+iQhUmPkhaxTMRVPDfBU=)
#    username: vip_refactor_4_user@gray_yun_member_refactor#ypcg_member_prod
  redis:
    host: ************
    password: ENC(w6V7yZOWZpUY/lwBHTocvrkznf/GAyWbDSDEePmFuvI=)
    port: 32066
    database: 0
    max-redirects: 3
    timeout: 30000
    jedis:
      pool:
        # 最大连接，单位：个。当前tomcat配置线程数为200，考虑每秒内有一半线程在操作redis，且每个线程操作不超过100ms，故线程数设置为50
        maxTotal: 200
        #最大空闲连接，单位：个
        maxIdle: 200
        # 最小空闲连接，单位：个
        minIdle: 20
        # 最大获取连接等待时间，单位：毫秒
        maxWaitMillis: 3000
        #空闲连接逐出时间，大于该值的空闲连接一直未被使用则会被释放，单位：毫秒
        minEvictableIdleTimeMillis: 30000
        #空闲连接探测时间间隔，单位：毫秒。 例如系统的空闲连接探测时间配置为30s，则代表每隔30s会对连接进行探测，如果30s内发生异常的连接，
        #经过探测后会进行连接排除。根据连接数的多少进行配置，如果连接数太大，配置时间太短，会造成请求资源浪费。
        timeBetweenEvictionRunsMillis: 30000
        #向资源池借用连接时是否做连接有效性检测（ping），检测到的无效连接将会被移除。对于业务连接极端敏感的，并且性能可以接受的情况下，
        #可以配置为True，一般来说建议配置为False，启用连接空闲检测。
        testOnBorrow: true
        # 是否在空闲资源监测时通过ping命令监测连接有效性，无效连接将被销毁。
        testWhileIdle: true
        # 向资源池归还连接时是否做连接有效性检测（ping），检测到无效连接将会被移除。耗费性能
        testOnReturn: false
        # 连接空闲检测的时间间隔，单位：毫秒
        timeout: 30000
        # 连接建立超时时间，单位：毫秒
        connectTimeout: 30000
        # 空闲连接检测时，每次检测的连接数
        numTestsPerEvictionRun: 3



mybatis-plus:
  # 启动时是否检查MyBatis XML文件是否存在
  check-config-location: true
  mapper-locations: classpath:mapper/*.xml
  #开启sql日志
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

#单机版
#spring:
#  redis:
#    host: ************
#    port: 18518
#    password: ENC(hhlKGT7hlbbmnHj4DA32bjfVtm75LnsbnPTCxQG7B6g=)
#    lettuce:
#      pool:
#        max-active: 200
#        max-idle: 8
#        max-wait: 10s
#        min-idle: 2
#      shutdown-timeout: 3s
rocketmq:
  producer:
    accessKey: 727f6f51df47489facab1910178b3fe7
    secretKey: 826e4606957a4b2c8ca709d9eac805cc
    produceGroup: produce-group
    serverAddr: ************:31563;************:31563
    instanceId: MQ_INST_1678440605886_rVJEkxlB
    isSdk: false
  consumer:
    user-change:
      #移动云bms
      accessKey: 727f6f51df47489facab1910178b3fe7
      secretKey: 826e4606957a4b2c8ca709d9eac805cc
      groupName: GID_LOCAL_BMSUITE_ORDER-INFO-SYNC
      topic: TOPIC_LOCAL_BMSUITE_ORDER-INFO-SYNC
      tag: userSpace
      instanceId: MQ_INST_1678440605886_rVJEkxlB
      serverAddr: ************:31563;************:31563
      addrType: 1
      isSdk: true
      isOpen: false
      maxRetryNumber: 3
    white-notify:
      accessKey: 727f6f51df47489facab1910178b3fe7
      secretKey: 826e4606957a4b2c8ca709d9eac805cc
      groupName: GID_LOCAL_MEMBER-BENIFIT_MEMBER-WHITE-NOTIFY
      topic: TOPIC_LOCAL_MEMBER-BENIFIT_WHITE-NOTIFY
      tag: operator
      instanceId: MQ_INST_1678440605886_rVJEkxlB
      serverAddr: ************:31563;************:31563
      addrType: 1
      isSdk: true
      isOpen: true
      isOrderly: true
      pullBatchSize: 1
      maxRetryNumber: 3
platform:
  external:
    user-domain:
      url: http://192.168.221.217:8080/
      app-key: 1118462887501971461
      app-secrete: DycLby$PIQH2PiLP
      algorithm-version: 1.0
      app-secrete-id: 1123749863394426881