package com.zyhl.yun.member.mcdmc.activation.domain.enums.local;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Date;

/**
 * 重试类型
 *
 * <AUTHOR>
 * @since 2024/07/05 16:54
 */
@Getter
@AllArgsConstructor
public enum RetryPolicyEnum {
    NOT_RETRY(-1, "不重试") {
        @Override
        public Date getRetryTime(String retryTime) {
            return null;
        }
    },

    SPECIFY_TIME_RETRY(0, "指定时间重试，单位：yyyyMMddHHmmss") {
        @Override
        public Date getRetryTime(String retryTime) {
            return DateUtil.parse(retryTime, DatePattern.PURE_DATETIME_PATTERN);
        }
    },
    RETRY_NOW(1, "立即重试") {
        @Override
        public Date getRetryTime(String retryTime) {
            return new Date();
        }
    },

    OFFSET_TIME_MILLISECOND_RETRY(2, "偏移时间重试,单位：毫秒") {
        @Override
        public Date getRetryTime(String retryTime) {
            return DateUtil.offsetMillisecond(new Date(), Integer.parseInt(retryTime));
        }
    },

    OFFSET_TIME_SECOND_RETRY(3, "偏移时间重试,单位：秒") {
        @Override
        public Date getRetryTime(String retryTime) {
            return DateUtil.offsetSecond(new Date(), Integer.parseInt(retryTime));
        }
    },

    OFFSET_TIME_MINUTE_RETRY(4, "偏移时间重试,单位：分钟") {
        @Override
        public Date getRetryTime(String retryTime) {
            return DateUtil.offsetMinute(new Date(), Integer.parseInt(retryTime));
        }
    },

    OFFSET_TIME_HOUR_RETRY(5, "偏移时间重试,单位：h") {
        @Override
        public Date getRetryTime(String retryTime) {
            return DateUtil.offsetHour(new Date(), Integer.parseInt(retryTime));
        }
    },

    OFFSET_TIME_DAY_RETRY(6, "偏移时间重试,单位：天") {
        @Override
        public Date getRetryTime(String retryTime) {
            return DateUtil.offsetDay(new Date(), Integer.parseInt(retryTime));
        }
    },

    OFFSET_TIME_WEEK_RETRY(7, "偏移时间重试,单位：周") {
        @Override
        public Date getRetryTime(String retryTime) {
            return DateUtil.offsetWeek(new Date(), Integer.parseInt(retryTime));
        }
    },

    OFFSET_TIME_MONTH_RETRY(8, "偏移时间重试,单位：月") {
        @Override
        public Date getRetryTime(String retryTime) {
            return DateUtil.offsetMonth(new Date(), Integer.parseInt(retryTime));
        }
    },
    CUSTOM_RETRY(99, "自定义重试策略") {
        @Override
        public Date getRetryTime(String retryTime) {
            // 这里不返回值，由调用者自己计算重试时间
            return null;
        }
    };

    private final Integer type;

    private final String desc;

    public boolean typeEquals(Integer anotherType) {
        return type.equals(anotherType);
    }

    public abstract Date getRetryTime(String retryTime);

    public static RetryPolicyEnum getByType(Integer type) {
        for (RetryPolicyEnum value : RetryPolicyEnum.values()) {
            if (value.typeEquals(type)) {
                return value;
            }
        }
        return null;
    }
}
