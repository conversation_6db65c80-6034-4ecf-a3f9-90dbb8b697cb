-- 插入流程配置
INSERT INTO member_sopen.activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(1, 'bmsuiteSubServiceCode', 'bmsuite订购/续订流程', 'bmsuiteSubFlow', 0, 3, 3, '10', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'xxx', 'tenantA', '1');
INSERT INTO member_sopen.activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(2, 'bmsuiteUnSubServiceCode', 'bmsuite退订流程', 'bmsuiteUnSubFlow', 0, 3, 3, '10', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'xxx', 'tenantA', '1');
INSERT INTO member_sopen.activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(3, 'normalRaiSubServiceCode', '普通权益订购流程', 'raiSubNormalFlow', 0, 3, 4, '5', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'yaq', 'tenantA', '1');
INSERT INTO member_sopen.activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(4, 'changXRaiSubServiceCode', '畅享权益订购流程', 'raiSubChangXFlow', 0, 3, 4, '5', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'yaq', 'tenantA', '1');
INSERT INTO member_sopen.activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(5, 'movieRaiSubServiceCode', '畅影权益订购流程', 'raiSubMovieFlow', 0, 3, 4, '5', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'yaq', 'tenantA', '1');
INSERT INTO member_sopen.activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(6, 'openSyncServiceCode', '一级能开免流订购流程', 'openSyncFlow', 0, 3, 6, '1', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'yaq', 'tenantA', '1');
INSERT INTO member_sopen.activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(7, 'raiChanYinRefundServiceCode', '畅影权益退费流程', 'raiRefundChangYingFlow', 0, 0, -1, '', 1, '2024-06-29 10:05:00', '2024-06-29 10:05:00', 'yaq', 'tenantA', '1');
INSERT INTO member_sopen.activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(8, 'raiNormalRefundServiceCode', '普通权益退费流程', 'raiRefundNormalFlow', 0, 0, -1, '', 1, '2024-06-29 10:05:00', '2024-06-29 10:05:00', 'yaq', 'tenantA', '1');
INSERT INTO member_sopen.activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(9, 'openReturnServiceCode', '一级能开免流退订流程', 'openReturnFlow', 0, 3, 4, '10', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'yaq', 'tenantA', '1');
INSERT INTO member_sopen.activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(10, 'parkUnSubServiceCode', '停车退订流程', 'parkUnSubFlow', 0, 0, -1, '', 1, '2024-06-29 10:05:00', '2024-06-29 10:05:00', 'yaq', 'tenantA', '1');
INSERT INTO member_sopen.activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(11, 'jiyunFlowSpecServiceCode', '集运免流流程', 'jiyunFlowSpecFlow', 0, 3, 4, '5', 1, '2024-07-10 14:39:14', '2024-07-10 14:39:14', 'gxq', 'tenantA', '1');
INSERT INTO member_sopen.activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(12, 'raiOrderReductionServiceCode', '权益订单核减流程', 'raiOrderReductionFlow', 0, 3, 4, '1', 1, '2024-07-10 14:39:14', '2024-07-10 14:39:14', 'gxq', 'tenantA', '1');
INSERT INTO member_sopen.activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(13, 'raiQueryNormalServiceCode', '普通权益主动查询状态流程', 'raiQueryNormalFlow', 0, 3, 1, '0', 1, '2024-07-30 14:39:14', '2024-07-30 14:39:14', 'gxq', 'tenantA', '1');
INSERT INTO member_sopen.activation_work_service_flow
(work_service_flow_id, service_code, flow_desc, service_flow_class, sync_flag, retry_count, retry_policy, retry_time, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(14, 'raiQueryMovieServiceCode', '明威/畅影权益主动查询状态流程', 'raiQueryMovieFlow', 0, 3, 1, '0', 1, '2024-07-30 14:39:14', '2024-07-30 14:39:14', 'gxq', 'tenantA', '1');

-- 插入流程接口配置
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(1, 1, 'bmsuiteSubServiceCode', 'bmsuiteSubIFace', 'bmsuiteSubIFace', '0', 'text/xml', 'http://10.12.12.128:18089/bmsuite/richlifeApp/devapp/bmsuite.IUser', NULL, '0', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'xxx', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(2, 2, 'bmsuiteUnSubServiceCode', 'bmsuiteUnSubIFace', 'bmsuiteUnSubIFace', '0', 'text/xml', 'http://10.12.12.128:18089/bmsuite/richlifeApp/devapp/bmsuite.IUser', NULL, '0', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'xxx', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(3, 3, 'normalRaiSubServiceCode', 'raiSubNormalIFace', 'raiSubNormalIFace', '0', 'application/json', 'http://10.19.16.38:18000/mock/cmpp-api/externalPlus/v4/orderAccept', '/cloudSEE/openApi/raiNotifyEquityResult', '0', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'yaq', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(4, 4, 'changXRaiSubServiceCode', 'raiSubChangXIFace', 'raiSubChangXIFace', '0', 'application/json', 'http://10.19.16.38:18000/mock/cmpp-api/externalPlus/v4/orderAccept', '/cloudSEE/openApi/notifyMovieEquityRightsResult', '0', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'yaq', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(5, 5, 'movieRaiSubServiceCode', 'raiSubMovieIFace', 'raiSubMovieIFace', '0', 'application/json', 'http://140.143.123.190:8081/cloudmovieapi/sub/order_accept', '/cloudSEE/openApi/notifyRightsSalesReturnResult', '0', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'yaq', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(6, 6, 'openSyncServiceCode', 'openNumberCheckIFace', 'openNumberCheckIFace', '0', 'application/xml', 'http://10.19.16.38:18000/mock/dlife/ifp/flowCheckAction/numberStateCheck.service', NULL, '0', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'yaq', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(7, 6, 'openSyncServiceCode', 'openFreeFlowCheckIFace', 'openFreeFlowCheckIFace', '0', 'application/xml', 'http://10.19.16.38:18000/mock/dlife/ifp/flowCheckAction/bizQualification.service', NULL, '1', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'yaq', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(8, 6, 'openSyncServiceCode', 'openFreeFlowSyncIFace', 'openFreeFlowSyncIFace', '0', 'application/json', 'http://10.19.16.38:18000/mock/dlife/ifp/flowOrderAction/intergratedSalesOrderSync.service', '/cloudSEE/openApi/salesorderResult', '2', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'yaq', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(9, 7, 'raiChanYinRefundServiceCode', 'raiRefundChangYingIFace', 'raiRefundChangYingIFace', '0', 'application/json', 'http://10.19.16.38:18000/mock/cmpp-api/external/rightsSalesReturn', '/cloudSEE/openApi/notifyRightsSalesReturnResult', '0', 1, '2024-06-29 10:05:00', '2024-06-29 10:05:00', 'yaq', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(10, 8, 'raiNormalRefundServiceCode', 'raiRefundNormalIFace', 'raiRefundNormalIFace', '0', 'application/json', 'http://10.19.16.38:18000/mock/cmpp-api/external/rightsSalesReturn', '/cloudSEE/openApi/notifyRightsSalesReturnResult', '0', 1, '2024-06-29 10:05:00', '2024-06-29 10:05:00', 'yaq', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(11, 9, 'openReturnServiceCode', 'openReturnIFace', 'openReturnIFace', '0', 'application/json', 'http://10.19.16.38:18000/mock/dlife/ifp/ReturnsyncAction/returnsyncForAll.service', '/cloudSEE/openApi/flowCardOrderResult', '0', 1, '2024-06-07 10:05:00', '2024-06-07 10:05:00', 'yaq', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(12, 10, 'parkUnSubServiceCode', 'parkUnSubIFace', 'parkUnSubIFace', '0', 'application/json', 'http://39.108.161.49:9009/bus/quanyi/refundYpOrder', NULL, '0', 1, '2024-06-29 10:05:00', '2024-06-29 10:05:00', 'yaq', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(13, 11, 'jiyunFlowSpecServiceCode', 'jiyunFlowSpecIFace', 'jiyunFlowSpecIFace', '0', 'application/json', 'http://10.1.4.72:18000/mock/gateway/api/common/activity/order/addOrder', '/cloudSEE/openApi/subscribeFlowSpecNotify', '0', 1, '2024-07-10 14:39:14', '2024-07-10 14:39:14', 'gxq', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(14, 12, 'raiOrderReductionServiceCode', 'raiOrderReductionIFace', 'raiOrderReductionIFace', '0', 'application/json', 'http://10.19.16.38:18000/mock/cmpp-api/external/rightsSalesReturn', '/cloudSEE/openApi/notifyRightsSalesReturnResult', '0', 1, '2024-07-10 14:39:14', '2024-07-10 14:39:14', 'gxq', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(15, 13, 'raiQueryNormalServiceCode', 'raiQueryNormalIFace', 'raiQueryNormalIFace', '0', 'application/json', 'http://10.19.16.38:18000/mock/cmpp-api/externalPlus/v4/queryOrders', '', '0', 1, '2024-07-30 14:39:14', '2024-07-30 14:39:14', 'gxq', 'tenantA', '0000000000000000');
INSERT INTO member_sopen.activation_work_service_flow_iface
(iface_id, work_service_flow_id, service_code, iface_name, iface_class_name, request_type, media_type, url, notify_uri, prior_sort, state, created_time, updated_time, operator, tenant_id, version_tag)
VALUES(16, 14, 'raiQueryMovieServiceCode', 'raiQueryMovieIFace', 'raiQueryMovieIFace', '0', 'application/json', 'http://10.19.16.38:18000/mock/cloudmovieapi/sub/query_order', '', '0', 1, '2024-07-30 14:39:14', '2024-07-30 14:39:14', 'gxq', 'tenantA', '0000000000000000');
