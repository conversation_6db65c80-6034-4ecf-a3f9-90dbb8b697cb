package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.query;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.mcdmc.activation.constants.WorkAttrCodeConstant;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.DbFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.ActivationContextUtil;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.Md5Util;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.SendTemplate;
import com.zyhl.yun.member.mcdmc.activation.util.MemberContextUtil;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiBaseProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.RaiSubQueryReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.RaiSubReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.RaiSubQueryRsp;
import com.zyhl.yun.member.mcdmc.activation.util.RaiUtil;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;

import static com.zyhl.yun.member.mcdmc.activation.constants.BusinessConstant.ORDER_SUFFIX;

/**
 * 权益订购主动查询接口
 * <p>
 * 权益订购后防止未接收到回调做的主动查询补偿机制
 * </p>
 * com.huawei.jaguar.vsbo.pub.rocketmq.ConsumerNotifySubStatusMessageThread#run
 *
 * <AUTHOR>
 * @since 2024/07/22 15:37
 */
public abstract class RaiQueryBaseIFace extends SendTemplate<RaiSubQueryReq, RaiSubQueryRsp> {

    protected RaiUtil raiUtil;

    private RaiBaseProperties raiBaseProperties;

    @PostConstruct
    private void initBean() {
        raiUtil = SpringUtil.getBean(RaiUtil.class);
        raiBaseProperties = getRaiProperties();
    }


    protected abstract RaiBaseProperties getRaiProperties();

    @Override
    protected RaiSubQueryReq getRequestBody(InterfaceContext<RaiSubQueryReq, RaiSubQueryRsp> interfaceContext) {
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        RaiSubQueryReq.Body body = new RaiSubQueryReq.Body();
        // 优先获取下单存时的渠道号，没有则用默认渠道号
        String orderChannelCode = comSendReq.getExtInfo(OtherFieldConstants.ORDER_CHANNEL_CODE, raiBaseProperties.getEquityChannelCode());
        body.setChannelCode(orderChannelCode);
        //截取req.getOrderID() 字符串中Constant.ORDER_SUFFIX之前的内容，并将其作为结果返回
        body.setChannelNo(CharSequenceUtil.subBefore(comSendReq.getOrderID(), ORDER_SUFFIX, true));
        RaiSubReq.Head head = new RaiSubReq.Head();
        head.setApiId(raiBaseProperties.getEquityQueryApiId());
        String formatCurTime = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        head.setTransactionId(formatCurTime);
        head.setReqTime(formatCurTime);
        head.setVersion(raiBaseProperties.getEquityVersion());
        head.setChannelCode(orderChannelCode);
        String authStr = formatCurTime + head.getTransactionId() +
                raiBaseProperties.getEquityQuerySecretKey() +
                JSONUtil.toJsonStr(body, JSONConfig.create().setNatureKeyComparator());
        authStr = authStr.replaceAll("\\s", "");
        String sign = Md5Util.md5Encode(authStr);
        head.setSign(sign.toUpperCase());
        return new RaiSubQueryReq(head, body);
    }

    /**
     * 业务处理成功逻辑
     *
     * @param interfaceContext 接口上下文
     */
    @Override
    protected void doBusiSuccess(InterfaceContext<RaiSubQueryReq, RaiSubQueryRsp> interfaceContext) {
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        String orderItemId = interfaceContext.getInterfaceRspObj().getContractRoot().getBody().getOrderList().get(0).getOrderItemId();
        GoodsInstanceDo goodsInstanceDo = MemberContextUtil.qryGoodsInstanceDo(
                comSendReq.getUserId(), comSendReq.getGoodsInstanceId());
        String outOrderId = getCallbackConditionWhileSC(interfaceContext);
        // 外部返回订单列表
        List<RaiSubQueryRsp.OrderListEntity> outOrderList = interfaceContext.getInterfaceRspObj().getContractRoot().getBody().getOrderList();
        String outUpdateTime = outOrderList.get(0).getUpdateTime();
        // 发送内容mq
        raiUtil.sendContentMq(comSendReq, outOrderId, outUpdateTime, goodsInstanceDo, orderItemId);
        // 将外部权益id保存到订购对应的工单属性里
        String subWorkId = comSendReq.getExtInfo(DbFieldConstants.WORK_ID);
        ActivationContextUtil.upsertWorkAttr(comSendReq.getUserId(), subWorkId, WorkAttrCodeConstant.RIGHTS_OUT_ACCOUNT_ID, orderItemId);
        this.doRaiReceiveSuccess(interfaceContext, goodsInstanceDo);
    }

    protected abstract void doRaiReceiveSuccess(InterfaceContext<RaiSubQueryReq, RaiSubQueryRsp> interfaceContext, GoodsInstanceDo goodsInstanceDo);


    @Override
    protected String getCallbackCondition(InterfaceContext<RaiSubQueryReq, RaiSubQueryRsp> context) {
        List<RaiSubQueryRsp.OrderListEntity> orderList = context.getInterfaceRspObj().getContractRoot().getBody().getOrderList();
        return CollUtil.emptyIfNull(orderList).get(0).getOrderId();
    }

    @Override
    protected String getResourceId(InterfaceContext<RaiSubQueryReq, RaiSubQueryRsp> context) {
        List<RaiSubQueryRsp.OrderListEntity> orderList = context.getInterfaceRspObj().getContractRoot().getBody().getOrderList();
        return CollUtil.emptyIfNull(orderList).get(0).getOrderItemId();
    }

    @Override
    protected Class<RaiSubQueryReq> getReqClass() {
        return RaiSubQueryReq.class;
    }

    @Override
    protected Class<RaiSubQueryRsp> getRspClass() {
        return RaiSubQueryRsp.class;
    }

    @Override
    protected boolean isSuccess(RaiSubQueryRsp rsp) {
        return RaiSubQueryRsp.isSuccess(rsp);
    }

}
