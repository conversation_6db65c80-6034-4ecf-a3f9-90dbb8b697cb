package com.zyhl.yun.member.common.domain.framework;

import com.zyhl.yun.member.common.domain.mono.ObjectSerial;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.util.Base64Utils;

import java.io.IOException;
import java.io.Serializable;

/**
 * 结果包装器
 * 远程调用结果包装器
 *
 * <AUTHOR>
 * @date 2024/05/28 9:27
 */

@Getter
@ToString
@NoArgsConstructor
public class ResultWrapper implements Serializable {


    private static final long serialVersionUID = 2070633337843056774L;


    /**
     * 返回信息
     */
    private String resultMsg;


    /**
     * 是否成功
     */
    private boolean success;


    /**
     * 结果数据
     */
    private String resultData;

    /**
     * 异常
     */
    private DomainException domainException;


    public ResultWrapper(Serializable result) throws IOException {

        this.resultData = Base64Utils.encodeToString(ObjectSerial.getBytesByObj(result));
        this.success = true;

    }


    public ResultWrapper(Throwable throwable) {
        if (throwable instanceof DomainException) {
            this.domainException = (DomainException) throwable;
        } else {
            this.domainException = new DomainException(throwable);
        }
        this.resultMsg = throwable.getMessage();
        this.success = false;
    }

}
