spring.cloud:
  loadbalancer:
    ribbon:
      enabled: true
    cache:
      enabled: true
  gateway:
    enabled: true
    httpclient:
      pool:
        maxIdleTime: 200
      #connect-timeout: 1000
      #response-timeout: 5s
    routes:
      - id: member-product
        predicates:
          - Path=/cloudSEE/openApi/updateProduct,/cloudSEE/openApi/queryProducts,/cloudSEE/openApi/createProduct,/cloudSEE/openApi/queryProductsV2,/cloudSEE/openApi/queryProductsByType,/cloudSEE/openApi/deleteProduct
        filters:
          - StripPrefix=0
          - PrefixPath=/member-product
        uri: lb://member-product

      - id: member-order
        predicates:
          - Path=/cloudSEE/openApi/preCheckFamilyNumber,/cloudSEE/openApi/notifyPaymentResultOfThirdParty,/cloudSEE/openApi/groupBuyNotify,/cloudSEE/openApi/differenceDigitalReconcile,/cloudSEE/openApi/preOrderCheck,/cloudSEE/openApi/prepareRefund,/cloudSEE/openApi/refundNotify,/cloudSEE/openApi/queryOrders,/cloudSEE/openApi/queryOrdersV1,/cloudSEE/openApi/syncAppOrder,/cloudSEE/openApi/manageSubscribeRelation,/cloudSEE/openApi/orderReduction,/cloudSEE/openApi/payOrder,/cloudSEE/openApi/createOrder,/cloudSEE/openApi/notifyOrderPayResult,/cloudSEE/openApi/createSubscribeRelation,/cloudSEE/openApi/activeGotoneRights,/cloudSEE/openApi/hySubscribe,/cloudSEE/openApi/bindFamilyNumber,/cloudSEE/openApi/queryOrdersForDY,/cloudSEE/openApi/queryOrderGroup,/cloudSEE/openApi/newInitiateGroupBuy,/cloudSEE/openApi/queryOrdersV3
        filters:
          - StripPrefix=0
          - PrefixPath=/member-order
        uri: lb://member-order

      - id: member-vip
        predicates:
          - Path=/cloudSEE/openApi/modifyFamilyNumber,/cloudSEE/openApi/querySubscribeFlow,/cloudSEE/openApi/queryIndependentSpaceSub,/cloudSEE/openApi/querySubscribeOperation,/cloudSEE/openApi/queryRights,/cloudSEE/openApi/queryGotone,/cloudSEE/openApi/queryPhoneType,/cloudSEE/openApi/cancelAccount,/cloudSEE/openApi/queryOrCreateUserID,/cloudSEE/openApi/querySubscribeRelation,/cloudSEE/openApi/querySubscribeRelationV1,/cloudSEE/openApi/querySubscribeRelationV2,/cloudSEE/openApi/queryContractListSubScription,/cloudSEE/openApi/queryContractListSubScriptionV2,/cloudSEE/openApi/queryContractListSubScriptionV3,/cloudSEE/openApi/queryContractListSubScriptionV4,/cloudSEE/openApi/queryContractListSubScriptionV5,/cloudSEE/openApi/querySubscribeFlowSpec,/cloudSEE/openApi/querySubscribeFlowSpecV2,/cloudSEE/openApi/querySubscribeFlowSpecV3,/cloudSEE/openApi/queryReturnSubscription,/cloudSEE/openApi/queryFamilyNumber,/cloudSEE/openApi/activeRightsPackage,/cloudSEE/openApi/unSubscribe,/cloudSEE/openApi/rollBackRight,/cloudSEE/openApi/chkValidMonthSubscribe,/cloudSEE/openApi/queryGotone,/cloudSEE/openApi/queryRights,/cloudSEE/openApi/queryRightsSubscribeRelation,/cloudSEE/openApi/subscribeFlowSpec,/cloudSEE/openApi/subscribeFlowSpecV2,/cloudSEE/openApi/querySubscribeOperationYearAgo,/cloudSEE/openApi/querySubscribeOperationWithinOneYear,/cloudSEE/openApi/queryActivitySubScription,/cloudSEE/openApi/queryContractListSubScriptionForDY,/cloudSEE/openApi/getSubscriptionByOrderId,/cloudSEE/openApi/acceptClearedDetail,/cloudSEE/openApi/queryFamilyPackageGroup
        filters:
          - StripPrefix=0
          - PrefixPath=/member-vip
        uri: lb://member-vip

      - id: member-payment
        predicates:
          - Path=/cloudSEE/openApi/thirdRefund,/cloudSEE/openApi/refundQuery,/cloudSEE/openApi/notifyTpctPayResult,/cgw/fsapay/{segment},/payment/{segment}
        filters:
          - StripPrefix=0
          - RewritePath=/cloudSEE/openApi/notifyTpctPayResult,/cgw/fsapay/notifyTpctPayResult
        uri: lb://member-payment

      - id: member-activation
        predicates:
          - Path=/cloudSEE/openApi/raiNotifyEquityResult,/cloudSEE/openApi/notifyMovieEquityRightsResult,/cloudSEE/openApi/salesorderResult,/cloudSEE/openApi/subscribeFlowSpecNotify,/cloudSEE/openApi/flowCardOrderResult,/cloudSEE/openApi/notifyRightsSalesReturnResult,/api/syncOrder
        filters:
          - StripPrefix=0
          - PrefixPath=/member-activation
        uri: lb://member-activation

      - id: member-support
        predicates:
          - Path=/cloudSEE/openApi/sendTemplateSms,/cloudSEE/openApi/createFamilyPackageGroup
        filters:
          - StripPrefix=0
          - PrefixPath=/member-support
        uri: lb://member-support

#################################第三方鉴权（调用用户域）########################################
third-auth-caller:
  url: 'http://***************:8080/user/status/query'
  appKey: 1118462887501971461
  appSecretId: 1123749863394426881
  appSecret: DycLby$PIQH2PiLP
  algorithmVersion: 1.0

###################################用户域地址################################################################
user-domain-url: http://***************:8080

###################################解析userDomainId表达式###################################################
express-config:
  interfaceUserDomainIdExpressConfig:
    'createOrder': 'createOrderReq.userDomainId'
    'payOrder': 'payOrderReq.userDomainId'
    'queryOrders': 'queryOrdersReq.orderQueryCond.userDomainId'
    'queryOrdersV1': 'queryOrdersReq.orderQueryCond.userDomainId'
    'preOrderCheck': 'createOrderReq.userDomainId'
    'cancelAccount': 'cancelAccountReq.userDomainId'
    'queryOrCreateUserID': 'queryOrCreateUserIDReq.account.userDomainId'
    'notifyOrderPayResult': 'notifyOrderPayResultReq.userDomainId'
    'syncAppOrder': 'SyncAppOrderReq.userDomainId'
    'notifyTpctPayResult': 'msgReq.userDomainId'
    'querySubscribeRelation': 'querySubscribeRelationReq.userDomainId'
    'querySubscribeRelationV1': 'querySubscribeRelationReq.userDomainId'
    'querySubscribeRelationV2': 'querySubscribeRelationReq.userDomainId'
    'createSubscribeRelation': 'createSubscribeRelationReq.userDomainId'
    'unSubscribe': 'unSubscribeReq.userDomainId'
    'queryContractListSubScription': 'queryContractListSubScriptionReq.userDomainId'
    'queryContractListSubScriptionV2': 'queryContractListSubScriptionReq.userDomainId'
    'queryContractListSubScriptionV3': 'queryContractListSubScriptionReq.userDomainId'
    'queryContractListSubScriptionV4': 'queryContractListSubScriptionReq.userDomainId'
    'queryContractListSubScriptionV5': 'queryContractListSubScriptionReq.userDomainId'
    'manageSubscribeRelation': 'manageSubscribeRelationReq.userDomainId'
    'querySubscribeOperation': 'querySubscribeOperationReq.userDomainId'
    'activeGotoneRights': 'activeGotoneRightsReq.userDomainId'
    'chkValidMonthSubscribe': 'chkValidMonthSubscribeReq.userDomainId'
    'hySubscribe': 'hySubscribeReq.userDomainId'
    'subscribeFlowSpec': 'subscribeFlowSpecReq.userDomainId'
    'querySubscribeFlowSpec': 'querySubscribeFlowSpecReq.userDomainId'
    'querySubscribeFlowSpecV2': 'querySubscribeFlowSpecReq.userDomainId'
    'querySubscribeFlowSpecV3': 'querySubscribeFlowSpecReq.userDomainId'
    'rollBackRight': 'rollBackRightReq.userDomainId'
    'queryRightsSubscribeRelation': 'queryRightsSubscribeRelationReq.userDomainId'
    'subscribeFlowSpecV2': 'subscribeFlowSpecReq.userDomainId'
    'queryReturnSubscription': 'queryReturnSubscriptionReq.userDomainId'
    'queryGotone': 'queryGotoneReq.account.userDomainId'
    'queryFamilyNumber': 'queryFamilyNumberReq.account.userDomainId'
    'queryRights': 'queryRightsReq.account.userDomainId'
    'prepareRefund': 'prepareRefundReq.userDomainId'
    'refundNotify': 'msgReq.userDomainId'
    'activeRightsPackage': 'activeRightsPackageReq.userDomainId'
    'salesorderResult': 'OPFlowPkgSubsFdbkReq.userDomainId'
    'flowCardOrderResult': 'OPFlowCardOrderFdbkReq.userDomainId'
    'queryPhoneType': 'queryPhoneTypeReq.account.userDomainId'
    'queryProductsV2': 'queryProductsReq.extensionInfo'
    'queryProducts': 'queryProductsReq.extensionInfo'
    'bindFamilyNumber': 'bindFamilyNumberReq.account.userDomainId|bindFamilyNumberReq.familyAccount.userDomainId'

  interfaceAccountExpressConfig:
    'createOrder': 'createOrderReq.userID'
    'payOrder': 'payOrderReq.userID'
    'queryOrders': 'queryOrdersReq.orderQueryCond.orderUserID'
    'queryOrdersV1': 'queryOrdersReq.orderQueryCond.orderUserID'
    'preOrderCheck': 'createOrderReq.userID'
    'cancelAccount': 'cancelAccountReq.account'
    'queryOrCreateUserID': 'queryOrCreateUserIDReq.account.accountName'
    'notifyOrderPayResult': 'notifyOrderPayResultReq.orderID'
    'syncAppOrder': 'SyncAppOrderReq.MSISDN'
    'notifyTpctPayResult': 'msgReq.orderId'
    'querySubscribeRelation': 'querySubscribeRelationReq.userId'
    'querySubscribeRelationV1': 'querySubscribeRelationReq.userId'
    'querySubscribeRelationV2': 'querySubscribeRelationReq.userId'
    'createSubscribeRelation': 'createSubscribeRelationReq.userID'
    'unSubscribe': 'unSubscribeReq.orderID'
    'queryContractListSubScription': 'queryContractListSubScriptionReq.account'
    'queryContractListSubScriptionV2': 'queryContractListSubScriptionReq.account'
    'queryContractListSubScriptionV3': 'queryContractListSubScriptionReq.account'
    'queryContractListSubScriptionV4': 'queryContractListSubScriptionReq.account'
    'queryContractListSubScriptionV5': 'queryContractListSubScriptionReq.account'
    'manageSubscribeRelation': 'manageSubscribeRelationReq.account'
    'querySubscribeOperation': 'querySubscribeOperationReq.account'
    'activeGotoneRights': 'activeGotoneRightsReq.account'
    'chkValidMonthSubscribe': 'chkValidMonthSubscribeReq.account'
    'hySubscribe': 'hySubscribeReq.account'
    'subscribeFlowSpec': 'subscribeFlowSpecReq.account'
    'querySubscribeFlowSpec': 'querySubscribeFlowSpecReq.account'
    'querySubscribeFlowSpecV2': 'querySubscribeFlowSpecReq.account'
    'querySubscribeFlowSpecV3': 'querySubscribeFlowSpecReq.account'
    'rollBackRight': 'rollBackRightReq.account'
    'queryRightsSubscribeRelation': 'queryRightsSubscribeRelationReq.account'
    'subscribeFlowSpecV2': 'subscribeFlowSpecReq.account'
    'queryReturnSubscription': 'queryReturnSubscriptionReq.account'
    'queryGotone': 'queryGotoneReq.account.accountName'
    'queryFamilyNumber': 'queryFamilyNumberReq.account.accountName'
    'queryRights': 'queryRightsReq.account.accountName'
    'prepareRefund': 'prepareRefundReq.account'
    'refundNotify': 'msgReq.userID'
    'activeRightsPackage': 'activeRightsPackageReq.userId'
    'salesorderResult': 'OPFlowPkgSubsFdbkReq.orderId'
    'flowCardOrderResult': 'OPFlowCardOrderFdbkReq.orderId'
    'queryPhoneType': 'queryPhoneTypeReq.account.accountName'
    'queryProductsV2': 'queryProductsReq.extensionInfo'
    'queryProducts': 'queryProductsReq.extensionInfo'
    'bindFamilyNumber': 'bindFamilyNumberReq.account.accountName|bindFamilyNumberReq.familyAccount.accountName'

# 接口配置鉴权方式
interface-auth-type:
  basicAuthUrl: 'http://***************:8080/user/auth/getUserInfoByToken'
  authTypeList:
    'preCheckFamilyNumber': ['ipWhiteListAuthCheckHandler','basicAuthCheckHandler','moduleCheckHandler']
    'cancelAccount': ['moduleCheckHandler']
    'changeAccount': ['moduleCheckHandler']
    'querySubscribeRelationV1': ['moduleCheckHandler','basicAuthCheckHandler']
    'createSubscribeRelation': ['moduleCheckHandler','ipWhiteListAuthCheckHandler']
    'manageSubscribeRelation': ['moduleCheckHandler','ipWhiteListAuthCheckHandler']
    'queryContractListSubScription': ['ipWhiteListAuthCheckHandler']
    'createContract': ['ipWhiteListAuthCheckHandler']
    'updateContract': ['ipWhiteListAuthCheckHandler']
    'deleteContract': ['ipWhiteListAuthCheckHandler']
    'queryContract': ['ipWhiteListAuthCheckHandler','moduleCheckHandler']
    'queryOrdersV1': ['ipWhiteListAuthCheckHandler']
    'notifyOrderPayResult': ['ipWhiteListAuthCheckHandler']
    'notifySendProduct': ['ipWhiteListAuthCheckHandler']
    'createOrder': ['basicAuthCheckHandler','ipWhiteListAuthCheckHandler']
    'applePay': ['basicAuthCheckHandler']
    'payOrder': ['basicAuthCheckHandler','ipWhiteListAuthCheckHandler']
    'deleteOrder': ['basicAuthCheckHandler']
    'queryOrders': ['moduleCheckHandler','basicAuthCheckHandler','ipWhiteListAuthCheckHandler']
    'batchFetchHistoryOrders': ['basicAuthCheckHandler']
    'querySubscribeRelation': ['basicAuthCheckHandler','ipWhiteListAuthCheckHandler','moduleCheckHandler']
    'querySubscribeOperation': ['basicAuthCheckHandler','moduleCheckHandler','ipWhiteListAuthCheckHandler']
    'unSubscribe': ['moduleCheckHandler','basicAuthCheckHandler','ipWhiteListAuthCheckHandler']
    'queryUserInfoByGroup': ['moduleCheckHandler']
    'createProduct': ['moduleCheckHandler','ipWhiteListAuthCheckHandler']
    'updateProduct': ['moduleCheckHandler']
    'deleteProduct': ['moduleCheckHandler']
    'queryProductList': ['moduleCheckHandler']
    'chkValidMonthSubscribe': ['moduleCheckHandler']
    'activeGotoneRights': ['basicAuthCheckHandler','moduleCheckHandler']
    'queryGotone': ['basicAuthCheckHandler','ipWhiteListAuthCheckHandler','moduleCheckHandler']
    'queryFamilyNumber': ['basicAuthCheckHandler','moduleCheckHandler','ipWhiteListAuthCheckHandler']
    'modifyFamilyNumber': ['ipWhiteListAuthCheckHandler','basicAuthCheckHandler','moduleCheckHandler']
    'bindFamilyNumber': ['ipWhiteListAuthCheckHandler','basicAuthCheckHandler','moduleCheckHandler']
    'salesorderResult': ['ipWhiteListAuthCheckHandler']
    'queryRights': ['basicAuthCheckHandler','ipWhiteListAuthCheckHandler']
    'queryPhoneType': ['basicAuthCheckHandler','ipWhiteListAuthCheckHandler']
    'queryFlowPrecondition': ['basicAuthCheckHandler']
    'queryContractListSubScriptionV2': ['ipWhiteListAuthCheckHandler','moduleCheckHandler']
    'hySubscribe': ['ipWhiteListAuthCheckHandler']
    'querySpaceSubscribeRelationV2': ['basicAuthCheckHandler']
    'querySubscribeRelationV2': ['basicAuthCheckHandler']
    'subscribeFlowSpec': ['basicAuthCheckHandler','ipWhiteListAuthCheckHandler']
    'subscribeFlowSpecNotify': ['ipWhiteListAuthCheckHandler']
    'querySubscribeFlowSpec': ['basicAuthCheckHandler','moduleCheckHandler']
    'rollBackRight': ['moduleCheckHandler']
    'notifyTpctPayResult': ['ipWhiteListAuthCheckHandler']
    'notifyTradeStatus': ['ipWhiteListAuthCheckHandler']
    'queryRightsSubscribeRelation': ['basicAuthCheckHandler','ipWhiteListAuthCheckHandler']
    'raiNotifyEquityResult': ['ipWhiteListAuthCheckHandler']
    'subscribeFlowSpecV2': ['basicAuthCheckHandler','moduleCheckHandler']
    'raiPayResultNotify': ['ipWhiteListAuthCheckHandler']
    'notifyMovieEquityRightsResult': ['ipWhiteListAuthCheckHandler']
    'activeRightsPackage': ['ipWhiteListAuthCheckHandler']
    'prepareRefund': ['ipWhiteListAuthCheckHandler']
    'refundNotify': ['ipWhiteListAuthCheckHandler']
    'querySubscribeFlowSpecV2': ['basicAuthCheckHandler','moduleCheckHandler']
    'queryReturnSubscription': ['moduleCheckHandler']
    'executeRenewRemindTask': ['ipWhiteListAuthCheckHandler']
    'getSubscriptionByOrderId': ['ipWhiteListAuthCheckHandler']
    'groupBuyNotify': ['ipWhiteListAuthCheckHandler']
    'differenceDigitalReconcile': ['ipWhiteListAuthCheckHandler']
    'refundQuery': ['ipWhiteListAuthCheckHandler']
    'sendTemplateSms': ['ipWhiteListAuthCheckHandler']
    'queryContractListSubScriptionV3': ['ipWhiteListAuthCheckHandler','moduleCheckHandler']
    'queryContractListSubScriptionV4': ['ipWhiteListAuthCheckHandler','moduleCheckHandler']
    'queryContractListSubScriptionV5': ['ipWhiteListAuthCheckHandler','moduleCheckHandler']
    'queryOrdersV3': ['ipWhiteListAuthCheckHandler']
    'notifyRefundResultOfThirdParty': ['ipWhiteListAuthCheckHandler']
    'newInitiateGroupBuy': ['ipWhiteListAuthCheckHandler']
    'queryOrderGroup': ['ipWhiteListAuthCheckHandler']
    'notifyPaymentResultOfThirdParty': ['ipWhiteListAuthCheckHandler']
    'orderReduction': ['ipWhiteListAuthCheckHandler']

# 网元鉴权信息
module-config:
  intfAuthMap:
    'cancelAccount': 'all'
    'changeAccount': 'all'
    'querySubscribeRelationV1': 'all'
    'createSubscribeRelation': 'all'
    'unSubscribe': 'all'
    'manageSubscribeRelation': 'all'
    'queryUserInfoByGroup': 'all'
    'createProduct': 'all'
    'updateProduct': 'all'
    'deleteProduct': 'all'
    'queryProductList': 'all'
    'chkValidMonthSubscribe': 'all'
    'activeGotoneRights': 'MarketingPlatform'
    'queryFamilyNumber': 'MarketingPlatform'
    'bindFamilyNumber': 'MarketingPlatform'
    'querySubscribeRelation': 'all'
    'rollBackRight': 'all'
    'querySubscribeFlowSpec': 'all'
    'querySubscribeFlowSpecV2': 'all'
    'subscribeFlowSpec': 'all'
    'subscribeFlowSpecV2': 'all'
    'queryReturnSubscription': 'all'
    'queryOrders': 'all'
    'differenceDigitalReconcile': 'all'
    'querySubscribeOperation': 'all'
    'queryContractListSubScription': 'all'
    'queryContractListSubScriptionForDY': 'all'
    'queryOrdersForDY': 'all'
    'querySubscribeFlowSpecV3': 'all'
    'querySubscribeFlow': 'all'
    'queryContractListSubScriptionV5': 'all'
  moduleList:
    - moduleName: Default SP
      passwd: qjWUxGJ+iPtCQB7sXAyYjkKT6GWpFBiDCp6mIcUIWxsGlLP8A3uKrKckgNBNjwgd
      ipPool: ''
    - moduleName: PSBO
      passwd: mwwvkPylCXgWuwJi62nCRtu4YgJ0VCZrZ+RMmrJDYkDya0IEms5UgjI5okyJF2z2
      ipPool: *************
    - moduleName: BenefitCenter
      passwd: oGOLBq3oA89kke6xkun7AdW+rCKwnd9sloU74RCP0eQpXlMOqOM+E36AN0AQk4k5
      ipPool: ''
    - moduleName: CloudWallet
      passwd: nW9j+A1hKi0Rob9k8LIlzTOUuhWAAlZUHyES88UAmw77HHToHQZukS3d5aFqi67P
      ipPool: ***********
    - moduleName: MarketingPlatform
      passwd: w4ZSuRRb7NQx03q1CP/V2pAY8bt0lx2NLMtuk626UcSHEusdXYwosL/cflsYmipA
      ipPool: ***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|***************|**********|**********|**********|**********|**********|**********|**********|**********|*************|**********|**********|**********|**********|**********|**********|**********|**********|***********|***********|***********|***********|***********|***********|*********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|**********|**********|**********|**********|***********|10.35.0.140|10.35.0.141|10.35.0.142|10.35.0.143|10.35.0.144|10.35.0.145|10.35.0.146|10.35.0.147|10.35.0.148|10.35.0.149|10.35.0.150|10.35.0.151|10.35.0.152|10.35.0.153|10.35.0.154|10.35.0.155|10.35.0.156|10.35.0.157|10.35.0.158|10.35.0.159|10.35.0.160|10.35.0.161|10.35.0.162|10.35.0.163|10.35.0.164|10.35.0.165|10.35.0.166|10.35.0.167|10.35.0.168|10.35.0.169|10.35.0.170|10.35.0.171|10.35.0.172|10.35.0.173|10.35.0.174|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********
    - moduleName: Jiangxihewoxin
      passwd: 7Lc5j2Gq1QrEtoYO6z9VIeAEvN8t8fabr5BZPGzJdbLCCxk+MmIicRZP4zAOaDBr
      ipPool: **************
    - moduleName: MOAC
      passwd: Gk8ystuqfABDIpjCmwaiWVITNxiJYAsw38EKEM1NJdHnNBmWCT2k1TeqJooQcFrQ
      ipPool: **************|*************
    - moduleName: MiguMusic
      passwd: PgVdnxu5mfOhqHhEeaMo0oLSXSGnArEp2rxoiUT/xn87cf1Ksnz98mgjG3zRrjcV
      ipPool: *************
    - moduleName: PlatAdapterCap
      passwd: aBE2+fle+NGXiaPnxIhNnElu/h+v8dkj+dQGW9DSlnyI3hJT39eu4fzL5Uc+QF4e
      ipPool: **********|**********|**********|**********
    - moduleName: CoupMgt
      passwd: 82r9qaTbwdZSjWqlnN8r5c47YGsTCFc1GY6OndSMuTE52LuyBSgxm5Zknf8T5Y7l
      ipPool: **********|***************|***************|**********|**********|**********|***************|***************
    - moduleName: CustomService
      passwd: B3KxlxgVDVr8xnMYaer1deQ2jvByHiPWyJUUN66kUsjF7ZamV0VkOpHlkvIvheJK
      ipPool: *********|*********|*************
    - moduleName: HuangYan
      passwd: XJIOn+RHhN2IIAgjp17BMhL/eaayPtWjF6ZQDdWjPmJ+vufq4ZTpPwUI2lDursK/
      ipPool: ***********|*************|***************|***************
    - moduleName: AbilitySys
      passwd: Gk8ystuqfABDIpjCmwaiWVITNxiJYAsw38EKEM1NJdHnNBmWCT2k1TeqJooQcFrQ
      ipPool: ***************|***************|**********|**********
    - moduleName: orchestration
      passwd: X1//x3c0KSBgwPIz1WOQOi7cPDn2N8oeyyqQjIR4ZWx3YuisWYqU1romKFRK65wQ
      ipPool: **********02|**********03|**********04|**********05|**********06|**********07|**********08|**********09|**********10|**********11|**********12|**********13|**********14|**********15|**********16|**********39|**********40|***************|***************
    - moduleName: h5MemberCenter
      passwd: LuarP+BdPfrxdvgkD0pO5iImUbX41lh/RbIWlFrGrbJHzQv83p/4QKofUacMzuyZ
      ipPool: *********|*********|*********|*********|***********|***********|***********|***********|*************|*************|********2|*********|*********
    - moduleName: Gdhcypt
      passwd: TpTXeT+4JWtKr8QZhIpDD77++AAgJez5i/7JD+Twef+nKlh42KaskVqLU2eFiL1k
      ipPool: **************|*************|*************
    - moduleName: open
      passwd: 07+2/hJ1Gqbx+fV4QPuODZwP57LiqFUDjShbhWvAaYVBTViMslme2KZNKiCJ5cbv
      ipPool: *********|*********|*********|*********|**********43|**********45|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********
    - moduleName: content
      passwd: PjpkucOwD5p0jv8Ay533URuC84hzZBtbdcVgykKb3WFq4sVO1xjK3lnoYz5AIk2r
      ipPool: **********|**********|**********|**********|**********|**********|**********
    - moduleName: HongKong
      passwd: 5VJF/P02JMQcQCUwq5+uKfQQfg6UtPl2Wj+OpZow+Kr1s5oqrGq6tr43Wd3wn5fo
      ipPool: ***************
    - moduleName: BEIJINGGONGWU
      passwd: frD6jUIUmJNlZMNv1v64+ll1g55LSs/UzWo9JJI5DcATq3PbrBIVR4DjDj4b6fQJ
      ipPool: *************
    - moduleName: ChannelManagementPlatform
      passwd: t/bASG//m67ctlMTAvvFBtFKKt1or/Hn2efvP9uvcy7HRex2GZxgDHFRaMV9cfgV
      ipPool: ***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********|***********
    - moduleName: ConSWMO-Sys
      passwd: Qm4UXDVXtCeMIzpOcF5EQxoRanPC/3Pm6ww2NvWdcMgHiLI77PpOPKbIKmrZTosm
      ipPool: ***********|***********|***********|***********|***********|***********
    - moduleName: userDomainPlatform
      passwd: elG6dpIPebuvoHJydMELCxT3lpwAr0EfAb38oq/foysD+xidv6W/7dKcKb9qJq5V
      ipPool: ***********|***********|**********|10.19.50.5|10.19.50.6|10.19.50.7|10.19.50.8|10.19.50.10|10.19.50.13|10.19.50.14|10.19.50.16|10.19.50.17|10.19.50.18|10.19.50.19|10.19.50.21|10.19.50.25|10.19.50.26|10.19.50.28|10.19.50.29|**********0|**********2|**********3|**********5|**********6|**********7|10.19.50.40|10.19.50.42|10.19.50.43|10.19.50.44|10.19.50.46|10.19.50.47|10.19.50.48|10.19.50.50|10.19.50.51|10.19.50.52|10.19.50.54|10.19.50.55|10.19.50.56|10.19.50.57|10.19.50.58|10.19.50.59|10.19.50.64|10.19.50.65|10.19.50.68|10.19.50.69|10.19.50.74|10.19.50.76|10.19.50.77|10.19.50.78|10.19.50.20|10.19.50.24|10.19.50.27|**********1|**********8|**********9|10.19.50.41|10.19.50.49|10.19.50.53|10.19.50.60|10.19.50.61|10.19.50.67|10.19.50.71|10.19.88.1|10.19.88.6|10.19.88.23|10.19.88.7|10.19.88.9|10.19.88.10|10.19.88.11|10.19.88.12|10.19.88.32|10.19.88.25|10.19.88.13|10.19.88.16|10.19.88.18|10.19.88.29|10.19.88.22|10.19.88.24|10.19.88.27|10.19.88.31|10.19.88.38|10.19.88.44|10.19.88.19|10.19.88.20|10.19.88.33|10.19.88.35|10.19.88.36|10.19.88.39|10.19.88.40|10.19.88.46|10.19.88.51|10.19.88.56|10.19.88.17|10.19.88.26|10.19.88.28|10.19.89.120|10.19.89.126|10.19.89.137|10.19.89.138|10.19.89.147|10.19.89.151|10.19.89.152|10.19.89.154|10.19.89.157|10.19.89.159|10.19.89.161|10.19.89.162|10.19.89.163|10.19.89.165|10.19.89.166|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************|************
    - moduleName: dayinPlatform
      passwd: lUG4yO3tPKF+bWo9jWOS5u93FcHH8FSyq81MlifkB4Hm2PYRaZ46WPwLznmGGgIE
      ipPool: **********

# 机器ip白名单
ip-white-list:
  ipWhiteList:
    - 127.0.0.1
    - **********
    - 2409:8c5b:ffff:6019:0:0:0:8
    # m4c地址
    - *********
    - *********
    - *********
    - *********
    - *********
    - *********
    - ********2
    # m4c灰度地址
    - *************
    - **********
    - ***************
    # isbo灰度地址
    - **********
    # 现网迁移的
    - **************
    - ***********
    - **************
    - **************
    - **************
    - **************
    - *************
    - ************
    - ************
    - ************
    - ************
    - **********
    - **********
    - **************
    - **************
    - **************
    - ************
    - **************
    - *************
    - ***************
    - **************
    - **************
    - **************
    - **************
    - **************
    - **************
    - **************
    - **************
    - **************
    - **************
    - **************
    - **************
    - 192.168.230.14
    - 192.168.230.138
    - 192.168.230.137
    - 192.168.230.133
    - 192.168.230.13
    - 192.168.230.128
    - 192.168.230.127
    - 192.168.230.126
    - 192.168.230.125
    - 192.168.230.124
    - 192.168.230.123
    - 192.168.230.122
    - 192.168.230.120
    - 192.168.230.119
    - 192.168.230.118
    - 192.168.230.117
    - 192.168.230.113
    - 192.168.230.103
    - 192.168.230.102
    - 192.168.230.101
    - 192.168.223.57
    - 192.168.223.56
    - 192.168.223.55
    - ***************
    - ***************
    - 192.168.221.251
    - ***************
    - 192.168.221.247
    - ***************
    - 192.168.221.245
    - 192.168.221.244
    - 192.168.221.243
    - 192.168.221.242
    - 192.168.221.241
    - 192.168.221.240
    - 192.168.221.239
    - 192.168.221.238
    - 192.168.221.237
    - 192.168.221.236
    - 192.168.221.235
    - 192.168.221.234
    - 192.168.221.233
    - ***************
    - ***************
    - 192.168.221.227
    - 192.168.221.226
    - 192.168.221.225
    - ***************
    - 192.168.221.216
    - 192.168.221.213
    - ***************
    - 192.168.221.207
    - 192.168.221.206
    - ***************
    - 192.168.221.204
    - 192.168.221.203
    - 192.168.221.202
    - ***************
    - ***************
    - ***************
    - ***************
    - ***************
    - ***************
    - ***************
    - ***************
    - ***************
    - ***************
    - ***************
    - ***************
    - ***************
    - ***************
    - ***************
    - 192.168.221.140
    - 192.168.221.139
    - 192.168.221.138
    - ***************
    - ***************
    - ***************
    - ***************
    - 192.168.120.102
    - 123.206.60.179
    - 139.199.23.90
    - 121.15.167.225
    - *************
    - 120.76.79.171
    - 120.232.169.161
    - 120.203.18.104
    - 120.197.235.60
    - 120.197.235.54
    - 120.197.235.39
    - 120.197.235.110
    - 120.197.235.109
    - 120.197.235.108
    - 120.197.235.107
    - **************
    - 117.169.36.152
    - 117.136.240.63
    - 117.136.240.54
    - 117.132.186.138
    - 117.132.186.117
    - 117.132.186.116
    - 10.50.5.14
    - *********
    - *********
    - *********
    - *********
    - 10.3.4.93
    - 10.3.4.92
    - 10.3.4.91
    - *********
    - *********
    - 10.3.4.84
    - *********
    - *********
    - ********2
    - **********
    - **********
    - **********
    - **********
    - **********
    - **********
    - **********
    - **********
    - 10.3.4.179
    - 10.3.4.178
    - 10.3.4.177
    - 10.3.4.122
    - 10.3.4.121
    - 10.3.4.120
    - 10.3.4.119
    - 10.3.4.118
    - **********
    - **********
    - **********
    - **********
    - **********
    - **********
    - **********
    - 10.2.78.66
    - 10.2.78.64
    - 10.2.78.36
    - 10.2.78.35
    - 10.2.78.34
    - 10.2.78.33
    - 10.2.78.32
    - 10.2.78.31
    - 10.27.4.4
    - 10.27.4.3
    - 10.27.4.2
    - 10.27.4.1
    - 10.27.3.9
    - 10.27.3.8
    - 10.27.3.7
    - 10.27.3.6
    - 10.27.3.5
    - 10.27.3.4
    - 10.27.3.3
    - 10.27.3.24
    - 10.27.3.22
    - 10.27.3.21
    - 10.27.3.20
    - 10.27.3.2
    - 10.27.3.19
    - 10.27.3.18
    - 10.27.3.17
    - 10.27.3.16
    - 10.27.3.15
    - 10.27.3.14
    - 10.27.3.13
    - 10.27.3.12
    - 10.27.3.11
    - 10.27.3.10
    - 10.27.3.1
    - 10.2.63.63
    - 10.2.63.62
    - 10.2.63.61
    - 10.2.63.60
    - 10.2.63.59
    - 10.2.63.58
    - 10.2.63.53
    - 10.2.63.52
    - 10.2.63.51
    - 10.26.19.190
    - 10.26.19.189
    - 10.26.19.188
    - 10.26.19.187
    - 10.26.19.186
    - 10.26.19.185
    - 10.26.19.184
    - 10.26.19.183
    - 10.26.19.182
    - 10.26.19.181
    - 10.26.19.180
    - 10.26.19.179
    - 10.26.19.178
    - 10.26.19.177
    - 10.26.19.176
    - 10.26.19.175
    - 10.26.19.174
    - 10.26.19.173
    - 10.26.19.172
    - 10.26.19.171
    - 10.250.0.70
    - 10.250.0.69
    - 10.250.0.65
    - 10.24.29.9
    - 10.24.29.8
    - 10.24.29.7
    - 10.24.29.60
    - 10.24.29.6
    - 10.24.29.59
    - 10.24.29.58
    - 10.24.29.57
    - 10.24.29.56
    - 10.24.29.55
    - 10.24.29.54
    - 10.24.29.53
    - 10.24.29.52
    - 10.24.29.51
    - 10.24.29.50
    - 10.24.29.5
    - 10.24.29.49
    - 10.24.29.48
    - 10.24.29.47
    - 10.24.29.46
    - 10.24.29.45
    - 10.24.29.44
    - 10.24.29.43
    - 10.24.29.42
    - 10.24.29.41
    - 10.24.29.40
    - 10.24.29.4
    - 10.24.29.39
    - 10.24.29.38
    - 10.24.29.37
    - 10.24.29.36
    - 10.24.29.35
    - 10.24.29.34
    - 10.24.29.33
    - 10.24.29.32
    - 10.24.29.31
    - 10.24.29.30
    - 10.24.29.3
    - 10.24.29.29
    - 10.24.29.28
    - 10.24.29.27
    - 10.24.29.26
    - 10.24.29.25
    - 10.24.29.24
    - 10.24.29.23
    - 10.24.29.22
    - 10.24.29.21
    - 10.24.29.20
    - 10.24.29.2
    - 10.24.29.194
    - 10.24.29.193
    - 10.24.29.192
    - 10.24.29.191
    - 10.24.29.190
    - 10.24.29.19
    - 10.24.29.189
    - 10.24.29.188
    - 10.24.29.187
    - 10.24.29.186
    - 10.24.29.185
    - 10.24.29.18
    - 10.24.29.17
    - 10.24.29.16
    - 10.24.29.15
    - 10.24.29.14
    - 10.24.29.13
    - 10.24.29.12
    - 10.24.29.11
    - 10.24.29.10
    - 10.24.29.1
    - 10.19.7.9
    - 10.19.7.8
    - 10.19.7.7
    - 10.19.7.6
    - 10.19.7.5
    - 10.19.7.4
    - 10.19.7.3
    - 10.19.7.20
    - 10.19.7.2
    - 10.19.7.19
    - 10.19.7.18
    - 10.19.7.17
    - 10.19.7.16
    - 10.19.7.15
    - 10.19.7.14
    - 10.19.7.13
    - 10.19.7.12
    - 10.19.7.11
    - 10.19.7.10
    - 10.19.7.1
    - 10.19.5.9
    - 10.19.5.8
    - 10.19.5.7
    - 10.19.5.6
    - 10.19.5.5
    - 10.19.5.4
    - 10.19.5.30
    - 10.19.5.3
    - 10.19.5.29
    - 10.19.5.28
    - 10.19.5.27
    - 10.19.5.26
    - 10.19.5.25
    - 10.19.5.24
    - 10.19.5.23
    - 10.19.5.22
    - 10.19.5.21
    - 10.19.5.20
    - 10.19.5.2
    - 10.19.5.19
    - 10.19.5.18
    - 10.19.5.17
    - 10.19.5.16
    - 10.19.5.15
    - 10.19.5.14
    - 10.19.5.13
    - 10.19.5.12
    - 10.19.5.11
    - 10.19.5.10
    - 10.19.5.1
    - 10.19.10.130
    - 10.19.10.129
    - 10.19.10.128
    - 10.19.10.127
    - 10.1.8.22
    - *********
    - 10.1.8.2
    - *********
    - *********
    - *********
    - *********
    - 10.17.49.99
    - 10.17.49.98
    - 10.17.49.97
    - 10.17.49.96
    - 10.17.49.95
    - 10.17.49.94
    - 10.17.49.93
    - 10.17.49.92
    - 10.17.49.91
    - 10.17.49.90
    - 10.17.49.89
    - 10.17.49.88
    - 10.17.49.87
    - 10.17.49.86
    - 10.17.49.85
    - 10.17.49.84
    - 10.17.49.83
    - 10.17.49.82
    - 10.17.49.81
    - 10.17.49.80
    - 10.17.49.79
    - 10.17.49.78
    - 10.17.49.77
    - 10.17.49.76
    - 10.17.49.75
    - 10.17.49.74
    - 10.17.49.73
    - 10.17.49.72
    - 10.17.49.71
    - 10.17.49.70
    - 10.17.49.69
    - 10.17.49.68
    - 10.17.49.67
    - 10.17.49.66
    - 10.17.49.65
    - 10.17.49.64
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - 10.17.49.113
    - 10.17.49.112
    - 10.17.49.111
    - 10.17.49.110
    - 10.17.49.109
    - 10.17.49.108
    - 10.17.49.107
    - 10.17.49.106
    - 10.17.49.105
    - 10.17.49.104
    - 10.17.49.103
    - 10.17.49.102
    - 10.17.49.101
    - 10.17.49.100
    - 10.17.41.83
    - 10.17.41.82
    - 10.17.41.81
    - 10.17.41.80
    - 10.17.41.79
    - 10.17.41.78
    - 10.17.41.77
    - 10.17.41.76
    - 10.17.41.75
    - 10.17.41.74
    - 10.17.41.73
    - 10.17.41.72
    - 10.17.41.71
    - 10.17.41.70
    - 10.17.41.63
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - **********17
    - **********91
    - **********90
    - **********89
    - **********88
    - **********87
    - **********86
    - **********85
    - **********84
    - **********83
    - **********82
    - **********81
    - **********80
    - **********79
    - **********78
    - **********77
    - **********76
    - **********75
    - **********74
    - **********73
    - **********72
    - **********71
    - **********70
    - **********69
    - **********68
    - **********67
    - **********66
    - **********65
    - **********64
    - **********63
    - **********62
    - **********61
    - **********60
    - **********59
    - **********58
    - **********57
    - **********56
    - **********55
    - **********54
    - 10.17.40.99
    - 10.17.40.98
    - 10.17.40.97
    - 10.17.40.96
    - 10.17.40.95
    - 10.17.40.94
    - 10.17.40.93
    - 10.17.40.92
    - 10.17.40.91
    - 10.17.40.90
    - 10.17.40.89
    - 10.17.40.88
    - 10.17.40.87
    - 10.17.40.38
    - 10.17.40.37
    - 10.17.40.36
    - 10.17.40.35
    - 10.17.40.34
    - 10.17.40.33
    - 10.17.40.32
    - 10.17.40.31
    - 10.17.40.30
    - 10.17.40.29
    - 10.17.40.28
    - 10.17.40.27
    - 10.17.40.26
    - 10.17.40.25
    - 10.17.40.24
    - 10.17.40.23
    - 10.17.40.22
    - 10.17.40.21
    - 10.17.40.20
    - 10.17.40.19
    - 10.17.40.18
    - 10.17.40.17
    - 10.17.40.16
    - 10.17.40.15
    - 10.17.40.14
    - 10.17.40.104
    - 10.17.40.103
    - 10.17.40.102
    - 10.17.40.101
    - 10.17.40.100
    - 10.1.72.80
    - 10.153.78.201
    - 10.153.103.35
    - 10.153.103.34
    - 10.153.103.33
    - 10.153.103.22
    - 10.153.103.21
    - 10.153.103.201
    - 10.153.103.20
    - 10.153.103.195
    - 10.153.103.194
    - 10.153.103.193
    - 10.153.103.191
    - 10.153.103.190
    - 10.153.103.19
    - 10.153.103.18
    - 10.153.103.17
    - 10.153.103.16
    - 10.153.103.15
    - 10.153.103.14
    - 10.153.103.13
    - 10.135.65.106
    - 10.135.192.67
    - 10.135.192.61
    - 10.135.119.239
    - **********
    - 10.3.4.142
    - 10.19.160.48
    - 10.19.160.49
    - 10.19.160.50
    - 10.19.160.51
    - 10.19.160.52
    - ************
    - ************
    - ************
    - ************
    - ************
    - ************
    - ************
    - ************
    - ************
    - ************
    - ************
    - ***********
    - ***********
    - ***********
    - ***********0
    - *************
    - ***********
    - ***********
    - ***********
    - *************
    - *************
    - *************
    - *************
    - *************
    - *************
    - *************
    # 上云机器ip
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - ***********
    - **********
    - **********
    - **********
    - ********
    - ********
    - ********
    - ********
server:
  tomcat:
    threads:
      max: 400
      min-spare: 50
    #等待队列长度，当可分配的线程数全部用完之后，后续的请求将进入等待队列等待，等待队列满后则拒绝处理，默认100
    accept-count: 20000
    #最大可被连接数
    max-connections: 10000
    #连接超时时间，该值需要大于nginx的keepalive_timeout，否则nginx会主动断开连接，默认60000
    connection-timeout: 70000

# 报文压缩
server.compressFlag: true
# http2开启
http2.flag: true