@startuml
'https://plantuml.com/activity-diagram-beta

|白名单服务|
start
: 解析传入报文;
switch (入参)
case ( account )
  :wait;
case ( userId )
  :查询新用户表得到account;
  :映射关系写入缓存;
case ( userDomainId )
  :查询用户域得到account;
  :映射关系写入缓存;
endSwitch
if (account在__白名单表__) then (yes)
    if(用户state=已生效？) then (yes)
        #cyan: 返回白名单;
        stop
    else (no)
    endif
(no  )elseif (account在__白名单号段__里) then (no)
    #red: 返回非白名单;
    stop
else (yes)
    if (account在__黑名单列表__?) then (yes)
          #red: 返回非白名单;
          stop
    else ( no )
    endif
endif
:此时account在白名单内;
if (currentTime<=effectTime(白名单生效时间)?) then (yes)
  #red: 返回非白名单;
  stop
(no  )elseif (用户存在于__待静默用户__缓存?) then (yes)
  #red: 返回非白名单;
  stop
else (no)
  #cyan: 返回白名单;
  stop
endif
@enduml
