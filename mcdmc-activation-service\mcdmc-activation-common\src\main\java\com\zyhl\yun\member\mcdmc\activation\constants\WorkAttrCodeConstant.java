package com.zyhl.yun.member.mcdmc.activation.constants;

/**
 * <AUTHOR>
 * @since 2024/07/24 10:08
 */
public class WorkAttrCodeConstant {
    /**
     * 外部权益账号id
     */
    public static final String RIGHTS_OUT_ACCOUNT_ID = "RIGHTS_OUT_ACCOUNT_ID";
    /**
     * 权益订购失败报文里的body，旧vsbo里面的extension3字段
     */
    public static final String RAI_SUB_FAIL_BODY = "RAI_SUB_FAIL_BODY";

    /**
     * 工单取消原因
     */
    public static final String CANCEL_CAUSE = "CANCEL_CAUSE";
    public static final String ERROR_MSG = "ERROR_MSG";
    public static final String RESET_MSG = "RESET_MSG";
    public static final String EARLY_FINISH_MSG = "EARLY_FINISH_MSG";

    private static final String RETRY_ERROR_MSG = "RETRY_ERROR_MSG";

    /**
     * 流程日志备份，用于记录流程日志更新失败异常的json备份信息
     */
    public static final String FLOW_LOG_BACKUP = "FLOW_LOG_BACKUP";

    public static String getRetryErrorMsgKey(Integer failCount) {
        return RETRY_ERROR_MSG + failCount;
    }

}

