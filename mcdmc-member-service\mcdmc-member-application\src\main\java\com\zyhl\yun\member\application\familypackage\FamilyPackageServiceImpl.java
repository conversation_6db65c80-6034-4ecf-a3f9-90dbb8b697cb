package com.zyhl.yun.member.application.familypackage;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
// import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zyhl.yun.member.circle.service.CircleService;
import com.zyhl.yun.member.common.ResultCodeEnum;
import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.constants.ErrorCode;
import com.zyhl.yun.member.common.constants.SubWayConstants;
import com.zyhl.yun.member.common.domain.framework.DomainException;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.result.XmlResult;
import com.zyhl.yun.member.common.util.MsisdnUtil;
import com.zyhl.yun.member.common.util.SnowflakeUtil;
import com.zyhl.yun.member.common.util.XmlUtil;
import com.zyhl.yun.member.config.RedisLockService;
import com.zyhl.yun.member.direct.manage.enums.EventTypeEnum;
import com.zyhl.yun.member.domain.circle.CircleMemberInfoDo;
import com.zyhl.yun.member.domain.circle.FailMemberInfo;
import com.zyhl.yun.member.domain.circle.PullMembersIntoGroupDo;
import com.zyhl.yun.member.domain.circle.req.PullMembersIntoGroupCondition;
import com.zyhl.yun.member.domain.circle.req.QueryCircleListCondition;
import com.zyhl.yun.member.domain.familypackage.condition.QueryFamilyPackageBindCondition;
import com.zyhl.yun.member.domain.familypackage.condition.QueryFamilyPackageGroupRelationCondition;
import com.zyhl.yun.member.domain.familypackage.domain.FamilyPackageBindDo;
import com.zyhl.yun.member.domain.familypackage.domain.FamilyPackageGroupRelationDo;
import com.zyhl.yun.member.domain.familypackage.enums.FamilyPackageStateEnum;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.facade.GoodsInstanceServiceFacade;
import com.zyhl.yun.member.domain.invite.MemberManageSubscribeRelationResp;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.dto.*;
import com.zyhl.yun.member.facade.FamilyPackageBindFacade;
import com.zyhl.yun.member.facade.FamilyPackageGroupRelationFacade;
import com.zyhl.yun.member.facade.UserServiceFacade;
import com.zyhl.yun.member.invite.gateway.VsboOrderGateway;
import com.zyhl.yun.member.order.common.constants.CommonConstant;
import com.zyhl.yun.member.order.domain.OrderDo;
import com.zyhl.yun.member.order.dto.ManageSubscribeRelationReq;
import com.zyhl.yun.member.order.dto.RollBackRightReq;
import com.zyhl.yun.member.product.common.enums.ChargeTypeEnum;
import com.zyhl.yun.member.product.common.enums.PayWayEnum;
import com.zyhl.yun.member.product.common.enums.ValidateTriggerTimmingEnum;
import com.zyhl.yun.member.product.domain.goods.GoodsDo;
import com.zyhl.yun.member.product.domain.goods.dto.QueryGoodsCondition;
import com.zyhl.yun.member.product.domain.goods.facade.GoodsServiceFacade;
import com.zyhl.yun.member.product.domain.smstemplate.domain.SmsTemplateDo;
import com.zyhl.yun.member.product.domain.smstemplate.dto.SmsTemplateIdCondition;
import com.zyhl.yun.member.product.domain.validaterule.SubRuleValidateResultDo;
import com.zyhl.yun.member.product.domain.validaterule.dto.SubRuleValidateCondition;
import com.zyhl.yun.member.support.common.circle.domain.CircleDetailInfoDo;
import com.zyhl.yun.member.support.common.circle.facade.CircleGroupFacade;
import com.zyhl.yun.member.support.common.circle.req.QueryMyGroupListCondition;
import com.zyhl.yun.member.support.common.domain.SmsSendDo;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zyhl.yun.member.common.ResultCodeEnum.*;
import static com.zyhl.yun.member.common.constants.ErrorCode.*;
import static com.zyhl.yun.member.common.constants.RedisKeyConstant.*;
import static com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceExtendServiceId.QUERY_CONDITION_GOODS_INSTANCE;
import static com.zyhl.yun.member.common.domain.serviceid.MessageNoticeServiceId.SEND;
import static com.zyhl.yun.member.common.domain.serviceid.SmsTemplateServiceId.QUERY_SMS_TEMPLATE_BY_ID;
import static com.zyhl.yun.member.consumer.common.constants.NumberConstant.ZERO;
import static com.zyhl.yun.member.domain.familypackage.serviceid.FamilyPackageServiceId.UPDATE_FAMILY_PACKAGE_BIND;
import static com.zyhl.yun.member.facade.FamilyPackageBindFacade.*;
import static com.zyhl.yun.member.facade.FamilyPackageGroupRelationFacade.queryFamilyPackageGroupRelationByCondition;
import static com.zyhl.yun.member.order.common.constants.ErrorCode.PARAMETER_CHECK_ERROR;
import static com.zyhl.yun.member.product.common.enums.GotoneLevelCheckOptionEnum.CHECK_LOW_USER_LEVEL;
import static com.zyhl.yun.member.validate.AccountValidate.isIoTCard;

/**
 * @Description:
 * @Author: zouzefeng
 * @Date: 2025/3/26
 */
@Service
@Slf4j
public class FamilyPackageServiceImpl implements FamilyPackageService {

    @Resource
    private VsboOrderGateway vsboOrderGateway;

    @Resource
    private RedisLockService redisLockService;

    @Resource
    private CircleService circleService;


    /**
     * 预校验家庭套餐绑定关系
     * 主要校验流程：
     * 1. 校验请求参数的合法性
     * 2. 获取并验证主用户信息
     * 3. 获取并验证商品实例
     * 4. 验证商品是否为家庭云套餐
     * 5. 验证套餐组关系
     * 6. 验证绑定数量是否超限
     * 7. 验证主用户未被绑定
     * 8. 处理待绑定用户列表
     *
     * @param req 预校验请求参数
     * @return 预校验结果响应
     */
    @Override
    public Mono<PreBindFamilyPackageCheckResp> preBindFamilyPackageCheck(PreBindFamilyPackageCheckReq req) {
        validateRequestNotNull(req);
        validateRequestFields(req);
        return preBindFamilyPackageCheckDto(req, false)
                .flatMap(dto -> Mono.just(
                        PreBindFamilyPackageCheckResp.builder()
                                .familyPackageBoundResultList(dto.getFamilyPackageBoundResultList())
                                .build())
                )
                .onErrorResume(this::handleError)
                .doOnError(e -> log.error("family package pre check fail", e));
    }

    /**
     * 预校验家庭套餐绑定关系
     *
     * @param req    请求体
     * @param isBind 是否绑定
     * @return Mono<PreBindFamilyPackageCheckDto>
     */
    private Mono<PreBindFamilyPackageCheckDto> preBindFamilyPackageCheckDto(PreBindFamilyPackageCheckReq req, boolean isBind) {
        return Mono.just(req)
                .flatMap(validReq -> getMasterUserMono(validReq)
                        .flatMap(masterUser -> getGoodsInstanceMono(masterUser, validReq.getOrderId())
                                .flatMap(goodsInstance -> getGoodsMono(goodsInstance)
                                        .flatMap(goods -> validatePackageGroupRelationMono(masterUser, isBind, goodsInstance.getOrderId())
                                                .then(validateBindingLimitsMono(masterUser, goodsInstance, goods, req.getBoundUserList()))
                                                .then(validateMasterNotBoundMono(masterUser, goodsInstance))
                                                .then(handleBoundUsersMono(validReq.getBoundUserList(),
                                                        goodsInstance.getGoodsId()))
                                                .map(resp -> {
                                                    resp.setMasterUserDo(masterUser);
                                                    resp.setFamilyPackageGiftGoodsDo(goods);
                                                    resp.setMasterUser(masterUser);
                                                    resp.setMasterGoodsInstance(goodsInstance);
                                                    return resp;
                                                })
                                        )
                                )
                        )
                );
    }


    /**
     * 获取商品实例信息
     * 根据用户ID和订单ID查询商品实例
     */
    private Mono<GoodsInstanceDo> getGoodsInstanceMono(UserDo masterUser, String orderId) {
        return Mono.fromCallable(() -> {
                    QueryGoodsInstanceCondition condition = new QueryGoodsInstanceCondition();
                    condition.setUserId(masterUser.getUserId());
                    condition.setOrderIdList(Collections.singletonList(orderId));
                    condition.setEffectiveEndTimeStart(new Date());
                    condition.setPhoneNumber(masterUser.getMsisdn());
                    GoodsInstanceDo goodsInstanceDo = GoodsInstanceServiceFacade.queryGoodsInstanceByCondition(condition, QUERY_CONDITION_GOODS_INSTANCE);
                    if (goodsInstanceDo != null && goodsInstanceDo.getEffectiveEndTime().getTime() <= System.currentTimeMillis()) {
                        return null;
                    }
                    return goodsInstanceDo;
                })
                .filter(Objects::nonNull)
                .switchIfEmpty(Mono.error(new ServiceException(SUBCONTRACT_NOT_EXISTS, SUBCONTRACT_NOT_EXISTS_MESSAGE)))
                .onErrorMap(e -> !(e instanceof ServiceException),
                        e -> new ServiceException(SUBCONTRACT_NOT_EXISTS, SUBCONTRACT_NOT_EXISTS_MESSAGE));
    }

    /**
     * 获取商品信息
     * 验证商品是否为家庭云套餐
     */
    private Mono<GoodsDo> getGoodsMono(GoodsInstanceDo goodsInstance) {
        return Mono.fromCallable(() -> {
                    QueryGoodsCondition condition = new QueryGoodsCondition();
                    condition.setGoodsIdList(Collections.singletonList(goodsInstance.getGoodsId()));
                    condition.setWithFamilyPackageGiftGoodsId(true);
                    return CollUtil.getFirst(GoodsServiceFacade.getGoodsListByCondition(condition, false));
                })
                .filter(Objects::nonNull)
                .switchIfEmpty(Mono.error(new ServiceException(NO_GIFT_PACKAGE)))
                .onErrorMap(e -> !(e instanceof ServiceException),
                        e -> new ServiceException(NO_GIFT_PACKAGE));
    }

    /**
     * 验证套餐组关系和圈子信息
     * 1. 验证套餐组关系是否存在
     * 2. 验证用户圈子信息是否存在
     */
    private Mono<FamilyPackageGroupRelationDo> validatePackageGroupRelationMono(UserDo masterUser, Boolean isBind, String orderId) {
        if (masterUser == null || StringUtils.isEmpty(masterUser.getUserId())) {
            return Mono.error(new ServiceException(ErrorCode.PARAMETER_CHECK_ERROR, "Master user information is invalid"));
        }
        if (isBind) {
            return Mono.just(new FamilyPackageGroupRelationDo());
        }
        return validateFamilyPackageGroup(masterUser, orderId)
                .flatMap(packageGroup -> validateUserCircleGroup(masterUser, packageGroup)
                        .thenReturn(packageGroup))
                .doOnError(error ->
                        log.error("Failed to validate package group relation for user: {}", masterUser.getUserId(), error))
                ;
    }

    /**
     * 验证家庭套餐组关系
     */
    private Mono<FamilyPackageGroupRelationDo> validateFamilyPackageGroup(UserDo masterUser, String orderId) {
        return Mono.fromCallable(() -> {
                    QueryFamilyPackageGroupRelationCondition condition = new QueryFamilyPackageGroupRelationCondition();
                    condition.setMasterUserId(masterUser.getUserId());
                    condition.setMasterOrderId(orderId);
                    return queryFamilyPackageGroupRelationByCondition(condition);
                })
                .filter(Objects::nonNull)
                .switchIfEmpty(Mono.error(new ServiceException(GROUP_NOT_CREATE)))
                .onErrorMap(
                        e -> !(e instanceof ServiceException),
                        e -> new ServiceException(INTERNAL_SERVER_ERROR)
                );
    }

    /**
     * 验证用户圈子信息
     */
    private Mono<List<CircleDetailInfoDo>> validateUserCircleGroup(UserDo masterUser, FamilyPackageGroupRelationDo packageGroup) {
        return Mono.fromCallable(() -> {
                    QueryMyGroupListCondition condition = new QueryMyGroupListCondition();
                    condition.setUserDomainId(masterUser.getUserDomainId());
                    condition.setGroupId(packageGroup.getGroupId());
                    return CircleGroupFacade.queryMyGroupList(condition);
                })
                .filter(groups -> !CollUtil.isEmpty(groups))
                .switchIfEmpty(Mono.error(new ServiceException(USER_DOMAIN_GROUP_NOT_CREATE)))
                .onErrorResume(this::handleError);
    }

    /**
     * 验证绑定数量限制
     * 1. 获取当前有效的绑定关系
     * 2. 获取当月的绑定关系
     * 3. 检查是否超过套餐允许的最大绑定数量
     */
    private Mono<Void> validateBindingLimitsMono(UserDo masterUser, GoodsInstanceDo goodsInstance, GoodsDo goods, List<BoundUser> boundUserList) {
        return Mono.fromCallable(() -> {
                    // 获取当前有效的绑定关系
                    Long currentBindingsCount = getCurrentBindingsCount(masterUser, goodsInstance);
                    // 获取当月的非绑定关系
                    Long monthlyBindingsCount = getMonthlyBindingsCount(masterUser, goodsInstance);
                    // 计算总绑定数量
                    int totalCount = (int) (currentBindingsCount + monthlyBindingsCount + (CollUtil.isEmpty(boundUserList) ? 1 : boundUserList.size()));
                    // 检查是否超过限制
                    return Optional.ofNullable(goods.getGoodsExt())
                            .map(ext -> ext.getFamilyPackageGiftQuota())
                            .filter(Objects::nonNull)
                            .map(maxQuota -> {
                                if (totalCount > maxQuota) {
                                    throw new ServiceException(FAMILY_PACKAGE_NUM_LIMIT);
                                }
                                return null;
                            })
                            .orElse(null);
                })
                .then()
                .onErrorMap(e -> !(e instanceof ServiceException),
                        e -> new ServiceException(FAMILY_PACKAGE_NUM_LIMIT));
    }

    /**
     * 验证主用户未被绑定
     * 确保主用户没有作为被绑定用户存在于其他绑定关系中
     */
    private Mono<Void> validateMasterNotBoundMono(UserDo masterUser, GoodsInstanceDo goodsInstance) {
        return Mono.fromCallable(() -> {
                    QueryFamilyPackageBindCondition condition = QueryFamilyPackageBindCondition.builder()
                            .boundPhoneNumber(masterUser.getMsisdn())
                            .boundOrderId(goodsInstance.getOrderId())
                            .boundGoodsInstanceId(goodsInstance.getGoodsInstanceId())
                            .familyPackageStateEnumList(Collections.singletonList(FamilyPackageStateEnum.BOUND))
                            .boundUserId(masterUser.getUserId())
                            .build();

                    FamilyPackageBindDo binding = FamilyPackageBindFacade.queryFamilyPackageBind(condition);
                    if (Objects.nonNull(binding)) {
                        throw new ServiceException(USER_GIFT_PACKAGE);
                    }
                    return null;
                })
                .then()
                .onErrorMap(e -> !(e instanceof ServiceException),
                        e -> new ServiceException(USER_GIFT_PACKAGE));
    }

    /**
     * 获取主用户信息
     * 通过手机号或域ID查询用户信息
     */
    private Mono<UserDo> getMasterUserMono(PreBindFamilyPackageCheckReq req) {
        return Mono.fromCallable(() ->
                        {
                            if (ErrorCode.ILLEGAL_TELEPHONE.equals(MsisdnUtil.getCellPhoneNo(req.getMasterUserPhoneNumber()))) {
                                throw new ServiceException(PARAMETER_CHECK_ERROR, "master msisdn is error");
                            }
                            return UserServiceFacade.queryUser(req.getMasterUserPhoneNumber(), req.getMasterUserDomainId());
                        }
                )
                .filter(Objects::nonNull)
                .switchIfEmpty(Mono.error(new ServiceException(USER_NOT_FOUND)))
                .onErrorMap(e -> !(e instanceof ServiceException),
                        e -> new ServiceException(USER_NOT_FOUND));
    }

    /**
     * 校验请求对象不为空
     */
    private void validateRequestNotNull(PreBindFamilyPackageCheckReq req) {
        if (req == null) {
            throw new ServiceException(ErrorCode.PARAMETER_CHECK_ERROR);
        }
    }


    /**
     * 处理待绑定用户列表
     * 检查每个待绑定用户的状态
     */
    private Mono<PreBindFamilyPackageCheckDto> handleBoundUsersMono(List<BoundUser> boundUsers, String goodsId) {
        PreBindFamilyPackageCheckDto response = new PreBindFamilyPackageCheckDto();
        if (CollUtil.isNotEmpty(boundUsers)) {
            List<FamilyPackageBoundResult> results = boundUsers.stream()
                    .map(boundUser -> checkUserBindingSafely(boundUser, goodsId))
                    .collect(Collectors.toList());
            response.setFamilyPackageBoundResultList(results);
        }
        return Mono.just(response);
    }

    private FamilyPackageBoundResult checkUserBindingSafely(BoundUser boundUser, String goodsId) {
        try {
            return checkUserBinding(boundUser, goodsId);
        } catch (Exception e) {
            log.error("failed to check user binding status: {}", boundUser.getUserPhoneNumber(), e);
            return FamilyPackageBoundResult.builder()
                    .resultCode(INTERNAL_SERVER_ERROR.getResultCode())
                    .resultDesc(e.getMessage())
                    .userPhoneNumber(boundUser.getUserPhoneNumber())
                    .userUserDomainId(boundUser.getUserDomainId())
                    .build();
        }
    }

    /**
     * 检查单个用户的绑定状态
     * 1. 校验用户信息
     * 2. 校验是否为物联网卡
     * 3. 校验预订单规则
     * 4. 校验绑定状态
     *
     * @param boundUser 待绑定用户信息
     * @param goodsId   商品id
     * @return 绑定检查结果
     */
    private FamilyPackageBoundResult checkUserBinding(BoundUser boundUser, String goodsId) {
        // 构建返回结果构造器
        FamilyPackageBoundResult.FamilyPackageBoundResultBuilder resultBuilder = FamilyPackageBoundResult.builder()
                .userPhoneNumber(boundUser.getUserPhoneNumber())
                .userUserDomainId(boundUser.getUserDomainId());
        try {
            if (StringUtils.isBlank(boundUser.getUserPhoneNumber()) && StringUtils.isBlank(boundUser.getUserDomainId())) {
                return resultBuilder.build();
            }

            UserDo user = checkAndGetUser(boundUser);
            if (StringUtils.isEmpty(boundUser.getUserDomainId())) {
                resultBuilder.userUserDomainId(user.getUserDomainId());
            }
            if (checkIsIoTCard(user, resultBuilder)) {
                return resultBuilder.build();
            }

            if (checkPreOrderRules(user, resultBuilder, goodsId)) {
                return resultBuilder.build();
            }
            checkBindingStatus(user, resultBuilder);
            return resultBuilder.build();
        } catch (ServiceException se) {
            log.error("[APPLICATION] check if there is a business exception in the user binding status, boundUser: {}, error: {}", boundUser, se.getMessage());
            return resultBuilder
                    .resultCode(se.getErrorCode())
                    .resultDesc(se.getErrorMessage())
                    .build();
        } catch (Exception e) {
            log.error("[APPLICATION] system exception occurred while checking user binding status, boundUser: {}", boundUser, e);
            return resultBuilder
                    .resultCode(INTERNAL_SERVER_ERROR.getResultCode())
                    .resultDesc(e.getMessage())
                    .build();
        }
    }

    /**
     * 校验并获取用户信息
     */
    private UserDo checkAndGetUser(BoundUser boundUser) {
        UserDo user = UserServiceFacade.queryUser(
                boundUser.getUserPhoneNumber(),
                boundUser.getUserDomainId()
        );
        if (user == null) {
            throw new ServiceException(USER_NOT_FOUND);
        }
        return user;
    }

    /**
     * 校验是否为物联网卡
     *
     * @return true 如果校验不通过
     */
    private boolean checkIsIoTCard(UserDo user, FamilyPackageBoundResult.FamilyPackageBoundResultBuilder resultBuilder) {
        if (isIoTCard(user.getMsisdn())) {
            resultBuilder
                    .resultCode(IOT_NUMBER_NOT_SUPPORT.getResultCode())
                    .resultDesc(IOT_NUMBER_NOT_SUPPORT.getResultDesc());
            return true;
        }
        return false;
    }

    /**
     * 校验预订单规则
     *
     * @return true 如果校验不通过
     */
    private boolean checkPreOrderRules(UserDo user, FamilyPackageBoundResult.FamilyPackageBoundResultBuilder resultBuilder, String goodsId) {
        OrderDo orderDo = new OrderDo();
        orderDo.setOrderUserId(user.getUserId());
        orderDo.setGoodsId(goodsId);
        SubRuleValidateResultDo validateResult = doPreOrderCheck(
                new DomainServiceContext(null),
                orderDo,
                user
        );

        if (validateResult != null && !validateResult.isValidatePass()) {
            log.error("[APPLICATION] pre order rule verification failed, validateResult: {}", validateResult);
            resultBuilder.resultCode(REPEAT_GIFT.getResultCode()).resultDesc(REPEAT_GIFT.getResultDesc());
            return true;
        }
        return false;
    }

    /**
     * 校验绑定状态
     */
    private void checkBindingStatus(UserDo user, FamilyPackageBoundResult.FamilyPackageBoundResultBuilder resultBuilder) {
        // 构建查询条件
        QueryFamilyPackageBindCondition condition = QueryFamilyPackageBindCondition.builder()
                .boundUserId(user.getUserId())
                .familyPackageStateEnumList(Arrays.asList(
                        FamilyPackageStateEnum.BOUND,
                        FamilyPackageStateEnum.BINDING,
                        FamilyPackageStateEnum.UNBINDING,
                        FamilyPackageStateEnum.UNBIND_FAILED
                ))
                .build();

        // 查询绑定关系
        List<FamilyPackageBindDo> bindings = FamilyPackageBindFacade.queryFamilyPackageBinds(condition);
        boolean canBind = CollUtil.isEmpty(bindings);

        // 设置结果
        if (canBind) {
            resultBuilder
                    .resultCode(CAN_BIND.getResultCode())
                    .resultDesc(CAN_BIND.getResultDesc());
        } else {
            resultBuilder
                    .resultCode(REPEAT_GIFT.getResultCode())
                    .resultDesc(REPEAT_GIFT.getResultDesc());
        }
    }

    /**
     * 订单校验
     *
     * @param serviceContext 上下文
     * @param orderDo        订单信息
     * @return 校验结果
     */
    public SubRuleValidateResultDo doPreOrderCheck(DomainServiceContext serviceContext, OrderDo orderDo, UserDo userDo) {

        if (log.isDebugEnabled()) {
            log.debug("[DOMAIN] check pre order start. GoodsId: {}, UserId: {}, OrderNo: {}", orderDo.getGoodsId(),
                    orderDo.getOrderUserId(), orderDo.getOrderNo());
        }

        DomainServiceContext validateServiceContext = new DomainServiceContext(serviceContext.getServiceId());
        SubRuleValidateCondition subRuleValidateCondition = new SubRuleValidateCondition();
        subRuleValidateCondition.setGoodsId(orderDo.getGoodsId());
        subRuleValidateCondition.setPhoneNumber(userDo.getMsisdn());
        subRuleValidateCondition.setUserDomainId(userDo.getUserDomainId());
        subRuleValidateCondition.setUserId(userDo.getUserId());
        subRuleValidateCondition.setSubTime(new Date());

        subRuleValidateCondition.setTriggerTimming(ValidateTriggerTimmingEnum.SUB);
        if (orderDo.getExtInfo() != null) {
            if (orderDo.getExtInfo().containsKey(CommonConstant.OrderExtParams.ORDER_EXT_FP)) {
                subRuleValidateCondition.setFp(
                        (String) orderDo.getExtInfo().get(CommonConstant.OrderExtParams.ORDER_EXT_FP)
                );
            }

            if (orderDo.getExtInfo().containsKey(CommonConstant.OrderExtParams.ACTIVITY_ID)) {
                subRuleValidateCondition.setActivityId(
                        (String) orderDo.getExtInfo().get(CommonConstant.OrderExtParams.ACTIVITY_ID)
                );
            }

            if (orderDo.getExtInfo().containsKey(CommonConstant.OrderExtParams.ACTIVITY_SOURCE)) {
                subRuleValidateCondition.setActivitySource(
                        (String) orderDo.getExtInfo().get(CommonConstant.OrderExtParams.ACTIVITY_SOURCE)
                );
            }

            if (orderDo.getExtInfo().containsKey(CommonConstant.OrderExtParams.PAY_WAY)) {
                subRuleValidateCondition.setPayWay(
                        PayWayEnum.fromCode((String) orderDo.getExtInfo().get(CommonConstant.OrderExtParams.PAY_WAY))
                );
            }

        }

        subRuleValidateCondition.setGotoneLevelCheckOption(CHECK_LOW_USER_LEVEL);
        SubRuleValidateResultDo subRuleValidateResultDo = validateServiceContext.readFirst(subRuleValidateCondition, SubRuleValidateResultDo.class);

        //校验不通过
//        if (subRuleValidateResultDo != null && !subRuleValidateResultDo.isValidatePass()) {
//            log.error("[DOMAIN] check pre order failed. subRuleValidateResultDo: {}", subRuleValidateResultDo);
//            throw new ServiceException(subRuleValidateResultDo.getValidateErrorCode(), subRuleValidateResultDo.getValidateErrorMsg());
//        }

        if (log.isDebugEnabled()) {
            log.debug("[DOMAIN] check pre order pass. GoodsId: {}, UserId: {}, OrderNo: {}", orderDo.getGoodsId(),
                    orderDo.getOrderUserId(), orderDo.getOrderNo());
        }

        return subRuleValidateResultDo;
    }

    /**
     * 统一错误处理
     * 区分业务异常和系统异常
     */
    private <T> Mono<T> handleError(Throwable error) {
        if (error instanceof ServiceException) {
            return Mono.error(error);
        }
        if (error instanceof DomainException) {
            return Mono.error(error);
        }
        log.error("operation failed: {}", error.getMessage(), error);
        return Mono.error(new ServiceException(INTERNAL_SERVER_ERROR));
    }


    private Long getCurrentBindingsCount(UserDo masterUser, GoodsInstanceDo goodsInstance) {
        QueryFamilyPackageBindCondition condition = QueryFamilyPackageBindCondition.builder()
                .masterOrderId(goodsInstance.getOrderId())
                .masterGoodsInstanceId(goodsInstance.getGoodsInstanceId())
                .familyPackageStateEnumList(Arrays.asList(FamilyPackageStateEnum.BOUND, FamilyPackageStateEnum.BINDING,
                        FamilyPackageStateEnum.CANCEL_ACCOUNT, FamilyPackageStateEnum.UNBINDING, FamilyPackageStateEnum.UNBIND_FAILED))
                .masterUserId(masterUser.getUserId())
                .build();

        return FamilyPackageBindFacade.queryFamilyPackageBindCount(condition);
    }

    /**
     * 获取当月绑定关系
     */
    private Long getMonthlyBindingsCount(UserDo masterUser, GoodsInstanceDo goodsInstance) {
        // 设置当月开始和结束时间
        Date now = new Date();
        Date startDate = DateUtil.beginOfMonth(now);

        Date endDate = DateUtil.endOfMonth(now);

        QueryFamilyPackageBindCondition condition = QueryFamilyPackageBindCondition.builder()
                .masterUserId(masterUser.getUserId())
                .masterOrderId(goodsInstance.getOrderId())
                .masterGoodsInstanceId(goodsInstance.getGoodsInstanceId())
                .familyPackageStateEnumList(Arrays.asList(FamilyPackageStateEnum.BIND_FAILED, FamilyPackageStateEnum.UNBOUND))
                .createTimeStartDate(startDate)
                .createTimeEndDate(endDate)
                .build();

        return FamilyPackageBindFacade.queryFamilyPackageBindCount(condition);
    }

    /**
     * 校验请求参数的合法性
     * 1. 主用户手机号或域ID不能为空
     * 2. 订单ID不能为空
     * 3. 待绑定用户列表不能为空
     * 4. 主用户不能在待绑定列表中
     */
    private void validateRequestFields(PreBindFamilyPackageCheckReq req) {
        if ((StringUtils.isEmpty(req.getMasterUserPhoneNumber())
                && StringUtils.isEmpty(req.getMasterUserDomainId()))) {
            throw new ServiceException(PARAMETER_CHECK_ERROR);
        }
        if (CollUtil.isNotEmpty(req.getBoundUserList()) && req.getMasterUserPhoneNumber() != null &&
                req.getBoundUserList().stream()
                        .anyMatch(user -> req.getMasterUserPhoneNumber().equals(user.getUserPhoneNumber()))) {
            throw new ServiceException(MASTER_CAN_NOT_BIND_SELF);
        }
        if (CollUtil.isNotEmpty(req.getBoundUserList())) {
            req.setBoundUserList(req.getBoundUserList().stream().distinct().collect(Collectors.toList()));
        }
    }

    /**
     * 绑定家庭套餐
     * 主要流程：
     * 1. 获取锁
     * 2. 执行前置校验
     * 3. 创建绑定关系
     * 4. 释放锁
     *
     * @param bindFamilyPackageReq 请求体
     * @return Mono<BindFamilyPackageResp>
     */
    @Override
    public Mono<BindFamilyPackageResp> bindFamilyPackage(BindFamilyPackageReq bindFamilyPackageReq) {
        return acquireLocks(bindFamilyPackageReq)
                .flatMap(lockContext -> {
                    PreBindFamilyPackageCheckReq preCheckReq = PreBindFamilyPackageCheckReq.builder()
                            .orderId(bindFamilyPackageReq.getOrderId())
                            .masterUserPhoneNumber(bindFamilyPackageReq.getMasterUserPhoneNumber())
                            .masterUserDomainId(bindFamilyPackageReq.getMasterUserDomainId())
                            .boundUserList(bindFamilyPackageReq.getBoundUserList())
                            .build();
                    return preBindFamilyPackageCheckDto(preCheckReq, true)
                            // 如果异常报错是FAMILY_PACKAGE_NUM_LIMIT，需要将其状态码更换为另外一个，使用onErrorMap()
                            .onErrorMap(e -> {
                                if (e instanceof ServiceException) {
                                    ServiceException serviceException = (ServiceException) e;
                                    if (FAMILY_PACKAGE_NUM_LIMIT.getResultCode().equals(serviceException.getErrorCode())) {
                                        throw new ServiceException(FAMILY_PACKAGE_BIND_LIMIT);
                                    }
                                }
                                return e;
                            })
                            .flatMap(checkResp -> {
                                if (checkResp == null || checkResp.getFamilyPackageBoundResultList().isEmpty()) {
                                    return Mono.just(new BindFamilyPackageResp());
                                }
                                List<FamilyPackageBoundResult> bindableCanBindResults = checkResp.getFamilyPackageBoundResultList().stream()
                                        .filter(result -> CAN_BIND.getResultCode().equals(result.getResultCode()))
                                        .collect(Collectors.toList());
                                List<FamilyPackageBoundResult> bindableNotBeenBindResults = checkResp.getFamilyPackageBoundResultList().stream()
                                        .filter(result -> !CAN_BIND.getResultCode().equals(result.getResultCode()))
                                        .collect(Collectors.toList());
                                return createFamilyPackageBindings(
                                        CreateFamilyPackageBindingsDto.builder()
                                                .masterUserDo(checkResp.getMasterUserDo())
                                                .familyPackageGiftGoodsDo(checkResp.getFamilyPackageGiftGoodsDo())
                                                .groupId(bindFamilyPackageReq.getGroupId())
                                                .bindableResults(bindableCanBindResults)
                                                .masterGoodsInstance(checkResp.getMasterGoodsInstance())
                                                .build()
                                ).map(bindings -> {
                                    BindFamilyPackageResp resp = new BindFamilyPackageResp();
                                    List<FamilyPackageBoundResult> allResults = new ArrayList<>();
                                    allResults.addAll(bindableNotBeenBindResults);
                                    allResults.addAll(bindableCanBindResults);
                                    resp.setFamilyPackageBoundResultList(allResults);
                                    return resp;
                                });
                            })
                            .doFinally(signalType -> {
                                try {
                                    releaseAllLocks(
                                            lockContext.getMasterLockKey(),
                                            lockContext.getMasterLockValue(),
                                            lockContext.getBoundLockKeys(),
                                            lockContext.getBoundLockValues()
                                    );
                                } catch (Exception e) {
                                    log.error("Failed to release locks", e);
                                }
                            });
                })
                .onErrorResume(this::handleError)
                ;
    }

    /**
     * 获取所有需要的锁
     * 包括主用户锁和被绑定用户锁
     *
     * @param bindFamilyPackageReq 绑定请求
     * @return Mono<LockContext> 包含所有锁信息的上下文
     */
    private Mono<LockContext> acquireLocks(BindFamilyPackageReq bindFamilyPackageReq) {
        return Mono.defer(() -> {
            String masterLockKey = null;
            String masterLockValue = UUID.randomUUID().toString();

            if (StringUtils.isNotEmpty(bindFamilyPackageReq.getMasterUserPhoneNumber())) {
                masterLockKey = String.format(FAMILY_PACKAGE_MSISDN_KEY, bindFamilyPackageReq.getMasterUserPhoneNumber());
            } else if (StringUtils.isNotEmpty(bindFamilyPackageReq.getMasterUserDomainId())) {
                masterLockKey = String.format(FAMILY_PACKAGE_USER_DOMAIN_ID_KEY, bindFamilyPackageReq.getMasterUserDomainId());
            }
            if (Objects.isNull(masterLockKey) || !redisLockService.acquireLock(masterLockKey, masterLockValue, 30)) {
                return Mono.error(new ServiceException(ErrorCode.MUTUALLY_LIMIT_CODE, ErrorCode.MUTUALLY_LIMIT_DESC));
            }
            List<String> boundLockKeys = new ArrayList<>();
            List<String> boundLockValues = new ArrayList<>();
            List<BoundUser> boundUserList = bindFamilyPackageReq.getBoundUserList();
            for (BoundUser boundUser : boundUserList) {
                String boundLockKey = null;
                String boundLockValue = UUID.randomUUID().toString();
                if (StringUtils.isNotEmpty(boundUser.getUserPhoneNumber())) {
                    boundLockKey = String.format(FAMILY_PACKAGE_BOUND_MSISDN_KEY, boundUser.getUserPhoneNumber());
                } else if (StringUtils.isNotEmpty(boundUser.getUserDomainId())) {
                    boundLockKey = String.format(FAMILY_PACKAGE_BOUND_USER_DOMAIN_ID_KEY, boundUser.getUserDomainId());
                }
                if (Objects.nonNull(boundLockKey)) {
                    boolean acquiredLock = redisLockService.acquireLock(boundLockKey, boundLockValue, 30);
                    if (!acquiredLock) {
                        releaseAllLocks(masterLockKey, masterLockValue, boundLockKeys, boundLockValues);
                        return Mono.error(new ServiceException(ErrorCode.MUTUALLY_LIMIT_CODE, ErrorCode.MUTUALLY_LIMIT_DESC));
                    }
                    boundLockKeys.add(boundLockKey);
                    boundLockValues.add(boundLockValue);
                }
            }
            return Mono.just(LockContext.builder()
                    .masterLockKey(masterLockKey)
                    .masterLockValue(masterLockValue)
                    .boundLockKeys(boundLockKeys)
                    .boundLockValues(boundLockValues)
                    .build());
        });
    }

    private void releaseAllLocks(String masterLockKey, String masterLockValue, List<String> boundLockKeys, List<String> boundLockValues) {
        if (masterLockKey != null) {
            try {
                redisLockService.releaseLock(masterLockKey, masterLockValue);
            } catch (Exception e) {
                log.error("Failed to release master user lock, lockKey: {}", masterLockKey, e);
            }
        }
        for (int i = 0; i < boundLockKeys.size(); i++) {
            try {
                redisLockService.releaseLock(boundLockKeys.get(i), boundLockValues.get(i));
            } catch (Exception e) {
                log.error("Failed to release bound user lock, lockKey: {}", boundLockKeys.get(i), e);
            }
        }
    }

    /**
     * 创建家庭套餐绑定关系
     */
    private Mono<List<FamilyPackageBindDo>> createFamilyPackageBindings(
            CreateFamilyPackageBindingsDto req) {

        return Mono.fromCallable(() -> {
            List<FamilyPackageBindDo> bindings = new ArrayList<>();
            for (FamilyPackageBoundResult result : req.getBindableResults()) {
                if (!result.getResultCode().equals(CAN_BIND.getResultCode())) {
                    continue;
                }
                try {
                    UserDo boundUser = UserServiceFacade.queryUser(
                            result.getUserPhoneNumber(),
                            result.getUserUserDomainId()
                    );
                    if (boundUser == null) {
                        log.warn("bound user is null, masterUserId: {}, masterOrderId: {}",
                                req.getMasterUserDo().getUserId(), req.getMasterGoodsInstance().getOrderId());
                        continue;
                    }
                    FamilyPackageBindDo binding = FamilyPackageBindDo.builder()
                            .bindId(SnowflakeUtil.getNextString())
                            .masterUserId(req.getMasterUserDo().getUserId())
                            .masterOrderId(req.getMasterGoodsInstance().getOrderId())
                            .masterGoodsInstanceId(req.getMasterGoodsInstance().getGoodsInstanceId())
                            .boundUserId(boundUser.getUserId())
                            .boundUserPhoneNumber(boundUser.getMsisdn())
                            .familyPackageStateEnum(FamilyPackageStateEnum.BINDING)
                            .createTime(new Date())
                            .masterUserDomainId(req.getMasterUserDo().getUserDomainId())
                            .masterUserPhoneNumber(req.getMasterUserDo().getMsisdn())
                            .boundUserDomainId(boundUser.getUserDomainId())
                            .build();
                    FamilyPackageBindFacade.insertFamilyPackageBind(binding);
                    try {
                        handleSubscribeRelation(SubscribeRelationDto.builder()
                                .boundUser(boundUser)
                                .goodsInstanceId(binding.getBoundGoodsInstanceId())
                                .eventType(EventTypeEnum.ESTABLISH)
                                .binding(binding)
                                .successState(FamilyPackageStateEnum.BOUND)
                                .failState(FamilyPackageStateEnum.BIND_FAILED)
                                .familyPackageGiftGoodsDo(req.getFamilyPackageGiftGoodsDo())
                                .buyType(String.valueOf(PayWayEnum.MARKETING_PAY.getCode()))
                                .accessSource("1")
                                .startTime(req.getMasterGoodsInstance().getEffectiveStartTime())
                                .endTime(req.getMasterGoodsInstance().getEffectiveEndTime())
                                .result(result)
                                .masterUserDo(req.getMasterUserDo())
                                .groupId(req.getGroupId())
                                .build());
                    } finally {
                        bindings.add(binding);
                    }
                } catch (Exception e) {
                    log.error("Error creating binding for user: {}, error: {}", result.getUserPhoneNumber(), e.getMessage(), e);
                }
            }
            if (CollUtil.isNotEmpty(req.getBindableResults())) {
                groupRelation(req, bindings, req.getBindableResults());
            }
            return bindings;
        });
    }

    /**
     * 处理订阅关系（绑定/解绑）
     */
    private void handleSubscribeRelation(SubscribeRelationDto request) {
        try {
            ManageSubscribeRelationReq req = new ManageSubscribeRelationReq();
            req.setAccount(request.getBoundUser().getMsisdn());
            req.setSubscriptionID(request.getGoodsInstanceId());
            req.setEventType(Integer.parseInt(request.getEventType().getEventType()));

            // 如果是绑定场景，设置额外参数
            if (request.getEventType() == EventTypeEnum.ESTABLISH) {
                req.setWho(SubWayConstants.INTERNAL_RECEIVE_CODE);
                req.setProductID(request.getFamilyPackageGiftGoodsDo().getGoodsId());
                req.setBuyType(request.getBuyType());
                req.setAccessSource(request.getAccessSource());
                req.setStartTime(DateUtil.format(request.getStartTime(), DatePattern.NORM_DATETIME_PATTERN));
                req.setEndTime(DateUtil.format(request.getEndTime(), DatePattern.NORM_DATETIME_PATTERN));
            }

            MemberManageSubscribeRelationResp response = vsboOrderGateway.manageSubscribeRelationV2(req);
            if (Objects.nonNull(response)) {
                Integer resultCode;
                String resultDesc;
                resultCode = response.getResultCode();
                resultDesc = response.getResultDesc();
                if (response.getManageSubscribeRelationResp() != null
                        && resultCode != null && ResultCodeEnum.SUCCESS.getResultCode().equals(String.valueOf(resultCode))) {
                    MemberManageSubscribeRelationResp.InnerManageSubscribeRelationResp manageSubscribeRelationResp = response.getManageSubscribeRelationResp();
                    // 如果是绑定场景，需要设置订单ID和商品实例ID
                    if (StringUtils.isNotEmpty(manageSubscribeRelationResp.getSubscriptionID())
                            && StringUtils.isNotEmpty(manageSubscribeRelationResp.getOrderID())) {
                        request.getBinding().setBoundOrderId(manageSubscribeRelationResp.getOrderID());
                        request.getBinding().setBoundGoodsInstanceId(manageSubscribeRelationResp.getSubscriptionID());
                        request.getBinding().setFamilyPackageStateEnum(request.getSuccessState());
                        updateBindingState(request.getBinding());
                        request.getResult().setResultCode(ErrorCode.SUCCESS);
                        request.getResult().setResultDesc(SUCCESS_MESSAGE);
                        sendFamilyPackageSms(request.getMasterUserDo().getMsisdn(), request.getBoundUser(), request.getFamilyPackageGiftGoodsDo());
                    } else {
                        throw new ServiceException("sub create error");
                    }
                } else {
                    request.getBinding().setFamilyPackageStateEnum(FamilyPackageStateEnum.INVALID_BINDING);
                    updateBindingState(request.getBinding());
                    request.getResult().setResultCode(String.valueOf(resultCode));
                    request.getResult().setResultDesc(resultDesc);
                }
            }
        } catch (Exception e) {
            log.error("subscription relationship processing failed", e);
            request.getResult().setResultCode(FAIL);
            request.getResult().setResultDesc(FAILED_MESSAGE + "sub family package failed");
            request.getBinding().setFamilyPackageStateEnum(request.getFailState());
            updateBindingState(request.getBinding());
        }

    }


    /**
     * 发送短信
     *
     * @param masterMsisdn 主账号
     * @param bonedUser    被绑账号
     * @param giftGoodsDo  赠送商品
     */
    private static void sendFamilyPackageSms(String masterMsisdn, UserDo bonedUser, GoodsDo giftGoodsDo) {
        try {

            if (giftGoodsDo != null && giftGoodsDo.getSmsPolicy() != null) {
                String templateId = giftGoodsDo.getSmsPolicy().getFamilyPackageBindTemplateId();
                if (StringUtils.isNotEmpty(templateId)) {
                    HashMap<String, String> map = new HashMap<>();
                    map.put("account", StrUtil.hide(masterMsisdn, 3, 7));
                    map.put("goodsName", giftGoodsDo.getGoodsName());
                    List<String> variables = getVariables(templateId, map);
                    DomainServiceContext serviceContext = new DomainServiceContext(SEND);
                    SmsSendDo smsSendDo = serviceContext.newInstance(SmsSendDo.class);
                    smsSendDo.setReqBizId(IdUtil.getSnowflake().nextIdStr());
                    smsSendDo.setReceiver(Collections.singletonList(bonedUser.getMsisdn()));
                    smsSendDo.setMessageTemplateId(templateId);
                    smsSendDo.setVariableList(variables);
                    smsSendDo.setNationCode(bonedUser.getNationCode());
                    if (log.isDebugEnabled()) {
                        log.debug("[DOMAIN] sendFamilyPackageSms, smsSendDo: {}", smsSendDo);
                    }
                    try {
                        serviceContext.writeAndFlush();
                    } catch (Exception e) {
                        log.error("[DOMAIN] sendFamilyPackageSms error", e);
                    }
                } else {
                    log.warn("[DOMAIN] sendFamilyPackageSms. templateId  is null");
                }
            } else {
                log.warn("[DOMAIN] sendFamilyPackageSms. goods or smsPolicy is null");
            }
        } catch (Exception e) {
            log.error("sendFamilyPackageSms error", e);
        }
    }


    private static List<String> getVariables(String templateId, Map<String, String> argsMap) {
        // 根据短信模板查询对应的占位符
        SmsTemplateIdCondition smsTemplateIdCondition = new SmsTemplateIdCondition();
        smsTemplateIdCondition.setSmsTemplateId(templateId);
        DomainServiceContext smsContext = new DomainServiceContext(QUERY_SMS_TEMPLATE_BY_ID);
        SmsTemplateDo smsTemplateIdDo = smsContext.readFirst(smsTemplateIdCondition, SmsTemplateDo.class);
        List<String> variables = new ArrayList<>();
        if (smsTemplateIdDo != null) {
            String keyOrder = smsTemplateIdDo.getKeyOrder();
            if (StringUtils.isNotEmpty(keyOrder)) {
                String[] split = keyOrder.split("\\,");
                for (String s : split) {
                    String variable = argsMap.get(s);
                    if (StringUtils.isNotEmpty(variable)) {
                        variables.add(variable);
                    }
                }
            }
        }
        return variables;
    }

    /**
     * 更新绑定状态
     *
     * @param binding 绑定关系对象
     */
    private void updateBindingState(FamilyPackageBindDo binding) {
        DomainServiceContext context = new DomainServiceContext(UPDATE_FAMILY_PACKAGE_BIND);
        context.putInstance(binding);
        context.writeAndFlush();
    }

    /**
     * 绑定关系处理
     *
     * @param req             请求
     * @param bindings        绑定关系
     * @param bindableResults 绑定结果
     */
    private void groupRelation(CreateFamilyPackageBindingsDto req, List<FamilyPackageBindDo> bindings, List<FamilyPackageBoundResult> bindableResults) {
        if (req == null || req.getMasterUserDo() == null || req.getMasterGoodsInstance() == null) {
            log.error("Invalid request parameters for groupRelation");
            throw new ServiceException(PARAMETER_CHECK_ERROR, "Invalid request parameters");
        }
        try {
            QueryFamilyPackageGroupRelationCondition condition = QueryFamilyPackageGroupRelationCondition.builder()
                    .masterUserId(req.getMasterUserDo().getUserId())
                    .masterOrderId(req.getMasterGoodsInstance().getOrderId())
                    .build();
            FamilyPackageGroupRelationDo familyPackageGroupRelationDo;
            familyPackageGroupRelationDo = queryFamilyPackageGroupRelationByCondition(condition);
            if (StringUtils.isNotEmpty(req.getGroupId())) {
                familyPackageGroupRelationDo = handleNewGroupId(req, bindings, familyPackageGroupRelationDo);
            }
            if (familyPackageGroupRelationDo != null) {
                if (log.isDebugEnabled()) {
                    log.debug("start pullMembersIntoGroup for groupId: {}", familyPackageGroupRelationDo.getGroupId());
                }
                startPullMembersIntoGroup(req, bindings, bindableResults, familyPackageGroupRelationDo.getGroupId());
                return;
            }
            log.error("Group not created or selected: masterUserId={}, orderId={}",
                    req.getMasterUserDo().getUserId(), req.getMasterGoodsInstance().getOrderId());
            throw new ServiceException(GROUP_NOT_CREATE_OR_SELECT);
        } catch (ServiceException se) {
            log.error("Service exception in groupRelation: code={}, message={}", se.getErrorCode(), se.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in groupRelation: masterUserId={}, error={}",
                    req.getMasterUserDo().getUserId(), e.getMessage(), e);
        }
    }

    /**
     * 处理新的groupId
     */
    private FamilyPackageGroupRelationDo handleNewGroupId(CreateFamilyPackageBindingsDto req, List<FamilyPackageBindDo> bindings,
                                                          FamilyPackageGroupRelationDo existingRelation) {
        long boundCount = bindings.stream()
                .filter(bind -> FamilyPackageStateEnum.BOUND.equals(bind.getFamilyPackageStateEnum()))
                .count();
        if (boundCount <= ZERO) {
            return null;
        }
        try {
            if (existingRelation == null) {
                FamilyPackageGroupRelationDo newRelation = FamilyPackageGroupRelationDo.builder()
                        .relationId(SnowflakeUtil.getNextString())
                        .masterUserId(req.getMasterUserDo().getUserId())
                        .masterOrderId(req.getMasterGoodsInstance().getOrderId())
                        .masterGoodsInstanceId(req.getMasterGoodsInstance().getGoodsInstanceId())
                        .groupId(req.getGroupId())
                        .createTime(new Date())
                        .build();
                FamilyPackageGroupRelationFacade.insertFamilyPackageGroupRelation(newRelation);
                return newRelation;

            } else {

                existingRelation.setGroupId(req.getGroupId());
                FamilyPackageGroupRelationFacade.updateFamilyPackageGroupRelation(existingRelation);
                return existingRelation;
            }
        } catch (Exception e) {
            log.error("Failed to handle new groupId: masterUserId={}, groupId={}, error={}",
                    req.getMasterUserDo().getUserId(), req.getGroupId(), e.getMessage(), e);
            throw new ServiceException(INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 开始拉人入圈
     */
    private void startPullMembersIntoGroup(CreateFamilyPackageBindingsDto req, List<FamilyPackageBindDo> bindings,
                                           List<FamilyPackageBoundResult> bindableResults, String groupId) {
        try {
            if (CollUtil.isEmpty(bindings)) {
                if (log.isDebugEnabled()) {
                    log.debug("handleExistingGroupId bindings is empty");
                }
                return;
            }
            List<String> boundMembers = bindings.stream()
                    .filter(bind -> FamilyPackageStateEnum.BOUND.equals(bind.getFamilyPackageStateEnum()))
                    .map(FamilyPackageBindDo::getBoundUserDomainId)
                    .collect(Collectors.toList());

            //查询圈子所有成员，过滤需要拉圈的
            List<String> userDomainIdList = filterBoundMembers(groupId, req.getMasterUserDo().getUserDomainId(), boundMembers);
            if (CollUtil.isEmpty(userDomainIdList)) {
                if (log.isDebugEnabled()) {
                    log.debug("pullMembersIntoGroup userDomainIdList is empty");
                }
                return;
            }

            PullMembersIntoGroupCondition pullCondition = PullMembersIntoGroupCondition.builder()
                    .groupId(groupId)
                    .userDomainId(req.getMasterUserDo().getUserDomainId())
                    .memberList(userDomainIdList)
                    .build();
            PullMembersIntoGroupDo result = circleService.pullMembersIntoGroup(pullCondition);
            if (result != null) {
                updateBindableResults(bindableResults, result, bindings);
            }
        } catch (ServiceException e) {
            //重新设置异常
            if (CollUtil.isNotEmpty(bindableResults)) {
                for (FamilyPackageBoundResult bindableResult : bindableResults) {
                    if (ResultCodeEnum.SUCCESS.getResultCode().equals(bindableResult.getResultCode())) {
                        bindableResult.setResultCode(e.getErrorCode());
                        bindableResult.setResultDesc(e.getMessage());
                    }
                }
            } else {
                bindableResults = new ArrayList<>();
                FamilyPackageBoundResult familyPackageBoundResult = new FamilyPackageBoundResult();
                familyPackageBoundResult.setResultCode(e.getErrorCode());
                familyPackageBoundResult.setResultDesc(e.getMessage());
                bindableResults.add(familyPackageBoundResult);
            }
            log.error("Failed to handle existing groupId: masterUserId={}, groupId={}, error={}",
                    req.getMasterUserDo().getUserId(), groupId, e.getMessage(), e);
        } catch (Exception e) {
            log.error("Failed to handle existing groupId: masterUserId={}, groupId={}, error={}",
                    req.getMasterUserDo().getUserId(), groupId, e.getMessage(), e);
        }
    }

    /**
     * 更新绑定结果
     */
    private void updateBindableResults(List<FamilyPackageBoundResult> bindableResults, PullMembersIntoGroupDo result, List<FamilyPackageBindDo> bindings) {
        // 预构建用户ID与错误描述的映射关系
        Map<String, FailMemberInfo> userIdToFailMember = result.getFailMemberList().stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        FailMemberInfo::getUserId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
        if (log.isDebugEnabled()) {
            log.debug("[DOMAIN] updateBindableResults, userIdToFailMember: {}", userIdToFailMember);
            log.debug("[DOMAIN] updateBindableResults, bindableResults: {}", bindableResults);
        }

//        List<String> userDomainIdcollect = bindableResults.stream().map(FamilyPackageBoundResult::getUserUserDomainId).collect(Collectors.toList());
//        if (CollUtil.isNotEmpty(userIdToFailMember) && CollUtil.isNotEmpty(userDomainIdcollect)){
//            for (String userDomainId : userIdToFailMember.keySet()) {
//                if(userDomainIdcollect.contains(userDomainId) || ObjectUtil.isNull(userIdToFailMember.get(userDomainId))){
//                    continue;
//                }
//                FamilyPackageBoundResult familyPackageBoundResult = new FamilyPackageBoundResult();
//                familyPackageBoundResult.setUserUserDomainId(userIdToFailMember.get(userDomainId).getUserId());
//                familyPackageBoundResult.setResultCode(userIdToFailMember.get(userDomainId).getErrorCode());
//                familyPackageBoundResult.setResultDesc(userIdToFailMember.get(userDomainId).getDesc());
//                bindableResults.add(familyPackageBoundResult);
//            }
//        }

        // 统一处理结果绑定
        bindableResults.forEach(binding -> {
            if (CollUtil.isNotEmpty(userIdToFailMember)
                    && ObjectUtil.isNotNull(userIdToFailMember.get(binding.getUserUserDomainId()))) {
                FailMemberInfo failMemberInfo = userIdToFailMember.get(binding.getUserUserDomainId());
                binding.setResultCode(failMemberInfo.getErrorCode());
                binding.setResultDesc(failMemberInfo.getDesc());
            } else {
                binding.setResultCode(ResultCodeEnum.SUCCESS.getResultCode());
            }
        });
    }


    /**
     * 查询圈子过滤出需要入圈的人
     *
     * @param groupId          圈子id
     * @param userDomainId     当前用户
     * @param userDomainIdList 已绑定的
     * @return List<String>
     */
    private List<String> filterBoundMembers(String groupId,
                                            String userDomainId,
                                            List<String> userDomainIdList) {
        QueryCircleListCondition queryCircleListCondition = QueryCircleListCondition.builder()
                .groupId(new Long(groupId))
                .userDomainId(userDomainId).build();
        List<CircleMemberInfoDo> circleMemberInfoDoList = circleService.queryGroupMemberList(queryCircleListCondition);
        if (CollUtil.isNotEmpty(circleMemberInfoDoList)) {
//            return circleMemberInfoDoList.stream()
//                    .map(CircleMemberInfoDo::getAccountUserId)
//                    .filter(accountUserId -> !userDomainIdList.contains(accountUserId))
//                    .collect(Collectors.toList());
            List<String> collect = circleMemberInfoDoList.stream()
                    .map(CircleMemberInfoDo::getAccountUserId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                return userDomainIdList.stream()
                        .filter(accountUserId -> !collect.contains(accountUserId))
                        .collect(Collectors.toList());
            }

        }
        return userDomainIdList;
    }

    /**
     * 修改家庭套餐绑定关系
     * 主要流程：
     * 1. 校验请求参数
     * 2. 查询主账号用户信息
     * 3. 查询绑定关系
     * 4. 处理解绑逻辑
     *
     * @param req 请求体
     * @return Mono<ModifyFamilyPackageBindResp>
     */
    @Override
    public Mono<ModifyFamilyPackageBindResp> modifyFamilyPackageBind(ModifyFamilyPackageBindReq req) {
        return Mono.defer(() -> Mono.just(req))
                .flatMap(this::acquireMasterLock)
                .flatMap(lockContext -> executeModifyProcess(lockContext)
                        .doFinally(signalType -> releaseMasterLock(lockContext)))
                .doOnError(e -> log.error("failed to modify the family package binding relationship, masterAccount: {}, bindId: {}, error: {}",
                        req.getMasterUserPhoneNumber(), req.getBindId(), e.getMessage()))
                .onErrorResume(this::handleError)
//                .onErrorMap(e -> (!(e instanceof ServiceException) || e instanceof DomainException),
//                        e -> new ServiceException(e.getMessage()))
                .doOnError(e -> log.error("family package unbinding error occurred", e))
                ;
    }


    /**
     * 获取主账号锁
     */
    private Mono<ModifyFamilyPackageBindReq.ModifyContext> acquireMasterLock(ModifyFamilyPackageBindReq req) {
        String lockKey = determineMasterLockKey(req);
        String lockValue = UUID.randomUUID().toString();

        if (lockKey == null || !redisLockService.acquireLock(lockKey, lockValue, 30)) {
            return Mono.error(new ServiceException(MUTUALLY_LIMIT_CODE, MUTUALLY_LIMIT_DESC));
        }

        return Mono.just(ModifyFamilyPackageBindReq.ModifyContext.builder()
                .request(req)
                .lockKey(lockKey)
                .lockValue(lockValue)
                .build());
    }

    /**
     * 确定主账号锁的key
     */
    private String determineMasterLockKey(ModifyFamilyPackageBindReq req) {
        if (StringUtils.isNotEmpty(req.getMasterUserPhoneNumber())) {
            return String.format(FAMILY_PACKAGE_MSISDN_KEY, req.getMasterUserPhoneNumber());
        } else if (StringUtils.isNotEmpty(req.getMasterUserDomainId())) {
            return String.format(FAMILY_PACKAGE_USER_DOMAIN_ID_KEY, req.getMasterUserDomainId());
        }
        return null;
    }

    /**
     * 执行修改流程
     */
    private Mono<ModifyFamilyPackageBindResp> executeModifyProcess(ModifyFamilyPackageBindReq.ModifyContext context) {
        return getMasterUser(context.getRequest())
                .doOnNext(context::setMasterUser)
                .flatMap(user -> queryFamilyPackageBindings(user, context.getRequest()))
                .doOnNext(context::setBinding)
                .flatMap(this::processUnbindingOfModify)
                .map(this::buildModifyResponse)
                .doOnError(this::handleError);
    }

    /**
     * 构建修改响应
     */
    private ModifyFamilyPackageBindResp buildModifyResponse(FamilyPackageBind updatedBinding) {
        return ModifyFamilyPackageBindResp.builder()
                .familyPackageBind(updatedBinding)
                .build();
    }

    /**
     * 释放主账号锁
     */
    private void releaseMasterLock(ModifyFamilyPackageBindReq.ModifyContext context) {
        if (context != null && context.getLockKey() != null) {
            try {
                redisLockService.releaseLock(context.getLockKey(), context.getLockValue());
            } catch (Exception e) {
                log.error("failed to release the main account lock, lockKey: {}", context.getLockKey(), e);
            }
        }
    }

    /**
     * 获取主账号用户信息
     */
    private Mono<UserDo> getMasterUser(ModifyFamilyPackageBindReq req) {
        return Mono.fromCallable(() -> {
            UserDo userDo = UserServiceFacade.queryUser(req.getMasterUserPhoneNumber(), req.getMasterUserDomainId());
            if (userDo == null) {
                //
                log.error("the master user information is not exist, account: {}, userDomainId: {}", req.getMasterUserPhoneNumber(), req.getMasterUserDomainId());
                throw new ServiceException(USER_NOT_FOUND);
            }
            return userDo;
        });
    }

    /**
     * 查询家庭套餐绑定关系
     */
    private Mono<FamilyPackageBindDo> queryFamilyPackageBindings(UserDo masterUser, ModifyFamilyPackageBindReq req) {
        return Mono.fromCallable(() -> {
            QueryFamilyPackageBindCondition condition = QueryFamilyPackageBindCondition.builder()
                    .masterUserId(masterUser.getUserId())
                    .bindId(req.getBindId())
                    .familyPackageStateEnumList(Arrays.asList(FamilyPackageStateEnum.BOUND, FamilyPackageStateEnum.UNBIND_FAILED))
                    .build();

            FamilyPackageBindDo familyPackageBindDo = FamilyPackageBindFacade.queryFamilyPackageBind(condition);
            if (Objects.isNull(familyPackageBindDo)) {
                throw new ServiceException(BOUNDED_RELATION_NULL);
            }
            // 判断创建时间是否在单月
            Date now = new Date();
            if (familyPackageBindDo.getCreateTime().getTime() > DateUtil.beginOfMonth(now).getTime()
                    && familyPackageBindDo.getCreateTime().getTime() < DateUtil.endOfMonth(now).getTime()) {
                throw new ServiceException(MONTH_CAN_NOT_UNBIND);
            }
            return familyPackageBindDo;
        });
    }


    private Mono<FamilyPackageBind> processUnbindingOfModify(FamilyPackageBindDo binding) {
        return Mono.just(binding)
                .flatMap(this::acquireAndManageBoundLock)
                .flatMap(context -> validateAndUnbind(context)
                        .doFinally(signalType -> releaseBoundLock(context)))
                .doOnError(e -> log.error("unbind processing failed，bindId: {}, error: {}",
                        binding.getBindId(), e.getMessage(), e));
    }

    /**
     * 释放绑定账号锁
     */
    private void releaseBoundLock(UnbindLockContext context) {
        if (context.getLockKey() != null) {
            try {
                redisLockService.releaseLock(context.getLockKey(), context.getLockValue());
            } catch (Exception e) {
                log.error("释放绑定账号锁失败, lockKey: {}", context.getLockKey(), e);
            }
        }
    }

    /**
     * 将 FamilyPackageBindDo 转换为 FamilyPackageBind
     */
    private FamilyPackageBind convertToFamilyPackageBind(FamilyPackageBindDo bindDo) {
        if (bindDo == null) {
            return null;
        }

        FamilyPackageBind bind = FamilyPackageBind.builder()
                .masterUserPhoneNumber(bindDo.getMasterUserPhoneNumber())
                .familyPackageBoundUserList(Collections.singletonList(
                        FamilyPackageBoundUser.builder()
                                .bindId(bindDo.getBindId())
                                .boundUserPhoneNumber(bindDo.getBoundUserPhoneNumber())
                                .state(Optional.ofNullable(bindDo.getFamilyPackageStateEnum())
                                        .map(state -> String.valueOf(state.getState()))
                                        .orElse(null))
                                .createTime(bindDo.getCreateTime())
                                .updateTime(bindDo.getUpdateTime())
                                .build()
                ))
                .build();
        // 填充商品实例信息
        fillBindWithGoodsInfo(bind, bindDo.getMasterUserId(), bindDo.getMasterOrderId(), bindDo.getMasterGoodsInstanceId());
        return bind;
    }

    /**
     * 查询商品实例信息并完善FamilyPackageBind对象
     */
    private void fillBindWithGoodsInfo(
            FamilyPackageBind bind, String userId, String orderId, String goodsInstanceId) {
        if (bind == null || StringUtils.isEmpty(goodsInstanceId)) {
            return;
        }
        try {
            QueryGoodsInstanceCondition condition = new QueryGoodsInstanceCondition();
            condition.setGoodsInstanceIdList(Collections.singletonList(goodsInstanceId));
            condition.setUserId(userId);
            GoodsInstanceDo goodsInstance = GoodsInstanceServiceFacade.queryGoodsInstanceByCondition(condition);
            if (Objects.isNull(goodsInstance)) {
                log.warn("[APPLICATION] No goods instance found for id: {}", goodsInstanceId);
                return;
            }
            String goodsId = goodsInstance.getGoodsId();
            bind.setMasterGoodsId(goodsId);
            bind.setChargeType(goodsInstance.getChargeType());
            GoodsDo goods;
            goods = GoodsServiceFacade.getGoods(goodsId);
            if (goods != null) {
                bind.setMasterGoodsName(goods.getGoodsName());
                if (goods.getGoodsExt() != null) {
                    bind.setFamilyPackageGiftQuota(goods.getGoodsExt().getFamilyPackageGiftQuota());
                }
            }
            bind.setEffectiveStartTime(goodsInstance.getEffectiveStartTime());
            bind.setEffectiveEndTime(goodsInstance.getEffectiveEndTime());
            bind.setMasterGoodsId(goodsInstance.getGoodsId());
            bind.setMasterOrderId(goodsInstance.getOrderId());
            bind.setMasterGoodsInstanceId(goodsInstance.getGoodsInstanceId());
            QueryFamilyPackageGroupRelationCondition groupCondition = QueryFamilyPackageGroupRelationCondition.builder()
                    .masterUserId(userId)
                    .masterOrderId(orderId)
                    .masterGoodsInstanceId(goodsInstanceId)
                    .build();
            bind.setGroupId(getGroupId(groupCondition));
        } catch (Exception e) {
            log.error("[APPLICATION] Error enriching bind with goods info: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取并管理绑定账号锁的上下文
     */
    private Mono<FamilyPackageServiceImpl.UnbindLockContext> acquireAndManageBoundLock(FamilyPackageBindDo binding) {
        String boundLockKey = getBoundLockKey(binding);
        String boundLockValue = UUID.randomUUID().toString();

        if (boundLockKey == null || !redisLockService.acquireLock(boundLockKey, boundLockValue, 30)) {
            if (log.isDebugEnabled()) {
                log.debug("获取锁失败，用户：{}", binding.getBoundUserPhoneNumber());
            }
            return Mono.just(FamilyPackageServiceImpl.UnbindLockContext.builder()
                    .binding(binding)
                    .build());
        }

        return Mono.just(FamilyPackageServiceImpl.UnbindLockContext.builder()
                .binding(binding)
                .lockKey(boundLockKey)
                .lockValue(boundLockValue)
                .build());
    }

    /**
     * 获取绑定账号锁的key
     */
    private String getBoundLockKey(FamilyPackageBindDo binding) {
        if (StringUtils.isNotEmpty(binding.getBoundUserPhoneNumber())) {
            return String.format(FAMILY_PACKAGE_BOUND_MSISDN_KEY, binding.getBoundUserPhoneNumber());
        } else if (StringUtils.isNotEmpty(binding.getBoundUserDomainId())) {
            return String.format(FAMILY_PACKAGE_BOUND_USER_DOMAIN_ID_KEY, binding.getBoundUserDomainId());
        }
        return null;
    }

    /**
     * 验证并执行解绑流程
     */
    private Mono<FamilyPackageBind> validateAndUnbind(FamilyPackageServiceImpl.UnbindLockContext context) {
        return Mono.just(context)
                .flatMap(ctx -> validatePreUnBind(ctx)
//                        .flatMap(this::validateGoodsInstanceNonToUnBind)
//                        .flatMap(this::validateChargeType)
                        .flatMap(this::executeUnbinding))
                .onErrorResume(ServiceException.class, e -> handleUnbindError(context.getBinding(), e))
                .doFinally(signalType -> {
                    if (!FamilyPackageStateEnum.BOUND.equals(context.getBinding().getFamilyPackageStateEnum())) {
                        FamilyPackageBindFacade.updateFamilyPackageBind(context.getBinding());
                    }
                });
    }

    /**
     * 处理解绑错误
     */
    private Mono<FamilyPackageBind> handleUnbindError(FamilyPackageBindDo binding, ServiceException e) {
        binding.setFamilyPackageStateEnum(FamilyPackageStateEnum.UNBIND_FAILED);
        FamilyPackageBindFacade.updateFamilyPackageBind(binding);
        return Mono.error(e);
    }

    /**
     * 执行解绑操作
     */
    private Mono<FamilyPackageBind> executeUnbinding(FamilyPackageServiceImpl.UnbindLockContext context) {
        if (context.getBinding().getFamilyPackageStateEnum() == FamilyPackageStateEnum.CANCEL_ACCOUNT
                || context.getBinding().getFamilyPackageStateEnum() == FamilyPackageStateEnum.UNBOUND
                || Objects.isNull(context.getGoodsInstance())) {
            return Mono.just(convertToFamilyPackageBind(context.getBinding()));
        }

        context.getBinding().setFamilyPackageStateEnum(FamilyPackageStateEnum.UNBINDING);
        FamilyPackageBindFacade.updateFamilyPackageBind(context.getBinding());

        try {
            RollBackRightReq rollBackRightReq = new RollBackRightReq();
            rollBackRightReq.setAccount(context.getBinding().getBoundUserPhoneNumber());
            rollBackRightReq.setOrderID(context.getGoodsInstance().getOrderId());
            String resp = vsboOrderGateway.rollBackRight(rollBackRightReq);
            XmlResult restfulResponse = XmlUtil.convertXmlToObj(resp, XmlResult.class);
            if (restfulResponse != null && ResultCodeEnum.SUCCESS.getResultCode().equals(restfulResponse.getResultCode())) {
                context.getBinding().setFamilyPackageStateEnum(FamilyPackageStateEnum.UNBOUND);
            } else {
                context.getBinding().setFamilyPackageStateEnum(FamilyPackageStateEnum.UNBIND_FAILED);
                FamilyPackageBindFacade.updateFamilyPackageBind(context.getBinding());
                return Mono.error(new ServiceException(UNBINDING_FAILED));
            }
        } catch (Exception e) {
            log.error("failed to roll back equity，bindId: {}, error: {}", context.getBinding().getBindId(), e.getMessage(), e);
            context.getBinding().setFamilyPackageStateEnum(FamilyPackageStateEnum.UNBIND_FAILED);
            return Mono.error(new ServiceException(UNBINDING_FAILED));
        }

        return Mono.just(convertToFamilyPackageBind(context.getBinding()));
    }

    /**
     * 1、校验解绑用户的用户信息
     * 2、
     */
    private Mono<FamilyPackageServiceImpl.UnbindLockContext> validatePreUnBind(FamilyPackageServiceImpl.UnbindLockContext unbindLockContext) {
        return Mono.fromCallable(() ->
                        UserServiceFacade.queryUser(unbindLockContext.getBinding().getBoundUserPhoneNumber(),
                                unbindLockContext.getBinding().getBoundUserDomainId()))
                .filter(Objects::nonNull)
                .map(user -> {
                    unbindLockContext.setBoundUser(user);
                    return unbindLockContext;
                })
                .switchIfEmpty(Mono.defer(() -> {
                    if (log.isDebugEnabled()) {
                        log.debug("user is not exist，msisdn is：{}", unbindLockContext.getBinding().getBoundUserPhoneNumber());
                    }
                    unbindLockContext.getBinding().setFamilyPackageStateEnum(FamilyPackageStateEnum.CANCEL_ACCOUNT);
                    return Mono.just(unbindLockContext);
                })).flatMap(context -> {
                    QueryGoodsInstanceCondition condition = new QueryGoodsInstanceCondition();
                    condition.setUserId(context.getBinding().getBoundUserId());
                    condition.setGoodsInstanceIdList(Collections.singletonList(context.getBinding().getBoundGoodsInstanceId()));
                    condition.setEffectiveEndTimeStart(new Date());
                    condition.setPhoneNumber(unbindLockContext.getBoundUser().getMsisdn());
                    GoodsInstanceDo goodsInstanceDo = GoodsInstanceServiceFacade.queryGoodsInstanceByCondition(condition, QUERY_CONDITION_GOODS_INSTANCE);
                    if (goodsInstanceDo != null && goodsInstanceDo.getEffectiveEndTime().getTime() <= System.currentTimeMillis()) {
                        log.warn("The goods instance does not exist, phone number: {}，goods instance ID：{}",
                                context.getBinding().getBoundUserPhoneNumber(),
                                context.getBinding().getBoundGoodsInstanceId());
                        context.getBinding().setFamilyPackageStateEnum(FamilyPackageStateEnum.UNBOUND);
                        return Mono.just(context);
                    }
                    context.setGoodsInstance(goodsInstanceDo);
                    return Mono.just(context);
                }).flatMap(context -> {
                    GoodsInstanceDo goodsInstance = context.getGoodsInstance();
                    if (Objects.nonNull(goodsInstance) && ChargeTypeEnum.BY_TIMES.equals(
                            ChargeTypeEnum.fromType(goodsInstance.getChargeType()))) {
                        return Mono.error(new ServiceException(TIME_CAN_NOT_UNBIND));
                    }
                    return Mono.just(context);
                });
    }


    @Data
    @Builder
    private static class UnbindLockContext {
        private FamilyPackageBindDo binding;
        private String lockKey;
        private String lockValue;
        private UserDo boundUser;
        private GoodsInstanceDo goodsInstance;
    }

    /**
     * 查询绑定关系
     *
     * @param queryFamilyPackageBindReq 请求体
     * @return 响应
     */
    @Override
    public Mono<QueryBindFamilyPackageResp> queryFamilyPackageBind(QueryFamilyPackageBindReq queryFamilyPackageBindReq) {
        // StopWatch stopWatch = new StopWatch();
        // stopWatch.start("queryFamilyPackageBind");
        return Mono.just(queryFamilyPackageBindReq)
                .flatMap(req -> Mono.fromCallable(() ->
                                UserServiceFacade.queryUser(
                                        req.getUserPhoneNumber(),
                                        req.getMasterUserDomainId()
                                ))
                        .filter(Objects::nonNull)
                        .switchIfEmpty(Mono.error(new ServiceException(USER_NOT_FOUND)))
                )
                .flatMap(userDo -> Mono.zip(
                        Mono.fromCallable(() -> processMasterBindings(userDo)),
                        Mono.fromCallable(() -> processBoundBindings(userDo))
                ))
                .map(tuple -> {
                    List<FamilyPackageBind> masterBinds = tuple.getT1();
                    List<FamilyPackageBind> boundBinds = tuple.getT2();
                    List<FamilyPackageBind> mergedBinds = Stream.of(masterBinds, boundBinds)
                            .filter(CollUtil::isNotEmpty)
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());
                    return QueryBindFamilyPackageResp.builder()
                            .familyPackageBindList(mergedBinds)
                            .build();
                })
                .doOnSuccess(response -> {
                    if (log.isDebugEnabled()) {
                        log.debug("[APPLICATION] queryFamilyPackageBind success, request is: {}, response: {}",
                                queryFamilyPackageBindReq, response);
                    }
                })
                .doOnError(error -> {
                    if (error instanceof ServiceException) {
                        log.error("[APPLICATION] queryFamilyPackageBind fail, request is: {}, error status: {}, error msg: {}",
                                queryFamilyPackageBindReq, ((ServiceException) error).getErrorCode(),
                                error.getMessage());
                    } else {
                        log.error("[APPLICATION] queryFamilyPackageBind fail, request is: : {}",
                                queryFamilyPackageBindReq, error);
                    }
                })
                .onErrorResume(ServiceException.class, Mono::error)
                .onErrorResume(e -> Mono.error(new ServiceException(
                        ResultCodeEnum.INTERNAL_SERVER_ERROR.getResultCode(), e.getMessage())));
    }

}
