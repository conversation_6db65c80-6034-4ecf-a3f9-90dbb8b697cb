package com.zyhl.yun.member.mcdmc.activation.domains.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

/**
 * 发货接口的公共入参请求
 *
 * <AUTHOR>
 * @since 2024/06/13 09:21
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ComSendInterfaceReq {
    /**
     * 订购关系ID/商品实例id
     */
    private String goodsInstanceId;

    /**
     * 父商品实例id
     */
    private String parentGoodsInstanceId;

    /**
     * 父订单id
     */
    private String parentOrderId;
    /**
     * 订单id
     */
    private String orderID;
    /**
     * 外部订单id
     * 权益发货时不为空
     */
    private String outOrderId;
    /**
     * 产品id
     */
    private String productId;

    /**
     * 商品ID
     */
    private String goodsId;
    /**
     * 商品名字
     */
    private String goodsName;

    /**
     * 手机号码
     */
    private String msisdn;

    /**
     * 用户域id
     */
    private String userDomainId;
    /**
     * 内部用户id
     */
    private String userId;

    /**
     * 区号。格式： +国家地区编码
     */
    private String nationCode;

    /**
     * 订单创建时间,时间格式：yyyyMMddHHmmss
     */
    private String createTime;

    /**
     * 生效时间,时间格式：yyyyMMddHHmmss
     */
    private String effectiveStartTime;

    /**
     * 销售对象订购关系的失效时间,时间格式：yyyyMMddHHmmss
     */
    private String effectiveEndTime;
    /**
     * 订购时间,时间格式：yyyyMMddHHmmss
     */
    private String subTime;

    /**
     * 对端产品ID
     */
    private String proServiceId;

    /**
     * 支付方式
     * 订购时传的子订单支付方式，退费时传的父订单支付方式
     */
    private String payWay;

    /**
     * 渠道id
     *
     * @apiNote 一级能开免流订购时使用
     */
    private String channelId;

    /**
     * 子渠道id
     *
     * @apiNote 一级能开免流订购时使用
     */
    private String subChannelId;

    /**
     * 支付金额（积分支付时为对应的积分值）
     */
    private Long totalAmount;
    /**
     * 活动续费价格(单位：分)-非积分支付才有
     */
    private Long activityRenewPrice;

    /**
     * 活动优化价格 单位：分-非积分支付时才有
     */
    private Long activityPrice;

    /**
     * 计费类型
     * 0 点播 1 包月
     */
    private String chargeType;

    /**
     * syncAppOrder省侧订购的活动标识
     */
    private String parentCampaignId;

    /**
     * 扩展信息
     */
    private Map<String, String> extInfoMap;

    public String getExtInfo(String key) {
        return getExtInfo(key, null);
    }

    public String getExtInfo(String key, String defaultValue) {
        if (null == extInfoMap) {
            return defaultValue;
        }
        return extInfoMap.getOrDefault(key, defaultValue);
    }

    public void putExtInfo(String key, String value) {
        if (null == extInfoMap) {
            extInfoMap = new HashMap<>();
        }
        extInfoMap.put(key, value);
    }
}
