package com.zyhl.yun.member.mcdmc.activation.remote.client;

import cn.hutool.core.map.MapUtil;
import com.zyhl.yun.member.common.JsonUtil;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.PlatformCommonProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/07/16 13:58
 */
@Slf4j
@Component
public class RemoteClient {

    @Resource
    private PlatformCommonProperties platformCommonProperties;
    private final WebClient webClient;

    public RemoteClient() {
        webClient = WebClient.builder()
                .filter(ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
                    log.info(toCurl(clientRequest));
                    return Mono.just(clientRequest);
                })).build();
    }

    private String toCurl(ClientRequest request) {
        String headers = Arrays.stream(request.headers().entrySet().toArray())
                .map(header -> header.toString()
                        .replaceFirst("=", ":")
                        .replaceFirst("\\[", " "))
                .map(h -> String.format(" --header '%s'%n", h.substring(0, h.length() - 1)))
                .collect(Collectors.joining());
        String httpMethod = request.method().name();
        String url = request.url().toString();

        return String.format("curl --location --request %s '%s' %n%s %n", httpMethod, url, headers);
    }

    public <T> RemoteResponse<T> post(RemoteRequest<T> request, RemoteResponse<T> remoteResponse) {
        if (request == null) {
            return null;
        }
        Mono<T> tMono = webClient
                .post()
                .uri(request.getRequestUrl())
                .headers(httpHeaders1 -> MapUtil.emptyIfNull(request.getHeaderMap()).forEach(httpHeaders1::add))
                .contentType(request.getMediaType())
                .bodyValue(request.getBody())
                .exchangeToMono(response -> {
                    log.debug("response status code is {}", response.rawStatusCode());
                    remoteResponse.setResponseHttpCode(response.rawStatusCode());
                    remoteResponse.setResponseHeader(response.headers().asHttpHeaders().toSingleValueMap());
                    if (response.statusCode().equals(HttpStatus.OK)) {
                        return response.bodyToMono(request.getResponseClass())
                                .doOnNext(body1 -> log.info("get body:{}", body1))
                                .doOnError(rsp -> log.info("error:" + rsp));
                    } else {
                        return response.createException().flatMap(Mono::error);
                    }
                })
                .doOnSubscribe(res -> log.info("postRequest subscribe success!"))
                .doOnSuccess(res -> log.info("postRequest success, the response is {}", (res))).doFinally(res -> log.info("postRequest finally"))
                .doOnError(error ->
                        log.error("postRequest error, the exception is {}", JsonUtil.toJson(error)));
        tMono = tMono.timeout(Duration.ofSeconds(platformCommonProperties.getRemoteTimeoutSec()));
        remoteResponse.setResponseMono(tMono);
        return remoteResponse;
    }

    /**
     * 同步请求
     */
    public <T> RemoteResponse<T> postSync(RemoteRequest<T> request) {
        RemoteResponse<T> remoteResponse = new RemoteResponse<>();
        try {
            post(request, remoteResponse);
            remoteResponse.setResponseData(remoteResponse.getResponseMono().block());
        } catch (Exception e) {
            remoteResponse.setRemoteException(e);
        }
        return remoteResponse;
    }
}
