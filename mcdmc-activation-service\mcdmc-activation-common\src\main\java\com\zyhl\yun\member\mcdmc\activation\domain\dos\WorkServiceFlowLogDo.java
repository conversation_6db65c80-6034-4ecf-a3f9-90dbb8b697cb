package com.zyhl.yun.member.mcdmc.activation.domain.dos;

import com.zyhl.yun.member.common.domain.framework.BaseDo;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.FlowLogStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 每个流程接口调用日志表
 **/
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WorkServiceFlowLogDo extends BaseDo implements Serializable {

    /**
     * 接口日志id
     */
    private String logId;
    /**
     * 流程编码
     */
    private String serviceCode;

    /**
     * 流程接口id
     */
    private Long iFaceId;

    /**
     * 工单实例表work_id
     */
    private String workId;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 工单实例表inst_work_order.order_id
     */
    private String orderId;
    /**
     * cls_work_service_flow表的work_service_flow_id
     */
    private String workServiceFlowId;
    /**
     * 请求url
     */
    private String url;
    /**
     * 请求头json报文
     */
    private String requestHeader;
    /**
     * 请求体
     */
    private String requestBody;
    /**
     * 响应http码
     */
    private Integer responseHttpCode;
    /**
     * 响应头
     */
    private String responseHeader;
    /**
     * 响应体
     */
    private String responseBody;
    /**
     * 回调地址
     */
    private String callbackUrl;
    /**
     * 设置回调条件，若有回调，则通过该字段来查到日志
     */
    private String callbackCondition;
    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 接口日志状态,具体状态值见
     * {@link FlowLogStateEnum FlowLogStateEnum}
     */
    private FlowLogStateEnum state;
    /**
     * 扩展字段上下文
     */
    private String extParams;


    public WorkServiceFlowLogDo(DomainServiceContext domainServiceContext) {
        this.domainServiceContext = domainServiceContext;
    }
}
