package com.zyhl.yun.member.mcdmc.activation;

import cn.hutool.core.text.CharSequenceUtil;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.AESUtils;
import com.zyhl.yun.member.mcdmc.activation.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.io.IOException;

@Slf4j
@EnableScheduling
@SpringBootApplication(scanBasePackages = "com.zyhl.yun.member")
@EnableConfigurationProperties
@MapperScan(value = {"com.zyhl.yun.member"}, annotationClass = Mapper.class)
public class ActivationApplication {
    public static void main(String[] args) throws IOException, IllegalAccessException {
        SpringApplication app = new SpringApplication(ActivationApplication.class);

        ApplicationContext appContext = app.run(args);
        Environment env = appContext.getEnvironment();
        // 设置aes算法的密钥
        AESUtils.setKey(env.getProperty("platform.aesKey", "************************"));
        String applicationName = CharSequenceUtil.str(env.getProperty("spring.application.name"));
        String jdkVersion = System.getProperty("java.version");
        String contextPath = env.getProperty("server.servlet.context-path");
        String port = env.getProperty("server.port");
        String profile = env.getProperty("spring.profiles.active");
        log.info("\n----------------------------------------------------------\n\t" +
                        "Application '{}' is running! env is {},  JDK version is '{}'. Access URLs:\n\t" +
                        "Local: \t\thttp://localhost:{}{}\n\t" +
                        "External: \thttp://{}:{}{}\n----------------------------------------------------------",
                applicationName, profile,
                jdkVersion,
                port, contextPath,
                IpUtil.getLocalHostAddress(), port, contextPath);

    }
}
