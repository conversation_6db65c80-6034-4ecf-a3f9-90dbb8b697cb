package com.zyhl.yun.member.application.subscription.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONObject;
import com.zyhl.yun.member.application.subscription.VsboSubscribeApplication;
import com.zyhl.yun.member.application.subscription.dto.RaiNotifyEquityInfo;
import com.zyhl.yun.member.application.subscription.vo.request.*;
import com.zyhl.yun.member.application.subscription.vo.response.*;
import com.zyhl.yun.member.application.user.UserApplication;
import com.zyhl.yun.member.common.*;
import com.zyhl.yun.member.common.constants.ErrorCode;
import com.zyhl.yun.member.common.constants.SymbolConstant;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.framework.PageCondition;
import com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceServiceId;
import com.zyhl.yun.member.common.enums.ChildGoodsActiveStateEnum;
import com.zyhl.yun.member.common.enums.ExpireEnum;
import com.zyhl.yun.member.common.enums.RightsStatusEnum;
import com.zyhl.yun.member.common.enums.SubscribeTypeEnum;
import com.zyhl.yun.member.common.util.ParameterUtil;
import com.zyhl.yun.member.common.util.TimeUtil;
import com.zyhl.yun.member.domain.goodsinstance.QueryConditionSubScriptionReq;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.domain.goodsinstance.service.dto.QueryGoodsInstanceReq;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.receive.domain.dto.QueryGoodsInstanceExtendCondition;
import com.zyhl.yun.member.domain.user.domain.QueryUserCondition;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.dto.UnSubscribeReq;
import com.zyhl.yun.member.dto.UnSubscribeResp;
import com.zyhl.yun.member.facade.UserServiceFacade;
import com.zyhl.yun.member.goods.service.GoodsDomainService;
import com.zyhl.yun.member.goodsInstance.service.GoodsInstanceDomainService;
import com.zyhl.yun.member.goodsinstance.dto.AllContractSubscription;
import com.zyhl.yun.member.goodsinstance.dto.ChildProductActiveInfo;
import com.zyhl.yun.member.goodsinstance.dto.MemberSubscription;
import com.zyhl.yun.member.goodsinstance.dto.VipMemberContractSubVO;
import com.zyhl.yun.member.goodsinstance.enums.GoodsInstanceStateEnum;
import com.zyhl.yun.member.goodsinstance.enums.ParkActiveEnum;
import com.zyhl.yun.member.mcdmc.activation.constants.WorkAttrCodeConstant;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderAttrDo;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.facade.ActivationServiceFacade;
import com.zyhl.yun.member.order.domain.OrderDo;
import com.zyhl.yun.member.order.service.OrderDomainService;
import com.zyhl.yun.member.product.common.constants.CommonConstant;
import com.zyhl.yun.member.product.common.constants.MapKey;
import com.zyhl.yun.member.product.common.constants.RightModelConstant;
import com.zyhl.yun.member.product.common.enums.ChargeTypeEnum;
import com.zyhl.yun.member.product.common.enums.GoodsSalesTypeEnum;
import com.zyhl.yun.member.product.common.enums.PayWayEnum;
import com.zyhl.yun.member.product.common.enums.ValidateTriggerTimmingEnum;
import com.zyhl.yun.member.product.common.util.ServiceUtil;
import com.zyhl.yun.member.product.domain.goods.GoodsDo;
import com.zyhl.yun.member.product.domain.goods.pack.ChildGoodsGroupDo;
import com.zyhl.yun.member.product.domain.goods.pack.GoodsPackageDo;
import com.zyhl.yun.member.product.domain.product.ProductDo;
import com.zyhl.yun.member.product.domain.validaterule.SubRuleValidateResultDo;
import com.zyhl.yun.member.product.domain.validaterule.dto.SubRuleValidateCondition;
import com.zyhl.yun.member.receive.service.GoodsInstanceExtendDomainService;
import com.zyhl.yun.member.vip.service.impl.UserDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zyhl.yun.member.common.Constant.NOT_ABILITY_RETURN;
import static com.zyhl.yun.member.common.constants.NamedParameterConstant.*;
import static com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceExtendServiceId.UPDATE_GOODS_INSTANCE_EXTEND;
import static com.zyhl.yun.member.common.domain.serviceid.ValidateRuleServiceId.QUERY_SUB_VALIDATE_DETAIL_INFO;
import static com.zyhl.yun.member.common.enums.RightsStatusEnum.OPENP_FAILED_RIGHTS_STSUS_LIST;
import static com.zyhl.yun.member.common.enums.SaleTypeEnum.RIGHTS;
import static com.zyhl.yun.member.product.common.constants.MapKey.CHILD_PRODUCTS;
import static com.zyhl.yun.member.product.common.enums.GoodsActiveStrategyEnum.FIRST_CYCLE_CHOOSE_1;
import static com.zyhl.yun.member.product.common.enums.GoodsActiveStrategyEnum.N_CHOOSE_1_STRATEGY;
import static com.zyhl.yun.member.product.common.enums.GoodsActiveTypeEnum.MAN_ACTIVE;
import static com.zyhl.yun.member.receive.service.SubscribeValidationService.checkUnSubscribe;
import static com.zyhl.yun.member.receive.service.SubscribeValidationService.preRollBackRightMono;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/20 19:23
 */

@Service
@Slf4j
public class VsboSubscribeApplicationImpl implements VsboSubscribeApplication {


    private static final int MAX_PAGE_SIZE = 100;

    private static final String DESC = "1";

    private static final List<RightsStatusEnum> ACTIVE_STATUS_FOR_PARK_GOODS = Arrays.asList(RightsStatusEnum.ACTIVATION_SUCCESS, RightsStatusEnum.ACTIVATION_SUCCESS_1, RightsStatusEnum.ACTIVATION_FAIL_1);

    @Resource
    private GoodsInstanceDomainService goodsInstanceDomainService;

    @Resource
    private UserApplication userApplication;

    @Resource
    private UserDomainService userDomainService;
    @Resource
    private OrderDomainService orderDomainService;
    @Resource
    private GoodsDomainService goodsDomainService;

    @Resource
    private GoodsInstanceExtendDomainService goodsInstanceExtendDomainService;


    /**
     * 查询用户订购关系接口
     * 只包含101，102，并且是包月，支持退订的商品
     *
     * @param req：查询请求体
     * @return Mono<QueryRightsSubscribeRelationResp>
     */
    @Override
    public Mono<ChkValidMonthSubscribeResp> chkValidMonthSubscribe(ChkValidMonthSubscribeReq req) {
        return Mono.defer(() -> Mono.just(buildQueryGoodsInstanceCondition(req))).map(queryGoodsInstanceReq -> {
            List<AllContractSubscription> allContractSubscriptionList = goodsInstanceDomainService.queryMonthSubscribe(queryGoodsInstanceReq);
            ChkValidMonthSubscribeResp chkValidMonthSubscribeResp = new ChkValidMonthSubscribeResp();
            chkValidMonthSubscribeResp.setAllContractSubscriptions(allContractSubscriptionList);
            return chkValidMonthSubscribeResp;
        });
    }

    /**
     * 查询订购关系V1
     * <p>
     * 可根据条件过滤条件中符合ownerId的商品订购
     * contractIDList条件可对所有类型商品实例做过滤
     *
     * @param req：查询请求体
     * @return
     */
    @Override
    public Mono<QueryContractListSubscriptionResp> queryContractListSubscription(QueryContractListSubscriptionReq req) {
        return Mono.defer(() -> Mono.just(buildQueryGoodsInstanceCondition(req)))
                .map(queryGoodsInstanceReq -> {
                    QueryUserCondition queryUserCondition = new QueryUserCondition();
                    queryUserCondition.setAccount(req.getAccount());
                    queryUserCondition.setUserDomainId(req.getUserDomainId());
                    UserDo userDo = UserServiceFacade.queryUser(queryUserCondition);
                    List<QueryGoodsInstanceCondition> queryGoodsInstanceConditions = goodsInstanceDomainService.buildQueryGoodsInstanceConditionList(userDo, queryGoodsInstanceReq);

                    return buildQueryContractListSubscriptionResp(
                            goodsInstanceDomainService.queryContractListSubscription(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions),
                            goodsInstanceDomainService.queryContractListSubscriptionCount(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions));
                });
    }


    /**
     * 查询订购关系V2
     * <p>
     * 不过滤独立空间商品的订购
     * contractIDList条件可对所有类型商品实例做过滤(未实现)
     *
     * @param req：查询请求体
     * @return
     */
    @Override
    public Mono<QueryContractListSubscriptionResp> queryContractListSubscriptionV2(QueryContractListSubscriptionReq req) {
        return Mono.defer(() -> Mono.just(buildQueryGoodsInstanceConditionCommon(req)))
                .map(queryGoodsInstanceReq -> {
                    UserDo userDo = goodsInstanceDomainService.getAndCheckUser(queryGoodsInstanceReq.getPhoneNumber(), queryGoodsInstanceReq.getUserDomainId());
                    List<QueryGoodsInstanceCondition> queryGoodsInstanceConditions = goodsInstanceDomainService.buildQueryGoodsInstanceConditionList(userDo, queryGoodsInstanceReq);

                    return buildQueryContractListSubscriptionResp(
                            goodsInstanceDomainService.queryContractListSubscription(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions),
                            goodsInstanceDomainService.queryContractListSubscriptionCount(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions));
                });
    }

    /**
     * 查询订购关系V3
     * <p>
     * 同权益组时间不会叠加, 过滤独立空间商品的订购
     * contractIDList条件可对所有类型商品实例做过滤（未实现）
     *
     * @param req：查询请求体
     * @return
     */
    @Override
    public Mono<QueryContractListSubscriptionResp> queryContractListSubscriptionV3(QueryContractListSubscriptionReq req) {
        return Mono.defer(() -> Mono.just(buildQueryGoodsInstanceConditionV3(req)))
                .map(queryGoodsInstanceReq -> {
                    UserDo userDo = goodsInstanceDomainService.getAndCheckUser(queryGoodsInstanceReq.getPhoneNumber(), queryGoodsInstanceReq.getUserDomainId());
                    List<QueryGoodsInstanceCondition> queryGoodsInstanceConditions = goodsInstanceDomainService.buildQueryGoodsInstanceConditionList(userDo, queryGoodsInstanceReq);

                    return buildQueryContractListSubscriptionResp(
                            goodsInstanceDomainService.queryContractListSubscription(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions),
                            goodsInstanceDomainService.queryContractListSubscriptionCount(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions));
                });
    }

    /**
     * 查询订购关系V4
     * <p>
     * 支持不传商品销售类型，过滤独立空间订购，其他同V1
     * contractIDList条件只对103类型商品实例做过滤
     *
     * @param req：查询请求体
     * @return
     */
    @Override
    public Mono<QueryContractListSubscriptionResp> queryContractListSubscriptionV4(QueryContractListSubscriptionReq req) {
        return Mono.defer(() -> Mono.just(buildQueryGoodsInstanceConditionV4(req)))
                .map(queryGoodsInstanceReq -> {
                    UserDo userDo = goodsInstanceDomainService.getAndCheckUser(queryGoodsInstanceReq.getPhoneNumber(), queryGoodsInstanceReq.getUserDomainId());
                    List<QueryGoodsInstanceCondition> queryGoodsInstanceConditions = goodsInstanceDomainService.buildQueryGoodsInstanceConditionList(userDo, queryGoodsInstanceReq);

                    return buildQueryContractListSubscriptionResp(
                            goodsInstanceDomainService.queryContractListSubscription(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions),
                            goodsInstanceDomainService.queryContractListSubscriptionCount(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions));
                });
    }

    /**
     * 查询订购关系V5
     * <p>
     * 同一个权益组id，只返回当前生效的记录，不进行叠加. 不过滤独立空间订购。其他同V1
     * contractIDList条件可对所有类型商品实例做过滤（未实现）
     *
     * @param req：查询请求体
     * @return
     */
    @Override
    public Mono<QueryContractListSubscriptionResp> queryContractListSubscriptionV5(QueryContractListSubscriptionReq req) {
        return Mono.defer(() -> Mono.just(buildQueryGoodsInstanceConditionV5(req)))
                .map(queryGoodsInstanceReq -> {
                    UserDo userDo = goodsInstanceDomainService.getAndCheckUser(queryGoodsInstanceReq.getPhoneNumber(), queryGoodsInstanceReq.getUserDomainId());
                    List<QueryGoodsInstanceCondition> queryGoodsInstanceConditions = goodsInstanceDomainService.buildQueryGoodsInstanceConditionList(userDo, queryGoodsInstanceReq);

                    return buildQueryContractListSubscriptionResp(
                            goodsInstanceDomainService.queryContractListSubscription(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions),
                            goodsInstanceDomainService.queryContractListSubscriptionCount(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions));
                });
    }

    /**
     * 查询独立空间订购关系列表
     *
     * @param req：查询请求体
     * @return Mono<QueryContractListSubscriptionResp>
     */
    @Override
    public Mono<QueryIndepentSpaceSubScriptionResp> queryIndependentSpaceSub(QueryIndependentSpaceSubReq req) {
        return Mono.defer(() -> Mono.just(buildQueryGoodsInstanceCondition(req)))
                .map(queryGoodsInstanceReq -> {
                    UserDo userDo = goodsInstanceDomainService.getAndCheckUser(queryGoodsInstanceReq.getPhoneNumber(), queryGoodsInstanceReq.getUserDomainId());
                    List<QueryGoodsInstanceCondition> queryGoodsInstanceConditions = goodsInstanceDomainService.buildQueryGoodsInstanceConditionList(userDo, queryGoodsInstanceReq);

                    return buildQueryIndepentSpaceSubScriptionResp(
                            goodsInstanceDomainService.queryIndependentSubscription(queryGoodsInstanceReq),
                            goodsInstanceDomainService.queryContractListSubscriptionCount(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions));
                });
    }


    /**
     * 查询订购关系(大音平台)
     * <p>
     * 基本同V4
     * contractIDList条件只对103类型商品实例做过滤
     *
     * @param req：查询请求体
     * @return
     */
    @Override
    public Mono<QueryContractListSubscriptionResp> queryContractListSubscriptionForDY(QueryContractListSubscriptionReq req) {
        return Mono.defer(() -> Mono.just(buildQueryGoodsInstanceConditionForDY(req)))
                .map(queryGoodsInstanceReq -> {
                    UserDo userDo = goodsInstanceDomainService.getAndCheckUser(queryGoodsInstanceReq.getPhoneNumber(), queryGoodsInstanceReq.getUserDomainId());
                    List<QueryGoodsInstanceCondition> queryGoodsInstanceConditions = goodsInstanceDomainService.buildQueryGoodsInstanceConditionList(userDo, queryGoodsInstanceReq);

                    return buildQueryContractListSubscriptionResp(
                            goodsInstanceDomainService.queryContractListSubscription(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions),
                            goodsInstanceDomainService.queryContractListSubscriptionCount(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions));
                });
    }

    /**
     * 退订
     *
     * @param unSubscribeReq 请求体
     * @return 响应
     */
    @Override
    public Mono<UnSubscribeResp> unSubscribe(UnSubscribeReq unSubscribeReq) {

        return Mono.defer(() -> Mono.just(unSubscribeReq))
                .flatMap(req -> Mono.justOrEmpty(goodsInstanceDomainService.unSubscribe(unSubscribeReq)));
    }

    /**
     * 回收用户权益
     *
     * @param req 请求体
     * @return Mono<RollBackRightResp> 响应eryOr
     */
    @Override
    public Mono<Void> rollBackRight(RollBackRightReq req) {
        return processRollback(req, new DomainServiceContext(GoodsInstanceDo.class, "rollBackRight"));
    }

    /**
     * 退货资格查询接口
     *
     * @param req 请求体
     * @return Mono<QueryReturnSubscriptionResp>
     */
    @Override
    public Mono<QueryReturnSubscriptionResp> queryReturnSubscription(QueryReturnSubscriptionReq req) {
        try {
            QueryUserCondition condition = new QueryUserCondition();
            condition.setAccount(req.getAccount());
            UserDo userDo = userDomainService.queryUserByCondition(condition);
            OrderDo orderDo = orderDomainService.queryOrderByUserId(req.getOrderID(), userDo.getUserId());
            if (null == orderDo) {
                log.error(ErrorCode.ORDER_NOT_EXISTS);
                throw new ServiceException(ErrorCode.ORDER_NOT_EXISTS, ErrorCode.ORDER_NOT_EXISTS_MESSAGE);
            }
            GoodsDo goodsDo = goodsDomainService.queryGoodsByGoodsId(orderDo.getGoodsId());
            QueryGoodsInstanceExtendCondition queryGoodsInstanceExtendCondition = new QueryGoodsInstanceExtendCondition();
            queryGoodsInstanceExtendCondition.setUserId(userDo.getUserId());
            queryGoodsInstanceExtendCondition.setGoodsId(Collections.singletonList(goodsDo.getGoodsId()));
            queryGoodsInstanceExtendCondition.setIsExpired(ExpireEnum.ALL.getState());
            List<GoodsInstanceDo> goodsInstanceExtendDos = goodsInstanceExtendDomainService.getGoodsInstanceExtendDos(queryGoodsInstanceExtendCondition, "query");
            if (CollUtil.isEmpty(goodsInstanceExtendDos)) {
                log.error("method subscribeServiceImpl.checkRollBackRight fail,parentContractBasicInfo is not exist!");
                throw new ServiceException(ErrorCode.SUBCONTRACT_NOT_EXISTS, ErrorCode.SUBCONTRACT_NOT_EXISTS_MESSAGE);
            }
            return checkRollBackRight(req, userDo, goodsInstanceExtendDos.get(0), goodsDo);
        } catch (ServiceException e) {
            QueryReturnSubscriptionResp queryReturnSubscriptionResp = new QueryReturnSubscriptionResp();
            String message = mappingVsboExceptionMessage(e);
            queryReturnSubscriptionResp.setAccount(req.getAccount());
            queryReturnSubscriptionResp.setReturnStatus(NOT_ABILITY_RETURN);
            queryReturnSubscriptionResp.setReturnDesc(message);
            return Mono.just(queryReturnSubscriptionResp);
        }
    }

    /**
     * 映射vsbo的异常信息为用户化语言
     *
     * @param e 异常信息
     * @return 返回用户化异常语言
     */
    private String mappingVsboExceptionMessage(ServiceException e) {
        switch (e.getErrorCode()) {
            case ErrorCode.ORDER_NOT_EXISTS:
                return ErrorCode.ORDER_NOT_EXISTS_MESSAGE_CN;
            case ErrorCode.CONTRACT_NOT_EXISTS_MESSAGE:
                return ErrorCode.PRODUCT_NOT_EXISTS_MESSAGE_CN;
            case ErrorCode.SUBCONTRACT_NOT_EXISTS:
                return ErrorCode.SUBCONTRACT_NOT_EXISTS_MESSAGE_CN;
            case ErrorCode.SUBSCRIPTION_HAS_BEEN_UNSUBSCRIBED_CODE:
                return ErrorCode.SUBSCRIPTION_HAS_BEEN_UNSUBSCRIBED_DESC_CN;
            case ErrorCode.BONUS_POINTS_PAY_NOT_UNSUB_CODE:
                return ErrorCode.BONUS_POINTS_PAY_NOT_UNSUB_DESC_CN;
            case ErrorCode.UNSUBSCRIBE_FAIL_GOTONE:
                return ErrorCode.UNSUB_FAIL_DESC_CN;
            default:
                return e.getMessage();

        }
    }

    /**
     * 权益包激活通知
     *
     * @param activeRightsPackageReq 请求
     * @return null
     */
    @Override
    public Mono<Void> activeRightsPackage(ActiveRightsPackageReq activeRightsPackageReq) {
        return Mono.defer(() -> Mono.just(activeRightsPackageReq)).<Mono<Object>>handle((req, sink) -> {
            QueryGoodsInstanceExtendCondition goodsInstanceExtendCondition = new QueryGoodsInstanceExtendCondition();
            goodsInstanceExtendCondition.setUserId(activeRightsPackageReq.getUserId());
            goodsInstanceExtendCondition.setGoodsInstanceId(activeRightsPackageReq.getSubscriptionId());
            List<GoodsInstanceDo> goodsInstanceExtendDos = goodsInstanceExtendDomainService.getGoodsInstanceExtendDos(goodsInstanceExtendCondition, "activeRightsPackage");
            if (CollUtil.isEmpty(goodsInstanceExtendDos)) {
                log.error("method subscribeServiceImpl.activeRightsPackage fail,goodsInstanceExtendDo is not exist!");
                sink.error(new ServiceException(ErrorCode.SUBCONTRACT_NOT_EXISTS, ErrorCode.SUBCONTRACT_NOT_EXISTS_MESSAGE));
                return;
            }
            GoodsInstanceDo goodsInstanceExtendDo = goodsInstanceExtendDos.stream().findFirst().orElseThrow(() -> new ServiceException(ErrorCode.SUBCONTRACT_NOT_EXISTS, ErrorCode.SUBCONTRACT_NOT_EXISTS_MESSAGE));
            ParkActiveEnum byRightsStatus = ParkActiveEnum.getByRightsStatus(activeRightsPackageReq.getRightsStatus());
            Assert.notNull(byRightsStatus, "rightsStatus is not exist");
            goodsInstanceExtendDo.setRightsStatusEnum(byRightsStatus.getRightsStatusEnum());
            DomainServiceContext serviceContext = new DomainServiceContext(GoodsInstanceDo.class, UPDATE_GOODS_INSTANCE_EXTEND);
            serviceContext.putInstance(goodsInstanceExtendDo);
            serviceContext.writeAndFlush();
            sink.next(Mono.empty());
        }).then();
    }

    /**
     * 订购关系查询
     *
     * @param queryRightsSubscribeRelationReq 请求体
     * @return Mono<QueryContractListSubscriptionResp>
     */
    @Override
    public Mono<QueryRightsSubscribeRelationResp> queryRightsSubscribeRelation(QueryRightsSubscribeRelationReq queryRightsSubscribeRelationReq) {
        return Mono.defer(() -> Mono.just(queryRightsSubscribeRelationReq)).flatMap(req -> {
            // 查询用户信息
            QueryUserCondition queryUserCondition = new QueryUserCondition();
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(req.getRawAccount())) {
                queryUserCondition.setAccount(req.getRawAccount());
            } else if (org.apache.commons.lang3.StringUtils.isNotEmpty(req.getUserDomainId())) {
                queryUserCondition.setUserDomainId(req.getUserDomainId());
            } else {
                return Mono.error(new ServiceException(ErrorCode.PARAMETER_CHECK_ERROR, ErrorCode.PARAMETER_CHECK_ERROR_MESSAGE));
            }
            // 查询用户数据
            UserDo userDo = userDomainService.queryUserByCondition(queryUserCondition);

            QueryGoodsInstanceExtendCondition queryGoodsInstanceExtendCondition = new QueryGoodsInstanceExtendCondition();
            queryGoodsInstanceExtendCondition.setSaleType(GoodsSalesTypeEnum.RIGHTS.getType());
            queryGoodsInstanceExtendCondition.setUserId(userDo.getUserId());
            queryGoodsInstanceExtendCondition.setGoodsId(req.getContractIDList());
            String isQueryExpired = ServiceUtil.getParameter(req.getNamedParameters(), MapKey.IS_QUERY_EXPIRED);
            final String rightsType = ServiceUtil.getParameter(req.getNamedParameters(), MapKey.RIGHTS_TYPE);
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(rightsType)) {
                final List<String> rightsTypes = Arrays.asList(rightsType.split("\\,"));
                queryGoodsInstanceExtendCondition.setRightsType(rightsTypes);
            }
            String subscriptionId = ServiceUtil.getParameter(req.getNamedParameters(), MapKey.SUBSCRIPTION_ID_V2);
            if (StringUtils.hasLength(subscriptionId)) {
                queryGoodsInstanceExtendCondition.setGoodsInstanceId(subscriptionId);
            }
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(isQueryExpired)) {
                queryGoodsInstanceExtendCondition.setIsExpired(ExpireEnum.ALL.getState());
            } else {
                queryGoodsInstanceExtendCondition.setStateList(
                        Arrays.asList(SubscribeTypeEnum.SUBSCRIBE_TYPE_ENUM.getCode().toString(),
                                SubscribeTypeEnum.UN_SUBSCRIBE_TYPE_ENUM.getCode().toString(),
                                SubscribeTypeEnum.PAUSE_TYPE_ENUM.getCode().toString()));
            }
            queryGoodsInstanceExtendCondition.setPageCondition(new PageCondition(1, 1000));
            // 查询父产品的对应的商品实例
            List<GoodsInstanceDo> goodsInstanceExtendDos = goodsInstanceExtendDomainService.getGoodsInstanceExtendDos(
                    queryGoodsInstanceExtendCondition, "queryRightsSubscribeRelation");
            if (CollectionUtils.isEmpty(goodsInstanceExtendDos)) {
                return Mono.empty();
            }
            // 未及时暂停到的数据，过期时间设为当前
            Date now = new Date();
            goodsInstanceExtendDos.forEach(goodsInstanceExtendDo -> {
                if (PayWayEnum.isThirdPayWay(goodsInstanceExtendDo.getPayWay()) &&
                        goodsInstanceExtendDo.getRenewTime() != null && goodsInstanceExtendDo.getRenewTime().before(now)) {
                    goodsInstanceExtendDo.setEffectiveEndTime(now);
                    goodsInstanceExtendDo.setStateEnum(GoodsInstanceStateEnum.PAUSE);
                }
            });

            // 构建商品缓存
            Map<String, GoodsDo> goodsCache = goodsInstanceDomainService.buildGoodsCacheMap(goodsInstanceExtendDos
                    .stream()
                    .map(GoodsInstanceDo::getGoodsId)
                    .collect(Collectors.toList()));
            // 根据权益组进行分组，再根据 effectiveEndTime 进行升序排序，拼接每一条首尾的数据（effectiveStartTime，effectiveEndTime）
            Map<String, List<GoodsInstanceDo>> goodsInstanceExtendDoMap = goodsInstanceExtendDos.stream()
                    .collect(Collectors.groupingBy(GoodsInstanceDo::getTimePlanGoodsGroupId));
            List<RightsSubscription> goodsInstanceExtendDosResult = new ArrayList<>();
            // 如果是商品包产品
            goodsInstanceExtendDoMap.forEach((timePlanGoodsGroupId, goodsInstanceExtendList) -> {
                goodsInstanceExtendDosResult.addAll(buildRightsSubscriptionList(goodsInstanceExtendList, userDo, goodsCache));
            });

            fillChildGoodInfo(goodsInstanceExtendDosResult, goodsInstanceExtendDos, userDo, goodsCache);
            fillParkChildGoodInfo(goodsInstanceExtendDosResult, goodsInstanceExtendDos, userDo, goodsCache);
            return Mono.just(new QueryRightsSubscribeRelationResp(goodsInstanceExtendDosResult, goodsInstanceExtendDos.size()));
        }).switchIfEmpty(Mono.just(new QueryRightsSubscribeRelationResp(null, 0)));
    }


    /**
     * 查101会员的订购关系
     * 同一个权益组id，不进行叠加
     * 查询订购关系的活动产品Id
     *
     * @return 订购关系查询结果
     */
    @Override
    public Mono<QueryActivitySubScriptionResp> queryActivitySubScription(QueryActivitySubScriptionReq req) {
        return Mono.defer(() -> Mono.just(buildQueryGoodsInstanceConditionActivity(req)))
                .map(queryGoodsInstanceReq -> {
                    UserDo userDo = goodsInstanceDomainService.getAndCheckUser(queryGoodsInstanceReq.getPhoneNumber(), queryGoodsInstanceReq.getUserDomainId());
                    List<QueryGoodsInstanceCondition> queryGoodsInstanceConditions = goodsInstanceDomainService.buildQueryGoodsInstanceConditionList(userDo, queryGoodsInstanceReq);

                    return buildQueryActivitySubScriptionResp(
                            goodsInstanceDomainService.queryContractListSubscription(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions),
                            goodsInstanceDomainService.queryContractListSubscriptionCount(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions));
                });
    }

    /**
     * 查sub订购
     * 同一个权益组id，不进行叠加
     * 过期也查询
     *
     * @return 订购关系查询结果
     */
    @Override
    public Mono<QueryConditionSubScriptionResp> queryConditionSubscription(QueryConditionSubScriptionReq queryConditionSubScriptionReq) {
        return Mono.defer(() -> Mono.just(buildQueryGoodsInstanceConditionV2(queryConditionSubScriptionReq)))
                .map(queryGoodsInstanceReq -> {
                    UserDo userDo = UserServiceFacade.getUserFromDbByMsisdn(queryConditionSubScriptionReq.getAccount());
                    if (userDo == null) {
                        log.error("[APP] user not found. account: {}", queryConditionSubScriptionReq.getAccount());
                        throw new ServiceException(ResultCodeEnum.USER_NOT_FOUND);
                    }

                    List<QueryGoodsInstanceCondition> queryGoodsInstanceConditions = goodsInstanceDomainService.buildQueryGoodsInstanceConditionListV2(userDo, queryGoodsInstanceReq);

                    return buildQueryConditionSubScriptionResp(
                            goodsInstanceDomainService.queryContractListSubscriptionV2(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions),
                            goodsInstanceDomainService.queryContractListSubscriptionCount(queryGoodsInstanceReq, userDo, queryGoodsInstanceConditions));
                });
    }

    /**
     * 查询101和103的subscription
     *
     * @return 订购关系查询结果
     */
    @Override
    public Mono<GetSubscriptionByOrderIdResp> getSubscriptionByOrderId(GetSubscriptionByOrderIdReq req) {
        // 查询用户信息
        return Mono.defer(() -> Mono.just(buildGetSubscriptionByOrderIdCondition(req))).map(getSubscriptionByOrderIdReq -> buildGetSubscriptionByOrderIdResp(goodsInstanceDomainService.queryVipMemberContractSubVO(getSubscriptionByOrderIdReq)));
    }


    /**
     * 设置查询商品实例条件活动查询接口
     *
     * @param req
     * @return
     */
    private QueryGoodsInstanceReq buildGetSubscriptionByOrderIdCondition(GetSubscriptionByOrderIdReq req) {
        QueryGoodsInstanceReq queryGoodsInstanceReq = new QueryGoodsInstanceReq();

        queryGoodsInstanceReq.setPhoneNumber(req.getAccount());
        queryGoodsInstanceReq.setUserDomainId(req.getUserDomainId());
        queryGoodsInstanceReq.setOrderId(req.getOrderId());

        queryGoodsInstanceReq.setQueryPaused(true);

        queryGoodsInstanceReq.setQueryExpired(true);

        PageCondition pageCondition = new PageCondition();
        pageCondition.setPageNo(1);
        pageCondition.setPageSize(100);
        queryGoodsInstanceReq.setPageCondition(pageCondition);

        return queryGoodsInstanceReq;
    }


    /**
     * 设置查询商品实例条件活动查询接口
     *
     * @param req 请求体
     * @return 返回请求条件
     */
    private QueryGoodsInstanceReq buildQueryGoodsInstanceConditionActivity(QueryActivitySubScriptionReq req) {
        QueryGoodsInstanceReq queryGoodsInstanceReq = new QueryGoodsInstanceReq();

        queryGoodsInstanceReq.setPhoneNumber(req.getRawAccount());
        queryGoodsInstanceReq.setUserDomainId(req.getUserDomainId());
        // 判断是多个商品类型还是单独商品类型
        if (StringUtils.hasLength(req.getProductType()) && req.getProductType().contains(SymbolConstant.COMMA)) {
            ArrayList<GoodsSalesTypeEnum> goodsSalesTypeEnums = new ArrayList<>();
            buildGoodsSalesTypeEnums(req.getProductType(), goodsSalesTypeEnums);
            queryGoodsInstanceReq.setGoodsSalesTypeEnumList(goodsSalesTypeEnums);
        } else {
            queryGoodsInstanceReq.setGoodsSalesType(GoodsSalesTypeEnum.fromType(req.getProductType()));
        }
        queryGoodsInstanceReq.setMergeByTimePlanGoodsGroupId(false);

        List<NamedParameter> extensionInfo = req.getExtensionInfo();
        if (!CollectionUtils.isEmpty(extensionInfo)) {

            if (QUERY_PAUSED_ON.equals(ParameterUtil.getParameter(extensionInfo, IS_QUERY_PAUSED))) {
                queryGoodsInstanceReq.setQueryPaused(true);
            }

            if (QUERY_EXPIRED_ON.equals(ParameterUtil.getParameter(extensionInfo, IS_QUERY_EXPIRED))) {
                queryGoodsInstanceReq.setQueryExpired(true);
            }

            String rightTypeList = ParameterUtil.getParameter(extensionInfo, RIGHTS_TYPE);
            if (StringUtils.hasLength(rightTypeList)) {
                queryGoodsInstanceReq.setRightsTypeList(Arrays.asList(rightTypeList.split(",")));
            }

        }
        PageCondition pageCondition = new PageCondition();
        if (req.getPageNum() != null && req.getPageSize() != null) {
            pageCondition.setPageNo(req.getPageNum());
            pageCondition.setPageSize(req.getPageSize());
        } else {
            pageCondition.setPageNo(1);
            pageCondition.setPageSize(100);
        }
        queryGoodsInstanceReq.setPageCondition(pageCondition);

        return queryGoodsInstanceReq;
    }

    /**
     * 构建商品销售类型枚举列表
     *
     * @param productType         产品类型入参
     * @param goodsSalesTypeEnums 商品销售类型枚举列表
     */
    private void buildGoodsSalesTypeEnums(String productType, ArrayList<GoodsSalesTypeEnum> goodsSalesTypeEnums) {
        String[] productTypes = productType.split(",");
        for (String goodsSalesType : productTypes) {
            GoodsSalesTypeEnum goodsSalesTypeEnum = GoodsSalesTypeEnum.fromType(goodsSalesType);
            if (goodsSalesTypeEnum != null) {
                goodsSalesTypeEnums.add(goodsSalesTypeEnum);
            }
        }
    }


    /**
     * 设置查询商品实例条件condition查询接口
     *
     * @param req
     * @return
     */
    private QueryGoodsInstanceReq buildQueryGoodsInstanceConditionV2(QueryConditionSubScriptionReq req) {
        QueryGoodsInstanceReq queryGoodsInstanceReq = new QueryGoodsInstanceReq();

        queryGoodsInstanceReq.setPhoneNumber(req.getRawAccount());
        queryGoodsInstanceReq.setUserDomainId(req.getUserDomainId());
        queryGoodsInstanceReq.setMergeByTimePlanGoodsGroupId(false);

        queryGoodsInstanceReq.setQueryPaused(Boolean.TRUE);

        queryGoodsInstanceReq.setQueryExpired(Boolean.TRUE);


        if (CollUtil.isNotEmpty(req.getGoodsIdList())) {
            queryGoodsInstanceReq.setGoodsIdList(req.getGoodsIdList());
        }


        if (CollUtil.isNotEmpty(req.getSubIdList())) {
            queryGoodsInstanceReq.setGoodsInstanceIdList(req.getSubIdList());
        }

        if (CollUtil.isNotEmpty(req.getOrderIdList())) {
            queryGoodsInstanceReq.setOrderIdList(req.getOrderIdList());
        }

        return queryGoodsInstanceReq;
    }

    /**
     * 构建返回列表
     * 畅影的不合并记录，非畅影的合并记录
     *
     * @param goodsInstanceExtendList
     * @param userDo
     * @param goodsCache
     * @return
     */
    private List<RightsSubscription> buildRightsSubscriptionList(List<GoodsInstanceDo> goodsInstanceExtendList, UserDo userDo, Map<String, GoodsDo> goodsCache) {

        if (CollectionUtils.isEmpty(goodsInstanceExtendList)) {
            return Collections.emptyList();
        }

        List<RightsSubscription> rightsSubscriptionList = new ArrayList<>();
        goodsInstanceExtendList.sort(Comparator.comparing(GoodsInstanceDo::getEffectiveEndTime));

        GoodsInstanceDo effectiveGoodsInstance = null;
        Date now = new Date();
        List<String> allOrderIdList = new ArrayList<>(goodsInstanceExtendList.size());
        for (GoodsInstanceDo goodsInstanceExtendDo : goodsInstanceExtendList) {
            if (now.before(goodsInstanceExtendDo.getEffectiveEndTime())
                    && now.after(goodsInstanceExtendDo.getEffectiveStartTime())
                    && Objects.isNull(effectiveGoodsInstance)) {
                effectiveGoodsInstance = goodsInstanceExtendDo;
            }
            allOrderIdList.add(goodsInstanceExtendDo.getOrderId());
        }
        // 获取工单属性
        List<WorkOrderDo> workOrderList = ActivationServiceFacade.getWorkOrderAttrList(userDo.getUserId(),
                allOrderIdList, Collections.singletonList(WorkAttrCodeConstant.RAI_SUB_FAIL_BODY));
        Map<String, List<WorkOrderAttrDo>> orderId2workAttrsMap = CollUtil.emptyIfNull(workOrderList).stream()
                .filter(work -> CollUtil.isNotEmpty(work.getWorkOrderAttrDoList()))
                .collect(Collectors.toMap(WorkOrderDo::getOrderId, WorkOrderDo::getWorkOrderAttrDoList,
                        (oldVal, newVal) -> newVal));

        for (GoodsInstanceDo goodsInstanceExtendDo : goodsInstanceExtendList) {

            RightsSubscription rightsSubscription = new RightsSubscription();
            rightsSubscriptionList.add(rightsSubscription);

            GoodsInstanceDo goodsInstanceResult = goodsInstanceExtendDo;
            //如果不是畅影，则合并记录，所返回信息为当前生效中商品实例的信息
            if (!isChangYingGoodsInstance(goodsInstanceExtendDo, goodsCache)) {
                goodsInstanceResult = effectiveGoodsInstance != null ? effectiveGoodsInstance : goodsInstanceExtendDo;
            }

            rightsSubscription.setAccount(userDo.getMsisdn());
            rightsSubscription.setEndTime(goodsInstanceResult.getEffectiveEndTime());

            if (goodsInstanceExtendDo.getRightsStatusEnum() != null
                    && OPENP_FAILED_RIGHTS_STSUS_LIST.contains(goodsInstanceExtendDo.getRightsStatusEnum())) {
                // 填充工单属性
                List<WorkOrderAttrDo> workOrderAttrDoList = orderId2workAttrsMap.get(goodsInstanceExtendDo.getOrderId());
                for (WorkOrderAttrDo attrDo : CollUtil.emptyIfNull(workOrderAttrDoList)) {
                    if (WorkAttrCodeConstant.RAI_SUB_FAIL_BODY.equals(attrDo.getAttrCode())) {
                        rightsSubscription.setRightsResultCode(attrDo.getAttrVal());
                        break;
                    }
                }
            }

            rightsSubscription.setRightsOutAccountID(goodsInstanceResult.getResourceId());
            rightsSubscription.setContractID(goodsInstanceResult.getGoodsId());
            rightsSubscription.setStatus(Optional.ofNullable(goodsInstanceResult.getStateEnum()).map(state -> String.valueOf(state.getState())).orElse(null)); // 订购状态
            rightsSubscription.setPrice(Optional.ofNullable(goodsInstanceResult.getDealPrice()).map(String::valueOf).orElse(null));
            rightsSubscription.setOrderID(goodsInstanceResult.getOrderId());
            rightsSubscription.setSubscriptionID(goodsInstanceResult.getGoodsInstanceId());
            rightsSubscription.setRightsStatus(Optional.ofNullable(goodsInstanceResult.getRightsStatusEnum()).map(state -> String.valueOf(state.getStatus())).orElse(null));
            rightsSubscription.setRightsType(goodsInstanceResult.getRightsType());
            rightsSubscription.setChargingType(goodsInstanceResult.getChargeType());
            rightsSubscription.setPayWay(goodsInstanceResult.getPayWay());
            rightsSubscription.setParentSubscriptionID(goodsInstanceResult.getGoodsPackageInstanceId());
            rightsSubscription.setParentProductID(goodsInstanceResult.getGoodsPackageId());
            rightsSubscription.setChannelID(goodsInstanceResult.getChannelId());
            rightsSubscription.setSubChannelID(goodsInstanceResult.getSubChannelId());
            rightsSubscription.setSubTime(goodsInstanceResult.getSubTime());
            rightsSubscription.setStartTime(goodsInstanceResult.getEffectiveStartTime());
            rightsSubscription.setSubChannel(getSubChannel(goodsInstanceResult));

            if (Constant.BOOS_OREDERID_LENGTH == goodsInstanceResult.getOrderId().length()) {
                rightsSubscription.setIsUnSubscribe(Constant.ISUNSUBSCRIBE_NO);
            }

            List<NamedParameter> params = new ArrayList<>();
            ParameterUtil.addParameter(params, PARENT_PRODUCT_ID, goodsInstanceResult.getGoodsPackageId());
            ParameterUtil.addParameter(params, RENEW_DATE, Optional.ofNullable(goodsInstanceResult.getRenewTime()).map(renewTime -> DateUtil.format(TimeUtil.getDay(renewTime, -1), DatePattern.NORM_DATETIME_PATTERN)).orElse(null));

            GoodsDo goodsDo = goodsCache.get(goodsInstanceResult.getGoodsId());
            if (goodsDo == null) {
                log.warn("[APP] goodsDo not found. goodsId: {}", goodsInstanceResult.getGoodsId());
            } else {
                rightsSubscription.setProductName(goodsDo.getGoodsName());
                rightsSubscription.setProductType(Optional.ofNullable(goodsDo.getGoodsSalesType()).map(salesType -> String.valueOf(salesType.getType())).orElse(null));

                if (!CollectionUtils.isEmpty(goodsDo.getNotifyServiceList()) && StringUtils.hasLength(goodsDo.getNotifyServiceList().get(0).getOuterProductId())) {
                    RaiNotifyEquityInfo raiNotifyEquityInfo = JsonUtil.fromJson(goodsDo.getNotifyServiceList().get(0).getOuterProductId(), RaiNotifyEquityInfo.class);
                    if (raiNotifyEquityInfo != null) {
                        rightsSubscription.setRightsOutProductID(raiNotifyEquityInfo.getProdId());
                    }
                }

                if (goodsDo.getGoodsExt() != null) {
                    rightsSubscription.setRightModel(goodsDo.getGoodsExt().getRightsModel());
                    rightsSubscription.setMemberLevel(goodsDo.getGoodsExt().getMemberLevel());
                    if (goodsDo.getGoodsExt().getDisplayImageSelection() != null) {
                        ParameterUtil.addParameter(params, DISPLAY_IMAGE_SELECTION,
                                goodsDo.getGoodsExt().getDisplayImageSelection());
                    }

                }
            }
            rightsSubscription.setNamedParameters(params);

            //如果是商品包产品并且商品的rightsModel不是畅影的, 则合并记录
            if (!isChangYingGoodsInstance(goodsInstanceResult, goodsCache)) {
                rightsSubscription.setEndTime(goodsInstanceExtendList.get(goodsInstanceExtendList.size() - 1).getEffectiveEndTime());
                break;
            } else {
                rightsSubscription.setEndTime(goodsInstanceResult.getEffectiveEndTime());
            }

        }

        return rightsSubscriptionList;
    }

    /**
     * 是否畅影
     *
     * @param goodsInstanceExtendDo
     * @param goodsCache
     * @return
     */
    private boolean isChangYingGoodsInstance(GoodsInstanceDo goodsInstanceExtendDo, Map<String, GoodsDo> goodsCache) {
        GoodsDo goodsDo = goodsCache.get(goodsInstanceExtendDo.getGoodsId());
        if (goodsDo == null) {
            log.warn("[APP] goodsDo not found. goodsId: {}", goodsInstanceExtendDo.getGoodsId());
        } else {

            if (goodsDo.getGoodsExt() != null) {
                if (goodsDo.isPackageCategory() && RightModelConstant.CHANG_YING.equals(goodsDo.getGoodsExt().getRightsModel())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取实例订购渠道
     *
     * @param goodsInstanceExtendDo
     * @return
     */
    private Integer getSubChannel(GoodsInstanceDo goodsInstanceExtendDo) {
        if (CharSequenceUtil.isNotEmpty(goodsInstanceExtendDo.getOrderId()) &&
                Constant.BOOS_OREDERID_LENGTH == goodsInstanceExtendDo.getOrderId().length()) {
            return Constant.BOOS_CHANNEL;
        }
        return Constant.NO_BOOS_CHANNEL;
    }


    /**
     * 补充子商品领取信息（组合产品）
     *
     * @param rightsSubscriptionList  订购记录
     * @param goodsInstanceExtendList 父产品实例记录
     * @param userDo                  用户数据
     * @param goodsCache              父商品缓存数据
     */
    private void fillChildGoodInfo(List<RightsSubscription> rightsSubscriptionList, List<GoodsInstanceDo> goodsInstanceExtendList,
                                   UserDo userDo, Map<String, GoodsDo> goodsCache) {
        if (CollectionUtils.isEmpty(rightsSubscriptionList) || CollectionUtils.isEmpty(goodsInstanceExtendList)) {
            return;
        }

        // 收集所有子商品Id
        Map<String, Map<String, List<String>>> childGoodsOfGoodsPackageInstanceMap = new HashMap<>();
        // 停车类商品Id，用于补全子商品领取信息
        Set<String> parkGoodsIds = new HashSet<>();
        for (GoodsInstanceDo goodsInstanceExtendDo : goodsInstanceExtendList) {

            GoodsDo goodsDo = goodsCache.get(goodsInstanceExtendDo.getGoodsId());
            if (goodsDo == null || !goodsDo.isPackageCategory() || !(goodsDo instanceof GoodsPackageDo)) {
                continue;
            }

            GoodsPackageDo goodsPackageDo = (GoodsPackageDo) goodsDo;
            if (CollectionUtils.isEmpty(goodsPackageDo.getChildGoods(true))) {
                continue;
            }

            // 获取权益组key（父产品id:激活权益组id）对应的子商品的goodsId
            //groupId -> childGoodsIdList
            Map<String, List<String>> childGoodsGroupMap = goodsPackageDo.getChildGoods(true).stream()
                    .filter(childGoodsDo -> !CollectionUtils.isEmpty(childGoodsDo.getGoodsList()) && MAN_ACTIVE.equals(childGoodsDo.getGoodsActiveType()))
                    .collect(Collectors.toMap(ChildGoodsGroupDo::getGoodsGroupId, childGoodsDo ->
                            childGoodsDo.getGoodsList().stream().map(GoodsDo::getGoodsId).distinct().collect(Collectors.toList())));

            if (!CollectionUtils.isEmpty(childGoodsGroupMap)) {
                childGoodsOfGoodsPackageInstanceMap.put(goodsInstanceExtendDo.getGoodsInstanceId(), childGoodsGroupMap);
            }
            // 判断是否为停车产品
            GoodsDo parkGoods = goodsPackageDo.getChildGoodsByProductId(ProductDo.ProductId.PARK_RIGHTS);
            if (parkGoods != null) {
                parkGoodsIds.add(parkGoods.getGoodsId());
            }
        }

        if (CollectionUtils.isEmpty(childGoodsOfGoodsPackageInstanceMap)) {
            return;
        }

        Date now = new Date();
        // 获取子商品的激活信息
        DomainServiceContext validateServiceContext = new DomainServiceContext(QUERY_SUB_VALIDATE_DETAIL_INFO);
        SubRuleValidateCondition subRuleValidateCondition = new SubRuleValidateCondition();
        subRuleValidateCondition.setChildGoodsOfGoodsPackageInstanceMap(childGoodsOfGoodsPackageInstanceMap);
        subRuleValidateCondition.setPhoneNumber(userDo.getMsisdn());
        subRuleValidateCondition.setSubTime(now);
        subRuleValidateCondition.setTriggerTimming(ValidateTriggerTimmingEnum.SUB);
        List<SubRuleValidateResultDo> subRuleValidateResultList = validateServiceContext.read(subRuleValidateCondition, SubRuleValidateResultDo.class);

        if (CollectionUtils.isEmpty(subRuleValidateResultList)) {
            return;
        }
        // 过滤停车产品的id
        Map<String, List<SubRuleValidateResultDo>> subRuleValidateResultCache = subRuleValidateResultList
                .stream()
                .filter(subRuleValidateResultDo -> !parkGoodsIds.contains(subRuleValidateResultDo.getGoodsId()))
                .collect(Collectors.groupingBy(SubRuleValidateResultDo::getGoodsPackageInstanceId));


        for (RightsSubscription rightsSubscription : rightsSubscriptionList) {
            //已过期和未生效的不构建领取记录信息
            if (now.after(rightsSubscription.getEndTime()) || now.before(rightsSubscription.getStartTime())) {
                continue;
            }
            List<SubRuleValidateResultDo> subRuleValidateResultDoList = subRuleValidateResultCache.get(rightsSubscription.getSubscriptionID());

            if (CollectionUtils.isEmpty(subRuleValidateResultDoList)) {
                continue;
            }
            Set<String> uniqueGoodsIdSet = new HashSet<>(subRuleValidateResultDoList.size());
            List<ChildProductActiveInfo> childProductActiveInfoList = subRuleValidateResultDoList.stream()
                    .filter(subRuleValidateResultDo -> uniqueGoodsIdSet.add(subRuleValidateResultDo.getGoodsId() + subRuleValidateResultDo.getChildGoodsActiveGroupId()))
                    .map(this::convertToChildProductActiveInfo).collect(Collectors.toList());


            List<NamedParameter> params = rightsSubscription.getNamedParameters();
            if (params == null) {
                params = new ArrayList<>();
                rightsSubscription.setNamedParameters(params);
            }
            // 填充子权益数据
            ParameterUtil.addParameter(params, CHILD_PRODUCTS, JsonUtil.toJson(childProductActiveInfoList));
        }


    }

    /**
     * 补充子商品领取信息（停车）
     *
     * @param rightsSubscriptionList
     * @param goodsInstanceExtendList
     * @param userDo
     * @param goodsCache
     */
    private void fillParkChildGoodInfo(List<RightsSubscription> rightsSubscriptionList, List<GoodsInstanceDo> goodsInstanceExtendList, UserDo userDo, Map<String, GoodsDo> goodsCache) {

        if (CollectionUtils.isEmpty(rightsSubscriptionList) || CollectionUtils.isEmpty(goodsInstanceExtendList)) {
            return;
        }

        HashMap<String, GoodsDo> parkGoodsCacheMap = new HashMap<>();
        //获取父商品实例id列表
        List<String> goodsPackageIds = goodsInstanceExtendList.stream().filter(goodsInstance -> {
            GoodsDo goodsDo = goodsCache.get(goodsInstance.getGoodsId());
            if (goodsDo instanceof GoodsPackageDo
                    && ((GoodsPackageDo) goodsDo).getChildGoodsByProductId(ProductDo.ProductId.PARK_RIGHTS) != null) {
                parkGoodsCacheMap.putAll(((GoodsPackageDo) goodsDo).getChildGoodsListByProductId(ProductDo.ProductId.PARK_RIGHTS).stream()
                        .collect(Collectors.toMap(GoodsDo::getGoodsId, goods -> goods)));
                return true;
            }

            return false;
        }).map(GoodsInstanceDo::getGoodsInstanceId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(parkGoodsCacheMap)) {
            return;
        }

        //获取父商品实例记录
        Date now = new Date();
        QueryGoodsInstanceCondition condition = new QueryGoodsInstanceCondition();
        condition.setUserId(userDo.getUserId());
        condition.setEffectiveEndTimeStart(now);
        condition.setEffectiveStartTimeEnd(now);
        condition.setStateList(Arrays.asList(GoodsInstanceStateEnum.NORMAL));
        condition.setGoodsPackageInstanceIdList(goodsPackageIds);
        condition.setGoodsIdList(new ArrayList<>(parkGoodsCacheMap.keySet()));
        condition.setExcludeAccounting(true);
        DomainServiceContext memberDomainContext = new DomainServiceContext(GoodsInstanceServiceId.QUERY_GOODS_INSTANCE);
        List<GoodsInstanceDo> goodsInstanceList = memberDomainContext.read(condition, GoodsInstanceDo.class);

        if (CollectionUtils.isEmpty(goodsInstanceList)) {
            return;
        }
        // 根据父商品实例id分组
        Map<String, Map<String, List<GoodsInstanceDo>>> packageId2GoodsId2InstanceMap = new HashMap<>();
        List<String> orderIdList = new ArrayList<>(goodsPackageIds.size());
        for (GoodsInstanceDo goodsInstanceDo : goodsInstanceList) {
            Map<String, List<GoodsInstanceDo>> goodsId2InstanceMap = packageId2GoodsId2InstanceMap
                    .getOrDefault(goodsInstanceDo.getGoodsPackageInstanceId(), new HashMap<>());
            List<GoodsInstanceDo> instanceList = goodsId2InstanceMap.getOrDefault(goodsInstanceDo.getGoodsId(), new ArrayList<>());
            instanceList.add(goodsInstanceDo);
            goodsId2InstanceMap.put(goodsInstanceDo.getGoodsId(), instanceList);
            packageId2GoodsId2InstanceMap.put(goodsInstanceDo.getGoodsPackageId(), goodsId2InstanceMap);
            // 添加订单id
            orderIdList.add(goodsInstanceDo.getOrderId());
        }
        List<WorkOrderDo> workOrderList = ActivationServiceFacade.getWorkOrderAttrList(userDo.getUserId(),
                orderIdList, Collections.singletonList(WorkAttrCodeConstant.RIGHTS_OUT_ACCOUNT_ID));
        Map<String, List<WorkOrderAttrDo>> orderId2workAttrsMap = CollUtil.emptyIfNull(workOrderList).stream()
                .filter(work -> CollUtil.isNotEmpty(work.getWorkOrderAttrDoList()))
                .collect(Collectors.toMap(WorkOrderDo::getOrderId, WorkOrderDo::getWorkOrderAttrDoList, (oldVal, newVal) -> newVal));

        for (RightsSubscription rightsSubscription : rightsSubscriptionList) {
            Map<String, List<GoodsInstanceDo>> goodsInstancesMapByGoodsId = packageId2GoodsId2InstanceMap.get(rightsSubscription.getSubscriptionID());

            if (CollectionUtils.isEmpty(goodsInstancesMapByGoodsId)) {
                continue;
            }

            //查询对应父商品
            GoodsDo packageGoodsDo = goodsCache.get(rightsSubscription.getContractID());
            GoodsPackageDo goodsPackageDo;
            if (Objects.nonNull(packageGoodsDo) && packageGoodsDo instanceof GoodsPackageDo) {
                goodsPackageDo = (GoodsPackageDo) packageGoodsDo;
            } else {
                goodsPackageDo = null;
            }

            List<ChildProductActiveInfo> childProductActiveInfoList = new ArrayList<>();

            goodsInstancesMapByGoodsId.forEach((goodsId, goodsInstances) -> {
                GoodsDo goodsDo = parkGoodsCacheMap.get(goodsId);
                if (goodsDo == null) {
                    return;
                }

                goodsInstances.forEach(goodsInstance -> {

                    ChildProductActiveInfo childProductActiveInfo = new ChildProductActiveInfo();
                    childProductActiveInfo.setProductId(goodsDo.getGoodsId());
                    childProductActiveInfo.setSubscriptionId(goodsInstance.getGoodsInstanceId());
                    // 填充工单属性
                    List<WorkOrderAttrDo> workAttrsList = orderId2workAttrsMap.getOrDefault(goodsInstance.getOrderId(), Collections.emptyList());
                    for (WorkOrderAttrDo attrDo : workAttrsList) {
                        if (WorkAttrCodeConstant.RIGHTS_OUT_ACCOUNT_ID.equals(attrDo.getAttrCode())) {
                            childProductActiveInfo.setRightsOutAccountId(attrDo.getAttrVal());
                            break;
                        }
                    }

                    if (Objects.nonNull(goodsPackageDo) && Objects.nonNull(goodsPackageDo.getChildGoodsGroupByGoodsId(goodsDo.getGoodsId()))) {
                        ChildGoodsGroupDo childGoodsGroupByGoodsId = goodsPackageDo.getChildGoodsGroupByGoodsId(goodsDo.getGoodsId());
                        childProductActiveInfo.setActiveGroupId(childGoodsGroupByGoodsId.getChildGoodsGroupId());
                    }

                    if (RightsStatusEnum.OPEN_SUCCESS.equals(goodsInstance.getRightsStatusEnum())) {
                        childProductActiveInfo.setActiveStatus(String.valueOf(ChildGoodsActiveStateEnum.NOT_ACTIVATED.getState()));
                    } else if (ACTIVE_STATUS_FOR_PARK_GOODS.contains(goodsInstance.getRightsStatusEnum())) {
                        childProductActiveInfo.setActiveStatus(String.valueOf(ChildGoodsActiveStateEnum.IS_ACTIVATED.getState()));
                    }

                    childProductActiveInfoList.add(childProductActiveInfo);

                });

            });

            List<NamedParameter> params = rightsSubscription.getNamedParameters();
            if (params == null) {
                params = new ArrayList<>();
                rightsSubscription.setNamedParameters(params);
            }
            //childProduct扩张参数补充
            String parameter = ParameterUtil.getParameter(params, CHILD_PRODUCTS);
            if (CharSequenceUtil.isNotEmpty(parameter)) {
                List<ChildProductActiveInfo> oldValue = JsonUtil.parseArray(parameter, ChildProductActiveInfo.class);
                List<ChildProductActiveInfo> collectionList = new ArrayList<>(oldValue);
                collectionList.addAll(childProductActiveInfoList);
                ParameterUtil.replaceOrAdd(params, CHILD_PRODUCTS, JsonUtil.toJson(collectionList));
            } else {
                ParameterUtil.addParameter(params, CHILD_PRODUCTS, JsonUtil.toJson(childProductActiveInfoList));
            }
        }
    }


    /**
     * 转换商品领取信息
     *
     * @param subRuleValidateResultDo
     * @return
     */
    private ChildProductActiveInfo convertToChildProductActiveInfo(SubRuleValidateResultDo subRuleValidateResultDo) {
        ChildProductActiveInfo childProductActiveInfo = new ChildProductActiveInfo();
        childProductActiveInfo.setProductId(subRuleValidateResultDo.getGoodsId());
        childProductActiveInfo.setActiveStrategy(
                Optional.ofNullable(subRuleValidateResultDo.getChildGoodsActiveStrategy())
                        .map(activeStrategy -> String.valueOf(activeStrategy.getStrategy()))
                        .orElse(null));
        childProductActiveInfo.setActiveGroupId(subRuleValidateResultDo.getChildGoodsActiveGroupId());
        childProductActiveInfo.setExpireDate(subRuleValidateResultDo.getChildGoodsCurrentActiveCycleEndTime());
        childProductActiveInfo.setActiveStatus(
                Optional.ofNullable(subRuleValidateResultDo.getChildGoodsActiveState())
                        .map(state -> String.valueOf(state.getState()))
                        .orElse(null));

        if (N_CHOOSE_1_STRATEGY.equals(subRuleValidateResultDo.getChildGoodsActiveStrategy())) {
            childProductActiveInfo.setActiveCount(
                    Optional.ofNullable(subRuleValidateResultDo.getChildGoodsMaxActiveCountInGroup())
                            .map(String::valueOf)
                            .orElse(null));
        } else if (FIRST_CYCLE_CHOOSE_1.equals(subRuleValidateResultDo.getChildGoodsActiveStrategy())) {
            childProductActiveInfo.setActiveCount(
                    Optional.ofNullable(subRuleValidateResultDo.getChildGoodsMaxActiveCount())
                            .map(String::valueOf)
                            .orElse(null));

            childProductActiveInfo.setActivableTimeCount(
                    Optional.ofNullable(subRuleValidateResultDo.getActivableTimeCount())
                            .map(String::valueOf)
                            .orElse(null)
            );
            childProductActiveInfo.setActivableTimeType(String.valueOf(subRuleValidateResultDo.getActivableTimeType().getType()));
        } else {
            childProductActiveInfo.setActiveCount(
                    Optional.ofNullable(subRuleValidateResultDo.getChildGoodsMaxActiveCount())
                            .map(String::valueOf)
                            .orElse(null));
        }

        return childProductActiveInfo;
    }


    private Mono<QueryReturnSubscriptionResp> checkRollBackRight(QueryReturnSubscriptionReq req, UserDo userDo, GoodsInstanceDo goodsInstanceExtendDo, GoodsDo goodsDo) {
        return Mono.defer(() -> Mono.just(req)).flatMap(req1 -> {
            QueryReturnSubscriptionResp queryReturnSubscriptionResp = new QueryReturnSubscriptionResp();
            switch (goodsDo.getGoodsSalesType()) {
                case RIGHTS:
                    String rightsModel = goodsDo.getGoodsExt().getRightsModel();
                    if (RightModelConstant.CHANGX_VIP.equals(rightsModel) || RightModelConstant.ONE_PLUS_ONE.equals(rightsModel)) {
                        QueryGoodsInstanceExtendCondition queryGoodsInstanceExtendConditionChild = new QueryGoodsInstanceExtendCondition();
                        queryGoodsInstanceExtendConditionChild.setIsExpired(ExpireEnum.ALL.getState());
                        queryGoodsInstanceExtendConditionChild.setUserId(userDo.getUserId());
                        queryGoodsInstanceExtendConditionChild.setSaleType(Integer.parseInt(RIGHTS.getCode()));
                        queryGoodsInstanceExtendConditionChild.setGoodsPackageInstanceId(goodsInstanceExtendDo.getGoodsInstanceId());
                        List<GoodsInstanceDo> goodsInstanceExtendDosChild = goodsInstanceExtendDomainService.getGoodsInstanceExtendDos(queryGoodsInstanceExtendConditionChild, "query");
                        if (CollUtil.isEmpty(goodsInstanceExtendDosChild)) {
                            fillAbilityReturnOfParent(req, goodsDo, goodsInstanceExtendDo, queryReturnSubscriptionResp);
                            break;
                        }
                        List<GoodsInstanceDo> havedObtains = goodsInstanceExtendDosChild.stream().filter(f -> RightsStatusEnum.INITIALIZATION_SUCCESS.equals(f.getRightsStatusEnum())
                                || RightsStatusEnum.OPEN_SUCCESS.equals(f.getRightsStatusEnum())).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(havedObtains)) {
                            // 将已领取的数据封装进子类
                            fillNotAbilityReturn(queryReturnSubscriptionResp, goodsInstanceExtendDosChild, goodsDo);
                            fillNotAbilityReturnOfParent(req, goodsDo, goodsInstanceExtendDo, queryReturnSubscriptionResp, "已领取外部权益，该订单不能被退货");
                        } else {
                            fillAbilityReturnOfParent(req, goodsDo, goodsInstanceExtendDo, queryReturnSubscriptionResp);
                        }
                    } else {
                        fillAbilityReturnOfParent(req, goodsDo, goodsInstanceExtendDo, queryReturnSubscriptionResp);
                    }
                    break;
                default:
                    fillAbilityReturnOfParent(req, goodsDo, goodsInstanceExtendDo, queryReturnSubscriptionResp);
                    break;
            }
            return Mono.just(queryReturnSubscriptionResp);
        }).doOnError((throwable) -> log.error("rollBackRight error", throwable));
    }

    /**
     * 填充封装响应
     *
     * @param queryReturnSubscriptionResp 查询退货资格响应
     * @param contractSubscriptions       已经领取的权益数据
     * @param goodsDo
     */
    private void fillNotAbilityReturn(QueryReturnSubscriptionResp queryReturnSubscriptionResp, List<GoodsInstanceDo> contractSubscriptions, GoodsDo goodsDo) {
        List<RightsSubscriptionVo> rightsSubscriptions = new ArrayList<>();
        for (GoodsInstanceDo contractSubscription : contractSubscriptions) {
            RightsSubscriptionVo rightsSubscription = new RightsSubscriptionVo();
            rightsSubscription.setContractID(contractSubscription.getGoodsId());
            GoodsDo queryGoodsByGoodsId = goodsDomainService.queryGoodsByGoodsId(contractSubscription.getGoodsId());
            rightsSubscription.setRightsServiceSkuID((String) Objects.requireNonNull(JsonUtil.fromJson(queryGoodsByGoodsId.getNotifyServiceList().get(0).getOuterProductId(), JSONObject.class)).get("prodId"));
            rightsSubscription.setSubTime(DateUtil.format(contractSubscription.getSubTime(), DatePattern.NORM_DATETIME_FORMAT));
            rightsSubscription.setRightsStatus(String.valueOf(contractSubscription.getRightsStatusEnum().getStatus()));
            rightsSubscriptions.add(rightsSubscription);
        }
        queryReturnSubscriptionResp.setRightsSubscriptionList(rightsSubscriptions);
    }

    /**
     * 封装不能退货的响应
     *
     * @param queryReturnSubscriptionReq  查询退货资格请求
     * @param queryReturnSubscriptionResp 查询退货资格响应
     * @param resultDesc                  结果描述
     */
    private void fillNotAbilityReturnOfParent(QueryReturnSubscriptionReq queryReturnSubscriptionReq, GoodsDo goodsDo, GoodsInstanceDo goodsInstanceExtendDo, QueryReturnSubscriptionResp queryReturnSubscriptionResp, String resultDesc) {
        // 获取产品信息
        queryReturnSubscriptionResp.setContractId(goodsDo.getGoodsId());
        queryReturnSubscriptionResp.setEffectiveTime(DateUtil.format(goodsDo.getEffectiveStartTime(), DatePattern.PURE_DATETIME_MS_FORMAT));
        queryReturnSubscriptionResp.setExpiryTime(DateUtil.format(goodsDo.getEffectiveEndTime(), DatePattern.PURE_DATETIME_MS_FORMAT));

        queryReturnSubscriptionResp.setAccount(queryReturnSubscriptionReq.getAccount());
        // 获取父权益信息
        queryReturnSubscriptionResp.setStatus(String.valueOf(goodsInstanceExtendDo.getStateEnum().getState()));
        // 直接设置不可以退货
        queryReturnSubscriptionResp.setReturnStatus(NOT_ABILITY_RETURN);
        queryReturnSubscriptionResp.setReturnDesc(resultDesc);
    }

    /**
     * 封装能退货的响应
     *
     * @param queryReturnSubscriptionReq  查询退货资格请求
     * @param queryReturnSubscriptionResp 查询退货资格响应
     */
    private void fillAbilityReturnOfParent(QueryReturnSubscriptionReq queryReturnSubscriptionReq, GoodsDo goodsDo, GoodsInstanceDo goodsInstanceExtendDo, QueryReturnSubscriptionResp queryReturnSubscriptionResp) {
        queryReturnSubscriptionResp.setAccount(queryReturnSubscriptionReq.getAccount());
        queryReturnSubscriptionResp.setContractId(goodsDo.getGoodsId());
        // 获取父权益信息
        // 设置产品状态
        queryReturnSubscriptionResp.setStatus(String.valueOf(goodsDo.getState().getState()));
        queryReturnSubscriptionResp.setEffectiveTime(DateUtil.format(goodsDo.getEffectiveStartTime(), DatePattern.PURE_DATETIME_MS_FORMAT));
        queryReturnSubscriptionResp.setExpiryTime(DateUtil.format(goodsDo.getEffectiveEndTime(), DatePattern.PURE_DATETIME_MS_FORMAT));

        // 直接设置可以退货
        queryReturnSubscriptionResp.setReturnStatus(Constant.ABILITY_RETURN);
    }

    public Mono<Void> processRollback(RollBackRightReq rollBackRightReq, DomainServiceContext context) {
        QueryUserCondition condition = new QueryUserCondition();
        condition.setAccount(rollBackRightReq.getAccount());
        condition.setUserDomainId(rollBackRightReq.getUserDomainId());
        UserDo userDo = userDomainService.queryUserByCondition(condition);
        OrderDo orderDo = orderDomainService.queryOrderByUserId(rollBackRightReq.getOrderID(), userDo.getUserId());
        if (null == orderDo) {
            log.error(ErrorCode.ORDER_NOT_EXISTS);
            throw new ServiceException(ErrorCode.ORDER_NOT_EXISTS, ErrorCode.ORDER_NOT_EXISTS_MESSAGE);
        }
        GoodsDo goodsDo = goodsDomainService.queryGoodsByGoodsId(orderDo.getGoodsId());
        log.debug("processRollback begin :{}", rollBackRightReq);
        return Mono.defer(() -> Mono.just(rollBackRightReq))
                .flatMap(req -> Mono.justOrEmpty(checkUnSubscribe(context, goodsDo, orderDo.getOrderNo(), userDo.getUserId()))
                        .flatMap(goodsInstanceExtendDo -> preRollBackRightMono(rollBackRightReq.getNamedParameters(), context, goodsDo, goodsInstanceExtendDo)
                                .flatMap(preRollbackResult -> {
                                    log.info("preRollBackRightMono is end, the result is {}", preRollbackResult);
                                    if (Boolean.TRUE.equals(preRollbackResult)) {
                                        goodsInstanceExtendDo.setUnsubWay(Integer.valueOf(CommonConstant.RefundChannelType.ROLLBACK_RIGHT_RECYCLE));
                                        // 如果 preRollBackRight 成功，继续处理
                                        String partialRefund = ServiceUtil.getParameter(rollBackRightReq.getNamedParameters(), Constant.PARTIAL_REFUND);
                                        // 1 代表部分回收
                                        if (!org.apache.commons.lang3.StringUtils.isEmpty(partialRefund) && Constant.ONE_STR.equals(partialRefund)) {
                                            goodsInstanceExtendDo.setUnsubWay(Integer.valueOf(CommonConstant.RefundChannelType.ROLLBACK_RIGHT_PARTIAL_REFUND));
                                        }
                                        return userApplication.rollBackRight(goodsInstanceExtendDo);
                                    } else {
                                        // 如果 preRollBackRight 失败，返回错误 Mono
                                        return Mono.error(new ServiceException("Pre-rollback validation failed"));
                                    }
                                }))).then().then(Mono.fromCallable(() -> null)); // 完成处理，返回 Void Mono
    }


    /**
     * 共有设置查询商品实例条件
     *
     * @param req
     * @return
     */
    private QueryGoodsInstanceReq buildQueryGoodsInstanceConditionCommon(QueryContractListSubscriptionReq req) {
        QueryGoodsInstanceReq queryGoodsInstanceReq = new QueryGoodsInstanceReq();

        queryGoodsInstanceReq.setPhoneNumber(req.getRawAccount());
        queryGoodsInstanceReq.setUserDomainId(req.getUserDomainId());
        queryGoodsInstanceReq.setChargeType(ChargeTypeEnum.fromType(req.getChargeType()));
        queryGoodsInstanceReq.setGoodsSalesType(GoodsSalesTypeEnum.fromType(req.getProductType()));
        queryGoodsInstanceReq.setGoodsInstanceIdList(req.getSubscriptionIDList());
        queryGoodsInstanceReq.setGoodsIdList(req.getContractIDList());
        queryGoodsInstanceReq.setMergeByTimePlanGoodsGroupId(true);

        List<NamedParameter> extensionInfo = req.getExtensionInfo();
        if (!CollectionUtils.isEmpty(extensionInfo)) {

            if (QUERY_PAUSED_ON.equals(ParameterUtil.getParameter(extensionInfo, IS_QUERY_PAUSED))) {
                queryGoodsInstanceReq.setQueryPaused(true);
            }

            if (QUERY_EXPIRED_ON.equals(ParameterUtil.getParameter(extensionInfo, IS_QUERY_EXPIRED))) {
                queryGoodsInstanceReq.setQueryExpired(true);
            }

            String rightTypeList = ParameterUtil.getParameter(extensionInfo, RIGHTS_TYPE);
            if (StringUtils.hasLength(rightTypeList)) {
                queryGoodsInstanceReq.setRightsTypeList(Arrays.asList(rightTypeList.split(",")));
            }

            String parentSubId = ParameterUtil.getParameter(extensionInfo, PARENT_SUB_ID);
            if (StringUtils.hasLength(parentSubId)) {
                queryGoodsInstanceReq.setGoodsPackageInstanceId(parentSubId);
            }

            String subTime = ParameterUtil.getParameter(extensionInfo, SUB_TIME);
            if (StringUtils.hasLength(subTime)) {
                queryGoodsInstanceReq.setSubStartTime(subTime);
            }

            String orderBySubTime = ParameterUtil.getParameter(extensionInfo, ORDER_BY_SUB_TIME);
            if (StringUtils.hasLength(orderBySubTime)) {
                queryGoodsInstanceReq.setOrderBySubTime(orderBySubTime.equals(DESC) ? Boolean.FALSE : Boolean.TRUE);
            }
        }

        if (req.getPageNum() != null && req.getPageSize() != null) {
            PageCondition pageCondition = new PageCondition();
            pageCondition.setPageNo(req.getPageNum());
            pageCondition.setPageSize(req.getPageSize());
            queryGoodsInstanceReq.setPageCondition(pageCondition);
        }

        return queryGoodsInstanceReq;
    }

    /**
     * 构建查询条件
     *
     * @param req
     * @return
     */
    private QueryGoodsInstanceReq buildQueryGoodsInstanceCondition(ChkValidMonthSubscribeReq req) {
        QueryGoodsInstanceReq queryGoodsInstanceReq = new QueryGoodsInstanceReq();
        queryGoodsInstanceReq.setUserDomainId(req.getUserDomainId());
        queryGoodsInstanceReq.setPhoneNumber(req.getAccount());
        return queryGoodsInstanceReq;
    }


    /**
     * 构建查询条件
     *
     * @param req
     * @return
     */
    private QueryGoodsInstanceReq buildQueryGoodsInstanceCondition(QueryIndependentSpaceSubReq req) {
        QueryGoodsInstanceReq queryGoodsInstanceReq = new QueryGoodsInstanceReq();
        queryGoodsInstanceReq.setUserDomainId(req.getUserDomainId());
        queryGoodsInstanceReq.setPhoneNumber(req.getAccount());
        queryGoodsInstanceReq.setGoodsSalesType(GoodsSalesTypeEnum.MEMBER);
        queryGoodsInstanceReq.setMergeByTimePlanGoodsGroupId(false);
        queryGoodsInstanceReq.setQueryExpired(true);
        if (StringUtils.hasLength(req.getOwnerId())) {
            queryGoodsInstanceReq.setOwnerIdList(Arrays.asList(req.getOwnerId().split("\\|")));
        }

        PageCondition pageCondition = new PageCondition();
        if (req.getPageSize() == null || req.getPageSize() >= MAX_PAGE_SIZE) {
            pageCondition.setPageSize(MAX_PAGE_SIZE);
        } else {
            pageCondition.setPageSize(req.getPageSize());
        }
        if (req.getPageNum() == null) {
            pageCondition.setPageNo(1);
        } else {
            pageCondition.setPageNo(req.getPageNum());
        }
        queryGoodsInstanceReq.setPageCondition(pageCondition);

        return queryGoodsInstanceReq;
    }



    /**
     * 设置查询商品实例条件V1
     *
     * @param req
     * @return
     */
    private QueryGoodsInstanceReq buildQueryGoodsInstanceCondition(QueryContractListSubscriptionReq req) {
        QueryGoodsInstanceReq queryGoodsInstanceReq = buildQueryGoodsInstanceConditionCommon(req);
        queryGoodsInstanceReq.setFilterByGoodsIdsForMemberAndSpace(true);
        List<NamedParameter> extensionInfo = req.getExtensionInfo();
        if (!CollectionUtils.isEmpty(extensionInfo)) {
            String ownerIdsString = ParameterUtil.getParameter(extensionInfo, OWNER_ID);
            if (StringUtils.hasLength(ownerIdsString) &&
                    OBTAIN_INNER_RIGHT_OFF.equals(ParameterUtil.getParameter(extensionInfo, IS_OBTAIN_INNER_RIGHT))) {
                queryGoodsInstanceReq.setOwnerIdList(Arrays.asList(ownerIdsString.split("\\|")));
                queryGoodsInstanceReq.setExcludeGoodsWithOwnerId(false);
            }

            if (QUERY_PAUSED_ON.equals(ParameterUtil.getParameter(extensionInfo, IS_QUERY_PAUSED))) {
                queryGoodsInstanceReq.setQueryPaused(true);
            }

            if (QUERY_EXPIRED_ON.equals(ParameterUtil.getParameter(extensionInfo, IS_QUERY_EXPIRED))) {
                queryGoodsInstanceReq.setQueryExpired(true);
            }
        }

        return queryGoodsInstanceReq;
    }


    /**
     * 设置查询商品实例条件(大音)
     *
     * @param req
     * @return
     */
    private QueryGoodsInstanceReq buildQueryGoodsInstanceConditionForDY(QueryContractListSubscriptionReq req) {
        QueryGoodsInstanceReq queryGoodsInstanceReq = buildQueryGoodsInstanceConditionCommon(req);

        List<NamedParameter> extensionInfo = req.getExtensionInfo();
        if (!CollectionUtils.isEmpty(extensionInfo)) {
            String ownerIdsString = ParameterUtil.getParameter(extensionInfo, OWNER_ID);
            if (StringUtils.hasLength(ownerIdsString) &&
                    OBTAIN_INNER_RIGHT_OFF.equals(ParameterUtil.getParameter(extensionInfo, IS_OBTAIN_INNER_RIGHT))) {
                queryGoodsInstanceReq.setOwnerIdList(Arrays.asList(ownerIdsString.split("\\|")));
                queryGoodsInstanceReq.setExcludeGoodsWithOwnerId(false);
            }

            if (QUERY_PAUSED_ON.equals(ParameterUtil.getParameter(extensionInfo, IS_QUERY_PAUSED))) {
                queryGoodsInstanceReq.setQueryPaused(true);
            }

            if (QUERY_EXPIRED_ON.equals(ParameterUtil.getParameter(extensionInfo, IS_QUERY_EXPIRED))) {
                queryGoodsInstanceReq.setQueryExpired(true);
            }
        }

        return queryGoodsInstanceReq;
    }


    /**
     * 设置查询商品实例条件V3
     *
     * @param req
     * @return
     */
    private QueryGoodsInstanceReq buildQueryGoodsInstanceConditionV3(QueryContractListSubscriptionReq req) {
        QueryGoodsInstanceReq queryGoodsInstanceReq = buildQueryGoodsInstanceConditionCommon(req);
        queryGoodsInstanceReq.setExcludeGoodsWithOwnerId(true);
        queryGoodsInstanceReq.setMergeByTimePlanGoodsGroupId(false);
        return queryGoodsInstanceReq;
    }

    /**
     * 设置查询商品实例条件V4
     *
     * @param req
     * @return
     */
    private QueryGoodsInstanceReq buildQueryGoodsInstanceConditionV4(QueryContractListSubscriptionReq req) {
        QueryGoodsInstanceReq queryGoodsInstanceReq = buildQueryGoodsInstanceConditionCommon(req);
        queryGoodsInstanceReq.setExcludeGoodsWithOwnerId(true);
        queryGoodsInstanceReq.setQueryPaused(true);
        queryGoodsInstanceReq.setQueryChildGoodsActiveInfo(true);
        List<NamedParameter> extensionInfo = req.getExtensionInfo();
        if (!CollectionUtils.isEmpty(extensionInfo)) {
            String ownerIdsString = ParameterUtil.getParameter(extensionInfo, OWNER_ID);
            if (StringUtils.hasLength(ownerIdsString) &&
                    OBTAIN_INNER_RIGHT_OFF.equals(ParameterUtil.getParameter(extensionInfo, IS_OBTAIN_INNER_RIGHT))) {
                queryGoodsInstanceReq.setOwnerIdList(Arrays.asList(ownerIdsString.split("\\|")));
                queryGoodsInstanceReq.setExcludeGoodsWithOwnerId(false);
            }
        }
        return queryGoodsInstanceReq;
    }

    /**
     * 设置查询商品实例条件V5
     *
     * @param req
     * @return
     */
    private QueryGoodsInstanceReq buildQueryGoodsInstanceConditionV5(QueryContractListSubscriptionReq req) {
        QueryGoodsInstanceReq queryGoodsInstanceReq = buildQueryGoodsInstanceConditionCommon(req);
        queryGoodsInstanceReq.setMergeByTimePlanGoodsGroupId(false);
        queryGoodsInstanceReq.setOnlyCurrentlyEffective(true);
        List<NamedParameter> extensionInfo = req.getExtensionInfo();
        if (!CollectionUtils.isEmpty(extensionInfo)) {
            String ownerIdsString = ParameterUtil.getParameter(extensionInfo, OWNER_ID);
            if (StringUtils.hasLength(ownerIdsString) && OBTAIN_INNER_RIGHT_OFF.equals(ParameterUtil.getParameter(extensionInfo, IS_OBTAIN_INNER_RIGHT))) {
                queryGoodsInstanceReq.setOwnerIdList(Arrays.asList(ownerIdsString.split("\\|")));
                queryGoodsInstanceReq.setExcludeGoodsWithOwnerId(false);
            }
        }
        return queryGoodsInstanceReq;
    }


    /**
     * 构建响应
     *
     * @param memberSubscriptionList
     * @param totalCount
     * @return
     */
    private QueryContractListSubscriptionResp buildQueryContractListSubscriptionResp(List<MemberSubscription> memberSubscriptionList, Long totalCount) {
        QueryContractListSubscriptionResp queryContractListSubscriptionResp = new QueryContractListSubscriptionResp();
        queryContractListSubscriptionResp.setMemberSubscriptionList(memberSubscriptionList);
        if (totalCount != null) {
            queryContractListSubscriptionResp.setTotalCount(totalCount.intValue());
        }

        return queryContractListSubscriptionResp;
    }


    /**
     * 构建响应
     *
     * @param memberSubscriptionList
     * @param totalCount
     * @return
     */
    private QueryActivitySubScriptionResp buildQueryActivitySubScriptionResp(List<MemberSubscription> memberSubscriptionList, Long totalCount) {
        QueryActivitySubScriptionResp queryActivitySubScriptionResp = new QueryActivitySubScriptionResp();
        queryActivitySubScriptionResp.setMemberSubscriptionList(memberSubscriptionList);
        if (totalCount != null) {
            queryActivitySubScriptionResp.setTotalCount(totalCount.intValue());
        }

        return queryActivitySubScriptionResp;
    }

    /**
     * 构建响应
     *
     * @param memberSubscriptionList
     * @param totalCount
     * @return
     */
    private QueryConditionSubScriptionResp buildQueryConditionSubScriptionResp(List<MemberSubscription> memberSubscriptionList, Long totalCount) {
        QueryConditionSubScriptionResp queryConditionSubScriptionResp = new QueryConditionSubScriptionResp();
        queryConditionSubScriptionResp.setMemberSubscriptionList(memberSubscriptionList);
        if (totalCount != null) {
            queryConditionSubScriptionResp.setTotalCount(totalCount.intValue());
        }

        return queryConditionSubScriptionResp;
    }


    /**
     * 构建getSubscriptionByOrderId的响应
     *
     * @return
     */
    private GetSubscriptionByOrderIdResp buildGetSubscriptionByOrderIdResp(List<VipMemberContractSubVO> vipMemberContractSubVOS) {
        GetSubscriptionByOrderIdResp getSubscriptionByOrderIdResp = new GetSubscriptionByOrderIdResp();
        if (CollUtil.isNotEmpty(vipMemberContractSubVOS)) {
            getSubscriptionByOrderIdResp.setVipMemberContractSub(vipMemberContractSubVOS.get(0));
        }
        getSubscriptionByOrderIdResp.setResultCode(Constant.SUCCESS);
        getSubscriptionByOrderIdResp.setResultDesc(Constant.SUCCESS_MESSAGE);
        return getSubscriptionByOrderIdResp;
    }


    /**
     * 构建响应
     *
     * @param memberSubscriptionList
     * @param totalCount
     * @return
     */
    private QueryIndepentSpaceSubScriptionResp buildQueryIndepentSpaceSubScriptionResp(List<MemberSubscription> memberSubscriptionList, Long totalCount) {
        QueryIndepentSpaceSubScriptionResp queryIndepentSpaceSubScriptionResp = new QueryIndepentSpaceSubScriptionResp();
        queryIndepentSpaceSubScriptionResp.setMemberSubscriptionList(memberSubscriptionList);
        if (totalCount != null) {
            queryIndepentSpaceSubScriptionResp.setTotalCount(totalCount.intValue());
        }

        return queryIndepentSpaceSubScriptionResp;
    }

}
