package com.zyhl.yun.member.mcdmc.activation.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zyhl.yun.member.mcdmc.activation.domains.conditions.QueryWorkOrderCondition;
import com.zyhl.yun.member.mcdmc.activation.mapper.WorkOrderMapper;
import com.zyhl.yun.member.mcdmc.activation.po.WorkOrderPo;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/06/04 18:40
 */
@Repository
public class WorkOrderRepository extends ServiceImpl<WorkOrderMapper, WorkOrderPo> {
    @Resource
    private WorkOrderMapper workOrderMapper;

    public int insertIgnoreWorkOrder(WorkOrderPo workOrderPO) {
        return workOrderMapper.insertIgnoreWorkOrder(workOrderPO);
    }

    /**
     * 根据查询条件返回对应数据
     *
     * @param columnsMap 查询条件
     */

    public List<WorkOrderPo> searchByCondition(Map<String, String> columnsMap) {
        if (CollectionUtils.isEmpty(columnsMap)) {
            return Collections.emptyList();
        }
        QueryWrapper<WorkOrderPo> queryWrapper = new QueryWrapper<>();
        columnsMap.forEach(queryWrapper::eq);
        return super.list(queryWrapper);
    }


    /**
     * 根据查询条件返回对应数据
     *
     * @param condition
     * @return
     */
    public List<WorkOrderPo> searchByCondition(QueryWorkOrderCondition condition) {

        LambdaQueryWrapper<WorkOrderPo> wrapper = Wrappers.lambdaQuery(WorkOrderPo.class)
                .eq(StringUtils.hasLength(condition.getUserId()), WorkOrderPo::getUserId, condition.getUserId())
                .in(!CollectionUtils.isEmpty(condition.getServiceCodeList()), WorkOrderPo::getServiceCode, condition.getServiceCodeList())
                .in(!CollectionUtils.isEmpty(condition.getOrderIdList()), WorkOrderPo::getOrderId, condition.getOrderIdList())
                .in(!CollectionUtils.isEmpty(condition.getWorkIdList()), WorkOrderPo::getWorkId, condition.getWorkIdList());

        return super.list(wrapper);
    }
}
