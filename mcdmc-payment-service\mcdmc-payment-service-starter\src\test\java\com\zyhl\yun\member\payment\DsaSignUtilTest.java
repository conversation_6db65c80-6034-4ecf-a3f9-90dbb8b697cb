package com.zyhl.yun.member.payment;

import com.zyhl.yun.member.common.util.DsaSignUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * @Author: Kiro
 * @Date: 2025-08-05
 * @Description: DsaSignUtil工具类单元测试
 */
@SpringBootTest(classes = PaymentApplication.class)
@Slf4j
public class DsaSignUtilTest {

    private String privateKeyStr;
    private String publicKeyStr;
    private Map<String, Object> testData;

    @BeforeEach
    void setUp() throws Exception {
        // 生成DSA密钥对用于测试
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("DSA");
        keyPairGenerator.initialize(1024);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        
        PrivateKey privateKey = keyPair.getPrivate();
        PublicKey publicKey = keyPair.getPublic();
        
        privateKeyStr = Base64.getEncoder().encodeToString(privateKey.getEncoded());
        // 现网密钥
        // privateKeyStr = 'MIIBSwIBADCCASwGByqGSM44BAEwggEfAoGBAP1/U4EddRIpUt9KnC7s5Of2EbdSPO9EAMMeP4C2USZpRV1AIlH7WT2NWPq/xfW6MPbLm1Vs14E7gB00b/JmYLdrmVClpJ+f6AR7ECLCT7up1/63xhv4O1fnxqimFQ8E+4P208UewwI1VBNaFpEy9nXzrith1yrv8iIDGZ3RSAHHAhUAl2BQjxUjC8yykrmCouuEC/BYHPUCgYEA9+GghdabPd7LvKtcNrhXuXmUr7v6OuqC+VdMCz0HgmdRWVeOutRZT+ZxBxCBgLRJFnEj6EwoFhO3zwkyjMim4TwWeotUfI0o4KOuHiuzpnWRbqN/C/ohNWLx+2J6ASQ7zKTxvqhRkImog9/hWuWfBpKLZl6Ae1UlZAFMO/7PSSoEFgIUMMJtfle8MwIjv9ecejp51QjG3Rg=';
        publicKeyStr = Base64.getEncoder().encodeToString(publicKey.getEncoded());
        
        // 准备测试数据 - 基于TPCTUnSubscribeReq XML字段
        testData = new TreeMap<>();
        testData.put("msgType", "TPCTUnSubscribeReq");
        testData.put("msgVer", "1.0");
        testData.put("outSystemID", "HCLOUD");
        testData.put("outOrderID", "2501040037570a181d64267162800000");
        testData.put("apID", "446605");
        testData.put("msisdn", "手机号");
        testData.put("actionTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
    }

    /**
     * 测试数字签名生成
     */
    @Test
    void testBuildSign() throws Exception {
        String sign = DsaSignUtil.buildSign(privateKeyStr, testData);
        
        Assertions.assertNotNull(sign, "签名不能为空");
        Assertions.assertTrue(sign.length() > 0, "签名长度应大于0");
        log.info("生成的签名: {}", sign);
    }

    /**
     * 测试数字签名验证
     */
    @Test
    void testVerifySign() throws Exception {
        // 先生成签名
        String sign = DsaSignUtil.buildSign(privateKeyStr, testData);
        
        // 验证签名
        boolean isValid = DsaSignUtil.verifySign(sign, publicKeyStr, testData);
        
        Assertions.assertTrue(isValid, "签名验证应该成功");
        log.info("签名验证结果: {}", isValid);
    }

    /**
     * 测试签名验证失败场景
     */
    @Test
    void testVerifySignWithWrongData() throws Exception {
        // 先生成签名
        String sign = DsaSignUtil.buildSign(privateKeyStr, testData);
        
        // 修改数据
        Map<String, Object> wrongData = new TreeMap<>(testData);
        wrongData.put("outOrderID", "wrongOrderId");
        
        // 验证签名应该失败
        boolean isValid = DsaSignUtil.verifySign(sign, publicKeyStr, wrongData);
        
        Assertions.assertFalse(isValid, "修改数据后签名验证应该失败");
        log.info("错误数据签名验证结果: {}", isValid);
    }

    /**
     * 测试map转字符串功能
     */
    @Test
    void testMap2String() {
        String result = DsaSignUtil.map2String(testData);
        
        Assertions.assertNotNull(result, "转换结果不能为空");
        Assertions.assertTrue(result.contains("msgType=TPCTUnSubscribeReq"), "应包含msgType参数");
        Assertions.assertTrue(result.contains("&"), "应包含参数分隔符");
        log.info("Map转字符串结果: {}", result);
    }

    /**
     * 测试空map转字符串
     */
    @Test
    void testMap2StringWithEmptyMap() {
        Map<String, Object> emptyMap = new HashMap<>();
        String result = DsaSignUtil.map2String(emptyMap);
        
        Assertions.assertNull(result, "空map转换结果应为null");
    }

    /**
     * 测试null map转字符串
     */
    @Test
    void testMap2StringWithNullMap() {
        String result = DsaSignUtil.map2String(null);
        
        Assertions.assertNull(result, "null map转换结果应为null");
    }

    /**
     * 测试getDataMap方法
     */
    @Test
    void testGetDataMap() {
        Map<String, Object> dataMap = DsaSignUtil.getDataMap(
                "testTransactionId", "testApId", "testServiceId", "1",
                "testChannelId", "testContentId", "testUserId", "1",
                "13800138000", "Beijing", "20250805120000", "testMethod"
        );
        
        Assertions.assertNotNull(dataMap, "数据map不能为空");
        Assertions.assertEquals("testTransactionId", dataMap.get("APTransactionID"));
        Assertions.assertEquals("testApId", dataMap.get("APId"));
        Assertions.assertEquals("testServiceId", dataMap.get("ServiceId"));
        Assertions.assertNull(dataMap.get("Backup1"), "Backup1应为null");
        Assertions.assertNull(dataMap.get("Backup2"), "Backup2应为null");
        log.info("生成的数据map: {}", dataMap);
    }

    /**
     * 测试SHA-256加密
     */
    @Test
    void testEncrypt() throws Exception {
        String source = "test string";
        String encrypted = DsaSignUtil.encrypt(source, "SHA-256");
        
        Assertions.assertNotNull(encrypted, "加密结果不能为空");
        Assertions.assertTrue(encrypted.length() > 0, "加密结果长度应大于0");
        log.info("SHA-256加密结果: {}", encrypted);
    }

    /**
     * 测试SHA-256 Base64加密
     */
    @Test
    void testEncryptSha256Base64() {
        String source = "test string";
        String encrypted = DsaSignUtil.encryptSha256Base64(source);
        
        Assertions.assertNotNull(encrypted, "加密结果不能为空");
        Assertions.assertTrue(encrypted.length() > 0, "加密结果长度应大于0");
        log.info("SHA-256 Base64加密结果: {}", encrypted);
    }

    /**
     * 测试bytes转Hex
     */
    @Test
    void testBytes2Hex() {
        byte[] testBytes = "test".getBytes();
        String hexString = DsaSignUtil.bytes2Hex(testBytes);
        
        Assertions.assertNotNull(hexString, "Hex字符串不能为空");
        Assertions.assertEquals("74657374", hexString, "Hex转换结果应正确");
        log.info("Bytes转Hex结果: {}", hexString);
    }

    /**
     * 测试sign方法
     */
    @Test
    void testSign() throws Exception {
        byte[] data = "test data".getBytes();
        String signature = DsaSignUtil.sign(data, privateKeyStr);
        
        Assertions.assertNotNull(signature, "签名不能为空");
        Assertions.assertTrue(signature.length() > 0, "签名长度应大于0");
        log.info("Sign方法生成的签名: {}", signature);
    }

    /**
     * 测试buildSignString方法
     */
    @Test
    void testBuildSignString() {
        TestObject testObj = new TestObject();
        testObj.setField1("value1");
        testObj.setField2("value2");
        testObj.setSign("shouldBeIgnored");
        
        String signature = DsaSignUtil.buildSignString(testObj, privateKeyStr, TestObject.class);
        
        Assertions.assertNotNull(signature, "签名不能为空");
        log.info("BuildSignString生成的签名: {}", signature);
    }

    /**
     * 测试用的内部类
     */
    public static class TestObject {
        private String field1;
        private String field2;
        private String sign;

        public String getField1() {
            return field1;
        }

        public void setField1(String field1) {
            this.field1 = field1;
        }

        public String getField2() {
            return field2;
        }

        public void setField2(String field2) {
            this.field2 = field2;
        }

        public String getSign() {
            return sign;
        }

        public void setSign(String sign) {
            this.sign = sign;
        }
    }
}
