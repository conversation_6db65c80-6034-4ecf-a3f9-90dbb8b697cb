package com.zyhl.yun.member.mcdmc.activation.callback.notify;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.zyhl.yun.member.common.ErrorCode;
import com.zyhl.yun.member.common.OperatorEnum;
import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.constants.JiyunConstants;
import com.zyhl.yun.member.common.constants.NumberConstant;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.JiyunServiceId;
import com.zyhl.yun.member.common.domain.serviceid.UserServiceId;
import com.zyhl.yun.member.common.enums.UserStatusEnum;
import com.zyhl.yun.member.common.util.DateUtils;
import com.zyhl.yun.member.consumer.common.producer.MqProducer;
import com.zyhl.yun.member.domain.jiyun.domain.FlowSpecSubscriptionDo;
import com.zyhl.yun.member.domain.user.domain.QueryUserCondition;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.dto.GivenPrdOrderRelQueryReq;
import com.zyhl.yun.member.dto.GivenPrdOrderRelQueryResp;
import com.zyhl.yun.member.dto.QueryJiyunFlowSpecCondition;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.CallbackContext;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.CallbackTemplate;
import com.zyhl.yun.member.mcdmc.activation.callback.req.SubscribeFlowSpecNotifyReq;
import com.zyhl.yun.member.mcdmc.activation.callback.rsp.SubscribeFlowSpecNotifyResp;
import com.zyhl.yun.member.mcdmc.activation.callback.rsp.SubscribeFlowSpecNotifyRespVo;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.CallbackException;
import com.zyhl.yun.member.mcdmc.activation.domain.mq.message.SpecFlowMqMessage;
import com.zyhl.yun.member.mcdmc.activation.domain.mq.properties.SpecFlowProperties;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.XMLUtil;
import com.zyhl.yun.member.mcdmc.activation.redis.RedisComponent;
import com.zyhl.yun.member.mcdmc.activation.remote.send.gateway.JiyunFlowSpecGateWay;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.JiyunProperties;
import com.zyhl.yun.member.mcdmc.activation.result.BaseResult;
import com.zyhl.yun.member.mcdmc.activation.result.ResultCode;
import com.zyhl.yun.member.mcdmc.activation.util.JiyunAuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 集运定向免流回调
 * com.huawei.jaguar.vsbo.service.serviceimpl.SubscribeServiceImpl#subscribeFlowSpecNotify
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/3 18:45
 */
@Slf4j
@Component
public class SubscribeFlowSpecCallback extends CallbackTemplate<SubscribeFlowSpecNotifyReq, SubscribeFlowSpecNotifyRespVo> {

    @Resource
    private JiyunProperties jiyunProperties;

    @Resource
    private JiyunAuthUtils jiyunAuthUtils;

    @Resource
    private RedisComponent redisComponent;

    @Resource
    private JiyunFlowSpecGateWay jiyunFlowSpecGateWay;

    @Resource
    private MqProducer mqProducer;

    @Resource
    private SpecFlowProperties specFlowProperties;

    @Override
    protected Class<SubscribeFlowSpecNotifyReq> getNotifyReqClass() {
        return null;
    }

    @Override
    protected void doCheck(CallbackContext<SubscribeFlowSpecNotifyReq, SubscribeFlowSpecNotifyRespVo> callbackContext) throws CallbackException {
        if (ObjectUtils.isNull(callbackContext.getCallbackReq())) {
            throw new CallbackException(ResultCode.EMPTY_BODY_ERROR.getCode(), ResultCode.EMPTY_BODY_ERROR.getMsg());
        }
    }

    @Override
    protected String getSearchCondition(SubscribeFlowSpecNotifyReq notifyReq) {
        return notifyReq.getSourceOrderNo();
    }

    @Override
    protected boolean isSuccess(SubscribeFlowSpecNotifyReq notifyReq) {
        return false;
    }

    @Override
    protected SubscribeFlowSpecNotifyReq convertReq(String body) {
        return XMLUtil.xmlToBean(body, SubscribeFlowSpecNotifyReq.class);
    }

    @Override
    protected BaseResult getSuccessResult() {
        return BaseResult.sc("0", "success");
    }

    @Override
    protected SubscribeFlowSpecNotifyRespVo buildNotifyRsp(CallbackContext<SubscribeFlowSpecNotifyReq, SubscribeFlowSpecNotifyRespVo> callbackContext, BaseResult result) {
        SubscribeFlowSpecNotifyResp subscribeFlowSpecNotifyResp = new SubscribeFlowSpecNotifyResp();
        subscribeFlowSpecNotifyResp.setResultCode(result.getResultCode());
        subscribeFlowSpecNotifyResp.setResultDesc(result.getMessage());
        return new SubscribeFlowSpecNotifyRespVo(subscribeFlowSpecNotifyResp);
    }

    @Override
    protected String convertRsp2Str(SubscribeFlowSpecNotifyRespVo subscribeFlowSpecNotifyRespVo) {
        return XMLUtil.beanToXml(subscribeFlowSpecNotifyRespVo, SubscribeFlowSpecNotifyRespVo.class);
    }

    /**
     * 更新免流订单表订购关系状态
     *
     * @param callbackContext 回调上下文
     */
    @Override
    protected void doNotify(CallbackContext<SubscribeFlowSpecNotifyReq, SubscribeFlowSpecNotifyRespVo> callbackContext) {

        SubscribeFlowSpecNotifyReq subscribeFlowSpecNotifyReq = callbackContext.getCallbackReq();
        String userDomainId = callbackContext.getComSendReq().getUserDomainId();
        String sourceGoodsId = jiyunProperties.getSkuIdByActivityId(subscribeFlowSpecNotifyReq.getActivityId());
        if (CharSequenceUtil.isBlank(sourceGoodsId)) {
            throw new ServiceException(ErrorCode.SUBSCRIBE_FLOWNOTIFY_SOURCEGOODSID_NOT_EXIST, ErrorCode.SUBSCRIBE_FLOWNOTIFY_SOURCEGOODSID_NOT_EXIST_MESSAGE);
        }

        //1、做redis删除限制请求频率处理
        this.delSubscribeFlowSpecRedis(subscribeFlowSpecNotifyReq.getTelephone(), sourceGoodsId, subscribeFlowSpecNotifyReq.getStatus());
        //2、更新免流订单表订购关系状态
        FlowSpecSubscriptionDo flowSpecSubscriptionDo = this.updateSubscription(subscribeFlowSpecNotifyReq, sourceGoodsId);
        // 更新资产表
        jiyunAuthUtils.updateResource(flowSpecSubscriptionDo);
        //3、发送mq消息
        if (this.isNeedSendMq(sourceGoodsId, subscribeFlowSpecNotifyReq)) {
            this.sendSpecFlowMessageToRocketMQ(flowSpecSubscriptionDo, userDomainId);
        }
    }

    /**
     * 2023-2-7
     * 流程正常情况下删除限制免流次数rediskey操作
     */
    private void delSubscribeFlowSpecRedis(String sourceGoodsId, String account, Integer status) {
        log.info("method delSubscribeFlowSpecRedis begin!");
        String subscribeFlowSpec = String.format(jiyunProperties.getSubscribeFlowSpec(), account, sourceGoodsId);
        redisComponent.delete(subscribeFlowSpec);

        // 如果集运订购成功，则删除缓存，下次查询进来再刷新
        String redisKey = String.format(jiyunProperties.getFlowSpec(), account);
        String redisKeyV2 = String.format(jiyunProperties.getFlowSpecV2(), account);
        String redisKeyV3 = String.format(jiyunProperties.getFlowSpecV3(), account);
        if (JiyunConstants.JiyunSubscriptionStatus.SUCCESS_CODE.equals(status)) {
            log.info("subscribeFlowSpecNotify subscribe success, delete the redis redisKey : {}," +
                    "redisKeyV2:{},redisKeyV3:{} ", redisKey, redisKeyV2, redisKeyV3);
            redisComponent.delete(redisKey);
            redisComponent.delete(redisKeyV2);
            redisComponent.delete(redisKeyV3);
        }
        log.info("method delSubscribeFlowSpecRedis end!");
    }

    private FlowSpecSubscriptionDo updateSubscription(SubscribeFlowSpecNotifyReq subscribeFlowSpecNotifyReq, String sourceGoodsId) {
        DomainServiceContext jiyunServiceContext = new DomainServiceContext(JiyunServiceId.UPDATE_JIYUN_FLOW_SPEC);
        QueryJiyunFlowSpecCondition qryCondition = new QueryJiyunFlowSpecCondition();
        String userId = queryUserIDByAccount(subscribeFlowSpecNotifyReq.getTelephone());
        qryCondition.setUserId(userId);
        qryCondition.setSourceApp(jiyunProperties.getJiyunSourceApp());
        qryCondition.setSourceGoodsId(sourceGoodsId);
        qryCondition.setSourceOrderNo(subscribeFlowSpecNotifyReq.getSourceOrderNo());
        FlowSpecSubscriptionDo flowSpecSubDo = jiyunServiceContext.readFirst(qryCondition, FlowSpecSubscriptionDo.class);
        // 更新免流订购关系
        flowSpecSubDo.setOrderNo(subscribeFlowSpecNotifyReq.getOrderNo());
        flowSpecSubDo.setActivityId(subscribeFlowSpecNotifyReq.getActivityId());
        flowSpecSubDo.setAccount(subscribeFlowSpecNotifyReq.getTelephone());
        flowSpecSubDo.setOrderIndex(String.valueOf(subscribeFlowSpecNotifyReq.getIndex()));
        flowSpecSubDo.setSkuCode(subscribeFlowSpecNotifyReq.getSkuCode());
        flowSpecSubDo.setSkuName(subscribeFlowSpecNotifyReq.getSkuName());
        flowSpecSubDo.setStatus(String.valueOf(subscribeFlowSpecNotifyReq.getStatus()));
        flowSpecSubDo.setExpectedExecuteTime(DateUtils.parseDateTime(subscribeFlowSpecNotifyReq.getExpectedExecuteTime()));
        flowSpecSubDo.setActualExecuteTime(DateUtils.parseDateTime(subscribeFlowSpecNotifyReq.getActualExecuteTime()));
        flowSpecSubDo.setBizCode(subscribeFlowSpecNotifyReq.getBizCode());
        flowSpecSubDo.setBizDesc(subscribeFlowSpecNotifyReq.getBizDesc());
        flowSpecSubDo.setUpdateTime(new Date());
        // 如果调用集运的订购关系校验查询接口的开关开启，需要到集运平台获取订购关系的生效时间、订购时间、失效时间、是否已订购等信息
        if (JiyunConstants.FLOW_SPEC_SWITCH_ON.equals(jiyunProperties.getFlowSpecSwitch())) {
            GivenPrdOrderRelQueryReq givenPrdOrderRelQueryReq = new GivenPrdOrderRelQueryReq();
            givenPrdOrderRelQueryReq.setServiceNumber(subscribeFlowSpecNotifyReq.getTelephone());
            givenPrdOrderRelQueryReq.setGoodsIdList(jiyunProperties.getJiyunGoodsID());
            givenPrdOrderRelQueryReq.setServiceType(JiyunConstants.JIYUN_SERVICE_TYPE);
            givenPrdOrderRelQueryReq.setGoodsType(JiyunConstants.JIYUN_GOODS_TYPE);
            GivenPrdOrderRelQueryResp givenPrdOrderRelQueryResp = jiyunFlowSpecGateWay.givenPrdOrderRelQuery(givenPrdOrderRelQueryReq);

            if (ObjectUtils.isNotNull(givenPrdOrderRelQueryResp) && ObjectUtils.isNotNull(givenPrdOrderRelQueryResp.getData()) &&
                    ObjectUtils.isNotNull(givenPrdOrderRelQueryResp.getData().getBizInfoList())) {
                flowSpecSubDo.setOrderTime(DateUtils.format(givenPrdOrderRelQueryResp.getData().getBizInfoList().get(0).getOrderTime()));
                flowSpecSubDo.setValidDate(DateUtils.format(givenPrdOrderRelQueryResp.getData().getBizInfoList().get(0).getValidDate()));
                flowSpecSubDo.setExpireDate(DateUtils.format(givenPrdOrderRelQueryResp.getData().getBizInfoList().get(0).getExpireDate()));
                flowSpecSubDo.setGoodsId(givenPrdOrderRelQueryResp.getData().getBizInfoList().get(0).getGoodsId());
            }
        }
        jiyunServiceContext.putInstance(flowSpecSubDo);
        jiyunServiceContext.writeAndFlush();
        return flowSpecSubDo;
    }

    /**
     * @return subscribeFlowSpecNotify_15112395842_sourceGoodsId   success 1个自然月
     * subscribeFlowSpecNotify_15112395842_sourceGoodsId  fail 6h
     * （1）指定时间范围内（1个自然月：1个月只有1次成功订单）已经发了成功的mq则不发失败的mq结果
     * （2）只有没有成功的缓存时，才可发失败的；指定时间范围内（6h内只有1次失败）如果已经有失败的，则不发失败的，但可以发成功的（覆盖失败的缓存）。
     */
    private boolean isNeedSendMq(String sourceGoodsId, SubscribeFlowSpecNotifyReq subscribeFlowSpecNotifyReq) {
        log.info("method subscribeFlowSpecNotifyLimitErrorCode  begin! ");
        //1.读取redis是否存在
        String lastNotifyRedisKey = String.format(jiyunProperties.getSubscribeFlowSpecNotify(), subscribeFlowSpecNotifyReq.getTelephone(), sourceGoodsId);
        String lastNotifyStatus = redisComponent.get(lastNotifyRedisKey);

        if (lastNotifyStatus != null && Objects.equals(Integer.parseInt(lastNotifyStatus), subscribeFlowSpecNotifyReq.getStatus())) {
            log.info("twice notify status is same, do not send mq ! ");
            return false;
        }
        if (SubscribeFlowSpecNotifyReq.isSuccess(lastNotifyStatus)) {
            log.info("last notify status is success,now notify status is fail,  do not send mq ! ");
            return false;
        }
        // 之前不存在回调，则更新缓存
        if (SubscribeFlowSpecNotifyReq.isSuccess(subscribeFlowSpecNotifyReq.getStatus())) {
            log.info("last notify status is null or fail,now is success, need send mq ! ");
            redisComponent.set(lastNotifyRedisKey, subscribeFlowSpecNotifyReq.getStatus().toString(), jiyunAuthUtils.getEndMonthExpirationTime() / 1000);
        } else {
            log.info("last notify status is null ,now is fail, need send mq ! ");
            redisComponent.set(lastNotifyRedisKey, subscribeFlowSpecNotifyReq.getStatus().toString(),
                    jiyunProperties.getSubscribeFlowSpecNotifyRedisExpirationTime() * 3600L);
        }
        return true;
    }

    /**
     * 发送给内容mq消息
     */
    private void sendSpecFlowMessageToRocketMQ(FlowSpecSubscriptionDo flowSpecSubscriptionDo, String userDomainId) {
        //回调成功则发送内容mq
        SpecFlowMqMessage specFlowMqMessage = new SpecFlowMqMessage();
        specFlowMqMessage.setTraceID(UUID.fastUUID().toString(true))
                .setMsgID(java.util.UUID.randomUUID().toString().replace("-", ""))
                .setEventTime(DateUtil.format(new Date(), DateUtils.DEFAULT_DATE_PATTERN_MS))
                .setUserID(flowSpecSubscriptionDo.getUserId())
                .setAccount(flowSpecSubscriptionDo.getAccount())
                .setSourceGoodsId(flowSpecSubscriptionDo.getSourceGoodsId())
                .setSkuCode(flowSpecSubscriptionDo.getSkuCode())
                .setSkuName(flowSpecSubscriptionDo.getSkuName())
                .setActivityId(flowSpecSubscriptionDo.getActivityId())
                .setSourceOrderNo(flowSpecSubscriptionDo.getSourceOrderNo())
                .setSourceApp(flowSpecSubscriptionDo.getSourceApp())
                .setOrderNo(flowSpecSubscriptionDo.getOrderNo())
                .setIndex(Integer.parseInt(flowSpecSubscriptionDo.getOrderIndex()))
                .setStatus(Integer.parseInt(flowSpecSubscriptionDo.getStatus()))
                .setExpectedExecuteTime(DateUtil.format(flowSpecSubscriptionDo.getExpectedExecuteTime(), DateUtils.DEFAULT_DATE_PATTERN_MS))
                .setActualExecuteTime(DateUtil.format(flowSpecSubscriptionDo.getActualExecuteTime(), DateUtils.DEFAULT_DATE_PATTERN_MS))
                .setBizCode(flowSpecSubscriptionDo.getBizCode())
                .setBizDesc(flowSpecSubscriptionDo.getBizDesc())
                .setOrderTime(DateUtil.format(flowSpecSubscriptionDo.getOrderTime(), DateUtils.DEFAULT_DATE_PATTERN_MS))
                .setValidDate(DateUtil.format(flowSpecSubscriptionDo.getValidDate(), DateUtils.DEFAULT_DATE_PATTERN_MS))
                .setExpireDate(DateUtil.format(flowSpecSubscriptionDo.getExpireDate(), DateUtils.DEFAULT_DATE_PATTERN_MS))
                .setEnvID("100001")
                .setExtInfo(StringUtils.EMPTY)
                .setChannelId(CharSequenceUtil.nullToEmpty(flowSpecSubscriptionDo.getChannelId()))
                .setSubChannelId(CharSequenceUtil.nullToEmpty(flowSpecSubscriptionDo.getSubChannelId()))
                .setUserDomainId(userDomainId)
        ;
        if (StringUtils.isEmpty(specFlowMqMessage.getSourceGoodsId())) {
            log.error("sourceGoodsId is empty,current flowSpecSubscriptionDo:{}", flowSpecSubscriptionDo);
        }
        mqProducer.sendOuterMessage(specFlowProperties, specFlowMqMessage);
    }

    public String queryUserIDByAccount(String account) {
        //1、查询用户手机号运营商
        DomainServiceContext userServiceContext = new DomainServiceContext(UserServiceId.QUERY_AND_CREATE_USER_INFO);
        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setAccount(account);
        queryUserCondition.setStatusList(Collections.singletonList(UserStatusEnum.NORMAL));
        List<UserDo> userDos = userServiceContext.read(queryUserCondition, UserDo.class);
        if (CollUtil.isEmpty(userDos)) {
            log.error("Failed to BMP getUserAccount.");
            throw new ServiceException(ErrorCode.SERVICE_INTERNAL_ERROR, ErrorCode.BMP_FAIL);
        }
        OperatorEnum operator = userDos.get(NumberConstant.ZERO).getOperator();
        String userId = userDos.get(NumberConstant.ZERO).getUserId();
        if (!operator.equals(OperatorEnum.CMCC)) {
            throw new ServiceException(ErrorCode.USER_NOT_CMCC_USER, "not a cmcc user.");
        }
        return userId;
    }

    @Override
    protected SendOperation getSendOperation() {
        return SendOperation.SUB;
    }
}