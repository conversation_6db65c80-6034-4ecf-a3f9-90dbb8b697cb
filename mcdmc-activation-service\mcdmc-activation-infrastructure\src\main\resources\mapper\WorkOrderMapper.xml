<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.zyhl.yun.member.mcdmc.activation.mapper.WorkOrderMapper">

    <insert id="insertIgnoreWorkOrder">
        insert ignore into activation_work_order(work_id,order_id,user_id,service_code,work_attrs,
        transaction_id,state)
        values(#{po.workId},#{po.orderId},#{po.userId},#{po.serviceCode},#{po.workAttrs},#{po.transactionId},
        #{po.state})
    </insert>
</mapper>