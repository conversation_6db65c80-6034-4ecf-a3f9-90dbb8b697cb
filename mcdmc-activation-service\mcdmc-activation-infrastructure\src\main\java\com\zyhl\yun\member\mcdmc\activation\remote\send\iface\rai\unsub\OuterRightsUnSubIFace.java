package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.unsub;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.facade.GoodsInstanceServiceFacade;
import com.zyhl.yun.member.mcdmc.activation.constants.BusinessConstant;
import com.zyhl.yun.member.mcdmc.activation.constants.IFaceConfigMapKey;
import com.zyhl.yun.member.mcdmc.activation.constants.SymbolConstant;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IFlowResult;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.Base64;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.enums.OuterRightsUnSubNotifyTypeEnum;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.SendTemplate;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.OuterRefundSyncReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.OuterRefundSyncRsp;
import com.zyhl.yun.member.mcdmc.activation.util.IFaceConfigUtil;
import com.zyhl.yun.member.mcdmc.activation.util.OuterRightsUnSubContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;

/**
 * 外部退订通知实现类（无回调）
 *
 * <AUTHOR>
 * @since 2024/11/21 17:47
 */
@Component
@Slf4j
public class OuterRightsUnSubIFace extends SendTemplate<OuterRefundSyncReq, OuterRefundSyncRsp> {

    @Autowired
    IFaceConfigUtil iFaceConfigUtil;

    @Autowired
    OuterRightsUnSubContextUtil outerRightsUnSubContextUtil;


    /**
     * 参数校验，若失败则直接结束
     *
     * @param interfaceContext 接口上下文
     */
    @Override
    protected void doCheckParam(InterfaceContext<OuterRefundSyncReq, OuterRefundSyncRsp> interfaceContext) throws FlowTerminationException {
        // 校验是否只发mq
        String iFaceConfig = interfaceContext.getWorkServiceFlowIFaceDo().getIFaceConfig();
        WorkOrderDo workOrderDo = interfaceContext.getWorkOrderDo();
        // 获取对应的Iface的外部配置信息
        String notifyType = iFaceConfigUtil.getFaceConfig(iFaceConfig, IFaceConfigMapKey.NOTIFY_TYPE);
        OuterRightsUnSubNotifyTypeEnum notifyTypeEnum = OuterRightsUnSubNotifyTypeEnum.getByNotifyCode(notifyType);
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        GoodsInstanceDo goodsInstanceDo = GoodsInstanceServiceFacade.qryGoodsInstance(comSendReq.getUserId(), comSendReq.getGoodsInstanceId());
        if (goodsInstanceDo == null) {
            log.error("outerRightsUnSubIFace doCheckParam outer Rights unSub fail,userId={},orderId={},goodsId={},goodsInstanceDo is null",
                    comSendReq.getUserId(), comSendReq.getOrderID(), comSendReq.getGoodsId());
            // 退订查不到订单则直接重置流程
            throw new FlowTerminationException(this.getClass(), interfaceContext.getWorkOrderDo(), NextHintEnum.RESTART,
                    "outerRightsUnSubIFace doCheckParam outer Rights unSub fail,work detail is %s,goodsInstanceDo is null", workOrderDo.toSimpleLogStr());
        }
        String resourceId = goodsInstanceDo.getResourceId();
        if (!StringUtils.hasText(resourceId)) {
            log.error("outerRightsUnSubIFace doCheckParam outer Rights unSub fail,userId={},orderId={},goodsId={},resourceId is null",
                    comSendReq.getUserId(), comSendReq.getOrderID(), comSendReq.getGoodsId());
            // 退订查不到订单则直接重置流程
            throw new FlowTerminationException(this.getClass(), interfaceContext.getWorkOrderDo(), NextHintEnum.FINISH,
                    BusinessConstant.RAI_UNSUBSCRIBE_MESSAGE_FLAG + "outerRightsUnSubIFace doCheckParam outer Rights" +
                            " unSub fail,work order detail is %s,resourceId of goodsInstance is null", workOrderDo.toSimpleLogStr());
        }
        comSendReq.putExtInfo(OtherFieldConstants.RESOURCE_ID, resourceId);
        if (notifyTypeEnum != null) {
            switch (notifyTypeEnum) {
                // 两个case都执行同样的操作
                case ONLY_SEND_MQ:
                    // 发mq给能开
                    outerRightsUnSubContextUtil.sendOpenCapacityUnsubscribeMessage(
                            iFaceConfig,
                            resourceId
                    );
                    // 发完mq退出流程
                    throw new FlowTerminationException(this.getClass(), workOrderDo, NextHintEnum.FINISH, "doCheckParam ONLY_SEND_MQ end");

                case NOTIFY_BOTH:
                    // 两个都处理
                    // 发mq给能开
                    outerRightsUnSubContextUtil.sendOpenCapacityUnsubscribeMessage(
                            iFaceConfig,
                            resourceId
                    );
                    break;

                case ONLY_NOTIFY_RIGHTSHOLDER:
                    break;

                case NOTHING:
                    // 什么都不做
                    throw new FlowTerminationException(this.getClass(), workOrderDo, NextHintEnum.FINISH, "doCheckParam数据库配置的外部通知类型不做处理");

                default:
                    throw new FlowTerminationException(this.getClass(), workOrderDo, "doCheckParam数据库配置的外部通知类型有误");
            }
        } else {
            throw new FlowTerminationException(this.getClass(), workOrderDo, "doCheckParam数据库配置的外部通知类型有误");
        }

    }

    @Override
    protected OuterRefundSyncReq getRequestBody(InterfaceContext<OuterRefundSyncReq, OuterRefundSyncRsp> interfaceContext) {
        OuterRefundSyncReq outerRefundSyncReq = new OuterRefundSyncReq();
        // 获取对应的Iface的外部配置信息
        String iFaceConfig = interfaceContext.getWorkServiceFlowIFaceDo().getIFaceConfig();
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        // 校验流程查出来的resourceId
        String resourceId = comSendReq.getExtInfo(OtherFieldConstants.RESOURCE_ID);
        outerRefundSyncReq.setOrderNo(resourceId);
        outerRefundSyncReq.setRefundTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        outerRefundSyncReq.setChannelId(iFaceConfigUtil.getFaceConfig(iFaceConfig, IFaceConfigMapKey.CHANNEL_ID));
        outerRefundSyncReq.setTimestamp(String.valueOf(Instant.now().toEpochMilli()));
        // 获取签名
        TreeMap<String, Object> objTreeMap = new TreeMap<>(JSONUtil.parseObj(outerRefundSyncReq));
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String, Object> entry : objTreeMap.entrySet()) {
            signStr.append(entry.getKey()).append(SymbolConstant.EQUAL)
                    .append(entry.getValue()).append(SymbolConstant.AND);
        }
        try {
            String realKey = new String(Base64.decode(iFaceConfigUtil.getFaceConfig(iFaceConfig, IFaceConfigMapKey.CHANNEL_KEY)), "UTF-8");
            signStr.append("channelSecret").append(SymbolConstant.EQUAL).append(realKey);
        } catch (UnsupportedEncodingException e) {
            log.error("outerRightsUnSubIFace getRequestBody decode error", e);
            throw new FlowTerminationException(this.getClass(), interfaceContext.getWorkOrderDo(), "base64解码失败");
        }
        String sign = DigestUtils.md5Hex(signStr.toString());
        outerRefundSyncReq.setSign(sign);
        return outerRefundSyncReq;
    }

    @Override
    protected Class<OuterRefundSyncReq> getReqClass() {
        return OuterRefundSyncReq.class;
    }

    @Override
    protected Class<OuterRefundSyncRsp> getRspClass() {
        return OuterRefundSyncRsp.class;
    }

    @Override
    protected boolean isSuccess(OuterRefundSyncRsp rsp) {
        return true;
    }


    /**
     * 业务完成前执行流程，不管业务响应成功与否均会执行（处理结果时发生异常时可能不会执行）
     *
     * @param interfaceContext 接口上下文
     */
    @Override
    protected void doBusiComplete(InterfaceContext<OuterRefundSyncReq, OuterRefundSyncRsp> interfaceContext) {
        if (interfaceContext == null || interfaceContext.getLastFaceResult() == null) {
            return;
        }
        IFlowResult lastFaceResult = interfaceContext.getLastFaceResult();
        interfaceContext.setNextHintEnum(lastFaceResult.getNextHintEnum());
        interfaceContext.setCallbackCondition(lastFaceResult.getCallbackCondition());
    }


    /**
     * 业务失败处理
     *
     * @param interfaceContext 接口上下文
     */
    @Override
    protected void doBusiFail(InterfaceContext<OuterRefundSyncReq, OuterRefundSyncRsp> interfaceContext) {
        if (interfaceContext == null || interfaceContext.getLastFaceResult() == null) {
            return;
        }
        IFlowResult lastFaceResult = interfaceContext.getLastFaceResult();
        interfaceContext.setNextHintEnum(lastFaceResult.getNextHintEnum());
        interfaceContext.setCallbackCondition(lastFaceResult.getCallbackCondition());
    }


    /**
     * 获取订购资产id
     */
    @Override
    protected String getResourceId(InterfaceContext<OuterRefundSyncReq, OuterRefundSyncRsp> context) {
        if (context == null || context.getLastFaceResult() == null) {
            return null;
        }
        return context.getLastFaceResult().getResourceId();
    }
}
