/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.zyhl.yun.member.mcdmc.compare.domain.cdr.helper;

import com.zyhl.yun.member.mcdmc.compare.common.constants.ExportConstant;
import com.zyhl.yun.member.mcdmc.compare.domain.cdr.config.CdrConfig;
import com.zyhl.yun.member.mcdmc.compare.domain.cdr.exception.ComparebillException;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 话单实现类
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
public final class CdrHelper {
    /**
     * 日志对象
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CdrHelper.class);

    @Setter
    private PrintWriter pw;

    @Setter
    private File tempFile;

    @Getter
    private CdrConfig config;

    private final AtomicLong counter = new AtomicLong(0);

    private final Object lockObj = new Object();

    private volatile boolean isInit = false;

    /**
     * 构造函数
     *
     * @param config config
     */
    public CdrHelper(CdrConfig config) {
        this.config = config;
    }

    /**
     * 初始化打印流
     *
     * @throws IOException IO异常
     * @throws ComparebillException 业务异常
     */
    public void initWriter() throws IOException, ComparebillException {
        if (isInit) {
            return;
        }

        synchronized (lockObj) {
            if (isInit) {
                return;
            }
            LineNumberReader lineNumberReader = null;
            try {
                createFolder(this.config.getTempPath());

                tempFile = new File(this.config.getTempPath() + File.separatorChar + this.config.getCdrTempName());
                this.setTempFile(tempFile);

                // 'true':考虑应用重启的场景，应该是追加模式
                pw = new PrintWriter(
                    new OutputStreamWriter(new FileOutputStream(tempFile, true), StandardCharsets.UTF_8));
                this.setPw(pw);

                if (tempFile.exists() && tempFile.length() > 0) {
                    lineNumberReader = new LineNumberReader(new BufferedReader(
                        new InputStreamReader(new FileInputStream(tempFile), StandardCharsets.UTF_8)));
                    long lineNumber = lineNumberReader.skip(Long.MAX_VALUE);
                    this.counter.set(lineNumber);

                } else {
                    this.counter.set(0);
                }
            } catch (ComparebillException e) {
                LOGGER.error("CdrFunctionServiceImpl.initWriter failed.", e);
                throw e;
            } catch (IOException e) {
                LOGGER.error("CdrFunctionServiceImpl.initWriter failed.", e);
                throw e;
            } finally {
                if (lineNumberReader != null) {
                    try {
                        lineNumberReader.close();
                    } catch (IOException e) {
                        LOGGER.error("CdrHelper.initWriter failed.", e);
                        lineNumberReader = null;
                    }
                }
            }

            isInit = true;
        }
    }

    /**
     * 核心方法，将话单写入文件
     *
     * @param message 话单信息
     */
    public void write(String message) {
        try {
            doWrite(message);
        } catch (Exception e) {
            LOGGER.error("CdrHelper.write failed.", e);
        }
    }

    /**
     * 打印话单内容
     *
     * @param message 话单内容
     */
    private void doWrite(String message) {
        synchronized (lockObj) {
            pw.print(message);
            pw.print(this.getConfig().getLineSeparator());
            pw.flush();
            counter.incrementAndGet();
        }
    }

    /**
     * 备份移动临时文件
     *
     * @param dateString 日期字符串
     * @throws Exception 异常
     */
    public void backupAndMove(String dateString) throws ComparebillException {
        if (!tempFile.exists() || tempFile.length() == 0) {
            return;
        }

        if (pw != null) {
            pw.close();
        }

        String normalPath = config.getNormalPath() + File.separator + config.getPrefix() + dateString
            + config.getSuffix() + "_" + formatCount();

        createFolder(config.getNormalPath());

        if (!tempFile.renameTo(new File(normalPath))) {
            LOGGER.error("move file failed!fileName={}, rename fileName={}", tempFile.getName(), normalPath);
            throw new ComparebillException(
                "move file failed!fileName=" + tempFile.getName() + ", rename fileName=" + normalPath);
        }
    }

    private String formatCount() {
        String count = String.valueOf(counter.get());

        // 文件中的记录总数，10位，右对齐，左补零
        StringBuilder countSb = new StringBuilder(ExportConstant.CDR_NUMBER_LENGTH);
        for (int i = 0; i < ExportConstant.CDR_NUMBER_LENGTH - count.length(); i++) {
            countSb.append(0);
        }

        return countSb.append(count).toString();
    }

    private static void createFolder(String folderFile) throws ComparebillException {
        File folder = new File(folderFile);

        if (!folder.exists()) {
            boolean mkdirs = folder.mkdirs();
            if (!mkdirs) {
                throw new ComparebillException("inner error:create dir failed!" + folder.getName());
            }
        }
    }

    public void setConfig(CdrConfig config) {
        this.config = config;
    }

    public PrintWriter getPw() {
        return pw;
    }

    public AtomicLong getCounter() {
        return counter;
    }
}
