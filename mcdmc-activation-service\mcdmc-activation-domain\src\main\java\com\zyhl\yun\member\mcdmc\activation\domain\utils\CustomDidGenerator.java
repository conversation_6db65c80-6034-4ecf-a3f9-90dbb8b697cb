package com.zyhl.yun.member.mcdmc.activation.domain.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.aspire.cbb.did.impl.CachedDidGenerator;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 生成分布式主键id的工具类
 *
 * <AUTHOR>
 * @since 2024/07/06 16:28
 */
@Configuration
public class CustomDidGenerator {
    private static CachedDidGenerator cachedDidGenerator;

    /**
     * 获取分布式主键id
     */
    public static String generateId() {
        if (cachedDidGenerator == null) {
            cachedDidGenerator = SpringUtil.getBean(CachedDidGenerator.class);
        }
        return String.valueOf(cachedDidGenerator.getDID());
    }


    @PostConstruct
    private void init() {
        CustomDidGenerator.setCachedDidGenerator(SpringUtil.getBean(CachedDidGenerator.class));
    }

    private static void setCachedDidGenerator(CachedDidGenerator cachedDidGenerator) {
        CustomDidGenerator.cachedDidGenerator = cachedDidGenerator;
    }

}
