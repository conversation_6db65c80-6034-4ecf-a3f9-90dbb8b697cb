package com.zyhl.yun.member.mcdmc.activation.domains.conditions;

import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/10 17:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class QueryWorkOrderCondition extends BaseCondition {


    private static final long serialVersionUID = -1062128211762937982L;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 流程编码列表
     */
    private List<String> serviceCodeList;

    /**
     * 工单id列表
     */
    private List<String> workIdList;

    /**
     * 订单id列表
     */
    private List<String> orderIdList;

    /**
     * 工单属性key列表
     *
     * @apiNote 若serviceId为QUERY_ACTIVATION_ATTR, 则该值为必填，且返回结果必须要包含attrKeyList中的任一属性
     * @see com.zyhl.yun.member.mcdmc.activation.constants.WorkAttrCodeConstant 工单属性key常量
     */
    private List<String> attrKeyList;

}
