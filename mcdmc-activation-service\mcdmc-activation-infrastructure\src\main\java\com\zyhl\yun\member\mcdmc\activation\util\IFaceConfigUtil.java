package com.zyhl.yun.member.mcdmc.activation.util;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.product.common.enums.TimePlanCycleTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class IFaceConfigUtil {


    /**
     * IFaceConfig配置
     */
    public String getFaceConfig(String iFaceConfig, String configKey) {
        if (Objects.isNull(iFaceConfig) || Objects.isNull(configKey)) {
            return null;
        }

        JSONObject entries = JSONUtil.parseObj(iFaceConfig);

        return entries.getStr(configKey);
    }
}
