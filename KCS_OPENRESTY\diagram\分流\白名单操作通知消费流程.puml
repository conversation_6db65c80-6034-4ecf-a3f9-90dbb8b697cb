@startuml
'https://plantuml.com/activity-diagram-beta

start
:收到消费消息;
:解析json报文;
:根据account从缓存获取对应白名单用户;
:从白名单用户获取state和effectTime;
if(state为已生效) then (true)
    stop;
else (false)
endif
if (effectTime为null and type = 号段用户操作) then (true)
    :根据号段从缓存获取对应effectTime;
else (false)
    stop;
endif
if (userOperateTime<redis.effectTime) then (true)
  :更新数据库及缓存白名单信息
   effectTime=(userOperateTime+silenceTimeSec)
   userLastOperateTime=userOperateTime;
else (false)
  :更新数据库及缓存白名单信息
   state=已生效;
endif
stop

@enduml
