package com.zyhl.yun.member.mcdmc.activation.remote.send.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/06/17 19:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RaiReturnReq {
    private ContractRoot contractRoot;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContractRoot {
        private Head head;
        private Body body;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Head {
        private String channelCode;

        private String reqTime;

        private String sign;

        private String apiId;

        private String transactionId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Body {
        private String channelNo;

        private String extfield1;
    }
}
