package com.zyhl.yun.member.mcdmc.activation.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.RetryPolicyEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024/6/4 14:40
 * 接口表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("activation_work_service_flow")
public class WorkServiceFlowPo extends BasePo {
    /**
     * 接口id
     */
    @TableId
    private String workServiceFlowId;

    /**
     * 服务id
     */
    private String serviceCode;
    /**
     * 流程描述
     */
    private String flowDesc;

    /**
     * 业务流程类名
     */
    private String serviceFlowClass;

    /**
     * 是否并行
     */
    private Short syncFlag;

    /**
     * 重试次数
     */
    private Integer retryCount;
    /**
     * 重试策略
     * 0.指定时间重试，单位：yyyyMMddHHmmss;
     * 1.立即重试;
     * 2.偏移时间重试,单位：毫秒;
     * 3.偏移时间重试,单位：秒;
     * 4.偏移时间重试,单位：分钟
     * 5.偏移时间重试,单位：小时
     * 6.偏移时间重试,单位：天
     * 7.偏移时间重试,单位：周
     * 8.偏移时间重试,单位：月
     *
     * @see RetryPolicyEnum RetryTypeEnum
     */
    private Integer retryPolicy;

    /**
     * 重试时间，根据retryType决定该值的含义，如果策略是偏移时间，则该值为偏移的时间
     */
    private String retryTime;

    /**
     * 状态
     */
    private Integer state;


}