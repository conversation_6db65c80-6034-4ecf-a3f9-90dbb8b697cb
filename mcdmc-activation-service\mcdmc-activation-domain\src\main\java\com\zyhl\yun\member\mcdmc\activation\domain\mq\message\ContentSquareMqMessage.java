package com.zyhl.yun.member.mcdmc.activation.domain.mq.message;


import lombok.Data;

import java.util.Map;

/**
 * 内容退费同步mq消息体
 *
 * <AUTHOR>
 * @since 2022/10/18 10:12
 */
@Data
public class ContentSquareMqMessage {
    /**
     * 消息唯一标识
     */
    private String traceID;
    /**
     * 操作时间，格式为yyyyMMssHH24mmssSSS，17位的字符串表示
     */
    private String oprTime;
    /**
     * 操作类型
     * 1：订购
     */
    private Integer actionType;
    /**
     * 订单ID
     */
    private String orderID;
    /**
     * 账号
     */
    private String account;
    /**
     * 订单中心用户唯一标识
     */
    private String ordUserID;
    /**
     * 产品ID
     */
    private String productID;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * "计费类型，默认0
     * 0.按次
     * 1.包月"
     */
    private Integer chargeType;
    /**
     * 支付渠道,当前取值有如下几种
     * 55：微信支付
     * 44：支付宝支付
     * 2：话费支付
     * 66:全业务计费平台微信支付
     * 77：全业务计费平台支付宝支付
     * 78：积分支付
     * 79：话费/积分支付
     * 86：苹果支付
     */
    private Integer payType;
    /**
     * 如果chargeType=1（包月）时有效
     * 1：续费  0：首次订购
     */
    private String renewFlag;

    /**
     * 操作类型，现在畅影4k可以支持这个
     * 1：首次订购，2：续费，3：点播，4：退费
     */
    private Integer operator;
    /**
     * rightsServiceSubID	权益中心订单ID	String	128	O
     * rightsServiceID	权益中心套餐ID	String	128	O
     * rightsServiceSkuID	权益中心套餐规格ID	String	128	O
     * rightsOutAccountID	权益方外部帐号ID	String	128	O	比如学信网开通权益返回的帐号ID
     * rightsOutProductID	权益中心外部产品ID	String	128	O	学信网的产品ID：m1、m2等)
     * rightsStartTime	权益开始时间	String	17	O	"格式为yyyyMMssHH24mmssSSS，17位的字符串表示，
     * 以权益中心开通权益后回调接口传入时间为准"
     * rightsEndTime	权益到期时间	String	17	O	格式为yyyyMMssHH24mmssSSS，17位的字符串表示
     * renewFlag	是否续费	String	1	O	"如果chargeType=1（包月）时有效
     * 1：续费  0：首次订购"
     */
    private Map<String, Object> extInfo;

    private String userDomainId;

}
