package com.zyhl.yun.member.facade;

import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.domain.user.domain.QueryUserCondition;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.common.enums.UserStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

import static com.zyhl.yun.member.common.domain.serviceid.DefaultServiceId.DEFAULT;
import static org.junit.jupiter.api.Assertions.*;
/**
 * <AUTHOR>
 * @date 2025/3/31
 * @description 单元测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class UserServiceFacadeTest {

    @Test
    public void getAfterCancelUserDoFromDb_ValidMsisdn_ReturnsUserDo() {
        String msisdn = "***********";
        UserDo expectedUserDo = new UserDo();
        List<UserDo> userDoList = new ArrayList<>();
        userDoList.add(expectedUserDo);

        QueryUserCondition queryUserCondition = new QueryUserCondition();
        queryUserCondition.setAccount(msisdn);
        List<UserStatusEnum> statusList = new ArrayList<>();
        statusList.add(UserStatusEnum.NORMAL);
        statusList.add(UserStatusEnum.CANCELLED);
        queryUserCondition.setStatusList(statusList);
        DomainServiceContext domainServiceContext = new DomainServiceContext(DEFAULT);
        List<UserDo> userDos = domainServiceContext.read(queryUserCondition, UserDo.class);

        assertNotNull(userDos);

        // 过滤掉 null 元素
        // 根据 updatedTime 排序
        // 处理 null 值，null 放到最后
        UserDo userDo = userDos.stream()
                .filter(Objects::nonNull)
                .min(Comparator.comparing(
                        UserDo::getUpdatedTime, // 根据 updatedTime 排序
                        Comparator.nullsLast(Comparator.naturalOrder()) // 处理 null 值，null 放到最后
                ))
                .orElse(null);

        assertEquals(expectedUserDo, userDo);
    }
}
