package com.zyhl.yun.member.mcdmc.activation.remote.send.req;


import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/3 10:37
 * @descrition 集运平台免流订购请求参数
 */
@Data
@ToString
public class AddOrderReq {
    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 来源订单ID
     */
    private String sourceOrderNo;

    /**
     * 来源系统
     */
    private String sourceApp;

    /**
     * 手机号码
     */
    private String telephone;

    /**
     * 来源goodsId
     */
    private String sourceGoodsId;

    /**
     * 三方账号扩展信息
     */
    private ThirdpartyAccountExt thirdpartyAccountExt;

    /**
     * <AUTHOR>
     * @version 1.0
     * @date 2024/7/3 10:37
     * @descrition 集运平台三方账号扩展信息
     */
    @Data
    @ToString
    public class ThirdpartyAccountExt {
        /**
         * 三方账号类型（0：QQ号 1：抖音号）
         */
        private String thirdpartyAccountType;

        /**
         * 三方账号ID
         */
        private String thirdpartyAccountId;
    }
}
