package com.zyhl.yun.member.mcdmc.activation.domains;

import com.zyhl.yun.member.common.domain.framework.BaseDo;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/06/18 09:40
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WorkOrderAttrDo extends BaseDo implements Serializable {

    /**
     * 属性id
     */
    private Long attrId;


    /**
     * 工单id
     */
    private String workId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 属性编码
     */
    private String attrCode;

    /**
     * 属性值
     */
    private String attrVal;

    /**
     * 状态
     */
    private String state;

    public WorkOrderAttrDo(DomainServiceContext domainServiceContext) {
        this.domainServiceContext = domainServiceContext;
    }
}
