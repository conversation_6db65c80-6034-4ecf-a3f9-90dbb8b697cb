package com.zyhl.yun.member.mcdmc.activation.remote.send.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @since 2024/06/26 20:03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RaiSubQueryReq {

    private ContractRoot contractRoot;

    public RaiSubQueryReq(RaiSubReq.Head head, Body body) {
        this.contractRoot = new ContractRoot(head, body);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContractRoot {
        private RaiSubReq.Head head;
        private Body body;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Body {
        /**
         * 渠道订单号
         * 手机号码为空时必填
         */
        private String channelNo;// ": "2001807130001",

        /**
         * 受理设备号码
         * 渠道流水号为空时必填
         */
        private String serverNum;// ": "13712341234",

        /**
         * 主订单状态：0-未处理，1-待支付，2-支付异常，3-待开通，4-开通异常 ，5-订单完成
         * 子订单状态：0-未处理，1-开通成功，2-开通失败，3-超时，4-开通中
         */
        private String status;// ": "状态",

        /**
         * 可选，订单所属渠道ID, 为空则默认HEAD相同的channelCode
         */
        private String channelCode;

        /**
         * 销售品ID
         */
        private String salesId;// ": "2018001",
    }
}
