package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.refund;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.domain.framework.service.MdcLogInterceptor;
import com.zyhl.yun.member.consumer.common.producer.MqProducer;
import com.zyhl.yun.member.facade.GoodsInstanceServiceFacade;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.rai.RenewFlagEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.mq.properties.ContentMqProperties;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.domain.mq.message.ContentSquareMqMessage;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.RaiReturnReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.RaiReturnRsp;
import com.zyhl.yun.member.product.common.enums.ChargeTypeEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 畅影权益退费流程
 * com.huawei.jaguar.vsbo.service.serviceimpl.RefundServiceImpl#rightUnSubscribeAndRefund
 *
 * <AUTHOR>
 * @since 2024/06/25 15:19
 */
@Component
public class RaiRefundChangYingIFace extends RaiRefundNormalIFace {
    @Resource
    private MqProducer mqProducer;
    @Resource
    private ContentMqProperties contentMqProperties;

    @Override
    protected void doBusiComplete(InterfaceContext<RaiReturnReq, RaiReturnRsp> interfaceContext) {
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        // 畅影权益退费后执行的动作
        ContentSquareMqMessage contentSquareMqMessage = new ContentSquareMqMessage();
        contentSquareMqMessage.setTraceID(MdcLogInterceptor.getCurrentTraceId());
        DateTime oprTime = DateUtil.parse(comSendReq.getEffectiveEndTime(), DatePattern.PURE_DATETIME_PATTERN);
        contentSquareMqMessage.setOprTime(DateUtil.format(oprTime, DatePattern.PURE_DATETIME_MS_PATTERN));
        contentSquareMqMessage.setActionType(1);
        contentSquareMqMessage.setOrderID(comSendReq.getOrderID());
        String userId = comSendReq.getUserId();
        contentSquareMqMessage.setOrdUserID(userId);
        contentSquareMqMessage.setAccount(comSendReq.getMsisdn());
        contentSquareMqMessage.setProductID(comSendReq.getGoodsId());
        contentSquareMqMessage.setProductName(comSendReq.getGoodsName());
        contentSquareMqMessage.setChargeType(ChargeTypeEnum.BY_TIMES.getType());
        contentSquareMqMessage.setPayType(Integer.parseInt(comSendReq.getPayWay()));
        contentSquareMqMessage.setOperator(RenewFlagEnum.REFUND.getOpType());
        contentSquareMqMessage.setUserDomainId(comSendReq.getUserDomainId());
        Map<String, Object> extInfoMap = new HashMap<>();
        GoodsInstanceDo goodInstanceDo = (GoodsInstanceDo) interfaceContext.getWorkOrderDo().getExtData(OtherFieldConstants.GOODS_INSTANCE_DO);
        if (null == goodInstanceDo) {
            goodInstanceDo = GoodsInstanceServiceFacade.qryGoodsInstance(userId, comSendReq.getGoodsInstanceId());
        }
        String receiveResourceId = goodInstanceDo.getResourceId();
        // 订购时权益方返回的订单id
        extInfoMap.put(OtherFieldConstants.RIGHTS_SERVICE_SUB_ID, receiveResourceId);
        extInfoMap.put(OtherFieldConstants.RIGHTS_OUT_ACCOUNT_ID, receiveResourceId);

        JSONObject proJsonObj = JSONUtil.parseObj(comSendReq.getProServiceId());
        extInfoMap.put(OtherFieldConstants.RIGHTS_SERVICE_ID, proJsonObj.getStr(OtherFieldConstants.SALES_ID));
        extInfoMap.put(OtherFieldConstants.RIGHTS_SERVICE_SKU_ID, proJsonObj.getStr(OtherFieldConstants.PROD_ID));
        extInfoMap.put(OtherFieldConstants.RIGHTS_OUT_PRODUCT_ID, proJsonObj.getStr(OtherFieldConstants.RIGHTS_PROD_ID));
        DateTime startTime = DateUtil.parse(comSendReq.getEffectiveStartTime(), DatePattern.PURE_DATETIME_PATTERN);
        extInfoMap.put(OtherFieldConstants.RIGHTS_START_TIME, DateUtil.format(startTime, DatePattern.NORM_DATETIME_PATTERN));
        extInfoMap.put(OtherFieldConstants.RIGHTS_END_TIME, DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));

        contentSquareMqMessage.setExtInfo(extInfoMap);
        mqProducer.sendOuterMessage(contentMqProperties, JSONUtil.toJsonStr(contentSquareMqMessage));

    }
}
