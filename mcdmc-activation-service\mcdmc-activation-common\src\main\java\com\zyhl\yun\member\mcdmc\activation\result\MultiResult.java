package com.zyhl.yun.member.mcdmc.activation.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.Collections;

/**
 * <AUTHOR>
 * @since 2023/01/06 21:33
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MultiResult<T> extends BaseResult {
    private Collection<T> data;


    public MultiResult(String resultCode, String message, Collection<T> data) {
        super(resultCode, message);
        this.data = data;
    }

    public MultiResult(boolean success, String resultCode, String message, Collection<T> data) {
        super(success, resultCode, message);
        this.data = data;
    }

    public static <T> MultiResult<T> success() {
        return success(Collections.emptyList());
    }

    public static <T> MultiResult<T> success(Collection<T> data) {
        return success(ResultCode.SUCCESS, data);
    }

    public static <T> MultiResult<T> success(ResultCode resultCode, Collection<T> data) {
        return success(resultCode.getCode(), resultCode.getMsg(), data);
    }

    public static <T> MultiResult<T> success(String code, String msg, Collection<T> data) {
        return new MultiResult<>(true, code, msg, data);
    }
}
