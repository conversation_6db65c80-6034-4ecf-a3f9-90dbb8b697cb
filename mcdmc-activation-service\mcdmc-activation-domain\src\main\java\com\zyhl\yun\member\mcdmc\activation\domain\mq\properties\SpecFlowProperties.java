package com.zyhl.yun.member.mcdmc.activation.domain.mq.properties;

import com.zyhl.yun.member.consumer.common.producer.MqFlowStateEnum;
import com.zyhl.yun.member.consumer.common.producer.properties.IMqFlow;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 定向免流mq配置
 *
 * <AUTHOR>
 * @since 2024/09/23 11:09
 */
@Data
@Configuration
@ConfigurationProperties("rocketmq.consumer.spec-flow")
public class SpecFlowProperties implements IMqFlow {

    /**
     * 重构后定向免流订购topic
     */
    String specFlowTopic = "SpecFlow";
    /**
     * 重构后定向免流订购tag
     */
    String specFlowTag = "subscribe";
    /**
     * 重构前定向免流订购topic
     */
    String oldSpecFlowTopic = "SpecFlow";
    /**
     * 重构前定向免流订购tag
     */
    String oldSpecFlowTag = "subscribe";

    /**
     * mq流转状态
     *
     * @see MqFlowStateEnum
     */
    private String flowStatus = MqFlowStateEnum.OLD.getState();
    @Override
    public String getNewTopic() {
        return specFlowTopic;
    }

    @Override
    public String getOldTopic() {
        return oldSpecFlowTopic;
    }

    @Override
    public String getNewTag() {
        return specFlowTag;
    }

    @Override
    public String getOldTag() {
        return oldSpecFlowTag;
    }
}
