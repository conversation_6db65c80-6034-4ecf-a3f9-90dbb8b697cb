package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.free_flow;

import com.zyhl.yun.member.common.constants.HeaderConstant;
import com.zyhl.yun.member.common.domain.framework.service.MdcLogInterceptor;
import com.zyhl.yun.member.common.util.MsisdnUtil;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.SendTemplate;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.OpenFreeFlowProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.OpenNumberStateCheckReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.OpenNumberStateCheckRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


/**
 * 一级能开免流号码校验
 * com.huawei.jaguar.vsbo.service.serviceimpl.asyncService.SubscribeNotifyFlowRetrySubThread#run
 * 地址配置：/config/vsbo/goToneConfig/numberStateCheckUrl
 *
 * <AUTHOR>
 * @since 2024/06/24 15:14
 */
@Slf4j
@Component
public class OpenNumberCheckIFace extends SendTemplate<OpenNumberStateCheckReq, OpenNumberStateCheckRsp> {
    @Resource
    protected OpenFreeFlowProperties openFreeFlowProperties;

    @Override
    protected Map<String, String> getRequestHeader(InterfaceContext<OpenNumberStateCheckReq, OpenNumberStateCheckRsp> interfaceContext) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(HttpHeaders.CONTENT_TYPE, interfaceContext.getMediaType().toString());
        headerMap.put(HeaderConstant.SRC_MOD_NAME, HeaderConstant.SRC_MOD_NAME_VALUE);
        headerMap.put(HeaderConstant.MCS_FLOW_ID, MdcLogInterceptor.getCurrentTraceId());
        return headerMap;
    }

    @Override
    protected OpenNumberStateCheckReq getRequestBody(InterfaceContext<OpenNumberStateCheckReq, OpenNumberStateCheckRsp> interfaceContext) {
        // 手机号去86(国家码)逻辑
        String msisdn = MsisdnUtil.getRawNumber(interfaceContext.getComSendReq().getMsisdn(), interfaceContext.getComSendReq().getNationCode());
        interfaceContext.getComSendReq().setMsisdn(msisdn);
        OpenNumberStateCheckReq req = new OpenNumberStateCheckReq();
        req.setNumType("1");
        req.setVersion(openFreeFlowProperties.getNumberStateVersion());
        req.setNumber(msisdn);
        return req;
    }

    @Override
    protected void doBusiFail(InterfaceContext<OpenNumberStateCheckReq, OpenNumberStateCheckRsp> interfaceContext) {
        log.debug("open free flow  number check fail and will restart,third platform return is {},workOrder is {}",
                interfaceContext.getInterfaceRspObjStr(), interfaceContext.getWorkOrderDo().toSimpleLogStr());
        throw new FlowTerminationException(this.getClass(), interfaceContext.getWorkOrderDo(), NextHintEnum.RESTART,
                "open free flow  number check fail and will restart,third platform return is %s,workOrder is %s",
                interfaceContext.getInterfaceRspObjStr(), interfaceContext.getWorkOrderDo().toSimpleLogStr());
    }

    @Override
    protected boolean isSuccess(OpenNumberStateCheckRsp rsp) {
        return OpenNumberStateCheckRsp.isSuccess(rsp);
    }

    @Override
    protected Class<OpenNumberStateCheckReq> getReqClass() {
        return OpenNumberStateCheckReq.class;
    }

    @Override
    protected Class<OpenNumberStateCheckRsp> getRspClass() {
        return OpenNumberStateCheckRsp.class;
    }

}
