package com.zyhl.yun.member.mcdmc.activation.remote.send.properties;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 明威权益相关配置
 *
 * <AUTHOR>
 * @since 2024/06/18 14:44
 */
@Data
@Configuration
@EqualsAndHashCode(callSuper = true)
@ConfigurationProperties(prefix = "platform.rai.movie")
public class RaiMovieProperties extends RaiBaseProperties {
}
