package com.zyhl.yun.member.mcdmc.activation.web;

import com.zyhl.yun.member.mcdmc.activation.service.CustomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/09/21 13:16
 */
@Slf4j
@RestController
public class FlowController {
    @Resource
    private CustomService customService;

    /**
     * 分流获取userId的接口
     */
    @PostMapping(value = "/custom/flowGetUserId")
    public String flowGetUserId(@RequestParam("uri") String uri,
                                @RequestParam("searchCondition") String searchCondition) {
        return customService.flowGetUserId(uri, searchCondition);
    }
}
