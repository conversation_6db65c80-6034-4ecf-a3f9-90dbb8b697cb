package com.zyhl.yun.member.common.cache.wrapper;

import cn.hutool.core.collection.CollectionUtil;
import com.zyhl.yun.member.common.cache.EntityCacheable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/11/20 14:33
 */
@Slf4j
public class CollectionWrapper implements CacheValueWrapper {


    private static final long serialVersionUID = -2416272811274973846L;

    private Map<Serializable, Serializable> cacheValueWrapperMap = new HashMap<>();

    private List<Serializable> keyList;

    private List<Serializable> valueList = new ArrayList<>();

    private List<Serializable> notMacheKeyList;

    public CollectionWrapper(List<Serializable> keyList, List<? extends Serializable> valueList) {

        this.keyList = new ArrayList<>(keyList);
        if (CollectionUtil.isEmpty(keyList)) {
            notMacheKeyList = Collections.emptyList();
            return;
        }

        if (CollectionUtil.isEmpty(valueList)) {
            notMacheKeyList = new ArrayList<>(keyList);
            return;
        }

        for (Object value : valueList) {
            if (value instanceof SimpleValueWrapper) {
                SimpleValueWrapper cacheValueWrapper = (SimpleValueWrapper) value;
                if (!CollectionUtils.isEmpty(cacheValueWrapper.getValueList())) {
                    cacheValueWrapperMap.put(cacheValueWrapper.getKey(), (Serializable) cacheValueWrapper.getValueList());
                } else {
                    cacheValueWrapperMap.put(cacheValueWrapper.getKey(), cacheValueWrapper.getValue());
                }
            } else if (value instanceof EntityCacheable) {
                EntityCacheable entityCacheable = (EntityCacheable) value;
                cacheValueWrapperMap.put(entityCacheable.getCacheKey(), entityCacheable);
            }
        }

        notMacheKeyList = new ArrayList<>();
        for (Serializable key : keyList) {
            if (!cacheValueWrapperMap.containsKey(key)) {
                notMacheKeyList.add(key);
            } else {
                if (cacheValueWrapperMap.get(key) != null) {
                    this.valueList.add(cacheValueWrapperMap.get(key));
                }
            }
        }
    }

    public List<Serializable> getNotMacheKeyList() {
        return new ArrayList<>(notMacheKeyList);
    }


    public List<Serializable> getMacheKeyList() {
        if (CollectionUtil.isEmpty(cacheValueWrapperMap)) {
            return Collections.emptyList();
        }

        return new ArrayList<>(cacheValueWrapperMap.keySet());
    }

    public Serializable getValueElement(Serializable key) {
        return cacheValueWrapperMap.get(key);
    }

    @Override
    public Serializable getKey() {
        return new ArrayList<>(this.keyList);
    }

    @Override
    public Serializable getValue() {
        return new ArrayList<>(this.valueList);
    }

    public List<Serializable> getValueList() {
        return new ArrayList<>(this.valueList);
    }


    public List<Serializable> getKeyList() {
        return new ArrayList<>(this.keyList);
    }
}
