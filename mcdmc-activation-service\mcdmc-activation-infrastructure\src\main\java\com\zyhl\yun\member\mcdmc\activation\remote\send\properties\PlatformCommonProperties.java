package com.zyhl.yun.member.mcdmc.activation.remote.send.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 公共配置
 *
 * <AUTHOR>
 * @since 2024/08/05 19:52
 */
@Data
@Configuration
@ConfigurationProperties("platform.common")
public class PlatformCommonProperties {
    /**
     * 远程超时时间，单位秒
     */
    private Long remoteTimeoutSec = 5L;
}
