package com.zyhl.yun.member.common.cache.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.zyhl.yun.member.common.cache.ConditionCacheable;
import com.zyhl.yun.member.common.cache.DelegationCache;
import com.zyhl.yun.member.common.cache.wrapper.CollectionWrapper;
import com.zyhl.yun.member.common.cache.wrapper.ConditionWrapper;
import com.zyhl.yun.member.common.cache.wrapper.SimpleValueWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/20 20:46
 */
@Slf4j
public abstract class BaseDelegationCacheImpl implements DelegationCache {


    @Override
    public void putList(String cacheSpace, CollectionWrapper collectionWrapper) {
        log.debug("[CACHE] put value list to cache. cacheSpace: {}", cacheSpace);
        if (CollectionUtil.isEmpty(collectionWrapper.getKeyList())) {
            return;
        }
        for (Serializable key : collectionWrapper.getKeyList()) {
            put(cacheSpace, key, new SimpleValueWrapper(key, collectionWrapper.getValueElement(key)));
        }
    }


    protected String getConditionHashKey(ConditionCacheable conditionCacheable) {
        return ConditionWrapper.getConditionHashKey(conditionCacheable);
    }


    protected String getConditionCountHashKey(ConditionCacheable conditionCacheable) {
        return ConditionWrapper.getConditionHashKey(conditionCacheable) + ":TOTAL_COUNT";
    }

    protected List<String> getConditionKeyList(String cacheSpace, ConditionCacheable conditionCacheable) {
        if (CollectionUtils.isEmpty(conditionCacheable.getCacheKeyList())) {
            return Collections.emptyList();
        }
        List<String> conditionKeyList = new ArrayList<>();
        for (Object cacheKey : conditionCacheable.getCacheKeyList()) {
            conditionKeyList.add(cacheSpace + ":Condition:" + cacheKey);
        }

        return conditionKeyList;
    }
}
