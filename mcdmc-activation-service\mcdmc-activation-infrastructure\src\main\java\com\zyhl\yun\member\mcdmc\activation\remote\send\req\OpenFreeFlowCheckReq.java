package com.zyhl.yun.member.mcdmc.activation.remote.send.req;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 免流/一级能开-业务办理资格校验 请求体
 *
 * <AUTHOR>
 * @apiNote 能力开放平台业务办理资格校验接口请求对象
 * @since 2024/06/19 11:30
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "BizQualificationReq")
public class OpenFreeFlowCheckReq {
    /**
     * 业务号码
     */
    @XmlElement(name = "Number")
    private String number;

    /**
     * 消息类型，默认BizQualificationReq
     */
    @XmlElement(name = "MsgType")
    private String msgType;

    /**
     * 该接口消息的版本号
     */
    @XmlElement(name = "Version")
    private String version;

    /**
     * 业务号码类型
     */
    @XmlElement(name = "NumType")
    private String numType;

    /**
     * 商品编码
     */
    @XmlElement(name = "GoodsId")
    private String goodsId;

    /**
     * APP服务列表
     */
    @XmlElement(name = "ServiceIdList")
    private String serviceIdList;
}
