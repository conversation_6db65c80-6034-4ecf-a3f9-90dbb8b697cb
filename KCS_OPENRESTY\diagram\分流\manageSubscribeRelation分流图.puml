@startuml
'https://plantuml.com/activity-diagram-beta

start
:开始分流;
if (uri=manageSubscribeRelation？) then (true)
  :解析报文，获取入参eventType;
    if (eventType=1？) then (yes)
      :获取用户信息;
      if (用户为白名单?) then (yes)
         : 转发新vsbo;
      else (no)
          : 转发旧vsbo;
      endif
    else (no)
      : 转发新vsbo(新vsbo需要改造orderId生成规则);
      : 新vsbo根据订单号判断是否新vsbo生成，\n若是旧vsbo生成规则，则调用旧vsbo\n若是新vsbo生成规则，则直接处理;
    endif
    stop
else (false)
endif
: 根据对应接口映射规则处理转发;
stop
@enduml
