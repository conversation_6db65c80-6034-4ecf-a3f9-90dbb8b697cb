package com.zyhl.yun.member.mcdmc.activation.convertor;

import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowLogDo;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.FlowLogStateEnum;
import com.zyhl.yun.member.mcdmc.activation.po.WorkServiceFlowLogPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/06/06 19:14
 */
@Mapper(componentModel = "spring", imports = FlowLogStateEnum.class)
public interface WorkServiceFlowLogConvertor {

    @Mapping(target = "state", expression = "java(workServiceFlowLogDo.getState().getState())")
    WorkServiceFlowLogPo toWorkFlowLogPO(WorkServiceFlowLogDo workServiceFlowLogDo);

    @Mapping(target = "state", expression = "java(FlowLogStateEnum.getByState(workServiceFlowLogPo.getState()))")
    WorkServiceFlowLogDo toDo(WorkServiceFlowLogPo workServiceFlowLogPo);
    List<WorkServiceFlowLogDo> toDoList(List<WorkServiceFlowLogPo> poList);
}
