flow = {}
local xml = require "aspire/xmlSimple"
local xmlParser = xml.newParser()

local jsonParser = require "aspire/json"
local http = require "aspire.resty.http"

function flow.turnTo()
    local startTime = ngx.now()
    local conf_dict = ngx.shared.conf_dict
    local globalSwitch = conf_dict:get("globalSwitch")
    if globalSwitch and globalSwitch == "new" then
        -- 全局开关打开且为直接转发至新vsbo
        flow.turnToNew(ngx.var.uri,"unknown","Vsbo","globalSwitch is turn to new")
        return
    end
    local headers = ngx.req.get_headers()
    if not headers["flow-type"] then
        -- 现网环境需要加异常控制
        local ok, errMsg = pcall(flow.realFlow)
        if not ok then
            -- 有异常则直接将流量打回旧vsbo
            ngx.log(ngx.ERR,"call flow exception,detail msg is " .. errMsg)
            local bodyStr = (ngx.req.get_body_data()==nil and "" or ngx.req.get_body_data())
            local contentType = (ngx.var.http_content_type == nil and 'nil' or ngx.var.http_content_type)
            local uri = ngx.var.uri
            local msg= "bodyStr is " .. bodyStr .. ",contentType is " .. contentType .. ",exception is " .. errMsg
            flow.turnToOld(uri,"unknown","Vsbo",msg)
        end
    else
        ngx.log(ngx.ERR,"++++++++ emptyFlow")
        flow.emptyFlow()
    end
   local endTime=ngx.now()
   ngx.log(ngx.INFO,"++++++++ flow cost time=",endTime-startTime,",startTime=",startTime,",endTime=",endTime)

end

function flow.emptyFlow()
    local isXml,pathDict=flow.getPathDict()
    local uri,dictKey,path=flow.getNextPath(pathDict,0)
    if not path then
        -- 没有映射规则则直接转发至旧vsbo
        flow.turnToOld(uri,"unknown","Vsbo","unknown")
        return
    end
    ngx.log(ngx.INFO,"++++++++ parsePath dictKey=[",dictKey,"],path=[",path,"]")
    local rule,platform,userType,realPath=flow.parsePath(path)
    flow.turnToOld(uri,"unknown",platform,"emptyFlow")
end

-- 检查全局开关，打开则一并跳转
function flow.checkGlobalSwitchAndTurn(uri,platform,path)
    local conf_dict = ngx.shared.conf_dict
    local globalSwitch = conf_dict:get("globalSwitch")
    if globalSwitch and globalSwitch == "old" then
        -- 全局开关打开，直接转发至旧平台
        flow.turnToOld(uri,"unknown",platform,"globalSwitch is turn to old,path is : " .. path)
        return true
    end
    return false
end

function flow.realFlow()
    local uri = ngx.var.uri
    local endingStr="syncAppOrder"
    if string.find(uri, endingStr, #uri - #endingStr + 1) then
        flow.syncAppOrder()
        return
    end

    local isXml,pathDict=flow.getPathDict()
    local index = 0
    -- 拼接规则 0：不需要分割；1：userId_xxx;2:xxx_userId
    local reqBodyObj
    while true do
        local uri,dictKey,path=flow.getNextPath(pathDict,index)
        if not path then
            -- 没有映射规则则直接转发至旧vsbo
            flow.turnToOld(uri,"unknown","Vsbo","isXml = " .. isXml .. ", not Path")
            return
        end
        ngx.log(ngx.INFO,"++++++++ parsePath dictKey=[",dictKey,"],path=[",path,"]")
        local rule,platform,userType,realPath=flow.parsePath(path)

        -- 全局开关打开，直接转发至旧平台
        local isGlobalSwitch = flow.checkGlobalSwitchAndTurn(uri,platform,path)
        if isGlobalSwitch then
            return
        end

        if rule == 4 then
            -- 直接转发至新vsbo
            flow.turnToNew(uri,userId,platform,rule)
            return
        elseif rule == 7 then
            flow.mirror(userType,platform)
            if userType and userType == "old" then
                -- 镜像到旧Vsbo,则转发到新Vsbo
                flow.turnToNew(uri,userId,platform,rule)
                return
            else
                -- 默认镜像到新Vsbo,转发到旧Vsbo
                flow.turnToOld(uri,userId,platform,rule)
            end
            return
        end
        if not reqBodyObj then
            -- 获取请求体对象
            reqBodyObj=flow.getReqBodyObj(isXml)
        end
        local userId = flow.getPathValue(reqBodyObj,realPath,isXml,rule)
        if  userId then
            if rule == 3 then
                if string.match(userId, "_rft$") then
                    flow.turnToNew(uri,userId,platform,rule)
                else
                    flow.turnToOld(uri,userId,platform,rule)
                end
                return
            elseif rule == 5 then
                if flow.isNewOrderId(userId) then
                    flow.turnToNew(uri,userId,platform,rule)
                else
                    flow.turnToOld(uri,userId,platform,rule)
                end
                return
            elseif rule == 6 then
                if flow.isNewOrderIdByRule6(userId) then
                    flow.turnToNew(uri,userId,platform,rule)
                else
                    flow.turnToOld(uri,userId,platform,rule)
                end
                return
            elseif rule == 1 and string.find(userId,"_") then
                -- userId_***分割规则，需要根据_分割，并获取第一个字符串，为userId
                if string.find(userId,"_") then
                    userId = string.match(userId, "^([^_]+)")
                else
                    flow.turnToOld(uri,userId,platform,rule)
                    return
                end
            elseif rule == 2 then
                if string.find(userId,"_") then
                    -- **_userId分割规则，需要根据_分割，并获取最后一个字符串，为userId
                    userId = string.match(userId, "[^_]+$")
                else
                    flow.turnToOld(uri,userId,platform,rule)
                    return
                end
            end
            -- 获取userId需要去调用白名单服务判断是否白名单
            local isWhite = flow.isRemoteWhiteUser(uri,userId,userType)
            if isWhite then
                flow.turnToNew(uri,userId,platform,rule)
            else
                flow.turnToOld(uri,userId,platform,rule)
            end
            return
        else
            index = index + 1
        end
    end
end


function flow.turnToNew(uri,userId,platform,rule)
    local conf_dict = ngx.shared.conf_dict
    -- 跳转到新vsbo
    local index = conf_dict:get("newIndex")
    ngx.ctx.port = conf_dict:get("newVsboPort" .. index)
    ngx.ctx.ip_addr = conf_dict:get("newVsboIp" .. index)
    ngx.log(ngx.ERR,"----------- uri:[",uri,"]userId =[",userId,"][rule=",rule,"] is contain whiteValue,turn to new",platform,"[",ngx.ctx.ip_addr,":",ngx.ctx.port,"]")
end
function flow.turnToOld(uri,userId,platform,rule)
    local conf_dict = ngx.shared.conf_dict
    -- 跳转到新vsbo
    local index = conf_dict:get("oldIndex")
    ngx.ctx.port = conf_dict:get("old" .. platform .. "Port" .. index)
    ngx.ctx.ip_addr = conf_dict:get("old" .. platform .. "Ip" .. index)
    ngx.log(ngx.ERR,"----------- uri:[",uri,"]userId =[",userId,"][rule=",rule,"] is not whiteValue,turn to old",platform,"[",ngx.ctx.ip_addr,":",ngx.ctx.port,"]")
end

function flow.getPathValue(reqBodyObj,realPath,isXml,joinRule)
    -- 遍历报文获取userId
    local destBody = reqBodyObj
    local pathIndex = 1
    for path in string.gmatch(realPath, "[^.]+") do
        -- 获取对应的value，可能为table，也可能为string
        if isXml then
            destBody = flow.getXmlValue(destBody,path,pathIndex)
        else
            destBody = flow.getJsonValue(destBody,path,pathIndex)
        end
        if not destBody then
            break
        end
        pathIndex = pathIndex + 1
    end
    if destBody then
        -- 正常获取到最后的报文就是指定的userId
        local result = destBody
        if isXml then
            result = result:getXmlValue()
        end
        ngx.log(ngx.INFO,"----------- realPath is [",realPath,"],getPathValue result is ",result)
        return result
    end
    return nil
end
-- 从xml里获取目标key（单层）
function flow.getXmlValue(xmlBody,destKey,keyIndex)
    if keyIndex == 1 then
        return xmlBody:getXmlChildren()[1]
    else
        return xmlBody[destKey]
    end
end
-- 从json里获取目标key（单层）
function flow.getJsonValue(jsonObj,destKey)
    if type(jsonObj) == "table" then
        -- 为table类型，则继续往下获取嵌套value
        for key, value in pairs(jsonObj) do
            if key == destKey then
                return value
            end
        end
    else
        -- 若为非table类型，且此时还没达到最后的path，则直接返回nil（忽略数组类型）
        return nil
    end
end

-- 根据contentType解析报文,获取对象
function flow.getReqBodyObj(isXml)
    local bodyStr = ngx.req.get_body_data()
    local headers = ngx.req.get_headers()
    local bodyEncryptFlag = headers["hcy-cool-flag"]
    if tostring(bodyEncryptFlag) == '1' then
        -- 表示报文被加密，需要先解密报文
        bodyStr = flow.decryptBody(bodyStr,headers)
    end
    ngx.log(ngx.INFO,"current isXml:[",isXml,"], bodyStr is ",bodyStr)
    if isXml then
        return xmlParser:ParseXmlText(bodyStr)
    else
        return jsonParser.decode(bodyStr)
    end
end

-- 获取字典映射
function flow.getPathDict()
    -- 优先判断接口是否在配置文件里，若在则直接返回对应的类型
    local xml_path_dict= ngx.shared.xml_path_dict
    local json_path_dict= ngx.shared.json_path_dict
    local uri = ngx.var.uri
    local lastUri = string.match(uri,"([^/]+)$")
    if xml_path_dict:get(lastUri) ~= nil then
        return true,xml_path_dict
    elseif json_path_dict:get(lastUri) ~= nil then
        return false,json_path_dict
    end
    -- 配置文件不存在，则根据contentType判断报文类型
    local contentType = ngx.var.http_content_type
    if not contentType then
        -- 如果没传content-type，则默认为xml
        return true,ngx.shared.xml_path_dict
    end
    local isXml = string.find(string.lower(contentType),"xml")
    if isXml then
        return true,ngx.shared.xml_path_dict
    else
        return false,ngx.shared.json_path_dict
    end
end

-- 获取下一个path
function flow.getNextPath(pathDict,index)
    local uri = ngx.var.uri
    local lastUri = string.match(uri,"([^/]+)$")
    local dictKey = lastUri
    if index >= 1 then
        dictKey = lastUri .. "_" .. index
        local nextPath=pathDict:get(dictKey)
        if not nextPath then
            -- 对于pgw，需要直接根据uri获取下一个path
            dictKey = uri .. "_" .. index
            nextPath=pathDict:get(dictKey)
            return uri,dictKey,nextPath
        end
        return lastUri,dictKey,nextPath
    end
    local nextPath=pathDict:get(dictKey)
    if not nextPath then
        -- 对于pgw，需要直接根据uri获取下一个path
        dictKey = uri
        nextPath=pathDict:get(dictKey)
    return uri,dictKey,nextPath
    end
    return lastUri,dictKey,nextPath
end

-- 解析映射表里的路径
function flow.parsePath(path)
    local rule,remainStr=string.match(path,"^(%w+)_*(.*)$")
    local platform,remainStr=string.match(remainStr,"^(%w+)_*(.*)$")
    if remainStr then
        local userType,remainStr=string.match(remainStr,"^(%w+)_*(.*)$")
        local realPath=remainStr
        ngx.log(ngx.INFO,"path=[",path,"]rule=[",rule,"],platform=[",platform,"],userType=[",userType,"],realPath=[",realPath,"]")
        return tonumber(rule),platform,userType,realPath
    end
    ngx.log(ngx.INFO,"path=[",path,"]rule=[",rule,"],platform=[",platform,"]")
    return tonumber(rule),platform,nil,nil
end

-- 是否本地内存白名单
function flow.isCacheWhiteUser(uri,userId,userType)
    if not userId then
        return false
    end
     --获取白名单列表
    local white_dict = ngx.shared.white_dict
    local value = white_dict:get(userId .. "_" .. userType)
    if value then
        return true
    else
        return false
    end
end

-- 是否远程白名单
function flow.isRemoteWhiteUser(uri,key,userType)
    local headers={["Content-Type"] = "application/json"}
    local type = tonumber(userType)
    local body={
            type = type,
            key = key,
            uri = uri
        }
    local body_str = jsonParser.encode(body)
    local res,response_body=flow.remoteCallWhite('user/flow/valid',body_str,headers)
    if res and response_body == "true" then
        return true
    else
        return false
    end
end

-- 解密报文，失败返回原报文
function flow.decryptBody(encryptBody,headers)
    local customHeader={}
    for key, value in pairs(headers) do
        customHeader[key]=value
    end
    local res,response_body=flow.remoteCallWhite('user/white/decryptRequestBody',encryptBody,customHeader)
    if res then
        return response_body
    else
        return encryptBody
    end
end

function flow.remoteCallWhite(uri,body_str,customHeader)
    local remoteUrl = "http://[fd11:1111:1111:23::ffb3]:18099/member-white/" .. uri

    ngx.log(ngx.ERR,"invoke remote url=[",remoteUrl,"],body=[",body_str,"]")
    -- 创建一个 HTTP 客户端
    local client = http.new()
    -- 设置超时时间，5s
    client:set_timeout(5000)
    -- 保持连接10秒，最多保持100个连接
    client:set_keepalive(10000,100)
    -- 设置连接池大小
    client.pool_size=100
    -- 发送 HTTP POST 请求
    local res, err = client:request_uri(remoteUrl, {
        method = "POST",
        body = body_str,
        headers = customHeader
    })

    -- 保持连接10秒，最多保持100个连接
    client:set_keepalive(10000,100)
    -- 检查请求是否成功
    if not res then
        ngx.log(ngx.ERR, "请求失败: ", err)
        ngx.log(ngx.ERR, "invoke remote url=[",remoteUrl,"],body=[",body_str,"]","request error: [", err,"]")
        return false
    end

    -- 检查响应状态码
    if res.status ~= 200 then
        ngx.log(ngx.ERR, "请求失败，状态码: ", res.status)
        ngx.log(ngx.ERR, "invoke remote url=[",remoteUrl,"],body=[",body_str,"]","request error: [", err,"]")
        return false
    end

    -- 获取响应体
    local response_body = res.body
    local pos = string.find(response_body,'{"code":"500","msg":"系统错误"}')
    ngx.log(ngx.ERR, "invoke remote url=[",remoteUrl,"],body=[",body_str,"]","response_body is: [", response_body,"]")
    if pos then
        ngx.log(ngx.ERR, "请求结果为响应失败，pos = ",pos,",使用默认值: ", body_str)
        return false
    end
    return true,response_body
end


function flow.syncAppOrder()
    local uri = 'syncAppOrder'
    local platform = 'Ad'
    if flow.checkGlobalSwitchAndTurn(uri,platform,"syncAppOrder") then
        -- 校验全局开关是否打开
        return
    end
    local isXml = true
    local reqBodyObj=flow.getReqBodyObj(isXml)
    local account = flow.getPathValue(reqBodyObj,"SyncAppOrderReq.MSISDN",isXml,0)
    -- 获取account需要去调用白名单服务判断是否白名单
    local isWhite = flow.isRemoteWhiteUser("syncAppOrder",account,0)
    if not isWhite then
        flow.turnToOld(uri,account,platform,"syncAppOrder")
        return
    end
    -- 判断是否省侧订购
    local subPlace = flow.getPathValue(reqBodyObj,"SyncAppOrderReq.Subplace",isXml,0)
    if subPlace and subPlace ~= "" and tonumber(subPlace) ~= 7 then
        local orderId = flow.getPathValue(reqBodyObj,"SyncAppOrderReq.TradeID",isXml,0)
        ngx.log(ngx.ERR,"----syncAppOrder orderId: ",orderId)
        if flow.isNewOrderId(orderId) then
            flow.turnToNew(uri,orderId,platform,"syncAppOrder")
        else
            flow.turnToOld(uri,orderId,platform,"syncAppOrder")
        end
    else
        flow.turnToNew(uri,"action!=1",platform,"syncAppOrder")
    end
end

function flow.isNewOrderId(orderId)
    local userId,uuid1,uuid2,uuid3=string.match(orderId,"([^_]+)_*([^-]*)-*([^-]*)-*(.*)$")
    local newOrderIdPrefix="3"
    if string.sub(uuid3,1,#newOrderIdPrefix) == newOrderIdPrefix then
        return true
    else
        return false
    end
end
function flow.isNewOrderIdByRule6(orderId)
    local char = orderId:sub(13, 13)
    if char == '3' and #orderId == 32 then
        return true
    else
        return false
    end
end

-- 镜像到新vsbo
function flow.mirrorNew()
    local conf_dict = ngx.shared.conf_dict
    -- 跳转到新vsbo
    local index = conf_dict:get("newIndex")
    local port = conf_dict:get("newVsboPort" .. index)
    local ip = conf_dict:get("newVsboIp" .. index)
    local request_uri=ngx.var.request_uri
    local remoteUrl = "http://" .. ip .. ":" .. port .. request_uri;
    local body=ngx.req.get_body_data()
    ngx.log(ngx.ERR,"invoke remote url=[",remoteUrl,"],body=[",body,"]")
    -- 创建一个 HTTP 客户端
    local client = http.new()
    -- 设置超时时间，2s
    client:set_timeout(2000)
    -- 设置连接池大小
    client.pool_size=100
    -- 发送 HTTP POST 请求
    local ok, err = client:request_uri(remoteUrl, {
        method = "POST",
        body = body,
        async = true,
        keepalive = true, -- 保持连接在连接池中
        headers = ngx.req.get_headers()
    })
    -- 检查请求是否成功
    if not ok then
        ngx.log(ngx.ERR, "发送异步请求失败，失败信息: ", err)
        return
    end
    -- 保持连接10秒，最多保持100个连接
    client:set_keepalive(10000,100)
end

function flow.mirror(type,platform)
    local conf_dict = ngx.shared.conf_dict
    -- 跳转到新vsbo
    local index = conf_dict:get(type .. "Index")
    local finalPlatform = platform
    if type == "new" then
        -- 镜像到新平台则直接跳转到新vsbo
        index = "Vsbo"
    end
    local port = conf_dict:get(type .. platform .. "Port" .. index)
    local ip = conf_dict:get(type .. platform .. "Ip" .. index)
    local request_uri=ngx.var.request_uri
    local remoteUrl = "http://" .. ip .. ":" .. port .. request_uri;
    local body=ngx.req.get_body_data()
    ngx.log(ngx.ERR,"invoke remote url=[",remoteUrl,"],body=[",body,"]")
    -- 创建一个 HTTP 客户端
    local client = http.new()
    -- 设置超时时间，2s
    client:set_timeout(2000)
    -- 设置连接池大小
    client.pool_size=100
    -- 发送 HTTP POST 请求
    local ok, err = client:request_uri(remoteUrl, {
        method = "POST",
        body = body,
        async = true,
        keepalive = true, -- 保持连接在连接池中
        headers = ngx.req.get_headers()
    })
    -- 检查请求是否成功
    if not ok then
        ngx.log(ngx.ERR, "发送异步请求失败，失败信息: ", err)
        return
    end
    -- 保持连接10秒，最多保持100个连接
    client:set_keepalive(10000,100)
end
return flow