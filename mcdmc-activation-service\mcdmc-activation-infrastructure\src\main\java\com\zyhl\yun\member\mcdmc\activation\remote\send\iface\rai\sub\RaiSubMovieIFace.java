package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.sub;

import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.query.RaiQueryBaseIFace;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.query.RaiQueryMovieIFace;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiBaseProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.RaiMovieProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.RaiSubReq;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 明威互动(畅影）权益中心发货
 * com.huawei.jaguar.vsbo.pub.thread.FilmSourcesRightThread#run
 *
 * <AUTHOR>
 * @apiNote 有回调，详情看 <hr/>RaiMovieSubCallback <hr/>
 * @since 2024/06/18 14:42
 */
@Component
public class RaiSubMovieIFace extends RaiSubBaseIFace {
    @Resource
    private RaiMovieProperties raiProperties;

    @Override
    protected RaiBaseProperties getBaseRaiProperties() {
        return raiProperties;
    }

    @Override
    protected RaiSubReq.Body buildRequestBody(ComSendInterfaceReq comReq) {
        RaiSubReq.Body body = super.buildRequestBody(comReq);
        //将营销平台的订单id透传到明威互动(注：只有明威需要传该值）
        body.setOrderItemId(comReq.getOutOrderId());
        return body;
    }

    @Override
    protected boolean isNeedPayWay() {
        //明威互动传了payWay会有验签问题
        return false;
    }

    @Override
    protected String getOrderChannelCode(String userId, String parentOrderId) {
        return raiProperties.getOrderChannelCode(null);
    }

}
