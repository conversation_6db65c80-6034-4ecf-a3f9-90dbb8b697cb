package com.zyhl.yun.member.mcdmc.activation.remote.send.resp;

import com.zyhl.yun.member.mcdmc.activation.util.RaiUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 权益下单的响应体
 *
 * <AUTHOR>
 * @since 2024/06/13 20:27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RaiSubRsp {

    /**
     * 响应根
     */
    private ContractRoot contractRoot;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContractRoot {
        /**
         * 响应头
         */
        private Head head;
        /**
         * 响应体
         */
        private Body body;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Head {
        /**
         * 请求时间
         */
        private String reqTime;
        /**
         * 签名
         */
        private String sign;
        /**
         * 交易流水号
         */
        private String transactionId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Body {
        /**
         * 响应状态码
         * 0000：成功
         * 1006：权益方风控拒绝
         */
        private String resultCode;
        /**
         * 响应消息
         */
        private String resultMsg;
        /**
         * 订单号
         */
        private String orderNo;

    }

    public static boolean isSuccess(RaiSubRsp rsp) {
        return rsp != null && rsp.getContractRoot() != null && rsp.getContractRoot().getBody() != null
                && RaiUtil.RECEIVE_SUCCESS_RESULT_CODE.equals(rsp.getContractRoot().getBody().getResultCode());
    }
}
