package com.zyhl.yun.member.mcdmc.activation.domain.remote;

import cn.hutool.extra.spring.SpringUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowDo;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowIFaceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.dto.WorkServiceCondition;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.ServiceTypeEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.local.ServiceFlowStateEnum;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务流程静态配置信息
 * serviceCode-IFlow 多对1
 * serviceCode-IFace 多对多
 *
 * <AUTHOR>
 * @since 2024/07/30 16:24
 */
@Configuration
public class FlowStaticConfig {
    /**
     * 业务流程映射
     */
    private static Map<String, WorkServiceFlowDo> serviceCode2FlowDoMap;
    /**
     * 业务流程映射
     */
    private static Map<String, IServiceFlow> serviceCode2IFlowMap;
    /**
     * 流程编码和接口列表的映射（排序后的接口）
     */
    private static Map<String, List<IOutInterface>> serviceCode2iFacesMap;

    /**
     * 初始化，将serviceCode和业务流程进行绑定
     */
    @PostConstruct
    private void init() {
        refreshConfig();
    }

    /**
     * 刷新配置
     */
    public static void refreshConfig() {
        // 查询所有生效中的业务流程
        DomainServiceContext workServiceFlowContext = new DomainServiceContext(ServiceTypeEnum.QRY_ALL.getServiceId());
        List<WorkServiceFlowDo> serviceFlowDoList = workServiceFlowContext.read(new WorkServiceCondition(ServiceTypeEnum.QRY_ALL), WorkServiceFlowDo.class);
        serviceCode2FlowDoMap = new HashMap<>(serviceFlowDoList.size());
        serviceCode2IFlowMap = new HashMap<>(serviceFlowDoList.size());
        serviceCode2iFacesMap = new HashMap<>(serviceFlowDoList.size());
        for (WorkServiceFlowDo serviceFlowDo : serviceFlowDoList) {
            if (!ServiceFlowStateEnum.VALID.codeEquals(serviceFlowDo.getState())) {
                continue;
            }
            serviceCode2FlowDoMap.put(serviceFlowDo.getServiceCode(), serviceFlowDo);
            IServiceFlow iFlow = SpringUtil.getBean(serviceFlowDo.getServiceFlowClass());
            serviceCode2IFlowMap.put(serviceFlowDo.getServiceCode(), iFlow);
            // 遍历流程接口(排序）
            List<IOutInterface> iFaceList = serviceFlowDo.getIFaceDoList().stream()
                    .sorted(Comparator.comparingInt(WorkServiceFlowIFaceDo::getPriorSort))
                    .map(faceDo -> (IOutInterface) SpringUtil.getBean(faceDo.getIFaceClassName()))
                    .collect(Collectors.toList());
            // 添加流程和接口列表的映射（排序后的接口）
            serviceCode2iFacesMap.put(serviceFlowDo.getServiceCode(), iFaceList);
        }
    }


    public static WorkServiceFlowDo getServiceFlowDo(String serviceCode) {
        return serviceCode2FlowDoMap.get(serviceCode);
    }

    public static IServiceFlow getIFlow(String serviceCode) {
        return serviceCode2IFlowMap.get(serviceCode);
    }

    public static List<IOutInterface> getOutInterfaceList(String serviceCode) {
        return serviceCode2iFacesMap.get(serviceCode);
    }

    public static WorkServiceFlowIFaceDo getFaceDoByIFaceClass(String serviceCode, Class<? extends IOutInterface> iFaceClass) {
        WorkServiceFlowDo workServiceFlowDo = serviceCode2FlowDoMap.get(serviceCode);
        if (workServiceFlowDo == null) {
            return null;
        }
        List<WorkServiceFlowIFaceDo> iFaceDoList = workServiceFlowDo.getIFaceDoList();
        for (WorkServiceFlowIFaceDo iFaceDo : iFaceDoList) {
            if (iFaceDo.getIFaceClassName().equalsIgnoreCase(iFaceClass.getSimpleName())) {
                return iFaceDo;
            }
        }
        return null;
    }

    /**
     * 根据IFlowClass获取第一个serviceCode
     *
     * @param iFlowClassName 流程类名
     * @apiNote 如果一个IFlowClass对应多个serviceCode，则返回第一个
     */
    public static String getFirstServiceCodeByIFlow(String iFlowClassName) {
        IServiceFlow iFlow = SpringUtil.getBean(iFlowClassName);
        Set<Map.Entry<String, IServiceFlow>> entrySet = serviceCode2IFlowMap.entrySet();
        for (Map.Entry<String, IServiceFlow> flowEntry : entrySet) {
            if (flowEntry.getValue().equals(iFlow)) {
                return flowEntry.getKey();
            }
        }
        return null;
    }

    /**
     * 获取流程重试次数
     *
     * @param serviceCode 业务编码
     * @return 重试次数
     */
    public static int getFlowRetryCount(String serviceCode) {
        return serviceCode2FlowDoMap.get(serviceCode).getRetryCount();
    }
}
