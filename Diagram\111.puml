@startuml
skinparam componentStyle uml2

package "索引动态扩容系统" {
  [元数据配置中心] as Config
  [动态路由引擎] as Router
  [资源监控与触发模块] as Monitor
  [数据迁移与双写模块] as Migrator
  [一致性校验与容错模块] as Validator
  [安全与审计模块] as Security

  Config --> Router : 分片规则/路由策略
  Monitor --> Router : 实时负载数据
  Router --> Migrator : 路由决策
  Migrator --> Validator : 迁移结果
  Validator --> Security : 校验报告
  Security --> Config : 审计日志
}
@enduml
@startuml
skinparam componentStyle uml2

package "动态索引扩容系统" {
  [动态分片映射配置模块] as Config
  [资源感知触发模块] as Trigger
  [数据割接与迁移模块] as Migrator
  [智能路由与一致性保障模块] as Router
  [分布式锁与容错模块] as Lock
  [安全与审计模块] as Security

  Config --> Router : 分片组规则/权重参数
  Trigger --> Migrator : 扩容触发指令
  Migrator --> Router : 迁移进度反馈
  Router --> Lock : 路由状态同步
  Lock --> Security : 操作日志
  Security --> Config : 审计报告
}

note right of Config
  **输入**：分片数、路由键、权重参数
  **输出**：动态分片组映射规则
end note

note right of Trigger
  **输入**：CPU/磁盘I/O指标
  **输出**：扩容触发信号
end note

note right of Migrator
  **输入**：旧索引数据
  **输出**：双写日志/迁移结果
end note
@enduml
@startuml
component "动态分片映射配置模块" as Config {
  [元数据表] as Meta
  [权重计算引擎] as Engine
  [路由规则生成器] as Router

  Meta --> Engine : 分片组配置/路由键
  Engine --> Router : 分片组权重表
  Router --> [外部系统] : 动态路由规则
}

note right of Meta
  **输入**：分片组数量、路由键、冷热策略
  **存储**：分片组元数据（ID、物理分片列表）
end note

note right of Engine
  **计算逻辑**：
  1. 采集CPU、磁盘I/O指标
  2. 计算冷热评分
  3. 动态生成权重表
end note

note right of Router
  **输出**：
  - 分片组ID与物理分片映射
  - 权重驱动的路由策略
end note
@enduml

@startuml
skinparam componentStyle uml2

component "元数据配置模块" as Config {
  [分片组初始化] --> [权重参数加载]
}

component "资源监控模块" as Monitor {
  [滑动窗口计算] --> [扩容触发]
}

component "数据迁移模块" as Migrator {
  [双写事务日志] --> [优先级迁移]
  [优先级迁移] --> [冷热分离]
}

component "路由模块" as Router {
  [二级路由决策] --> [流量切换]
}

component "容错模块" as Lock {
  [分布式锁竞争] --> [断点续传]
}

component "审计模块" as Audit {
  [一致性校验] --> [性能报告]
}

Config --> Monitor : 分片组规则
Monitor --> Migrator : 扩容指令
Migrator --> Router : 迁移进度
Router --> Lock : 路由状态
Lock --> Audit : 恢复日志
Audit --> Config : 优化建议

note right of Config
  **输入**：分片组数量、路由键
  **输出**：分片组映射规则
end note

note right of Migrator
  **核心算法**：
  - 双写事务日志
  - 优先级迁移公式
end note

note right of Router
  **核心算法**：
  - 二级动态路由
  - 蓝绿流量切换
end note
@enduml