package com.zyhl.yun.member.mcdmc.activation.common.properties;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/03/17 10:26
 */
@Getter
@Setter
@ToString
@Configuration
@ConfigurationProperties(prefix = "async")
public class AsyncProperties {

    /**
     * 核心线程数：线程池创建时候初始化的线程数
     */
    private Integer corePoolSize = 12;

    /**
     * 最大线程数：线程池运行的最大线程数 ,只有在缓冲队列满了之后才会申请超过核心线程数的线程
     */
    private Integer maxPoolSize = 12;

    /**
     * 缓冲队列：用来缓冲执行任务的队列
     */
    private Integer queueCapacity = 500;

    /**
     * 允许线程的空闲时间60秒：当超过了核心线程之外的线程在空闲时间到达之后会被销毁
     */
    private Integer keepAliveSecond = 60;

    /**
     * 线程池名的前缀：设置好了之后可以方便我们定位处理任务所在的线程池
     */
    private String threadNamePrefix = "async-pool";
}
