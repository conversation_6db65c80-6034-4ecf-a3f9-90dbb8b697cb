package com.zyhl.yun.member.common.domain.framework.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/07/31 11:45
 */
@Getter
@AllArgsConstructor
public enum ExecOperate {
    WRITE("write"),
    DELETE("delete"),
    DELETE_PK("deleteByPk"),
    READ_BY_PK("readByPk"),
    READ_BY_CONDITION("readByCondition"),
    GET_COUNT("getCount"),
    WRAPPER_ENTITY_LIST("wrapperEntityList"),
    ;
    private String desc;
}
