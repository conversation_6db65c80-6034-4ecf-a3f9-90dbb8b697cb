package com.zyhl.yun.member.mcdmc.activation.facade;

import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.activation.WorkOrderServiceId;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderAttrDo;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.conditions.QueryWorkAttrCondition;
import com.zyhl.yun.member.mcdmc.activation.domains.conditions.QueryWorkOrderCondition;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/02/18 16:38
 */
public class ActivationServiceFacade {
    private ActivationServiceFacade() {
    }

    /**
     * 查询用户指定订单对应的所有包含指定工单属性的服开工单
     *
     * @param userId      用户id
     * @param orderIdList 订单id列表
     * @param attrKeyList 工单属性key列表
     */
    public static List<WorkOrderDo> getWorkOrderAttrList(String userId, List<String> orderIdList, List<String> attrKeyList) {
        return getWorkOrderAttrList(userId, orderIdList, Collections.emptyList(), attrKeyList);
    }

    public static List<WorkOrderDo> getWorkOrderAttrList(String userId, String orderId,
                                                         String serviceCode, List<String> attrKeyList) {
        return getWorkOrderAttrList(userId, Collections.singletonList(orderId),
                Collections.singletonList(serviceCode), attrKeyList);
    }

    public static List<WorkOrderDo> getWorkOrderAttrList(String userId, List<String> orderIdList,
                                                         List<String> serviceCodeList, List<String> attrKeyList) {
        if (!StringUtils.hasText(userId) || CollectionUtils.isEmpty(orderIdList) || CollectionUtils.isEmpty(attrKeyList)) {
            return Collections.emptyList();
        }
        QueryWorkOrderCondition qryCondition = new QueryWorkOrderCondition();
        qryCondition.setUserId(userId);
        qryCondition.setOrderIdList(orderIdList);
        qryCondition.setServiceCodeList(serviceCodeList);
        qryCondition.setAttrKeyList(attrKeyList);
        DomainServiceContext activationContext = new DomainServiceContext(WorkOrderServiceId.QUERY_ACTIVATION_ATTR);
        return activationContext.read(qryCondition, WorkOrderDo.class);
    }

    public static List<WorkOrderAttrDo> getWorkOrderAttrs(String userId, List<String> workIdList, List<String> attrCodeList) {
        QueryWorkAttrCondition qryCondition = new QueryWorkAttrCondition();
        qryCondition.setUserId(userId);
        qryCondition.setWorkIdList(workIdList);
        qryCondition.setAttrKeyList(attrCodeList);
        DomainServiceContext context = new DomainServiceContext(WorkOrderServiceId.INNER_QUERY_WORK_ORDER_ATTR);
        return context.read(qryCondition, WorkOrderAttrDo.class);
    }


    public static String getSingleWorkAttrValue(String userId, String workId, String attrCode) {
        QueryWorkAttrCondition qryCondition = new QueryWorkAttrCondition();
        qryCondition.setUserId(userId);
        qryCondition.setWorkIdList(Collections.singletonList(workId));
        qryCondition.setAttrKeyList(Collections.singletonList(attrCode));
        DomainServiceContext context = new DomainServiceContext(WorkOrderServiceId.INNER_QUERY_WORK_ORDER_ATTR);
        WorkOrderAttrDo workOrderAttrDo = context.readFirst(qryCondition, WorkOrderAttrDo.class);
        if (workOrderAttrDo != null) {
            return workOrderAttrDo.getAttrVal();
        }
        return null;
    }
}
