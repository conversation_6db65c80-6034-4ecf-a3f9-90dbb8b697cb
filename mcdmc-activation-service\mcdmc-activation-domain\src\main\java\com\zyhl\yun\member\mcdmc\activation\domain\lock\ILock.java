package com.zyhl.yun.member.mcdmc.activation.domain.lock;

/**
 * <AUTHOR>
 * @since 2024/06/28 18:40
 */
public interface ILock {
    /**
     * 加锁
     *
     * @param lockKey   锁key
     * @param ttlSecond 超时时间，单位秒
     * @return 加锁成功与否
     */
    boolean lock(String lockKey, long ttlSecond);

    /**
     * 分布式加锁
     *
     * @param lockKey   锁key
     * @param ttlSecond 锁时长
     * @return 锁标识
     */
    String distributedLock(String lockKey, long ttlSecond);

    /**
     * 续锁
     *
     * @param lockKey   锁key
     * @param lockId    锁标识
     * @param ttlSecond 超时时间，单位秒
     * @return 续锁成功与否
     */
    boolean continueLock(String lockKey, String lockId, long ttlSecond);

    /**
     * 解锁
     *
     * @param lockKey 锁key
     * @param lockId  锁标识
     * @return 解锁成功与否
     */
    boolean unlock(String lockKey, String lockId);

}
