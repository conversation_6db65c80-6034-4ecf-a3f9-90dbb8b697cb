package com.zyhl.yun.member.mcdmc.activation.remote.send.resp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 免流/一级能开-业务校验资格接口响应体
 *
 * <AUTHOR>
 * @apiNote 能力开放平台业务办理资格校验接口响应对象
 * @since 2024/06/19 11:41
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "BizQualificationResp")
public class OpenFreeFlowCheckRsp {
    /**
     * 消息类型，默认BizQualificationResp
     */
    @XmlElement(name = "MsgType")
    private String msgType;

    /**
     * 开放平台返回值
     */
    private Integer hRet;

    /**
     * 该接口消息的版本号
     */
    @XmlElement(name = "Version")
    private String version;

    /**
     * 结果描述
     */
    private String bizDesc;

    /**
     * 结果码
     * 0000表示成功
     */
    private String bizCode;

    public static boolean isSuccess(OpenFreeFlowCheckRsp rsp) {
        return rsp != null && rsp.getHRet() == 0 && "0000".equals(rsp.getBizCode());
    }
}
