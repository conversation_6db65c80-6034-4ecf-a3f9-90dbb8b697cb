package com.zyhl.yun.member.common.cache;

import cn.hutool.core.collection.CollectionUtil;
import com.zyhl.yun.member.common.cache.wrapper.CollectionWrapper;
import com.zyhl.yun.member.common.cache.wrapper.ConditionCountWrapper;
import com.zyhl.yun.member.common.cache.wrapper.ConditionWrapper;
import com.zyhl.yun.member.common.cache.wrapper.SimpleValueWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/20 10:47
 */
@Slf4j
public class EntityCacheImpl implements EntityCache {


    protected DelegationCache cache;

    protected String cacheName;

    public EntityCacheImpl(DelegationCache cache, String cacheName) {
        this.cache = cache;
        this.cacheName = cacheName;
    }

    @Override
    public <T extends EntityCacheable> T getByKey(Serializable key,
                                                  Class<T> clazz,
                                                  Function<Serializable, T> dataGetter) {

        log.debug("[CACHE] get entity from redis: {}， cacheName: {}", key, getCacheName());

        if (key == null) {
            log.error("[CACHE] cache key is null");
            throw new CacheException("cache key is null");
        }

        SimpleValueWrapper valueWrapper = cache.get(getCacheName(), key, clazz);
        if (valueWrapper == null) {
            T value = dataGetter.apply(key);
            valueWrapper = new SimpleValueWrapper(key, value);
            cache.put(getCacheName(), key, valueWrapper);
        }
        return (T) valueWrapper.getValue();
    }

    @Override
    public <T extends EntityCacheable> List<T> getListByKey(String key, Class<T> clazz, Function<String, List<T>> dataGetter) {

        log.debug("[CACHE] get entity list from redis: {}， cacheName: {}", key, getCacheName());

        if (key == null) {
            log.error("[CACHE] cache key is null");
            throw new CacheException("cache key is null");
        }

        SimpleValueWrapper valueWrapper = cache.get(getCacheName(), key, clazz);
        if (valueWrapper == null) {
            List<T> value = dataGetter.apply(key);
            valueWrapper = new SimpleValueWrapper(key, (List<EntityCacheable>) value);
            cache.put(getCacheName(), key, valueWrapper);
        }
        return (List<T>) valueWrapper.getValueList();
    }

    @Override
    public <T extends EntityCacheable> List<T> getByKeyList(List<Serializable> keyList, Class<T> clazz,
                                                            Function<List<? extends Serializable>, List<T>> dataGetter) {
        if (CollectionUtil.isEmpty(keyList)) {
            log.error("[CACHE] cache keyList is empty");
            throw new CacheException("cache keyList is empty");
        }

        CollectionWrapper collectionWrapper = cache.getList(getCacheName(), keyList, clazz);

        if (collectionWrapper == null) {
            List<T> valuesList = dataGetter.apply(keyList);
            collectionWrapper = new CollectionWrapper(keyList, valuesList);
            cache.putList(getCacheName(), collectionWrapper);
        } else {

            List<Serializable> notMacheKeyList = collectionWrapper.getNotMacheKeyList();
            if (!CollectionUtil.isEmpty(notMacheKeyList)) {
                List<EntityCacheable> totalValueList = new ArrayList<>();

                List<T> newValuesList = dataGetter.apply(keyList);
                totalValueList.addAll(newValuesList);
                List<EntityCacheable> oldValueList = (List<EntityCacheable>) collectionWrapper.getValue();
                totalValueList.addAll(oldValueList);

                collectionWrapper = new CollectionWrapper(keyList, totalValueList);
//                cache.putList(getCacheName(), collectionWrapper);

                CollectionWrapper collectionCacheValueWrapperToCache
                        = new CollectionWrapper(notMacheKeyList, newValuesList);
                cache.putList(getCacheName(), collectionCacheValueWrapperToCache);
            }
        }


        return (List<T>) collectionWrapper.getValue();
    }


    @Override
    public <C extends ConditionCacheable, T extends EntityCacheable> List<T> getByCondition(C condition,
                                                                                            Class<T> clazz,
                                                                                            Function<List<? extends Serializable>, List<T>> dataByKeyGetter,
                                                                                            Function<C, List<T>> dataByConditionGetter) {


        if (condition == null) {
            log.error("[CACHE] condition is null");
            throw new CacheException(" cache condition is null");
        }

        ConditionWrapper conditionWrapper = cache.getKeyListByCondition(getCacheName(), condition);
        if (conditionWrapper == null) {
            String conditionHashKey = ConditionWrapper.getConditionHashKey(condition);
            List<T> valueList = dataByConditionGetter.apply(condition);
            List<Serializable> keyList = Collections.emptyList();

            if (!CollectionUtil.isEmpty(valueList)) {
                // 查询结果非空时，将查询结果缓存起来
                keyList = valueList.stream().map(value -> value.getCacheKey()).collect(Collectors.toList());
                CollectionWrapper collectionWrapper = new CollectionWrapper(keyList,
                        valueList);
                cache.putList(getCacheName(), collectionWrapper);
            }

            conditionWrapper = new ConditionWrapper(conditionHashKey, keyList);
            cache.put(getCacheName(), condition, conditionWrapper);

            return valueList;
        } else {

            if (CollectionUtils.isEmpty(conditionWrapper.getKeyList())) {
                log.debug("[CACHE] query key list by condition and return empty list ");
                return Collections.emptyList();
            }
            return getByKeyList(conditionWrapper.getKeyList(),
                    clazz, dataByKeyGetter);
        }
    }


    @Override
    public <C extends ConditionCacheable, T extends EntityCacheable> Long getCountByCondition(C condition,
                                                                                              Class<T> clazz,
                                                                                              Function<C, ? extends Number> totalCountByConditionGetter) {

        if (condition == null) {
            log.error("[CACHE] condition is null");
            throw new CacheException("cache condition is null");
        }

        ConditionCountWrapper conditionCountWrapper = cache.getTotalCountByCondition(getCacheName(), condition);
        if (conditionCountWrapper == null) {
            String conditionHashKey = ConditionWrapper.getConditionHashKey(condition);
            Number count = totalCountByConditionGetter.apply(condition);

            conditionCountWrapper = new ConditionCountWrapper(conditionHashKey, count);
            cache.put(getCacheName(), condition, conditionCountWrapper);

        }

        return conditionCountWrapper.getCount() == null ? 0L : conditionCountWrapper.getCount();
    }


    @Override
    public void deleteByKeyList(List<Serializable> keyList,
                                ConditionCacheable condition) {

        if (CollectionUtil.isEmpty(keyList)) {
            log.error("[CACHE] cache keyList is empty");
            throw new CacheException("cache keyList is empty");
        }
        cache.deleteByKeyList(getCacheName(), keyList);
        if (condition != null) {
            cache.clearConditions(getCacheName(), condition);
        }
    }

    @Override
    public <C extends ConditionCacheable, T extends EntityCacheable> Long deleteByKeyForUpdate(String key,
                                                                                               Function<String, T> dataGetter,
                                                                                               Callable<? extends Number> dataUpdater,
                                                                                               Function<T, C> conditionBuilder) {
        if (key == null) {
            log.error("[CACHE] key is null");
            throw new CacheException("cache key is null");
        }

        T oldEntity = dataGetter.apply(key);

        Number count = null;
        Set<String> deleteCacheKeyList = new HashSet<>();
        try {
            count = dataUpdater.call();

            if (count != null && count.longValue() > 0) {
                T newEntity = dataGetter.apply(key);
                if (newEntity != null) {
                    // 删除可能包含修改后实体的条件缓存
                    C condition = conditionBuilder.apply(newEntity);
                    if (condition != null) {
                        cache.clearConditions(getCacheName(), condition);
                    }
                }
            }

        } catch (Exception e) {
            log.error("[CACHE] deleteByKey error. msg: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] deleteByKey error.");
            }
            throw new CacheException("cache deleteByKey error");
        } finally {
            //旧值相关缓存，无论持久化层操作是否成功，都删除
            if (StringUtils.hasLength(key)) {
                deleteCacheKeyList.add(key);
            }
            if (oldEntity != null) {
                deleteCacheKeyList.add(oldEntity.getCacheKey());
                // 删除可能包含修改前实体的条件缓存
                C condition = conditionBuilder.apply(oldEntity);
                if (condition != null) {
                    cache.clearConditions(getCacheName(), condition);
                }
            }
            cache.deleteByKeyList(getCacheName(), deleteCacheKeyList.stream().collect(Collectors.toList()));
        }

        return count == null ? 0L : count.longValue();
    }

    @Override
    public <C extends ConditionCacheable, T extends EntityCacheable> Long deleteByKeyForInsert(T entity,
                                                                                               Function<T, ? extends Number> dataInserter,
                                                                                               Function<T, C> conditionBuilder) {
        if (entity == null) {
            log.error("[CACHE] entity is null");
            throw new CacheException("cache entity is null");
        }

        Number count = dataInserter.apply(entity);

        if (count != null && count.longValue() > 0) {
            // 需要删除旧值相关缓存，防止用户先查后插未清除空缓存
            cache.deleteByKeyList(getCacheName(), Collections.singletonList(entity.getCacheKey()));
            C condition = conditionBuilder.apply(entity);
            if (condition != null) {
                cache.clearConditions(getCacheName(), condition);
            }
        }

        return count == null ? 0L : count.longValue();
    }

    @Override
    public void deleteByCondition(ConditionCacheable condition) {

        if (condition == null) {
            log.error("[CACHE] condition is null");
            throw new CacheException("condition is null");
        }

        ConditionWrapper conditionWrapper = cache.getKeyListByCondition(getCacheName(), condition);
        if (conditionWrapper != null
                && !CollectionUtils.isEmpty((Collection<?>) conditionWrapper.getValue())) {
            cache.deleteByKeyList(getCacheName(), (List<Serializable>) conditionWrapper.getValue());
        }
        cache.clearConditions(getCacheName(), condition);

    }


    @Override
    public String getCacheName() {
        return cacheName;
    }


    @Override
    public <C extends ConditionCacheable, T extends EntityCacheable> void put(T entity,
                                                                              Function<T, C> conditionBuilder) {

        if (entity != null && StringUtils.hasLength(entity.getCacheKey())) {
            SimpleValueWrapper valueWrapper = new SimpleValueWrapper(entity.getCacheKey(), entity);
            cache.put(getCacheName(), entity.getCacheKey(), valueWrapper);

            if (conditionBuilder != null) {
                ConditionCacheable condition = conditionBuilder.apply(entity);
                ConditionWrapper conditionWrapper = new ConditionWrapper(condition, Arrays.asList(entity.getCacheKey()));
                cache.put(getCacheName(), condition, conditionWrapper);
            }
        }
    }

}
