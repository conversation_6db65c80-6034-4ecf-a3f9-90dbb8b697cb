package com.zyhl.yun.member.common.domain.serviceid;

/**
 * @Description: 用户领域服务类型
 * @Author: zouzef<PERSON>
 * @Date: 2024/5/30
 */
public interface UserServiceId {
    /**
     * 查询用户信息
     */
    String QUERY_USER_INFO = "queryUserInfo";
    /**
     * 查询用户信息
     */
    String QUERY_USER_INFO_OR_CREATE = "queryUserInfoOrCreate";

    /**
     * 查询用户（创建）服务
     */
    String QUERY_AND_CREATE_USER_INFO = "queryAndCreateUserInfo";

    String QUERY_USER_GOTONE_INFO = "queryUserGotoneInfo";

    /**
     * 更新用户信息
     */
    String USER_QUERY_CREATE = "userQueryCreate";

    /**
     * 插入用户信息
     */
    String INSERT_USER_INFO = "insertUserInfo";
    /**
     * 更新用户信息
     */
    String UPDATE_USER_INFO = "updateUserInfo";
    /**
     * 插入用户信息
     */
    String INSERT_USER = "insertUser";
    /**
     * 用户换绑
     */
    String USER_BIND = "userBind";
    /**
     * 用户重新开户
     */
    String USER_RE_OPEN = "userReOpen";

    /**
     * 亲情号新增
     */
    String USER_LSB_SERVICE = "queryUserInfo";
}
