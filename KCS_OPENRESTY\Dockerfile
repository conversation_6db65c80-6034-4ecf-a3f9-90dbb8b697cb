FROM 10.19.19.84/k8s/mcdmc-openresty-service-test-base:1.0

RUN addgroup --gid 1368 hcaiyun && adduser --uid 1368 --gid 1368 hcaiyun
WORKDIR /home/<USER>

#请修改为应用端口
EXPOSE 80 443

ENV TZ=Asia/Shanghai

#请修改应用名，需要区分环境，研发（联调）环境需要加上dev后缀，测试环境需要加上test后缀
ENV applicationName=mcdmc-openresty-service-test
#按照应用实际需求修改

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
#RUN In -snf /dev/stdout /var/log/app.log

RUN sed -i s/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g /etc/apt/sources.list
RUN apt update && apt upgrade -y && apt install curl -y
RUN apt install procps -y
RUN apt install vim -y
RUN apt install util-linux -y
#RUN apt install iputils-ping -y
RUN apt install net-tools -y
RUN apt install apache2-utils -y
#RUN apt update && apt upgrade -y
#RUN apt install openssh-client cronie cron net-tools iputils-ping telnet wget lftp fontconfig -y

# 安装必要的工具和依赖
RUN apt-get update && apt-get install -y \
    logrotate \
    && rm -rf /var/lib/apt/lists/*

#创建自定义lua脚本目录
RUN mkdir /usr/local/openresty/lualib/aspire
RUN mkdir /usr/local/openresty/temp
COPY KCS_OPENRESTY/cloud-test/lua /usr/local/openresty/lualib/aspire
COPY KCS_OPENRESTY/cloud-test/conf/nginx.conf /usr/local/openresty/nginx/conf/nginx.conf
COPY KCS_OPENRESTY/cloud-test/conf/entrypoint.sh /usr/local/openresty/nginx/conf/entrypoint.sh
# 创建日志目录
RUN mkdir -p /logs/nginx/web
RUN chown -R hcaiyun:hcaiyun /usr/local/openresty
RUN chown -R hcaiyun:hcaiyun /logs/nginx/web
RUN chown -R hcaiyun:hcaiyun /usr/local/openresty/nginx/conf/entrypoint.sh
RUN chmod a+x /usr/local/openresty/nginx/conf/entrypoint.sh
USER hcaiyun

#CMD  /usr/local/openresty/nginx/sbin/nginx  -g  "daemon off;"
CMD ["/usr/local/openresty/nginx/conf/entrypoint.sh"]