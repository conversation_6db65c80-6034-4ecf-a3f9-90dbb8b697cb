package com.zyhl.yun.member.invite;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.constants.ErrorCode;
import com.zyhl.yun.member.common.constants.SubWayConstants;
import com.zyhl.yun.member.common.domain.framework.BaseCondition;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.enums.InviteRelationStatusEnum;
import com.zyhl.yun.member.common.result.XmlResult;
import com.zyhl.yun.member.common.util.XmlUtil;
import com.zyhl.yun.member.consumer.common.producer.MqProducer;
import com.zyhl.yun.member.direct.manage.enums.EventTypeEnum;
import com.zyhl.yun.member.domain.invite.domain.InviteRelationDo;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.goodInstance.GoodsInstanceDomainService;
import com.zyhl.yun.member.invite.advert.AdverMqProperties;
import com.zyhl.yun.member.invite.converter.BindFamilyConvertor;
import com.zyhl.yun.member.invite.message.AdverMessage;
import com.zyhl.yun.member.order.dto.ManageSubscribeRelationReq;
import com.zyhl.yun.member.order.remote.VsboClient;
import cn.hutool.core.util.JAXBUtil;
import org.springframework.beans.factory.annotation.Value;
import java.util.HashMap;
import java.util.Map;
import static com.zyhl.yun.member.common.Constant.HttpHeader.X_NE_AUTH_DSDP;
import com.zyhl.yun.member.order.vo.ManageSubscribeRelationResp;
import com.zyhl.yun.member.product.common.enums.GoodsSalesTypeEnum;
import com.zyhl.yun.member.product.common.enums.PayWayEnum;
import com.zyhl.yun.member.product.domain.goods.GoodsDo;
import com.zyhl.yun.member.product.domain.goods.dto.QueryGoodsCondition;
import com.zyhl.yun.member.product.domain.goods.policy.timeplan.TimePlanCalculator;
import com.zyhl.yun.member.product.domain.smstemplate.domain.SmsTemplateDo;
import com.zyhl.yun.member.product.domain.smstemplate.dto.SmsTemplateIdCondition;
import com.zyhl.yun.member.support.common.domain.SmsSendDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.zyhl.yun.member.common.domain.serviceid.GoodsServiceId.QUERY_GOODS_INFO;
import static com.zyhl.yun.member.common.domain.serviceid.InviteRelationServiceId.UPDATE_FAMILY_NUMBER_INFO;
import static com.zyhl.yun.member.common.domain.serviceid.MessageNoticeServiceId.SEND;
import static com.zyhl.yun.member.common.domain.serviceid.SmsTemplateServiceId.QUERY_SMS_TEMPLATE_BY_ID;
import static com.zyhl.yun.member.common.enums.InviteRelationStatusEnum.GIVEN_FAIL;

@Service
@Slf4j
public class InviteRelationDomainService {

    public static final Integer M4C_CHANNLE = 503;

    @Resource
    private BindFamilyConvertor vsboOrderRespConvertor;

    @Resource
    private GoodsInstanceDomainService goodsInstanceDomainService;

    @Resource
    private MqProducer mqProducer;

    @Resource
    private AdverMqProperties advertiseProperties;

    @Resource
    private VsboClient vsboClient;

    @Value("${manage.neAuth}")
    private String neAuth;

    /**
     * 查询亲情号绑定关系
     *
     * @param serviceId 服务id
     * @return 邀请关系列表
     */
    public List<InviteRelationDo> queryInviteByConditions(BaseCondition condition, String serviceId) {
        DomainServiceContext inviteRelationDomainServiceContext = new DomainServiceContext(serviceId);
        return inviteRelationDomainServiceContext.read(condition, InviteRelationDo.class);
    }

    private static DomainServiceContext getDomainServiceContext(String serviceId) {
        return new DomainServiceContext(serviceId);
    }

    /**
     * 新增亲情号绑定
     *
     * @param map       map
     * @param serviceId 服务id
     */
    public void addRelation(InviteRelationDo map, String serviceId) {
        DomainServiceContext inviteRelationDomainServiceContext = getDomainServiceContext(serviceId);

        inviteRelationDomainServiceContext.putInstance(map);
        inviteRelationDomainServiceContext.writeAndFlush(null);
    }

    /**
     * 亲情号绑定
     *
     * @param inviteRelationDo 邀请关系
     * @param invitingUserDo
     */
    public void inviteRelationBind(String giftProduct, InviteRelationDo inviteRelationDo,
                                   UserDo invitingUserDo) {
        log.info("[domain inviteRelationBind] user bind inviteRelation");
        try {
            QueryGoodsCondition queryGoodsCondition = new QueryGoodsCondition();
            queryGoodsCondition.setGoodsIdList(Collections.singletonList(inviteRelationDo.getGoodsId()));
            ManageSubscribeRelationReq manageSubscribeRelationReq = new ManageSubscribeRelationReq();
            manageSubscribeRelationReq.setProductID(giftProduct);
            manageSubscribeRelationReq.setWho(SubWayConstants.INTERNAL_RECEIVE_CODE);
            manageSubscribeRelationReq.setSubChannelID(null);
            manageSubscribeRelationReq.setAccount(inviteRelationDo.getInvitedAccount());
            manageSubscribeRelationReq.setSaleType(GoodsSalesTypeEnum.SPACE.getType());
            manageSubscribeRelationReq.setBuyType(String.valueOf(PayWayEnum.MARKETING_PAY.getCode()));
            manageSubscribeRelationReq.setAccessSource("1");
            manageSubscribeRelationReq.setAccount(inviteRelationDo.getInvitedAccount());
            manageSubscribeRelationReq.setStartTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
            manageSubscribeRelationReq.setEndTime(DateUtil.format(TimePlanCalculator.getMaxEndTime(), DatePattern.NORM_DATETIME_PATTERN));
            manageSubscribeRelationReq.setEventType(Integer.parseInt(EventTypeEnum.ESTABLISH.getEventType()));
            String xmlReqStr = JAXBUtil.beanToXml(manageSubscribeRelationReq);
            Map<String, String> headers = new HashMap<>();
            headers.put(X_NE_AUTH_DSDP, neAuth);
            String manageSubscribeRelationRespRestfulResponse =
                    vsboClient.manageSubscribeRelationString(headers, xmlReqStr);
            if (null != manageSubscribeRelationRespRestfulResponse) {
                log.info("[domain inviteRelationBind] user bind inviteRelation,{}", manageSubscribeRelationRespRestfulResponse);
                XmlResult result = XmlUtil.convertXmlToObj(manageSubscribeRelationRespRestfulResponse, XmlResult.class);
                if (result != null) {
                    ManageSubscribeRelationResp manageSubscribeRelationResp = result.getManageSubscribeRelationResp();
                    log.debug("[domain inviteRelationBind] user bind inviteRelation,{}", manageSubscribeRelationResp);
                    if (manageSubscribeRelationResp != null && null != manageSubscribeRelationResp.getSubscriptionID()) {
                        inviteRelationDo.setState(InviteRelationStatusEnum.GIVEN);
                        inviteRelationDo.setInvitedOrderId(manageSubscribeRelationResp.getOrderID());
                        DomainServiceContext context = new DomainServiceContext(UPDATE_FAMILY_NUMBER_INFO);
                        context.putInstance(inviteRelationDo);
                        context.writeAndFlush();
                        QueryGoodsCondition queryGoodsConditionGift = new QueryGoodsCondition();
                        queryGoodsConditionGift.setGoodsIdList(Collections.singletonList(giftProduct));
                        DomainServiceContext contextGoods = new DomainServiceContext(QUERY_GOODS_INFO);
                        GoodsDo giftGoodsDo = contextGoods.readFirst(queryGoodsConditionGift, GoodsDo.class);
                        sendAdMq( inviteRelationDo, manageSubscribeRelationResp,giftGoodsDo);
                        sendReceivedSms( inviteRelationDo, invitingUserDo,giftGoodsDo);
                    } else {
                        throw new ServiceException("sub create error");
                    }
                }
            }
        } catch (Exception e) {
            log.error("method bindFamilyNumber error", e);
            inviteRelationDo.setState(GIVEN_FAIL);
            DomainServiceContext context = new DomainServiceContext(UPDATE_FAMILY_NUMBER_INFO);
            context.putInstance(inviteRelationDo);
            context.writeAndFlush();
            throw new ServiceException(ErrorCode.BIND_FAMILY_NUMBER_FAIL_CODE, ErrorCode.BIND_FAMILY_NUMBER_FAIL_MESSAGE);
        }
    }

    /**
     * 发送短信
     *
     * @param inviteRelationDo            邀请关系
     * @param manageSubscribeRelationResp 邀请关系
     * @param giftGoodsDo
     */
    private void sendAdMq(InviteRelationDo inviteRelationDo,
                          ManageSubscribeRelationResp manageSubscribeRelationResp, GoodsDo giftGoodsDo) {
        try {
            AdverMessage message = AdverMessage.builder()
                    .msgID(UUID.randomUUID().toString(true))
                    .orderID(manageSubscribeRelationResp.getOrderID())
                    .invitedMsisdn(inviteRelationDo.getInvitedAccount())
                    .invitingMsisdn(inviteRelationDo.getInvitingAccount())
                    .productID(giftGoodsDo.getGoodsExt() == null ? "" : giftGoodsDo.getGoodsExt().getGotoneGiftGoodsId())
                    .productName(giftGoodsDo.getGoodsName())
                    .startTime(manageSubscribeRelationResp.getStartTime())
                    .endTime(manageSubscribeRelationResp.getEndTime())
                    .subTime(manageSubscribeRelationResp.getStartTime()).build();
            mqProducer.sendOuterMessage(advertiseProperties, message);
        } catch (Exception e) {
            log.error("sendAdMq error", e);
        }
    }

    /**
     * 发送短信
     *
     * @param inviteRelationDo 邀请关系
     * @param invitingUserDo   邀请人信息
     * @param giftGoodsDo
     */
    private static void sendReceivedSms(InviteRelationDo inviteRelationDo, UserDo invitingUserDo, GoodsDo giftGoodsDo) {
        try {
            log.debug("[DOMAIN] sendReceivedSms start");
            if (giftGoodsDo != null && giftGoodsDo.getSmsPolicy() != null) {
                String templateId = giftGoodsDo.getSmsPolicy().getInviteMessageTemplateId();
                if (StringUtils.isNotEmpty(templateId)) {
                    HashMap<String, String> map = new HashMap<>();
                    map.put("invitingAccount", StrUtil.hide(inviteRelationDo.getInvitingAccount(), 3, 7));
                    List<String> variables = getVariables(templateId, map);
                    DomainServiceContext serviceContext = new DomainServiceContext(SEND);
                    SmsSendDo smsSendDo = serviceContext.newInstance(SmsSendDo.class);
                    smsSendDo.setReqBizId(IdUtil.getSnowflake().nextIdStr());
                    //只支持单个号码
                    smsSendDo.setReceiver(Collections.singletonList(inviteRelationDo.getInvitedAccount()));
                    smsSendDo.setMessageTemplateId(templateId);
                    smsSendDo.setVariableList(variables);
                    smsSendDo.setNationCode(invitingUserDo.getNationCode());
                    log.info("[DOMAIN] sendReceivedSms, smsSendDo: {}", smsSendDo);
                    try {
                        serviceContext.writeAndFlush();
                    } catch (Exception e) {
                        log.error("[DOMAIN] sendReceivedSms error", e);
                    }
                } else {
                    log.warn("[DOMAIN] sendReceivedSms. inviteMessageTemplateId  is null");
                }
            } else {
                log.warn("[DOMAIN] sendReceivedSms. goods or smsPolicy is null");
            }
        } catch (Exception e) {
            log.error("sendReceivedSms error", e);
        }
    }

    private static List<String> getVariables(String templateId, Map<String, String> argsMap) {
        // 根据短信模板查询对应的占位符
        SmsTemplateIdCondition smsTemplateIdCondition = new SmsTemplateIdCondition();
        smsTemplateIdCondition.setSmsTemplateId(templateId);
        DomainServiceContext smsContext = new DomainServiceContext(QUERY_SMS_TEMPLATE_BY_ID);
        SmsTemplateDo smsTemplateIdDo = smsContext.readFirst(smsTemplateIdCondition, SmsTemplateDo.class);
        List<String> variables = new ArrayList<>();
        if (smsTemplateIdDo != null) {
            String keyOrder = smsTemplateIdDo.getKeyOrder();
            if (StringUtils.isNotEmpty(keyOrder)) {
                String[] split = keyOrder.split("\\,");
                for (String s : split) {
                    String variable = argsMap.get(s);
                    if (StringUtils.isNotEmpty(variable)) {
                        variables.add(variable);
                    }
                }
            }
        }
        return variables;
    }
}
