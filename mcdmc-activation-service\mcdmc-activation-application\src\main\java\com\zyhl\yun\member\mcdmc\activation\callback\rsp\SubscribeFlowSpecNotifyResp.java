package com.zyhl.yun.member.mcdmc.activation.callback.rsp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;


/*
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/3 10:37
 * @descrition 定向免流结果通知响应
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement(name = "subscribeFlowSpecNotifyResp")
@XmlAccessorType(XmlAccessType.FIELD)
public class SubscribeFlowSpecNotifyResp{
    /**
     * 响应码  0：成功 非0：失败
     */
    @JsonIgnore
    private String resultCode;

    /**
     * 响应描述
     */
    @JsonIgnore
    private String resultDesc;
}
