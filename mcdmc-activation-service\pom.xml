<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zyhl.yun.member.mcdmc</groupId>
        <artifactId>mcdmc</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zyhl.yun.member.mcdmc.activation</groupId>
    <artifactId>mcdmc-activation-service</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>mcdmc-activation-common</module>
        <module>mcdmc-activation-domain</module>
        <module>mcdmc-activation-infrastructure</module>
        <module>mcdmc-activation-application</module>
        <module>mcdmc-activation-adapter</module>
        <module>mcdmc-activation-service-stater</module>
    </modules>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-cloud-starter-bootstrap.version>3.1.3</spring-cloud-starter-bootstrap.version>
        <mapstruct.version>1.4.1.Final</mapstruct.version>
        <slf4j-api.version>1.7.30</slf4j-api.version>
        <redisson-spring-boot-starter.version>3.17.1</redisson-spring-boot-starter.version>
    </properties>
    <dependencies>
        <!--<dependency>-->
        <!--    <groupId>com.aspire.cbb</groupId>-->
        <!--    <artifactId>did-spring-boot-starter</artifactId>-->
        <!--    <version>2.0.0</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>io.projectreactor.netty</groupId>
            <artifactId>reactor-netty-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.projectreactor.netty</groupId>
            <artifactId>reactor-netty-http</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.zyhl.yun.member.mcdmc.activation</groupId>-->
        <!--            <artifactId>mcdmc-activation-application</artifactId>-->
        <!--            <version>1.0-SNAPSHOT</version>-->
        <!--            <scope>compile</scope>-->
        <!--        </dependency>-->
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j-api.version}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <propertyFile>archetype.yaml</propertyFile>
                    <encoding>UTF-8</encoding>
                    <archetypeFilteredExtentions>java,xml,yaml,yml,properties,txt</archetypeFilteredExtentions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source> <!-- depending on your project -->
                    <target>1.8</target> <!-- depending on your project -->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <!-- other annotation processors -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>