package com.zyhl.yun.member.mcdmc.activation.domain.mq.properties;

import com.zyhl.yun.member.consumer.common.producer.MqFlowStateEnum;
import com.zyhl.yun.member.consumer.common.producer.properties.IMqFlow;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 能开mq
 *
 * <AUTHOR>
 * @since 2024/11/25 10:40
 */
@Data
@Configuration
@ConfigurationProperties("rocketmq.consumer.outer-rights")
public class OuterRightsOpenCapacityMqProperties implements IMqFlow {

    /**
     * topic
     */
    private String unsubTopic;
    /**
     * tag
     */
    private String unsubTag;

    /**
     * mq流转状态
     *
     * @see MqFlowStateEnum
     */
    private String flowStatus = MqFlowStateEnum.OLD.getState();

    @Override
    public String getNewTopic() {
        return unsubTopic;
    }

    @Override
    public String getOldTopic() {
        return getNewTopic();
    }

    @Override
    public String getNewTag() {
        return unsubTag;
    }

    @Override
    public String getOldTag() {
        return getNewTag();
    }
}
