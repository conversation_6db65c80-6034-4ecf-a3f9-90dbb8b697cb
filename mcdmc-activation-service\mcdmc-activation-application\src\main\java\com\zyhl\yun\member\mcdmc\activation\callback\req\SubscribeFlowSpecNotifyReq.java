package com.zyhl.yun.member.mcdmc.activation.callback.req;

import com.zyhl.yun.member.common.constants.JiyunConstants;
import lombok.Data;
import lombok.ToString;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/3 10:37
 * @descrition 定向免流结果通知请求体
 */
@Data
@ToString
@XmlRootElement
@XmlAccessorType(XmlAccessType.FIELD)
public class SubscribeFlowSpecNotifyReq {
    /**
     * 履约ID
     */
    private String  orderNo;

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 手机号码
     */
    private String telephone;

    /**
     * 来源订单ID
     */
    private String sourceOrderNo;

    /**
     * 履约次数
     */
    private Integer index;

    /**
     * 商品编码
     */
    private String skuCode;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 订单状态  50 成功 ；60 失败
     */
    private Integer status;

    /**
     * 期望执行时间
     */
    private String expectedExecuteTime;

    /**
     * 实际执行时间
     */
    private String actualExecuteTime;

    /**
     * 业务码
     */
    private String bizCode;

    /**
     * 业务描述
     */
    private String bizDesc;

    private String userDomainId;

    public static boolean isSuccess(Integer status) {
        return JiyunConstants.JiyunSubscriptionStatus.SUCCESS_CODE.equals(status);
    }

    public static boolean isSuccess(String status) {
        if (status == null) {
            return false;
        }
        return isSuccess(Integer.parseInt(status));
    }
}
