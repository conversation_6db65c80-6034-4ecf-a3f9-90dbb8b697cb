@startuml
'https://plantuml.com/state-diagram

scale 350 width
IN_TRANSIT:订购初始状态，state=0
RECEIVED: 领取成功/已领取，state=1，\n rightStatus=3(权益开通成功）
OPEN_FAIL: 开通失败，state=2，\n rightStatus=1（发起权益失败）
RECEIVE_ING: 领取中，state=3，\n rightStatus=1（发起权益成功）
RECEIVED_FAIL: 领取失败，state=4，\n rightStatus=4(权益开通失败）
UNSUBSCRIBE: 已退订，state=8
REFUND_SUCCESS:退费成功,state=9，\n rightStatus=9(停车券退货成功）
REFUND_FAIL:退费失败,state=10，\n rightStatus=10(停车券退货失败）
REFUND_ING: 退费中，state=11

note "只有当产品是权益相关时才会修改rightStatus值" as N1
[*] --> IN_TRANSIT: 发起订购
IN_TRANSIT --> RECEIVE_ING: 服开调用远程接口成功
IN_TRANSIT --> OPEN_FAIL: 服开调用远程接口失败（重试结束）
RECEIVE_ING --> RECEIVED: 第三方回调返回订购成功
RECEIVE_ING --> RECEIVED_FAIL: 第三方回调返回订购失败

[*] --> UNSUBSCRIBE: 发起退订（停车退订/一级能开退订）
UNSUBSCRIBE --> UNSUBSCRIBE: 服开退订成功

[*] --> REFUND_ING: 发起退费（一级能开退费/权益退费）
REFUND_ING --> REFUND_FAIL: 服开调用第三方接口返回退费失败
REFUND_ING --> REFUND_SUCCESS: 服开调用第三方接口返回退费成功

ORIGINAL_STATE: 核减前对应的state
[*] --> ORIGINAL_STATE: 发起核减（权益核减）
ORIGINAL_STATE --> ORIGINAL_STATE: 服开处理核减请求，仅更新核减标识，状态不变
@enduml
