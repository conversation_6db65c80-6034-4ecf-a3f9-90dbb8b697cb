package com.zyhl.yun.member.mcdmc.activation.remote.send.req;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/3 10:37
 * @descrition 集运订购关系校验查询请求
 */
@Data
@ToString
public class GivenPrdOrderRelQueryReq {
    /**
     * 业务类型，固定值01
     */
    private String serviceType;

    /**
     * 手机号
     */
    private String serviceNumber;

    /**
     * 商品编码，多个goodsID用英文分号分割，如goodsId1;goodsId2;goodsId3
     */
    private String goodsIdList;

    /**
     * 一级能开编码，固定值2
     */
    private String goodsType;
}
