package com.zyhl.yun.member.mcdmc.activation.aspect;


import cn.hutool.core.util.IdUtil;
import com.zyhl.hcy.plugin.logger.constants.LogConstants;
import com.zyhl.yun.member.common.util.NetworkUtil;
import com.zyhl.yun.member.mcdmc.activation.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;

import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * 定义切面日志
 *
 * <AUTHOR>
 * @since 2023/11/03 15:49
 */

@Slf4j
@Aspect
@Component
public class LoggingAspect {


    @Pointcut("execution(* com.aspire.xthstore.adapter.web..*.*(..))")
    public void controllerMethods() {
        // 切点不做逻辑
    }


    @Before("controllerMethods()")
    public void logMethodEntry(JoinPoint joinPoint) {
        // 新增MDC链路追踪
        String traceId = IdUtil.getSnowflake(1, 1).nextIdStr();
        MDC.put(LogConstants.TRACE_ID, traceId);
        // 获取请求方法
        Signature signature = joinPoint.getSignature();
        Method method = ((MethodSignature) signature).getMethod();
        String methodName = method.getName();
        String className = method.getDeclaringClass().getName();
        Class<?> targetClass = joinPoint.getTarget().getClass();
        String controllerUrlPrefix = getControllerUrlPrefix(targetClass);
        String requestIp = NetworkUtil.getRequestIp(RequestUtil.getRequest());
        // 获取请求入参
        Object[] args = joinPoint.getArgs();
        String argsStr = Arrays.toString(args);
        log.info("当前请求接口为：{}，{}/{}，请求ip为：{}，请求入参：{}", className, controllerUrlPrefix,
                methodName, requestIp, argsStr);
    }

    @AfterReturning(pointcut = "controllerMethods()", returning = "result")
    public void logMethodExit(JoinPoint joinPoint, Object result) {
        // 获取请求方法
        Signature signature = joinPoint.getSignature();
        Method method = ((MethodSignature) signature).getMethod();
        String methodName = method.getName();
        String className = method.getDeclaringClass().getName();
        Class<?> targetClass = joinPoint.getTarget().getClass();
        String controllerUrlPrefix = getControllerUrlPrefix(targetClass);
        log.info("当前请求接口为：{}，{}/{},响应值为: {}", className, controllerUrlPrefix, methodName, result);
        // 清除MDC内容，避免线程复用
        MDC.clear();
    }

    @AfterThrowing(pointcut = "controllerMethods()", throwing = "exception")
    public void logMethodException(JoinPoint joinPoint, Exception exception) {
        // 获取请求方法
        Signature signature = joinPoint.getSignature();
        Method method = ((MethodSignature) signature).getMethod();
        String methodName = method.getName();
        String className = method.getDeclaringClass().getName();
        Class<?> targetClass = joinPoint.getTarget().getClass();
        String controllerUrlPrefix = getControllerUrlPrefix(targetClass);
        log.error("当前请求接口为：{}，{}/{},异常信息为: {}", className, controllerUrlPrefix, methodName, exception.getMessage());

        // 清除MDC内容，避免线程复用
        MDC.clear();
    }


    private String getControllerUrlPrefix(Class<?> controllerClass) {
        RequestMapping requestMapping = controllerClass.getAnnotation(RequestMapping.class);
        if (requestMapping != null && requestMapping.value().length > 0) {
            return requestMapping.value()[0];
        }
        return "";
    }
}
