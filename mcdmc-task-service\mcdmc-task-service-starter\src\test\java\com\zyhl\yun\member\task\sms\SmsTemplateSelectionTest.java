package com.zyhl.yun.member.task.sms;

import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.product.domain.goods.GoodsDo;
import com.zyhl.yun.member.product.domain.goods.policy.ProvSmsPolicyDo;
import com.zyhl.yun.member.product.domain.goods.policy.SmsPolicyDo;
import com.zyhl.yun.member.task.application.job.impl.TaskMessageNoticeExecImpl;
import com.zyhl.yun.member.task.common.domain.MessageNoticeTaskDo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

/**
 * 短信模板选择逻辑测试
 * 专门测试TaskMessageNoticeExecImpl中根据分省策略选择短信模板的逻辑
 */
@SpringBootTest
@DisplayName("短信模板选择逻辑测试")
public class SmsTemplateSelectionTest {

    @SpyBean
    private TaskMessageNoticeExecImpl taskMessageNoticeExecImpl;

    private MessageNoticeTaskDo messageNoticeTaskDo;
    private UserDo beijingUser;
    private UserDo shanghaiUser;
    private UserDo guangdongUser;
    private GoodsDo goodsWithFullProvPolicy;
    private GoodsDo goodsWithPartialProvPolicy;
    private GoodsDo goodsWithGlobalPolicyOnly;

    @BeforeEach
    void setUp() {
        // 准备消息通知任务
        messageNoticeTaskDo = new MessageNoticeTaskDo();
        messageNoticeTaskDo.setNoticeTaskId("template-test-001");
        messageNoticeTaskDo.setUserId("template-user-001");
        messageNoticeTaskDo.setReceiver("13800138000");
        messageNoticeTaskDo.setGoodsIds("template-goods-001");
//        messageNoticeTaskDo.setGoodsPackageInstanceId("template-instance-001");
        messageNoticeTaskDo.setSourceId("template-source-001");
        messageNoticeTaskDo.setNoticeTime(new Date());

        // 准备不同省份的用户
        beijingUser = createUser("bj-user", "110000", "13800138001");
        shanghaiUser = createUser("sh-user", "310000", "13800138002");
        guangdongUser = createUser("gd-user", "440000", "13800138003");

        // 准备不同策略配置的商品
        goodsWithFullProvPolicy = createGoodsWithFullProvPolicy();
        goodsWithPartialProvPolicy = createGoodsWithPartialProvPolicy();
        goodsWithGlobalPolicyOnly = createGoodsWithGlobalPolicyOnly();

        // Mock checkBeforeSending方法
        doReturn(beijingUser).when(taskMessageNoticeExecImpl).checkBeforeSending(any(MessageNoticeTaskDo.class));
    }

    /**
     * 创建用户
     */
    private UserDo createUser(String userId, String provCode, String msisdn) {
        UserDo user = new UserDo();
        user.setUserId(userId);
        user.setProvCode(provCode);
        user.setMsisdn(msisdn);
        user.setNationCode("86");
        return user;
    }

    /**
     * 创建包含完整分省策略的商品
     */
    private GoodsDo createGoodsWithFullProvPolicy() {
        GoodsDo goods = new GoodsDo();
        goods.setGoodsId("full-prov-goods");
        goods.setGoodsName("完整分省策略商品");

        SmsPolicyDo smsPolicy = new SmsPolicyDo();
        setupGlobalSmsPolicy(smsPolicy);

        List<ProvSmsPolicyDo> provSmsPolicyList = new ArrayList<>();
        
        // 北京分省策略 - 完整配置
        ProvSmsPolicyDo beijingPolicy = new ProvSmsPolicyDo("110000");
        beijingPolicy.setIsSendMsg(1);
        beijingPolicy.setSubMessageTemplateId("BJ_SUB_TEMPLATE");
        beijingPolicy.setUnSubMessageTemplateId("BJ_UNSUB_TEMPLATE");
        beijingPolicy.setInnerSubMessageTemplateId("BJ_INNER_SUB_TEMPLATE");
        beijingPolicy.setOuterSubMessageTemplateId("BJ_OUTER_SUB_TEMPLATE");
        beijingPolicy.setExpiredRemindTemplateId("BJ_EXPIRED_TEMPLATE");
        provSmsPolicyList.add(beijingPolicy);
        
        // 上海分省策略 - 完整配置
        ProvSmsPolicyDo shanghaiPolicy = new ProvSmsPolicyDo("310000");
        shanghaiPolicy.setIsSendMsg(1);
        shanghaiPolicy.setSubMessageTemplateId("SH_SUB_TEMPLATE");
        shanghaiPolicy.setUnSubMessageTemplateId("SH_UNSUB_TEMPLATE");
        shanghaiPolicy.setInnerSubMessageTemplateId("SH_INNER_SUB_TEMPLATE");
        shanghaiPolicy.setOuterSubMessageTemplateId("SH_OUTER_SUB_TEMPLATE");
        shanghaiPolicy.setExpiredRemindTemplateId("SH_EXPIRED_TEMPLATE");
        provSmsPolicyList.add(shanghaiPolicy);

        smsPolicy.setProvSmsPolicyList(provSmsPolicyList);
        goods.setSmsPolicy(smsPolicy);
        return goods;
    }

    /**
     * 创建包含部分分省策略的商品
     */
    private GoodsDo createGoodsWithPartialProvPolicy() {
        GoodsDo goods = new GoodsDo();
        goods.setGoodsId("partial-prov-goods");
        goods.setGoodsName("部分分省策略商品");

        SmsPolicyDo smsPolicy = new SmsPolicyDo();
        setupGlobalSmsPolicy(smsPolicy);

        List<ProvSmsPolicyDo> provSmsPolicyList = new ArrayList<>();
        
        // 北京分省策略 - 只配置订购相关模板
        ProvSmsPolicyDo beijingPolicy = new ProvSmsPolicyDo("110000");
        beijingPolicy.setIsSendMsg(1);
        beijingPolicy.setInnerSubMessageTemplateId("BJ_INNER_SUB_TEMPLATE");
        beijingPolicy.setOuterSubMessageTemplateId("BJ_OUTER_SUB_TEMPLATE");
        // 注意：没有配置退订和过期提醒模板
        provSmsPolicyList.add(beijingPolicy);

        smsPolicy.setProvSmsPolicyList(provSmsPolicyList);
        goods.setSmsPolicy(smsPolicy);
        return goods;
    }

    /**
     * 创建只有全局策略的商品
     */
    private GoodsDo createGoodsWithGlobalPolicyOnly() {
        GoodsDo goods = new GoodsDo();
        goods.setGoodsId("global-only-goods");
        goods.setGoodsName("仅全局策略商品");

        SmsPolicyDo smsPolicy = new SmsPolicyDo();
        setupGlobalSmsPolicy(smsPolicy);
        smsPolicy.setProvSmsPolicyList(new ArrayList<>()); // 空的分省策略列表

        goods.setSmsPolicy(smsPolicy);
        return goods;
    }

    /**
     * 设置全局短信策略
     */
    private void setupGlobalSmsPolicy(SmsPolicyDo smsPolicy) {
        smsPolicy.setIsSendMsg(1);
        smsPolicy.setSubMessageTemplateId("GLOBAL_SUB_TEMPLATE");
        smsPolicy.setUnSubMessageTemplateId("GLOBAL_UNSUB_TEMPLATE");
        smsPolicy.setInnerSubMessageTemplateId("GLOBAL_INNER_SUB_TEMPLATE");
        smsPolicy.setOuterSubMessageTemplateId("GLOBAL_OUTER_SUB_TEMPLATE");
        smsPolicy.setExpiredRemindTemplateId("GLOBAL_EXPIRED_TEMPLATE");
    }

    @Test
    @DisplayName("测试完整分省策略 - 北京用户端内订购短信模板选择")
    void testFullProvPolicy_BeijingUser_InnerSubTemplate() {
        String templateId = goodsWithFullProvPolicy.getSmsPolicy()
            .selectInnerSubMessageTemplateId(beijingUser.getProvCode());
        
        assertEquals("BJ_INNER_SUB_TEMPLATE", templateId);
        assertNotEquals("GLOBAL_INNER_SUB_TEMPLATE", templateId);
    }

    @Test
    @DisplayName("测试完整分省策略 - 北京用户端外订购短信模板选择")
    void testFullProvPolicy_BeijingUser_OuterSubTemplate() {
        String templateId = goodsWithFullProvPolicy.getSmsPolicy()
            .selectOuterSubMessageTemplateId(beijingUser.getProvCode());
        
        assertEquals("BJ_OUTER_SUB_TEMPLATE", templateId);
        assertNotEquals("GLOBAL_OUTER_SUB_TEMPLATE", templateId);
    }

    @Test
    @DisplayName("测试完整分省策略 - 北京用户退订短信模板选择")
    void testFullProvPolicy_BeijingUser_UnsubTemplate() {
        String templateId = goodsWithFullProvPolicy.getSmsPolicy()
            .selectUnSubMessageTemplateId(beijingUser.getProvCode());
        
        assertEquals("BJ_UNSUB_TEMPLATE", templateId);
        assertNotEquals("GLOBAL_UNSUB_TEMPLATE", templateId);
    }

    @Test
    @DisplayName("测试完整分省策略 - 上海用户端内订购短信模板选择")
    void testFullProvPolicy_ShanghaiUser_InnerSubTemplate() {
        String templateId = goodsWithFullProvPolicy.getSmsPolicy()
            .selectInnerSubMessageTemplateId(shanghaiUser.getProvCode());
        
        assertEquals("SH_INNER_SUB_TEMPLATE", templateId);
        assertNotEquals("GLOBAL_INNER_SUB_TEMPLATE", templateId);
    }

    @Test
    @DisplayName("测试完整分省策略 - 广东用户使用全局模板")
    void testFullProvPolicy_GuangdongUser_UseGlobalTemplate() {
        String templateId = goodsWithFullProvPolicy.getSmsPolicy()
            .selectInnerSubMessageTemplateId(guangdongUser.getProvCode());
        
        assertEquals("GLOBAL_INNER_SUB_TEMPLATE", templateId);
    }

    @Test
    @DisplayName("测试部分分省策略 - 北京用户端内订购使用分省模板")
    void testPartialProvPolicy_BeijingUser_InnerSubUseProvTemplate() {
        String templateId = goodsWithPartialProvPolicy.getSmsPolicy()
            .selectInnerSubMessageTemplateId(beijingUser.getProvCode());
        
        assertEquals("BJ_INNER_SUB_TEMPLATE", templateId);
        assertNotEquals("GLOBAL_INNER_SUB_TEMPLATE", templateId);
    }

    @Test
    @DisplayName("测试部分分省策略 - 北京用户退订使用全局模板")
    void testPartialProvPolicy_BeijingUser_UnsubUseGlobalTemplate() {
        String templateId = goodsWithPartialProvPolicy.getSmsPolicy()
            .selectUnSubMessageTemplateId(beijingUser.getProvCode());
        
        assertEquals("GLOBAL_UNSUB_TEMPLATE", templateId);
    }

    @Test
    @DisplayName("测试仅全局策略 - 所有用户都使用全局模板")
    void testGlobalPolicyOnly_AllUsersUseGlobalTemplate() {
        // 北京用户
        String beijingTemplateId = goodsWithGlobalPolicyOnly.getSmsPolicy()
            .selectInnerSubMessageTemplateId(beijingUser.getProvCode());
        assertEquals("GLOBAL_INNER_SUB_TEMPLATE", beijingTemplateId);

        // 上海用户
        String shanghaiTemplateId = goodsWithGlobalPolicyOnly.getSmsPolicy()
            .selectInnerSubMessageTemplateId(shanghaiUser.getProvCode());
        assertEquals("GLOBAL_INNER_SUB_TEMPLATE", shanghaiTemplateId);

        // 广东用户
        String guangdongTemplateId = goodsWithGlobalPolicyOnly.getSmsPolicy()
            .selectInnerSubMessageTemplateId(guangdongUser.getProvCode());
        assertEquals("GLOBAL_INNER_SUB_TEMPLATE", guangdongTemplateId);
    }

    @Test
    @DisplayName("测试SmsSubType与模板选择方法的对应关系")
    void testSmsSubTypeTemplateMethodMapping() {
        SmsPolicyDo smsPolicy = goodsWithFullProvPolicy.getSmsPolicy();
        String provCode = beijingUser.getProvCode();

        // 验证不同SmsSubType对应的模板选择方法
        assertEquals("BJ_INNER_SUB_TEMPLATE", 
            smsPolicy.selectInnerSubMessageTemplateId(provCode));
        assertEquals("BJ_OUTER_SUB_TEMPLATE", 
            smsPolicy.selectOuterSubMessageTemplateId(provCode));
        assertEquals("BJ_UNSUB_TEMPLATE", 
            smsPolicy.selectUnSubMessageTemplateId(provCode));
        assertEquals("BJ_EXPIRED_TEMPLATE", 
            smsPolicy.selectExpiredRemindTemplateId(provCode));
    }

    @Test
    @DisplayName("测试模板选择的一致性")
    void testTemplateSelectionConsistency() {
        SmsPolicyDo smsPolicy = goodsWithFullProvPolicy.getSmsPolicy();

        // 同一个省份的同一种模板，多次调用应该返回相同结果
        String template1 = smsPolicy.selectInnerSubMessageTemplateId("110000");
        String template2 = smsPolicy.selectInnerSubMessageTemplateId("110000");
        String template3 = smsPolicy.selectInnerSubMessageTemplateId("110000");

        assertEquals(template1, template2);
        assertEquals(template2, template3);
        assertEquals("BJ_INNER_SUB_TEMPLATE", template1);
    }

    @Test
    @DisplayName("测试空省份编码的模板选择")
    void testTemplateSelectionWithNullProvCode() {
        SmsPolicyDo smsPolicy = goodsWithFullProvPolicy.getSmsPolicy();

        // null省份编码应该使用全局模板
        assertEquals("GLOBAL_INNER_SUB_TEMPLATE", 
            smsPolicy.selectInnerSubMessageTemplateId(null));
        assertEquals("GLOBAL_OUTER_SUB_TEMPLATE", 
            smsPolicy.selectOuterSubMessageTemplateId(null));
        assertEquals("GLOBAL_UNSUB_TEMPLATE", 
            smsPolicy.selectUnSubMessageTemplateId(null));

        // 空字符串省份编码也应该使用全局模板
        assertEquals("GLOBAL_INNER_SUB_TEMPLATE", 
            smsPolicy.selectInnerSubMessageTemplateId(""));
        assertEquals("GLOBAL_OUTER_SUB_TEMPLATE", 
            smsPolicy.selectOuterSubMessageTemplateId(""));
        assertEquals("GLOBAL_UNSUB_TEMPLATE", 
            smsPolicy.selectUnSubMessageTemplateId(""));
    }

    @Test
    @DisplayName("测试分省策略优先级验证")
    void testProvPolicyPriorityVerification() {
        SmsPolicyDo smsPolicy = goodsWithFullProvPolicy.getSmsPolicy();

        // 验证分省策略优先于全局策略
        String beijingTemplate = smsPolicy.selectInnerSubMessageTemplateId("110000");
        String shanghaiTemplate = smsPolicy.selectInnerSubMessageTemplateId("310000");
        String guangdongTemplate = smsPolicy.selectInnerSubMessageTemplateId("440000");

        // 北京和上海有分省配置，应该使用分省模板
        assertEquals("BJ_INNER_SUB_TEMPLATE", beijingTemplate);
        assertEquals("SH_INNER_SUB_TEMPLATE", shanghaiTemplate);
        
        // 广东没有分省配置，应该使用全局模板
        assertEquals("GLOBAL_INNER_SUB_TEMPLATE", guangdongTemplate);

        // 验证分省模板与全局模板不同
        assertNotEquals(beijingTemplate, guangdongTemplate);
        assertNotEquals(shanghaiTemplate, guangdongTemplate);
    }

    @Test
    @DisplayName("测试模板ID非空验证")
    void testTemplateIdNotEmpty() {
        SmsPolicyDo smsPolicy = goodsWithFullProvPolicy.getSmsPolicy();

        // 验证所有模板ID都不为空
        assertNotNull(smsPolicy.selectInnerSubMessageTemplateId("110000"));
        assertNotNull(smsPolicy.selectOuterSubMessageTemplateId("110000"));
        assertNotNull(smsPolicy.selectUnSubMessageTemplateId("110000"));
        assertNotNull(smsPolicy.selectExpiredRemindTemplateId("110000"));

        // 验证模板ID不是空字符串
        assertFalse(smsPolicy.selectInnerSubMessageTemplateId("110000").trim().isEmpty());
        assertFalse(smsPolicy.selectOuterSubMessageTemplateId("110000").trim().isEmpty());
        assertFalse(smsPolicy.selectUnSubMessageTemplateId("110000").trim().isEmpty());
        assertFalse(smsPolicy.selectExpiredRemindTemplateId("110000").trim().isEmpty());
    }

    @Test
    @DisplayName("测试分省策略数据完整性")
    void testProvPolicyDataIntegrity() {
        SmsPolicyDo smsPolicy = goodsWithFullProvPolicy.getSmsPolicy();

        // 验证分省策略列表不为空
        assertNotNull(smsPolicy.getProvSmsPolicyList());
        assertEquals(2, smsPolicy.getProvSmsPolicyList().size());

        // 验证北京分省策略
        ProvSmsPolicyDo beijingPolicy = smsPolicy.getProvSmsPolicyList().stream()
            .filter(p -> "110000".equals(p.getProvCode()))
            .findFirst()
            .orElse(null);
        assertNotNull(beijingPolicy);
        assertEquals("110000", beijingPolicy.getProvCode());
        assertNotNull(beijingPolicy.getInnerSubMessageTemplateId());
        assertNotNull(beijingPolicy.getOuterSubMessageTemplateId());
        assertNotNull(beijingPolicy.getUnSubMessageTemplateId());

        // 验证上海分省策略
        ProvSmsPolicyDo shanghaiPolicy = smsPolicy.getProvSmsPolicyList().stream()
            .filter(p -> "310000".equals(p.getProvCode()))
            .findFirst()
            .orElse(null);
        assertNotNull(shanghaiPolicy);
        assertEquals("310000", shanghaiPolicy.getProvCode());
        assertNotNull(shanghaiPolicy.getInnerSubMessageTemplateId());
        assertNotNull(shanghaiPolicy.getOuterSubMessageTemplateId());
        assertNotNull(shanghaiPolicy.getUnSubMessageTemplateId());
    }
}
