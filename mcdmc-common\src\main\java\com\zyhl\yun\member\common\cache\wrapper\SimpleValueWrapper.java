package com.zyhl.yun.member.common.cache.wrapper;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.zyhl.yun.member.common.JsonUtil;
import com.zyhl.yun.member.common.cache.EntityCacheable;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/20 14:33
 */
@Data
@NoArgsConstructor
@Slf4j
public class SimpleValueWrapper implements CacheValueWrapper {


    private Serializable key;


    private Serializable value;

    private List<EntityCacheable> valueList;

    public SimpleValueWrapper(Serializable key, Serializable value) {
        this.key = key;
        if (value == null) {
            this.value = null;
        } else if (value instanceof SimpleValueWrapper) {
            this.value = ((SimpleValueWrapper) value).getValue();
            this.valueList = ((SimpleValueWrapper) value).getValueList();
        } else {
            this.value = value;
        }
    }


    public SimpleValueWrapper(Serializable key, List<EntityCacheable> valueList) {
        this.key = key;
        if (!CollectionUtils.isEmpty(valueList)) {
            this.valueList = new ArrayList<>();
            for (EntityCacheable value : valueList) {
                if (value != null) {
                    this.valueList.add(value);
                }
            }
        }
    }


    public static SimpleValueWrapper fromJson(String jsonString, Class<?> clazz) {
        try {
            JSONObject jsonObject = JsonUtil.parseObject(jsonString);
            String key = jsonObject.get("key", String.class);

            JSONArray valueListJsonArray = jsonObject.getJSONArray("valueList");
            if (valueListJsonArray != null) {
                return new SimpleValueWrapper(key, (List<EntityCacheable>) valueListJsonArray.toList(clazz));
            }

            Serializable value = (Serializable) jsonObject.get("value", clazz);
            return new SimpleValueWrapper(key, value);
        } catch (Exception e) {
            log.warn("[CACHE] parse json error: {}", e.getMessage());
            if (log.isDebugEnabled()) {
                log.error("[CACHE] parse json error.", e);
            }
            return null;
        }
    }

}
