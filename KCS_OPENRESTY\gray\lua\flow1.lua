flow = {}

function flow.turnTo()
    local startTime = ngx.now()
    local conf_dict = ngx.shared.conf_dict
    local globalSwitch = conf_dict:get("globalSwitch")
    if globalSwitch and globalSwitch == "open" then
        -- 全局开关打开，直接转发至新vsbo
        flow.turnToNew("unknown","unknown","Vsbo","globalSwitch is open")
        return
    end

    local headers = ngx.req.get_headers()
    if not headers["flow-type"] then
        local ok, userId = pcall(flow.realFlow)
        if not ok then
            -- 有异常则直接将流量打回旧vsbo
            flow.turnToOld("unknown","unknown","Vsbo","unknown error")
        end
    else
        ngx.log(ngx.ERR,"++++++++ emptyFlow")
        flow.emptyFlow()
    end
   local endTime=ngx.now()
   ngx.log(ngx.ERR,"++++++++ flow cost time=",endTime-startTime,",startTime=",startTime,",endTime=",endTime)

end

function flow.emptyFlow()
    local isXml,pathDict=flow.getPathDict()
    local uri,dictKey,path=flow.getNextPath(pathDict,0)
    if not path then
        -- 没有映射规则则直接转发至旧vsbo
        flow.turnToOld(uri,"unknown","Vsbo","unknown")
        return
    end
    ngx.log(ngx.ERR,"++++++++ parsePath dictKey=[",dictKey,"],path=[",path,"]")
    local rule,platform,userType,realPath=flow.parsePath(path)
    flow.turnToOld(uri,"unknown",platform,"emptyFlow")
end


function flow.realFlow()
    local uri = ngx.var.uri
    local endingStr="syncAppOrder"
    if string.find(uri, endingStr, #uri - #endingStr + 1) then
        flow.syncAppOrder()
        return
    end

    local isXml,pathDict=flow.getPathDict()
    local index = 0
    -- 拼接规则 0：不需要分割；1：userId_xxx;2:xxx_userId
    local reqBodyObj
    while true do
        local uri,dictKey,path=flow.getNextPath(pathDict,index)
        if not path then
            -- 没有映射规则则直接转发至旧vsbo
            flow.turnToOld(uri,"unknown","Vsbo","unknown")
            return
        end
        ngx.log(ngx.ERR,"++++++++ parsePath dictKey=[",dictKey,"],path=[",path,"]")
        local rule,platform,userType,realPath=flow.parsePath(path)
        if rule == 4 then
            -- 直接转发至新vsbo
            flow.turnToNew(uri,userId,platform,rule)
            return
        end
        if not reqBodyObj then
            -- 获取请求体对象
            reqBodyObj=flow.getReqBodyObj(isXml)
        end
        local userId = flow.getPathValue(reqBodyObj,realPath,isXml,rule)
        if rule == 3 then
            if userId and string.match(userId, "_rft$") then
                flow.turnToNew(uri,userId,platform,rule)
            else
                flow.turnToOld(uri,userId,platform,rule)
            end
            return
        end
        if rule == 5 then
            if flow.isNewOrderId(userId) then
                flow.turnToNew(uri,userId,platform,rule)
            else
                flow.turnToOld(uri,userId,platform,rule)
            end
            return
        end
        if rule == 1 and string.find(userId,"_") then
            -- userId_***分割规则，需要根据_分割，并获取第一个字符串，为userId
            userId = string.match(userId, "^([^_]+)")
        elseif rule == 2  and userId then
            if string.find(userId,"_") then
                -- **_userId分割规则，需要根据_分割，并获取最后一个字符串，为userId
                userId = string.match(userId, "[^_]+$")
            else
                flow.turnToOld(uri,userId,platform,rule)
                return
            end
        end
        if userId then
            -- 获取userId需要去调用白名单服务判断是否白名单
            local isWhite = flow.isWhiteUser(uri,userId,userType)
            if isWhite then
                flow.turnToNew(uri,userId,platform,rule)
            else
                flow.turnToOld(uri,userId,platform,rule)
            end
            return
        end
        index = index + 1
    end
end


function flow.turnToNew(uri,userId,platform,rule)
    local conf_dict = ngx.shared.conf_dict
    -- 跳转到新vsbo
    local index = conf_dict:get("newIndex")
    ngx.ctx.port = conf_dict:get("newVsboPort" .. index)
    ngx.ctx.ip_addr = conf_dict:get("newVsboIp" .. index)
    ngx.log(ngx.ERR,"----------- uri:[",uri,"]userId =[",userId,"][rule=",rule,"] is contain whiteValue,turn to new",platform,"[",ngx.ctx.ip_addr,":",ngx.ctx.port,"]")
end
function flow.turnToOld(uri,userId,platform,rule)
    local conf_dict = ngx.shared.conf_dict
    -- 跳转到新vsbo
    local index = conf_dict:get("oldIndex")
    ngx.ctx.port = conf_dict:get("old" .. platform .. "Port" .. index)
    ngx.ctx.ip_addr = conf_dict:get("old" .. platform .. "Ip" .. index)
    ngx.log(ngx.ERR,"----------- uri:[",uri,"]userId =[",userId,"][rule=",rule,"] is not whiteValue,turn to old",platform,"[",ngx.ctx.ip_addr,":",ngx.ctx.port,"]")
end

function flow.getPathValue(reqBodyObj,realPath,isXml,joinRule)
    -- 遍历报文获取userId
    local destBody = reqBodyObj
    local pathIndex = 1
    for path in string.gmatch(realPath, "[^.]+") do
        -- 获取对应的value，可能为table，也可能为string
        if isXml then
            destBody = flow.getXmlValue(destBody,path,pathIndex)
        else
            destBody = flow.getJsonValue(destBody,path,pathIndex)
        end
        if not destBody then
            break
        end
        pathIndex = pathIndex + 1
    end
    if destBody then
        -- 正常获取到最后的报文就是指定的userId
        local result = destBody
        if isXml then
            result = result:getXmlValue()
        end
        ngx.log(ngx.ERR,"----------- realPath is [",realPath,"],getPathValue result is ",result)
        return result
    end
    return nil
end
-- 从xml里获取目标key（单层）
function flow.getXmlValue(xmlBody,destKey,keyIndex)
    if keyIndex == 1 then
        return xmlBody:getXmlChildren()[1]
    else
        return xmlBody[destKey]
    end
end
-- 从json里获取目标key（单层）
function flow.getJsonValue(jsonObj,destKey)
    if type(jsonObj) == "table" then
        -- 为table类型，则继续往下获取嵌套value
        for key, value in pairs(jsonObj) do
            if key == destKey then
                return value
            end
        end
    else
        -- 若为非table类型，且此时还没达到最后的path，则直接返回nil（忽略数组类型）
        return nil
    end
end

-- 根据contentType解析报文,获取对象
function flow.getReqBodyObj(isXml)
    local bodyStr = ngx.req.get_body_data()
    ngx.log(ngx.ERR,"current isXml:[",isXml,"], bodyStr is ",bodyStr)
    if isXml then
        local xml = require "aspire/xmlSimple"
        local xmlParser = xml.newParser()
        return xmlParser:ParseXmlText(bodyStr)
    else
        local json = require "aspire/json"
        return json.decode(bodyStr)
    end
end

-- 获取字典映射
function flow.getPathDict()
    local contentType = ngx.var.http_content_type
    local isXml = string.find(string.lower(contentType),"xml")
    if isXml then
        return true,ngx.shared.xml_path_dict
    else
        return false,ngx.shared.json_path_dict
    end
end

-- 获取下一个path
function flow.getNextPath(pathDict,index)
    local uri = ngx.var.uri
    local lastUri = string.match(uri,"([^/]+)$")
    local dictKey = lastUri
    if index >= 1 then
        dictKey = lastUri .. "_" .. index
        local nextPath=pathDict:get(dictKey)
        if not nextPath then
            -- 对于pgw，需要直接根据uri获取下一个path
            dictKey = uri .. "_" .. index
            nextPath=pathDict:get(dictKey)
            return uri,dictKey,nextPath
        end
        return lastUri,dictKey,nextPath
    end
    local nextPath=pathDict:get(dictKey)
    if not nextPath then
        -- 对于pgw，需要直接根据uri获取下一个path
        dictKey = uri
        nextPath=pathDict:get(dictKey)
    return uri,dictKey,nextPath
    end
    return lastUri,dictKey,nextPath
end

-- 解析映射表里的路径
function flow.parsePath(path)
    local rule,remainStr=string.match(path,"^(%w+)_*(.*)$")
    local platform,remainStr=string.match(remainStr,"^(%w+)_*(.*)$")
    if remainStr then
        local userType,remainStr=string.match(remainStr,"^(%w+)_*(.*)$")
        local realPath=remainStr
        ngx.log(ngx.ERR,"path=[",path,"]rule=[",rule,"],platform=[",platform,"],userType=[",userType,"],realPath=[",realPath,"]")
        return tonumber(rule),platform,tonumber(userType),realPath
    end
    ngx.log(ngx.ERR,"path=[",path,"]rule=[",rule,"],platform=[",platform,"]")
    return tonumber(rule),platform,nil,nil
end

function flow.isWhiteUser(uri,userId,userType)
    if not userId then
        return false
    end
     --获取白名单列表
    local white_dict = ngx.shared.white_dict
    local value = white_dict:get(userId .. "_" .. userType)
    if value then
        return true
    else
        return false
    end
end
function flow.remoteCallWhiteService(userType,key,uri)
    local http = require "aspire.resty.http"
    local remoteUrl = "http://[fd11:1111:1111:23::ffb3]:18099/member-white/user/flow/valid"
    local body={
        type = userType,
        key = key,
        uri = uri
    }
    local json = require "aspire/json"
    local body_str = json.encode(body)
    ngx.log(ngx.ERR,"invoke remote url=[",remoteUrl,"],body=[",body_str,"]")
    -- 创建一个 HTTP 客户端
    local client = http.new()
    -- 发送 HTTP POST 请求
    local res, err = client:request_uri(remoteUrl, {
        method = "POST",
        body = body_str,
        headers = {
            ["Content-Type"] = "application/json",
            ["Host"] = "mcdmc-white-service-starter-test.test.com"
        }
    })

    -- 检查请求是否成功
    if not res then
        ngx.log(ngx.ERR, "请求失败: ", err)
        ngx.exit(500)
    end

    -- 检查响应状态码
    if res.status ~= 200 then
        ngx.log(ngx.ERR, "请求失败，状态码: ", res.status)
        return nil
    end

    -- 获取响应体
    local response_body = res.body
    ngx.log(ngx.ERR, "请求响应结果为: ", response_body)
    if response_body == "true" then
        return true
    else
        return false
    end
end

function flow.syncAppOrder()
    local isXml = true
    local reqBodyObj=flow.getReqBodyObj(isXml)
    local account = flow.getPathValue(reqBodyObj,"SyncAppOrderReq.MSISDN",isXml,0)
    -- 获取account需要去调用白名单服务判断是否白名单
    local isWhite = flow.isWhiteUser("syncAppOrder",account,0)
    if not isWhite then
        flow.turnToOld("syncAppOrder",account,"Vsbo","syncAppOrder")
        return
    end
    -- 判断是否省侧订购
    local subPlace = flow.getPathValue(reqBodyObj,"SyncAppOrderReq.Subplace",isXml,0)
    if tonumber(subPlace) ~= 7 then
        local orderId = flow.getPathValue(reqBodyObj,"SyncAppOrderReq.TradeID",isXml,0)
        ngx.log(ngx.ERR,"----syncAppOrder orderId: ",orderId)
        if flow.isNewOrderId(orderId) then
            flow.turnToNew("syncAppOrder",orderId,"Vsbo","syncAppOrder")
        else
            flow.turnToOld("syncAppOrder",orderId,"Vsbo","syncAppOrder")
        end
    else
        flow.turnToNew("syncAppOrder","action!=1","Vsbo","syncAppOrder")
    end
end

function flow.isNewOrderId(orderId)
    local userId,uuid1,uuid2,uuid3=string.match(orderId,"([^_]+)_*([^-]*)-*([^-]*)-*(.*)$")
    local newOrderIdPrefix="3"
    if string.sub(uuid3,1,#newOrderIdPrefix) == newOrderIdPrefix then
        return true
    else
        return false
    end
end
return flow