package com.zyhl.yun.member.mcdmc.activation.callback.notify;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceExtendServiceId;
import com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceRecServiceId;
import com.zyhl.yun.member.common.domain.serviceid.SubscribeServiceId;
import com.zyhl.yun.member.common.enums.SubscribeTypeEnum;
import com.zyhl.yun.member.common.util.SnowflakeUtil;
import com.zyhl.yun.member.domain.goodsinstance.domain.GoodsInstanceRecDo;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.receive.domain.RightsExtInfo;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.CallbackContext;
import com.zyhl.yun.member.mcdmc.activation.callback.notify.base.CallbackTemplate;
import com.zyhl.yun.member.mcdmc.activation.callback.req.RaiComNotifyReq;
import com.zyhl.yun.member.mcdmc.activation.callback.rsp.RaiComNotifyRsp;
import com.zyhl.yun.member.mcdmc.activation.constant.SendOperation;
import com.zyhl.yun.member.mcdmc.activation.constants.BusinessConstant;
import com.zyhl.yun.member.mcdmc.activation.constants.WorkAttrCodeConstant;
import com.zyhl.yun.member.mcdmc.activation.domain.dos.WorkServiceFlowIFaceDo;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.CallbackException;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.FlowStaticConfig;
import com.zyhl.yun.member.mcdmc.activation.domain.remote.IServiceFlow;
import com.zyhl.yun.member.mcdmc.activation.domain.utils.ActivationContextUtil;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderAttrDo;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.facade.ActivationServiceFacade;
import com.zyhl.yun.member.mcdmc.activation.flow.rai.check.RaiOrderIncreaseFlow;
import com.zyhl.yun.member.mcdmc.activation.flow.rai.check.RaiOrderRePushFlow;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.unsub.OuterRightsUnSubIFace;
import com.zyhl.yun.member.mcdmc.activation.result.BaseResult;
import com.zyhl.yun.member.mcdmc.activation.result.ResultCode;
import com.zyhl.yun.member.mcdmc.activation.util.OuterRightsUnSubContextUtil;
import com.zyhl.yun.member.mcdmc.activation.util.RaiUtil;
import com.zyhl.yun.member.product.domain.goods.facade.ProductServiceFacade;
import com.zyhl.yun.member.product.domain.product.ProductDo;
import com.zyhl.yun.member.product.domain.product.ProductServiceDo;
import com.zyhl.yun.member.subscribe.domain.SubscriptionRecDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;


/**
 * 权益相关统一回调
 * com.huawei.jaguar.vsbo.service.action.RaiEquityNotifyResultAction#raiNotifyEquityResult
 *
 * <AUTHOR>
 * @since 2024/06/22 11:07
 */
@Slf4j
public abstract class RaiComSubCallback extends CallbackTemplate<RaiComNotifyReq, RaiComNotifyRsp> {

    private RaiUtil raiUtil;

    private OuterRightsUnSubContextUtil outerRightsUnSubContextUtil;

    @PostConstruct
    private void initBean() {
        raiUtil = SpringUtil.getBean(RaiUtil.class);
        outerRightsUnSubContextUtil = SpringUtil.getBean(OuterRightsUnSubContextUtil.class);
    }

    /**
     * 获取版本号
     */
    protected abstract String getDefaultEquityVersion();

    @Override
    protected void doCheck(CallbackContext<RaiComNotifyReq, RaiComNotifyRsp> callbackContext) throws CallbackException {
        doCheckEmpty(callbackContext.getCallbackReq());
        IServiceFlow sendFlow = FlowStaticConfig.getIFlow(callbackContext.getServiceCode());
        // 核增重推不关心回调结果
        if (sendFlow instanceof RaiOrderIncreaseFlow || sendFlow instanceof RaiOrderRePushFlow) {
            throw new CallbackException(ResultCode.SUCCESS, null);
        }
    }

    @Override
    protected Class<RaiComNotifyReq> getNotifyReqClass() {
        return RaiComNotifyReq.class;
    }

    @Override
    protected BaseResult getSuccessResult() {
        return BaseResult.sc("0", "ok");
    }

    @Override
    protected RaiComNotifyRsp buildNotifyRsp(CallbackContext<RaiComNotifyReq, RaiComNotifyRsp> callbackContext, BaseResult result) {
        RaiComNotifyRsp mingWeiNotifyRsp = new RaiComNotifyRsp();
        mingWeiNotifyRsp.setMsgVer(getDefaultEquityVersion());
        mingWeiNotifyRsp.setReturnCode(Integer.parseInt(result.getResultCode()));
        mingWeiNotifyRsp.setReturnMsg(result.getMessage());
        return mingWeiNotifyRsp;
    }

    @Override
    protected void doAfterNotify(CallbackContext<RaiComNotifyReq, RaiComNotifyRsp> callbackContext, GoodsInstanceDo subGoodsInstance) {
        // 回调成功则发送内容mq
        RaiComNotifyReq raiComNotifyReq = callbackContext.getCallbackReq();
        String outOrderId = getSearchCondition(raiComNotifyReq);
        // 查询
        RightsExtInfo rightsExtInfoObj = subGoodsInstance.getRightsExtInfoObj();
        if (rightsExtInfoObj == null) {
            rightsExtInfoObj = new RightsExtInfo();
        }
        rightsExtInfoObj.setRightsServiceSubId(outOrderId);
        subGoodsInstance.setRightsExtInfoObj(rightsExtInfoObj);
        // 更新会员资产id
        String orderItemId = raiComNotifyReq.getOrderList().get(0).getOrderItemId();
        subGoodsInstance.setResourceId(orderItemId);
        // 更新商品实例
        this.updateGoodsInstance(subGoodsInstance);
        // 插入到工单属性
        String sendWorkId = callbackContext.getSendServiceFlowLogDo().getWorkId();
        String userId = callbackContext.getSendServiceFlowLogDo().getUserId();
        // 回调失败时需要记录回调报文
        if (isSuccess(raiComNotifyReq)) {
            ActivationContextUtil.upsertWorkAttr(userId, sendWorkId, WorkAttrCodeConstant.RIGHTS_OUT_ACCOUNT_ID, orderItemId);
        } else {
            ActivationContextUtil.upsertWorkAttr(userId, sendWorkId, WorkAttrCodeConstant.RAI_SUB_FAIL_BODY, callbackContext.getCallbackReqStr());
        }

        // 发送内容mq
        String outUpdateTime = raiComNotifyReq.getOrderList().get(0).getUpdateTime();
        raiUtil.sendContentMq(callbackContext.getComSendReq(), outOrderId, outUpdateTime, subGoodsInstance, orderItemId);
        if (this.isSuccess(raiComNotifyReq)) {
            // 补发给能开的退订mq
            this.resendUnSubMqToOpenCapacity(subGoodsInstance);
        }
    }

    /**
     * 补发给能开的退订mq
     *
     * @param goodsInstanceDo 商品实例
     */
    private void resendUnSubMqToOpenCapacity(GoodsInstanceDo goodsInstanceDo) {
        // 1. 通过productId查询ProductDo
        ProductDo productDo = ProductServiceFacade.getProduct(goodsInstanceDo.getProductId());
        if (productDo == null) {
            log.error("resendUnsubMqToOpenCapacity productDo is null, productId={}", goodsInstanceDo.getProductId());
            return;
        }
        // 2. 从productServiceList筛选出unSub的productServiceDo
        ProductServiceDo unSubProductService = productDo.getProductService(SubscribeTypeEnum.UN_SUBSCRIBE_TYPE_ENUM);
        if (unSubProductService == null) {
            log.error("resendUnsubMqToOpenCapacity unSubProductService not found, productId={}", goodsInstanceDo.getProductId());
            return;
        }

        // 3. 查询工单属性表
        String userId = goodsInstanceDo.getUserId();
        String orderId = goodsInstanceDo.getOrderId();
        String soServiceCode = unSubProductService.getSoServiceCode();
        List<WorkOrderDo> workOrderDoList = ActivationServiceFacade.getWorkOrderAttrList(userId, orderId,
                soServiceCode, Collections.singletonList(WorkAttrCodeConstant.EARLY_FINISH_MSG));
        if (CollectionUtils.isEmpty(workOrderDoList) || CollectionUtils.isEmpty(workOrderDoList.get(0).getWorkOrderAttrDoList())) {
            log.warn("resendUnsubMqToOpenCapacity workOrderDoList not found, userId={}, serviceCode={}, orderId={}",
                    userId, soServiceCode, orderId);
            return;
        }
        WorkOrderAttrDo workOrderAttrDo = workOrderDoList.get(0).getWorkOrderAttrDoList().get(0);
        if (workOrderAttrDo == null || !StringUtils.hasText(workOrderAttrDo.getAttrVal())) {
            log.warn("resendUnsubMqToOpenCapacity workOrderAttrDo not found, userId={}, serviceCode={}, orderId={}",
                    userId, soServiceCode, orderId);
            return;
        }
        if (!workOrderAttrDo.getAttrVal().startsWith(BusinessConstant.RAI_UNSUBSCRIBE_MESSAGE_FLAG)) {
            log.warn("resendUnsubMqToOpenCapacity workOrderAttrDo not start with RAI_UNSUBSCRIBE_MESSAGE_FLAG, userId={}, serviceCode={}, orderId={}",
                    userId, soServiceCode, orderId);
            return;
        }
        // 4. 获取对应的iface配置
        WorkServiceFlowIFaceDo iFaceDo = FlowStaticConfig.getFaceDoByIFaceClass(soServiceCode, OuterRightsUnSubIFace.class);
        if (iFaceDo == null) {
            log.warn("resendUnsubMqToOpenCapacity iFaceDo not found, serviceCode={}", soServiceCode);
            return;
        }
        // 5. 发送退订mq给能开
        outerRightsUnSubContextUtil.sendOpenCapacityUnsubscribeMessage(
                iFaceDo.getIFaceConfig(),
                goodsInstanceDo.getResourceId()
        );
    }

    public void updateGoodsInstance(GoodsInstanceDo goodsInstanceDo) {
        // 更新商品实例
        DomainServiceContext memberContext = new DomainServiceContext(GoodsInstanceExtendServiceId.UPDATE_GOODS_INSTANCE_EXTEND);
        GoodsInstanceDo insertGoodsInstanceDo = new GoodsInstanceDo();
        insertGoodsInstanceDo.setGoodsInstanceId(goodsInstanceDo.getGoodsInstanceId());
        insertGoodsInstanceDo.setUserId(goodsInstanceDo.getUserId());
        insertGoodsInstanceDo.setResourceId(goodsInstanceDo.getResourceId());
        // 更新bi参数
        insertGoodsInstanceDo.setRightsExtInfoObj(goodsInstanceDo.getRightsExtInfoObj());
        memberContext.putInstance(insertGoodsInstanceDo);
        memberContext.writeAndFlush();

        // 插入sub_t_subscription_rec流水表
        DomainServiceContext subscribeContext = new DomainServiceContext(SubscribeServiceId.INSERT_SUBSCRIBE);
        SubscriptionRecDo subscriptionRecDo = new SubscriptionRecDo();
        BeanUtil.copyProperties(goodsInstanceDo, subscriptionRecDo);
        subscriptionRecDo.setId(IdUtil.getSnowflakeNextIdStr());
        subscriptionRecDo.setRightsExtInfo(JSONUtil.toJsonStr(ObjectUtil.defaultIfNull(goodsInstanceDo.getRightsExtInfoObj(), "")));
        subscribeContext.putInstance(subscriptionRecDo);
        subscribeContext.writeAndFlush(null);

        // 插入member_t_goods_instance_rec流水表
        DomainServiceContext context = new DomainServiceContext(GoodsInstanceRecServiceId.GOODS_INSTANCE_REC_INSERT);
        GoodsInstanceRecDo goodsInstanceRecDo = new GoodsInstanceRecDo();
        goodsInstanceRecDo.setId(SnowflakeUtil.getNextString());
        BeanUtil.copyProperties(goodsInstanceDo, goodsInstanceRecDo);
        goodsInstanceRecDo.setState(goodsInstanceDo.getStateEnum().getState());
        context.putInstance(goodsInstanceRecDo);
        context.writeAndFlush();
    }

    @Override
    protected String getSearchCondition(RaiComNotifyReq notifyReq) {
        return notifyReq.getOrderNo();
    }

    @Override
    protected boolean isSuccess(RaiComNotifyReq notifyReq) {
        return RaiUtil.RECEIVE_ORDER_SUCCESS_STATUS.equals(String.valueOf(notifyReq.getStatus()));
    }

    @Override
    protected SendOperation getSendOperation() {
        return SendOperation.SUB;
    }
}
