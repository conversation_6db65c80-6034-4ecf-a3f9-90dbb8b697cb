package com.zyhl.yun.member.mcdmc.activation.remote.send.iface.rai.unsub;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.facade.GoodsInstanceServiceFacade;
import com.zyhl.yun.member.mcdmc.activation.constants.BusinessConstant;
import com.zyhl.yun.member.mcdmc.activation.constants.IFaceConfigMapKey;
import com.zyhl.yun.member.mcdmc.activation.constants.SymbolConstant;
import com.zyhl.yun.member.mcdmc.activation.domain.constants.OtherFieldConstants;
import com.zyhl.yun.member.mcdmc.activation.domain.enums.NextHintEnum;
import com.zyhl.yun.member.mcdmc.activation.domain.exception.FlowTerminationException;
import com.zyhl.yun.member.mcdmc.activation.domains.WorkOrderDo;
import com.zyhl.yun.member.mcdmc.activation.domains.req.ComSendInterfaceReq;
import com.zyhl.yun.member.mcdmc.activation.enums.OuterRightsUnSubNotifyTypeEnum;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.InterfaceContext;
import com.zyhl.yun.member.mcdmc.activation.remote.send.iface.base.SendTemplate;
import com.zyhl.yun.member.mcdmc.activation.remote.send.properties.ParkUnSubProperties;
import com.zyhl.yun.member.mcdmc.activation.remote.send.req.ParkUnSubReq;
import com.zyhl.yun.member.mcdmc.activation.remote.send.resp.ParkUnSubRsp;
import com.zyhl.yun.member.mcdmc.activation.util.IFaceConfigUtil;
import com.zyhl.yun.member.mcdmc.activation.util.OuterRightsUnSubContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;

/**
 * 停车退订实现类（无回调）
 * com.huawei.jaguar.vsbo.service.util.ParkPlatSyncUtil#refundSync
 *
 * <AUTHOR>
 * @since 2024/06/18 17:47
 */
@Slf4j
@Component
public class ParkUnSubIFace extends SendTemplate<ParkUnSubReq, ParkUnSubRsp> {
    @Resource
    private ParkUnSubProperties parkUnSubProperties;

    @Autowired
    IFaceConfigUtil iFaceConfigUtil;


    @Autowired
    OuterRightsUnSubContextUtil outerRightsUnSubContextUtil;

    /**
     * 参数校验，若失败则直接结束
     *
     * @param interfaceContext 接口上下文
     */
    @Override
    protected void doCheckParam(InterfaceContext<ParkUnSubReq, ParkUnSubRsp> interfaceContext) throws FlowTerminationException {
        WorkOrderDo workOrderDo = interfaceContext.getWorkOrderDo();
        // 校验是否只发mq
        String iFaceConfig = interfaceContext.getWorkServiceFlowIFaceDo().getIFaceConfig();
        String goodsId = interfaceContext.getComSendReq().getGoodsId();
        if (!StringUtils.hasText(goodsId)) {
            throw new FlowTerminationException(this.getClass(), workOrderDo, "doCheckParam工单入参缺少goodsId");
        }
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        GoodsInstanceDo goodsInstanceDo = GoodsInstanceServiceFacade.qryGoodsInstance(comSendReq.getUserId(), comSendReq.getGoodsInstanceId());
        if (goodsInstanceDo == null) {
            log.error("parkUnSubIFace doCheckParam outer Rights unSub fail,userId={},orderId={},goodsId={},goodsInstanceDo is null",
                    comSendReq.getUserId(), comSendReq.getOrderID(), comSendReq.getGoodsId());
            // 退订查不到订单则直接重置流程
            throw new FlowTerminationException(this.getClass(), interfaceContext.getWorkOrderDo(), NextHintEnum.RESTART,
                    "parkUnSubIFace doCheckParam outer Rights unSub fail,work detail is %s,goodsInstanceDo is null", workOrderDo.toSimpleLogStr());
        }
        String resourceId = goodsInstanceDo.getResourceId();
        if (!StringUtils.hasText(resourceId)) {
            log.error("parkUnSubIFace doCheckParam outer Rights unSub fail,userId={},orderId={},goodsId={},resourceId is null",
                    comSendReq.getUserId(), comSendReq.getOrderID(), comSendReq.getGoodsId());
            // 退订查不到订单则直接重置流程
            throw new FlowTerminationException(this.getClass(), interfaceContext.getWorkOrderDo(), NextHintEnum.FINISH,
                    BusinessConstant.RAI_UNSUBSCRIBE_MESSAGE_FLAG + "parkUnSubIFace doCheckParam outer Rights " +
                            "unSub fail,work order detail is %s,resourceId of goodsInstance is null", workOrderDo.toSimpleLogStr());
        }
        // 记录以备拼接请求体使用
        comSendReq.putExtInfo(OtherFieldConstants.RESOURCE_ID, resourceId);
        // 获取对应的Iface的外部配置信息
        String notifyType = iFaceConfigUtil.getFaceConfig(iFaceConfig, IFaceConfigMapKey.NOTIFY_TYPE);
        OuterRightsUnSubNotifyTypeEnum notifyTypeEnum = OuterRightsUnSubNotifyTypeEnum.getByNotifyCode(notifyType);
        if (notifyTypeEnum != null) {
            switch (notifyTypeEnum) {
                case ONLY_SEND_MQ:
                    // 发mq给能开
                    outerRightsUnSubContextUtil.sendOpenCapacityParkUnsubscribeMessage(
                            iFaceConfig,
                            resourceId,
                            goodsId
                    );
                    // 发完mq退出流程
                    throw new FlowTerminationException(this.getClass(), workOrderDo, NextHintEnum.FINISH, "doCheckParam ONLY_SEND_MQ end");


                case NOTIFY_BOTH:
                    // 两个都处理
                    // 发mq给能开
                    outerRightsUnSubContextUtil.sendOpenCapacityParkUnsubscribeMessage(
                            iFaceConfig,
                            resourceId,
                            goodsId
                    );
                    break;

                case ONLY_NOTIFY_RIGHTSHOLDER:
                    break;

                case NOTHING:
                    // 什么都不做
                    throw new FlowTerminationException(this.getClass(), workOrderDo, NextHintEnum.FINISH, "doCheckParam数据库配置的外部通知类型不做处理");

                default:
                    throw new FlowTerminationException(this.getClass(), workOrderDo, "doCheckParam数据库配置的外部通知类型有误");
            }
        } else {
            throw new FlowTerminationException(this.getClass(), workOrderDo, "doCheckParam数据库配置的外部通知类型有误");
        }

    }

    @Override
    protected ParkUnSubReq getRequestBody(InterfaceContext<ParkUnSubReq, ParkUnSubRsp> interfaceContext) {
        ParkUnSubReq parkUnSubReq = new ParkUnSubReq();
        ComSendInterfaceReq comSendReq = interfaceContext.getComSendReq();
        String resourceId = comSendReq.getExtInfo(OtherFieldConstants.RESOURCE_ID);
        parkUnSubReq.setOrderNo(resourceId);
        parkUnSubReq.setRefundTime(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        parkUnSubReq.setChannelid(parkUnSubProperties.getChannelId());
        parkUnSubReq.setTimestamp(String.valueOf(Instant.now().toEpochMilli()));
        // 获取签名
        TreeMap<String, Object> objTreeMap = new TreeMap<>(JSONUtil.parseObj(parkUnSubReq));
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String, Object> entry : objTreeMap.entrySet()) {
            signStr.append(entry.getKey()).append(SymbolConstant.EQUAL)
                    .append(entry.getValue()).append(SymbolConstant.AND);
        }
        signStr.append("key").append(SymbolConstant.EQUAL).append(parkUnSubProperties.getKey());
        String sign = DigestUtils.md5Hex(signStr.toString());
        parkUnSubReq.setSign(sign);
        return parkUnSubReq;
    }

    @Override
    protected Class<ParkUnSubReq> getReqClass() {
        return ParkUnSubReq.class;
    }

    @Override
    protected Class<ParkUnSubRsp> getRspClass() {
        return ParkUnSubRsp.class;
    }

    @Override
    protected boolean isSuccess(ParkUnSubRsp rsp) {
        return true;
    }
}
