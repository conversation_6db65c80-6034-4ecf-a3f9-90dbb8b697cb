-- mysql数据库执行
-- 修改索引为唯一索引
ALTER TABLE member_sopen.activation_work_order_attr DROP INDEX idx_workid_attrcode;
CREATE UNIQUE INDEX uidx_workid_attrcode USING BTREE ON member_sopen.activation_work_order_attr (`user_id`,`work_id`, `attr_code`)
-- 上面修改索引的回退方案
--ALTER TABLE member_sopen.activation_work_order_attr DROP INDEX uidx_workid_attrcode;
-- CREATE INDEX idx_workid_attrcode USING BTREE ON member_sopen.activation_work_order_attr (`work_id`, `attr_code`)

-- oceanbase数据库执行
-- 修改索引为唯一索引
ALTER TABLE member_sopen.activation_work_order_attr DROP INDEX idx_workid_attrcode;
CREATE UNIQUE INDEX uidx_workid_attrcode USING BTREE ON member_sopen.activation_work_order_attr (`user_id`,`work_id`, `attr_code`) local;
-- 上面修改索引的回退方案
-- ALTER TABLE member_sopen.activation_work_order_attr DROP INDEX uidx_workid_attrcode;
-- CREATE  INDEX uidx_workid_attrcode USING BTREE ON member_sopen.activation_work_order_attr (`work_id`, `attr_code`) local;

ALTER TABLE member_sopen.task_t_message_notice DROP INDEX task_t_message_notice_notice_time_IDX;
CREATE INDEX task_t_message_notice_notice_way_IDX USING BTREE ON member_sopen.task_t_message_notice (`notice_way`,`status`,`notice_is_done`,`slice`, `notice_time`,`sum_fail`) local;
-- 回退
-- ALTER TABLE member_sopen.task_t_message_notice DROP INDEX task_t_message_notice_notice_way_IDX;
-- CREATE INDEX task_t_message_notice_notice_time_IDX USING BTREE ON member_sopen.task_t_message_notice (`notice_time`,`slice`,`prior_sort`) local;

CREATE INDEX task_t_inst_work_order_status_IDX USING BTREE ON member_sopen.task_t_inst_work_order (status,open_is_done,sum_fail,open_time,slice) local;
ALTER TABLE member_sopen.task_t_inst_work_order DROP INDEX task_t_inst_work_order_open_time_IDX;

CREATE INDEX task_t_inst_schedule_status_IDX USING BTREE ON member_sopen.task_t_inst_schedule (status,slice,next_trigger_time);
ALTER TABLE member_sopen.task_t_inst_schedule DROP INDEX task_t_inst_schedule_next_trigger_time_IDX;
