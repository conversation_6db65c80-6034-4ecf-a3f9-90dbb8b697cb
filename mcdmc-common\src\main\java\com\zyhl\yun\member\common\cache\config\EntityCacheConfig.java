package com.zyhl.yun.member.common.cache.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.zyhl.yun.member.common.cache.impl.CaffeineCacheImpl;
import com.zyhl.yun.member.common.cache.impl.RedisCacheImpl;
import com.zyhl.yun.member.common.cache.impl.TwoLevelCacheImpl;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/11/25 11:32
 */
@Configuration
@Getter
public class EntityCacheConfig {

    @Value("${entityCache.caffeine.ttl:300}")
    private Long caffeineTtl;


    @Bean
    public CaffeineCacheManager caffeineCacheManager() {
        CaffeineCacheManager caffeineCacheManager = new CaffeineCacheManager();
        Caffeine<Object, Object> objectObjectCaffeine = Caffeine.newBuilder()
                .maximumSize(10_1000)
                .expireAfterWrite(caffeineTtl, TimeUnit.SECONDS);
        caffeineCacheManager.setCaffeine(objectObjectCaffeine);

        return caffeineCacheManager;
    }


    @Bean
    @ConditionalOnBean(CaffeineCacheManager.class)
    public CaffeineCacheImpl caffeineCacheImpl(CaffeineCacheManager caffeineCacheManager) {
        return new CaffeineCacheImpl(caffeineCacheManager);
    }

    @Bean
    @ConditionalOnBean(RedisTemplate.class)
    public RedisCacheImpl redisCacheImpl(RedisTemplate redisTemplate) {
        return new RedisCacheImpl(redisTemplate);
    }


    @Bean
    @ConditionalOnBean(value = {CaffeineCacheImpl.class, RedisCacheImpl.class})
    public TwoLevelCacheImpl towLevelCacheImpl(CaffeineCacheImpl caffeineCacheImpl, RedisCacheImpl redisCacheImpl) {
        return new TwoLevelCacheImpl(caffeineCacheImpl, redisCacheImpl);
    }
}
