@startuml
'https://plantuml.com/state-diagram

scale 350 width
SUB_INIT:订购初始状态，order.state=5;\n goodInstance.state=1\n goodInstance.rightState is null
SUB_SC:订购成功，order.state=5;\n goodInstance.state=1\n  goodInstance.rightState=3\n goodInstance.resourceId=outer.seqId
SUB_FAIL_ING:订购失败（重试中），order.state=6;\n goodInstance.state=1\n goodInstance.rightState=2
SUB_FAIL:订购失败，order.state=10;\n goodInstance.state=1\n goodInstance.rightState=4
UNSUB_INIT: 退订初始状态，order.state=8;\n goodInstance.state=8
UNSUB_SC: 退订成功，order.state=8;\n goodInstance.state=8;\n goodInstance.rightState=13
UNSUB_FAIL_ING: 退订失败(重试中)，order.state=7;\n goodInstance.state=8;\n goodInstance.rightState=14
UNSUB_FAIL: 退订失败(重试结束)，order.state=8;\n goodInstance.state=8;\n goodInstance.rightState=14
REFUNN_INIT: 退费初始状态，order.state=8;\n goodInstance.state=10;
REFUND_SC: 退费成功，order.state=8;\n goodInstance.state=10; \n goodInstance.rightState=9
REFUND_FAIL_ING: 退费失败(重试中)，order.state=7;\n goodInstance.state=10; \n goodInstance.rightState=11
REFUND_FAIL: 退费失败，order.state=8;\n goodInstance.state=10; \n goodInstance.rightState=10
PAUSE_INIT: 暂停初始状态（不更新），order.state=5;\n goodInstance.state=9;
ACTIVE_INIT: 激活初始状态（不更新），order.state=5;\n goodInstance.state=1;

[*] --> SUB_INIT: 发起订购
SUB_INIT --> SUB_SC: 服开调用远程接口成功
SUB_INIT --> SUB_FAIL_ING: 服开调用远程接口失败（重试中）
SUB_INIT --> SUB_FAIL: 服开调用远程接口失败（重试结束）
[*] --> UNSUB_INIT: 发起退订
UNSUB_INIT --> UNSUB_SC: 第三方回调返回订购成功
UNSUB_INIT --> UNSUB_FAIL_ING: 第三方回调返回订购失败，重试中
UNSUB_INIT --> UNSUB_FAIL: 第三方回调返回订购失败，重试结束
[*] -->  REFUNN_INIT: 发起退费
REFUNN_INIT --> REFUND_SC: 第三方回调返回退费成功
REFUNN_INIT --> REFUND_FAIL_ING: 第三方回调返回退费失败（重试中）
REFUNN_INIT --> REFUND_FAIL: 第三方回调返回退费失败（重试结束）
[*] --> ACTIVE_INIT: 发起激活
[*]  --> PAUSE_INIT: 发起暂停
@enduml
