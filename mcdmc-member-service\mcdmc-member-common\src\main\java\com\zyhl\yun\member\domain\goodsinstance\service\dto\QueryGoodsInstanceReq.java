package com.zyhl.yun.member.domain.goodsinstance.service.dto;

import com.zyhl.yun.member.common.domain.framework.PageCondition;
import com.zyhl.yun.member.product.common.enums.ChargeTypeEnum;
import com.zyhl.yun.member.product.common.enums.GoodsSalesTypeEnum;
import com.zyhl.yun.member.product.common.enums.StateEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/11 20:14
 */
@Data
public class QueryGoodsInstanceReq {


    /**
     * 分页信息
     */
    private PageCondition pageCondition;

    /**
     * 商品实例id列表
     */
    private List<String> goodsInstanceIdList;

    /**
     * 排除商品id列表
     */
    private List<String> excludeGoodsIdList;


    /**
     * 商品id列表
     */
    private List<String> goodsIdList;



    /**
     * 订单id列表
     */
    private List<String> orderIdList;

    /**
     * 独立空间ownerId列表
     */
    private List<String> ownerIdList;


    /**
     * 权益发货类型列表
     */
    private List<String> rightsTypeList;

    /**
     * 计费类型
     */
    private ChargeTypeEnum chargeType;


    /**
     * 商品销售类型
     */
    private GoodsSalesTypeEnum goodsSalesType;

    /**
     * 商品销售类型列表
     */
    private List<GoodsSalesTypeEnum> goodsSalesTypeEnumList;

    /**
     * 状态列表
     */
    private List<StateEnum> stateList;

    /**
     * 只查当前生效
     */
    private boolean onlyCurrentlyEffective;

    /**
     * 是否查询暂停
     */
    private boolean queryPaused;

    /**
     * 是否查询已过期
     */
    private boolean queryExpired;

    /**
     * 过滤包含ownerId的商品
     */
    private boolean excludeGoodsWithOwnerId;

    /**
     * 用户Id
     */
    private String userId;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 用户域Id
     */
    private String userDomainId;


    /**
     * 订单号
     */
    private String orderId;


    /**
     * 时间分组Id
     */
    private String timePlanGoodsGroupId;

    /**
     * 是否按时间分组Id合并
     */
    private boolean mergeByTimePlanGoodsGroupId;


    /**
     * 是否查询子商品领取信息
     */
    private boolean queryChildGoodsActiveInfo;

    /** 查询父权益的id */
    private String goodsPackageInstanceId;

    /** 提交开始时间 */
    private String subStartTime;

    /** subTime排序标识,true标识subTime正序，false标识倒序 */
    private Boolean orderBySubTime;

    /**
     * 会员、空间类型是否会根据goodsId过滤
     */
    private boolean filterByGoodsIdsForMemberAndSpace;
}
