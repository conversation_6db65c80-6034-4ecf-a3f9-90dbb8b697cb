package com.zyhl.yun.member;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.application.order.enums.JiFeiPayWayEnum;
import com.zyhl.yun.member.application.order.req.FeatureDataDto;
import com.zyhl.yun.member.application.order.req.ThirdPaymentNotifyReq;
import com.zyhl.yun.member.application.order.service.impl.ThirdNotifyServiceImpl;

import java.lang.reflect.Method;

/**
 * Demonstrates the serialized JSON format from ThirdNotifyServiceImpl.buildReqParams method
 * This class has a simple main method that can be run without Spring context
 */
public class BuildReqParamsFormatDemo {

    public static void main(String[] args) {
        try {
            // Create an instance manually without Spring injection
            ThirdNotifyServiceImpl service = new ThirdNotifyServiceImpl();
            
            // Access the private buildReqParams method
            Method buildReqParamsMethod = ThirdNotifyServiceImpl.class.getDeclaredMethod(
                    "buildReqParams", ThirdPaymentNotifyReq.class);
            buildReqParamsMethod.setAccessible(true);
            
            // Test XIAO_HONG_SHU format
            System.out.println("==== Testing XIAO_HONG_SHU Format ====");
            testXiaohongshuFormat(service, buildReqParamsMethod);
            
            System.out.println("\n-------------------------------------\n");
            
            // Test VIDEO_CHANNEL format
            System.out.println("==== Testing VIDEO_CHANNEL Format ====");
            testVideoChannelFormat(service, buildReqParamsMethod);
            
        } catch (Exception e) {
            System.err.println("Exception occurred during test execution: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Test the buildReqParams method with Xiaohongshu payment way
     */
    private static void testXiaohongshuFormat(ThirdNotifyServiceImpl service, Method method) {
        try {
            // Create the request 
            ThirdPaymentNotifyReq req = new ThirdPaymentNotifyReq();
            req.setOrderId("XHS_ORDER_123456789");
            req.setPayWay(JiFeiPayWayEnum.XIAO_HONG_SHU.getPayWay());
            
            // Create and populate the feature data
            FeatureDataDto featureData = new FeatureDataDto();
            featureData.setOrderId("XHS_ORDER_123456789");
            featureData.setProductId("XHS_PRODUCT_001");
            
            // Set the feature data as JSON in the request
            req.setFeatureData(JSONUtil.toJsonStr(featureData));
            
            // Call the method and get the result
            String result = (String) method.invoke(service, req);
            
            // Print the results
            System.out.println("Raw JSON:");
            System.out.println(result);
            
            // Pretty print if possible
            if (result != null) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                System.out.println("\nPretty Printed JSON:");
                System.out.println(jsonObject.toStringPretty());
                
                System.out.println("\nJSON Structure:");
                System.out.println("- orderId: " + jsonObject.getStr("orderId"));
                System.out.println("- expressNo: " + jsonObject.getStr("expressNo"));
                System.out.println("- expressCompanyCode: " + jsonObject.getStr("expressCompanyCode"));
            }
        } catch (Exception e) {
            System.err.println("Error testing Xiaohongshu format: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Test the buildReqParams method with Video Channel payment way
     */
    private static void testVideoChannelFormat(ThirdNotifyServiceImpl service, Method method) {
        try {
            // Create the request 
            ThirdPaymentNotifyReq req = new ThirdPaymentNotifyReq();
            req.setOrderId("VIDEO_ORDER_123456789");
            req.setPayWay(JiFeiPayWayEnum.VIDEO_CHANNEL.getPayWay());
            
            // Create and populate the feature data
            FeatureDataDto featureData = new FeatureDataDto();
            featureData.setOrderId("VIDEO_ORDER_123456789");
            featureData.setProductId("VIDEO_PRODUCT_001");
            featureData.setSkuId("VIDEO_SKU_001");
            
            // Set the feature data as JSON in the request
            req.setFeatureData(JSONUtil.toJsonStr(featureData));
            
            // Call the method and get the result
            String result = (String) method.invoke(service, req);
            
            // Print the results
            System.out.println("Raw JSON:");
            System.out.println(result);
            
            // Pretty print if possible
            if (result != null) {
                JSONObject jsonObject = JSONUtil.parseObj(result);
                System.out.println("\nPretty Printed JSON:");
                System.out.println(jsonObject.toStringPretty());
                
                System.out.println("\nExamining Video Channel JSON Structure");
                System.out.println("- orderId: " + jsonObject.getStr("order_id"));
                
                // Navigation through the JSON structure for Video Channel
                if (jsonObject.containsKey("delivery_list")) {
                    System.out.println("- Contains delivery_list");
                    Object deliveryList = jsonObject.get("delivery_list");
                    System.out.println("  delivery_list structure: " + deliveryList.getClass().getSimpleName());
                }
            }
        } catch (Exception e) {
            System.err.println("Error testing Video Channel format: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 