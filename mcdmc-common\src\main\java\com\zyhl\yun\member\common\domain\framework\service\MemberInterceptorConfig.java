package com.zyhl.yun.member.common.domain.framework.service;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * 功能描述。
 * 包名称:  com.zyhl.yun.member.isbo.adapter.global
 * 类名称:  InterceptorConfig
 * 类描述:  。
 * 创建人: xiaojianbiao
 * 创建时间: 2023/6/3 15:41
 * 版本：    V1.0.0
 */
@Configuration
public class MemberInterceptorConfig implements WebMvcConfigurer {

    @Resource
    private MdcLogInterceptor mdcLogInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //mdc
        registry.addInterceptor(mdcLogInterceptor).addPathPatterns("/**");
    }
}
