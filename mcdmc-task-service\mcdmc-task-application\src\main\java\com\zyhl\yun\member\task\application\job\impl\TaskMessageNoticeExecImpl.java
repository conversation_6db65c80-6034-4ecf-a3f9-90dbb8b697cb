package com.zyhl.yun.member.task.application.job.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.zyhl.yun.member.common.ServiceException;
import com.zyhl.yun.member.common.constants.ErrorCode;
import com.zyhl.yun.member.common.domain.framework.DomainServiceContext;
import com.zyhl.yun.member.common.enums.SmsSubType;
import com.zyhl.yun.member.common.properties.SMSConfProperties;
import com.zyhl.yun.member.common.util.StringUtil;
import com.zyhl.yun.member.domain.goodsinstance.dto.QueryGoodsInstanceCondition;
import com.zyhl.yun.member.domain.receive.domain.GoodsInstanceDo;
import com.zyhl.yun.member.domain.user.domain.QueryUserCondition;
import com.zyhl.yun.member.domain.user.domain.UserDo;
import com.zyhl.yun.member.mcdmc.activation.constants.BusinessConstant;
import com.zyhl.yun.member.order.domain.OrderDo;
import com.zyhl.yun.member.order.domain.dto.QueryOrderCondition;
import com.zyhl.yun.member.product.common.constants.Constant;
import com.zyhl.yun.member.product.common.enums.ChargeTypeEnum;
import com.zyhl.yun.member.product.common.enums.PayWayEnum;
import com.zyhl.yun.member.product.common.enums.SubTimePlanPolicyEnum;
import com.zyhl.yun.member.product.common.enums.TimePlanCycleTypeEnum;
import com.zyhl.yun.member.product.domain.goods.GoodsDo;
import com.zyhl.yun.member.product.domain.goods.policy.timeplan.GoodsTimePlanDo;
import com.zyhl.yun.member.product.domain.smstemplate.domain.SmsTemplateDo;
import com.zyhl.yun.member.product.domain.smstemplate.dto.SmsTemplateIdCondition;
import com.zyhl.yun.member.support.common.domain.SmsSendDo;
import com.zyhl.yun.member.task.common.properties.JobPageProperties;
import com.zyhl.yun.member.task.common.properties.NoticeTimeProperties;
import com.zyhl.yun.member.task.application.job.AbstractTask;
import com.zyhl.yun.member.task.common.constants.SmsKeyConstant;
import com.zyhl.yun.member.task.common.domain.MessageNoticeTaskDo;
import com.zyhl.yun.member.task.common.domain.SMSTemplate;
import com.zyhl.yun.member.task.infra.repository.TaskTMessageNoticeRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

import static com.zyhl.yun.member.common.ResultCodeEnum.GOODS_NOT_FOUND;
import static com.zyhl.yun.member.common.domain.serviceid.GoodsInstanceServiceId.QUERY_GOODS_INSTANCE;
import static com.zyhl.yun.member.common.domain.serviceid.GoodsServiceId.QUERY_GOODS_INFO;
import static com.zyhl.yun.member.common.domain.serviceid.OrderServiceId.QUERY_ORDER;
import static com.zyhl.yun.member.common.domain.serviceid.SmsTemplateServiceId.QUERY_SMS_TEMPLATE_BY_ID;
import static com.zyhl.yun.member.common.domain.serviceid.UserServiceId.QUERY_USER_INFO;
import static com.zyhl.yun.member.product.common.constants.Constant.*;

@Slf4j
@Service
public class TaskMessageNoticeExecImpl extends AbstractTask<MessageNoticeTaskDo> {
    public static final String SPACE = " ";

    private static final String CHINESE_DATE_TIME_FORMATTER_MIN = "yyyy年MM月dd日HH时mm分";

    @Resource
    public TaskTMessageNoticeRepository taskTMessageNoticeRepository;

    @Resource
    private SMSConfProperties smsConfProperties;

    @Resource
    private JobPageProperties jobPageProperties;

    @Resource
    private NoticeTimeProperties noticeTimeProperties;

    /**
     * 查询多个对象
     */
    public List<MessageNoticeTaskDo> getTaskMessageSMSNoticeList(int shardIndex, int shardTotal) {
        Long pageSize = jobPageProperties.getSmsNoticeTaskScanPageSize();
        List<MessageNoticeTaskDo> messageNoticeList = taskTMessageNoticeRepository.selectMessageSMSNoticeList(shardIndex, shardTotal, pageSize);
        if (CollUtil.isEmpty(messageNoticeList)) {
            return Collections.emptyList();
        }
        int[] updateResult = taskTMessageNoticeRepository.updateMessageNoticeList(messageNoticeList);

        return filterUpdateSuccessRecord(messageNoticeList, updateResult);
    }

    /**
     * 过滤更新成功的记录
     *
     * @param messageNoticeTaskDoList
     * @param updateResult
     * @return
     */
    private List<MessageNoticeTaskDo> filterUpdateSuccessRecord(List<MessageNoticeTaskDo> messageNoticeTaskDoList, int[] updateResult) {
        List<MessageNoticeTaskDo> filterResult = new ArrayList<>();
        for (int i = 0; i < updateResult.length; i++) {
            if (updateResult[i] > 0) {
                filterResult.add(messageNoticeTaskDoList.get(i));
            } else {
                log.error("更新失败,该记录被锁定,updateResult id:{}", updateResult[i]);
            }
        }
        return filterResult;
    }

    @Override
    protected boolean before(MessageNoticeTaskDo obj) {
        return true;
    }

    @Override
    protected boolean invoke(MessageNoticeTaskDo obj) {
        return submitToMessageSystem(obj);
    }

    @Override
    protected boolean after(MessageNoticeTaskDo obj, boolean result, boolean ifFailCanIncrNum) {
        return false;
    }

    public boolean submitToMessageSystem(MessageNoticeTaskDo messageNoticeTaskDo) {
        log.info("submitToMessageSystem messageNoticeTaskDo is:{}", messageNoticeTaskDo);
        // 支撑领域的上下文
        DomainServiceContext serviceContext = null;
        int submitResult = 0;

        UserDo userDo = this.checkBeforeSending(messageNoticeTaskDo);

        if (userDo == null) {
            log.error("submitToMessageSystem userDo is null");
            return false;
        }

        SmsSubType smsSubType = SmsSubType.fromCode(String.valueOf(messageNoticeTaskDo.getNoticeItem()));
        if (null == smsSubType) {
            log.error("submitToMessageSystem smsSubType is null");
            taskTMessageNoticeRepository.finishFailMessageNotice(messageNoticeTaskDo);
            return true;
        }
        // 短信模板占位值
        Map<String, String> argsMap = new HashMap<>();
        // 默认订购模板
        String templateId = null;
        GoodsDo goodsDo = null;
        boolean doNotDisturb = false;

        // 获取用户省份编码
        String provCode = getUserProvCode(userDo);

        switch (smsSubType) {
            case IN_SMS:
                // 查询产品信息
                // 请求本地产品领域
                goodsDo = getGoodsDo(messageNoticeTaskDo);
                // 获取是否开启免打扰，true为开启免打扰
                doNotDisturb = goodsDo.getSmsPolicy().isDoNotDisturb(provCode,SmsSubType.IN_SMS);
                if (checkAllowedTime(messageNoticeTaskDo, doNotDisturb)) {
                    return true;
                }
                // 端内订购类型模板id - 支持分省策略
                templateId = goodsDo.getSmsPolicy().selectInnerSubMessageTemplateId(provCode);
                // 根据订单号，查询订购记录
                getArgs(messageNoticeTaskDo, userDo, goodsDo, argsMap);
                break;
            case OUT_SMS:
                goodsDo = getGoodsDo(messageNoticeTaskDo);

                doNotDisturb = goodsDo.getSmsPolicy().isDoNotDisturb(provCode,SmsSubType.OUT_SMS);
                if (checkAllowedTime(messageNoticeTaskDo, doNotDisturb)) {
                    return true;
                }
                // 端外订购类型模板id - 支持分省策略
                templateId = goodsDo.getSmsPolicy().selectOuterSubMessageTemplateId(provCode);
                // 根据订单号，查询订购记录
                getArgs(messageNoticeTaskDo, userDo, goodsDo, argsMap);
                break;
            case EXPIRE_SMS:
                templateId = handlerTempIdAndArgs(messageNoticeTaskDo, userDo, argsMap);
                break;
            case UNSUB_SMS:
                goodsDo = getGoodsDo(messageNoticeTaskDo);
                doNotDisturb = goodsDo.getSmsPolicy().isDoNotDisturb(provCode,SmsSubType.UNSUB_SMS);
                if (checkAllowedTime(messageNoticeTaskDo, doNotDisturb)) {
                    return true;
                }
                // 退订短信模板id - 支持分省策略
                templateId = goodsDo.getSmsPolicy().selectUnSubMessageTemplateId(provCode);
                // 根据订单号，查询订购记录
                getArgs(messageNoticeTaskDo, userDo, goodsDo, argsMap);
                break;
            // 发送全球通降档短信
            case GLOBAL_DOWN_SMS:
                goodsDo = getGoodsDo(messageNoticeTaskDo);
                doNotDisturb = goodsDo.getSmsPolicy().isDoNotDisturb(provCode, SmsSubType.GLOBAL_DOWN_SMS);
                if (checkAllowedTime(messageNoticeTaskDo, doNotDisturb)) {
                    return true;
                }
                // 退订短信模板id - 支持分省策略
                templateId = goodsDo.getSmsPolicy().selectGotoneDegradationTemplateId(provCode);
                // 根据订单号，查询订购记录
                getArgs(messageNoticeTaskDo, userDo, goodsDo, argsMap);
                break;
            default:
                break;
        }

        // 获取不到模板id则将消息通知任务插入到失败记录表
        if (StringUtils.isEmpty(templateId)) {
            if (!messageNoticeTaskDo.isUpdateFlag()) {
                log.error("submitToMessageSystem templateId is null, smsSubType is :{}", smsSubType.getDesc());
                taskTMessageNoticeRepository.finishFailMessageNotice(messageNoticeTaskDo);
            }
            return true;
        }
        List<String> variables = getVariables(templateId, argsMap);
        serviceContext = new DomainServiceContext(SmsSendDo.class, "smsSend");
        SmsSendDo smsSendDo = serviceContext.newInstance(SmsSendDo.class);
        SMSTemplate template = JSONUtil.toBean((String) messageNoticeTaskDo.getContext(), SMSTemplate.class);
        log.info("context is:{},template is:{}", messageNoticeTaskDo.getContext(), template);
        smsSendDo.setReqBizId(IdUtil.getSnowflake().nextIdStr());
        // 只支持单个号码
        smsSendDo.setReceiver(Collections.singletonList(userDo.getMsisdn()));
        smsSendDo.setMessageTemplateId(templateId);
        smsSendDo.setVariableList(variables);
        smsSendDo.setNationCode(userDo.getNationCode());

        try {
            // 新增续费短信到期提醒开关
            if (Boolean.TRUE.equals(smsConfProperties.getExpireRemindSmsSwitch()) && SmsSubType.EXPIRE_SMS.equals(smsSubType)) {
                submitResult = ONE_INT;
            } else {
                submitResult = serviceContext.writeAndFlush();
            }
        }
        catch (Exception e) {
            log.error("支撑域领域调用失败。", e);
            submitResult = 0;
        }

        // 发送完记录迁移到历史表
        if (submitResult > 0) {
            taskTMessageNoticeRepository.finishSuccessMessageNotice(messageNoticeTaskDo);
        } else {
            taskTMessageNoticeRepository.finishFailMessageNotice(messageNoticeTaskDo);
        }
        return submitResult > 0;
    }

    /**
     * 校验是否允许在允许的时间段内发送短信，不允许则需要更新通知时间
     * @param messageNoticeTaskDo 消息通知类型
     * @param doNotDisturb 是否允许发送
     * @return 返回检查并更新结果
     */
    private boolean checkAllowedTime(MessageNoticeTaskDo messageNoticeTaskDo, boolean doNotDisturb) {
        if (doNotDisturb) {
            // 检查通知时间是否在允许的时间段内
            LocalDateTime noticeDateTime = LocalDateTime.ofInstant(messageNoticeTaskDo.getNoticeTime().toInstant(), ZoneId.systemDefault());
            if (!isWithinAllowedTimeWindow(noticeDateTime)) {
                // 调整下一次短信发送通知时间
                LocalDateTime nextAllowedTime = calculateNextAllowedTime(noticeDateTime);
                nextAllowedTime = addRandomOffset(nextAllowedTime);
                messageNoticeTaskDo.setUpdateFlag(true);
                messageNoticeTaskDo.setNoticeTime(Date.from(nextAllowedTime.atZone(ZoneId.systemDefault()).toInstant()));
                taskTMessageNoticeRepository.updateNoticeTime(messageNoticeTaskDo);
                return true;
            }
        }
        return false;
    }

    private static GoodsDo getGoodsDo(MessageNoticeTaskDo messageNoticeTaskDo) {
        // 查询产品信息
        // 请求本地产品领域
        DomainServiceContext goodContext = new DomainServiceContext(QUERY_GOODS_INFO);
        GoodsDo goodsDo = goodContext.read(messageNoticeTaskDo.getGoodsIds(), GoodsDo.class);
        if (null == goodsDo) {
            log.error("submitToMessageSystem goodsDo is null");
            throw new ServiceException(GOODS_NOT_FOUND);
        }
        return goodsDo;
    }

    /**
     * 处理短信模板id和参数
     *
     * @param messageNoticeTaskDo 消息通知任务
     * @param userDo              用户
     * @param argsMap             参数
     * @return 短信模板
     */
    private String handlerTempIdAndArgs(MessageNoticeTaskDo messageNoticeTaskDo, UserDo userDo, Map<String, String> argsMap) {
        String templateId = null;
        GoodsDo goodsDo = null;
        DomainServiceContext goodContext = new DomainServiceContext(QUERY_GOODS_INFO);
        QueryGoodsInstanceCondition queryGoodsInstanceCondition = new QueryGoodsInstanceCondition();
        queryGoodsInstanceCondition.setGoodsIdList(Collections.singletonList(messageNoticeTaskDo.getGoodsIds()));
        queryGoodsInstanceCondition.setUserId(userDo.getUserId());
        queryGoodsInstanceCondition.setEffectiveEndTimeStart(new Date());
        DomainServiceContext context = new DomainServiceContext(QUERY_GOODS_INSTANCE);
        List<GoodsInstanceDo> goodsInstanceDos = context.read(queryGoodsInstanceCondition, GoodsInstanceDo.class);
        if (CollUtil.isEmpty(goodsInstanceDos)) {
            log.warn("submit expire sms, goodsInstanceDos is null,the userId is {},the orderId is{}", userDo.getUserId(), messageNoticeTaskDo.getGoodsIds());
            log.error(ErrorCode.SUBCONTRACT_NOT_EXISTS + ":" + ErrorCode.SUBCONTRACT_NOT_EXISTS_MESSAGE);
            return null;
        }
        // 根据endtime进行排序降序，取出第一个
        GoodsInstanceDo goodsInstanceDo = goodsInstanceDos.stream().max(Comparator.comparing(GoodsInstanceDo::getEffectiveEndTime)).get();
        if (goodsInstanceDo.getEffectiveEndTime().before(new Date())) {
            log.warn("submit expire sms, goodsInstance is invalid");
            return null;
        }
        goodsDo = goodContext.read(goodsInstanceDo.getGoodsId(), GoodsDo.class);

        if (null == goodsDo) {
            log.error("submitToMessageSystem goodsDo is null, goodsId is {}", goodsInstanceDo.getGoodsId());
            return null;
        }

        // 判断是否有提前提醒
        if (Objects.nonNull(goodsDo.getGoodsExt()) && Objects.nonNull(goodsDo.getGoodsExt().getExpiredRemindBeforeDays())) {
            // 计算最开始的提醒的时间
            LocalDateTime forwardNoticeDateTime = LocalDateTime.ofInstant(goodsInstanceDo.getEffectiveEndTime().toInstant(), ZoneId.systemDefault())
                    .minusDays(goodsDo.getGoodsExt().getExpiredRemindBeforeDays());
            LocalDateTime noticeTime = LocalDateTime.now();
            // 当前时间早于提醒时间则需要更新通知时间
            if (noticeTime.isBefore(forwardNoticeDateTime)) {
                messageNoticeTaskDo.setUpdateFlag(true);
                messageNoticeTaskDo.setNoticeTime(Date.from(forwardNoticeDateTime.atZone(ZoneId.systemDefault()).toInstant()));
                taskTMessageNoticeRepository.updateNoticeTime(messageNoticeTaskDo);
                return null;
            }
        }

        // 判断时间类型是否为顺延
        if (null != goodsDo.getTimePlan()
                && SubTimePlanPolicyEnum.PUT_OFF.equals(goodsDo.getTimePlan().getSubTimePlanPolicy())) {
            // 获取同一个权益组的商品实例最后过期提醒时间
            QueryGoodsInstanceCondition queryGoodsInstanceCondition1 = new QueryGoodsInstanceCondition();
            queryGoodsInstanceCondition1.setTimePlanGoodsGroupId(goodsDo.getTimePlanGoodsGroupId());
            queryGoodsInstanceCondition1.setUserId(userDo.getUserId());
            queryGoodsInstanceCondition1.setEffectiveEndTimeStart(new Date());
            DomainServiceContext context1 = new DomainServiceContext(QUERY_GOODS_INSTANCE);
            List<GoodsInstanceDo> result = context1.read(queryGoodsInstanceCondition1, GoodsInstanceDo.class);

            // 获取权益组中根据endtime进行排序降序，取出第一个
            goodsInstanceDo = result.stream().max(Comparator.comparing(GoodsInstanceDo::getEffectiveEndTime)).get();
        }

        // 计算最后的过期提醒的时间
        LocalDateTime forwardNoticeDateTime = LocalDateTime.ofInstant(goodsInstanceDo.getEffectiveEndTime().toInstant(), ZoneId.systemDefault())
                .minusDays(goodsDo.getGoodsExt().getExpiredRemindBeforeDays());
        LocalDateTime noticeTime = LocalDateTime.now();
        // 当前时间早于提醒时间则需要更新通知时间，移入短信历史表
        if (noticeTime.isBefore(forwardNoticeDateTime)) {
            taskTMessageNoticeRepository.finishFailMessageNotice(messageNoticeTaskDo);
            return null;
        }
        String userProvCode = getUserProvCode(userDo);
        // 获取是否开启免打扰开关
        boolean doNotDisturb = goodsDo.getSmsPolicy().isDoNotDisturb(userProvCode,SmsSubType.EXPIRE_SMS);
        if (checkAllowedTime(messageNoticeTaskDo, doNotDisturb)) {
            return null;
        }

        templateId = goodsDo.getSmsPolicy().getExpiredRemindTemplateId();
        // 短信模板占位值
        argsMap.put("goodsName", goodsDo.getGoodsName());
        argsMap.put("endTime", DateUtil.format(goodsInstanceDo.getEffectiveEndTime(), DatePattern.NORM_DATETIME_PATTERN));
        return templateId;
    }

    /**
     * 通过数据库配置的KeyOrder，将
     *
     * @param templateId 短信模板id
     * @param argsMap    短信参数Map
     * @return 返回短信模板配置的占位符归属的参数列表
     */
    private static List<String> getVariables(String templateId, Map<String, String> argsMap) {
        // 根据短信模板查询对应的占位符
        SmsTemplateIdCondition smsTemplateIdCondition = new SmsTemplateIdCondition();
        smsTemplateIdCondition.setSmsTemplateId(templateId);
        DomainServiceContext smsContext = new DomainServiceContext(QUERY_SMS_TEMPLATE_BY_ID);
        SmsTemplateDo smsTemplateIdDo = smsContext.readFirst(smsTemplateIdCondition, SmsTemplateDo.class);
        List<String> variables = new ArrayList<>();
        if (smsTemplateIdDo != null) {
            String keyOrder = smsTemplateIdDo.getKeyOrder();
            if (StringUtils.isNotEmpty(keyOrder)) {
                String[] split = keyOrder.split(",");
                for (String s : split) {
                    String variable = argsMap.get(s);
                    if (null != variable) {
                        variables.add(variable);
                    }
                }
            }
        }
        return variables;
    }

    /**
     * 获取发送短信
     * @param messageNoticeTaskDo 消息通知任务
     * @param userDo  用户
     * @param goodsDo 商品
     * @param argsMap 短信参数封装map
     */
    private void getArgs(MessageNoticeTaskDo messageNoticeTaskDo, UserDo userDo, GoodsDo goodsDo, Map<String, String> argsMap) {
        QueryGoodsInstanceCondition queryGoodsInstanceCondition = new QueryGoodsInstanceCondition();
        queryGoodsInstanceCondition.setOrderId(messageNoticeTaskDo.getSourceId());
        queryGoodsInstanceCondition.setUserId(userDo.getUserId());
        DomainServiceContext context = new DomainServiceContext(QUERY_GOODS_INSTANCE);
        GoodsInstanceDo goodsInstanceDo = context.readFirst(queryGoodsInstanceCondition, GoodsInstanceDo.class);
        // 没有商品实例则移入短信发送历史表
        if (null == goodsInstanceDo) {
            log.error(ErrorCode.SUBCONTRACT_NOT_EXISTS + ":" + ErrorCode.SUBCONTRACT_NOT_EXISTS_MESSAGE);
            log.error("submitToMessageSystem goodsInstanceDo is null,orderId:{}", messageNoticeTaskDo.getSourceId());
            taskTMessageNoticeRepository.finishFailMessageNotice(messageNoticeTaskDo);
            throw new ServiceException(ErrorCode.SUBCONTRACT_NOT_EXISTS, ErrorCode.SUBCONTRACT_NOT_EXISTS_MESSAGE);
        }

        GoodsTimePlanDo timePlan = goodsDo.getTimePlan();
        String cycleCount = null;
        // V7.3.0 cycleCount先使用扩展字段的值，若没有值，则按原有方法获取
        if (goodsDo.getGoodsExt() != null && StringUtils.isNotEmpty(goodsDo.getGoodsExt().getExpireTips())) {
            cycleCount = goodsDo.getGoodsExt().getExpireTips();
        }
        if (StringUtils.isEmpty(cycleCount) && timePlan != null) {
            cycleCount = queryCycleTypeByUnit(timePlan.getCycleType(), timePlan.getCycleCount(), goodsDo);
        }
        argsMap.put(SmsKeyConstant.GOOD_NAME, goodsDo.getGoodsName());
        argsMap.put(SmsKeyConstant.CYCLE_COUNT, cycleCount);
        String msisdn = userDo.getMsisdn();
        argsMap.put(SmsKeyConstant.ACCOUNT_NAME, msisdn);
        argsMap.put(SmsKeyConstant.SUB_TIME, DateUtil.format(goodsInstanceDo.getSubTime(), BusinessConstant.CHINESE_DATE_PATTERN));
        String totalAmountStr = getTotalAmountStr(goodsInstanceDo);
        argsMap.put(SmsKeyConstant.TOTAL_AMOUNT, totalAmountStr);
        // 因为短信平台的原因，当数据没有值的时候，也需要添加一个空格，避免当前的占位符为空导致短信平台报错
        if (null != goodsDo.getGoodsExt()) {
            argsMap.put(SmsKeyConstant.BUSINESS_DETAIL,
                    StringUtil.isNotEmpty(goodsDo.getGoodsExt().getBusinessDetail()) ? goodsDo.getGoodsExt().getBusinessDetail() : SPACE);

            if (PayWayEnum.PHONE_BILLS.getCode().equals(goodsInstanceDo.getPayWay())) {
                // 添加解决方案编码到订购短信参数中
                argsMap.put(SmsKeyConstant.SOLUTION_CODE,
                        StringUtil.isNotEmpty(goodsDo.getGoodsExt().getSolutionCode()) ? goodsDo.getGoodsExt().getSolutionCode() : SPACE);
            } else {
                argsMap.put(SmsKeyConstant.SOLUTION_CODE, SPACE);
            }
        }
        argsMap.put(SmsKeyConstant.UNSUB_TIME, DateUtil.format(goodsInstanceDo.getUnsubTime(), BusinessConstant.CHINESE_DATE_PATTERN));

        log.info("submitToMessageSystem argsMap:{}", argsMap);
    }

    /**
     * 获取用户省份编码
     *
     * @param userDo 用户信息
     * @return 省份编码
     */
    private String getUserProvCode(UserDo userDo) {
        try {
            if (userDo != null) {
                // 从用户信息中获取省份编码
                return userDo.getProvCode();
            }
            return null;
        } catch (Exception e) {
            log.warn("Failed to get user prov code", e);
            return null;
        }
    }

    /**
     * 获取支付方式对应的金额短信相关字符串
     *
     * @param subGoodsInstance 子订购关系
     */
    protected static String getTotalAmountStr(GoodsInstanceDo subGoodsInstance) {

        QueryOrderCondition queryOrderCondition = new QueryOrderCondition();
        queryOrderCondition.setUserId(subGoodsInstance.getUserId());
        queryOrderCondition.setOrderNo(subGoodsInstance.getOrderId());
        DomainServiceContext context = new DomainServiceContext(QUERY_ORDER);
        OrderDo orderDo = context.readFirst(queryOrderCondition, OrderDo.class);

        String totalAmountStr;
        if (BOUNDS_POINT_PAY_TYPE.contains(String.valueOf(subGoodsInstance.getPayWay()))) {
            // 积分支付,计算对应的积分
            totalAmountStr = subGoodsInstance.getDealPrice() + "积分";
        } else {
            // 非积分支付
            DecimalFormat df = new DecimalFormat("0.00");
            String totalAmount = df.format((double) orderDo.getTotalAmount() / 100);
            if (orderDo.getExtInfo().get(Constant.ACTIVITY_PRICE) != null) {
                String activityPrice = df.format(Double.parseDouble((String) orderDo.getExtInfo().get(ACTIVITY_PRICE)) / 100);
                if (ChargeTypeEnum.MONTHLY.equals(ChargeTypeEnum.fromType(subGoodsInstance.getChargeType()))) {
                    String activityRenewPrice = df.format(Double.parseDouble((String) orderDo.getExtInfo().get(ACTIVITY_RENEW_PRICE)) / 100);
                    totalAmount = String.format("首月%s元，次月%s", activityPrice, activityRenewPrice);
                } else {
                    totalAmount = activityPrice;
                }
            }
            totalAmountStr = totalAmount + "元";
        }
        return totalAmountStr;
    }

    /**
     * 查询周期类型
     *
     * @param cycleTypeEnum 周期类型
     * @param cycleCount    周期数量
     * @param goodsDo
     * @return 周期类型
     */
    public String queryCycleTypeByUnit(TimePlanCycleTypeEnum cycleTypeEnum, Integer cycleCount, GoodsDo goodsDo) {
        if (Objects.isNull(cycleTypeEnum) || Objects.isNull(cycleCount)) {
            return null;
        }
        if (ChargeTypeEnum.MONTHLY.equals(goodsDo.getChargeType())) {
            return "连续包月";
        }
        String smsContent = null;
        int cycleType = cycleTypeEnum.getType();
        if (TimePlanCycleTypeEnum.RELATIVE_MONTH.typeEquals(cycleType)
                || TimePlanCycleTypeEnum.FIXED_MONTH.typeEquals(cycleType)) {
            smsContent = "个月";
        } else if (TimePlanCycleTypeEnum.FIXED_DAY.typeEquals(cycleType)
                || TimePlanCycleTypeEnum.RELATIVE_DAY.typeEquals(cycleType)) {
            smsContent = "天";
        }
        // 20250521 加上cycle = 6的短信发送文案
        else if (TimePlanCycleTypeEnum.FIX_TIME.typeEquals(cycleType)) {
            smsContent = "有效期至";
            return smsContent + cycleCount;
        }
        return cycleCount + smsContent;
    }

    /**
     * 发送前校验，校验接收方用户信息
     *
     * @param messageNoticeTaskDo 消息定时任务数据
     * @return 返回接收方数据
     */
    public UserDo checkBeforeSending(MessageNoticeTaskDo messageNoticeTaskDo) {
        try {
            // 校验接受人用户信息
            DomainServiceContext userContext = new DomainServiceContext(UserDo.class, QUERY_USER_INFO);
            QueryUserCondition queryUserCondition = new QueryUserCondition();
            queryUserCondition.setAccount(messageNoticeTaskDo.getReceiver());
            UserDo userDo = userContext.readFirst(queryUserCondition, UserDo.class);
            if (userDo == null) {
                log.error("用户信息查询为空，消息不发送，手机号码={}", messageNoticeTaskDo.getReceiver());
                // 消息不发送，迁移到历史表
                taskTMessageNoticeRepository.deleteMessageNotice(messageNoticeTaskDo);
                return null;
            }
            return userDo;
        } catch (Exception e) {
            log.error("member域领域调用失败。", e);
            // 消息不发送，迁移到历史表
            taskTMessageNoticeRepository.deleteMessageNotice(messageNoticeTaskDo);
            return null;
        }
    }

    /**
     * 判断指定时间是否处于允许的通知时间窗口内。
     *
     * @param dateTime 待检查的日期时间对象
     * @return 若时间处于允许的时间窗口范围内则返回true，否则返回false
     * <p>
     * 时间窗口由以下属性定义：
     * - 开始小时/分钟：windowStartHour/windowStartMinute
     * - 结束小时/分钟：windowEndHour/windowEndMinute
     * 支持处理跨天时间窗口（如23:00-01:00）的边界情况
     */
    private boolean isWithinAllowedTimeWindow(LocalDateTime dateTime) {
        int startHour = noticeTimeProperties.getWindowStartHour();
        int startMinute = noticeTimeProperties.getWindowStartMinute();
        int endHour = noticeTimeProperties.getWindowEndHour();
        int endMinute = noticeTimeProperties.getWindowEndMinute();

        LocalTime startTime = LocalTime.of(startHour, startMinute);
        LocalTime endTime = LocalTime.of(endHour, endMinute);
        LocalTime currentTime = dateTime.toLocalTime();

        /*
         * 处理跨天时间窗口的特殊情况
         * 当开始时间晚于结束时间时（如23:00-01:00）：
         * 返回当前时间在开始时间之后 或 在结束时间之前的判断结果
         *
         * 普通情况（如09:00-18:00）：
         * 返回当前时间在开始时间和结束时间之间的判断结果
         */
        if (startTime.isAfter(endTime)) {
            return currentTime.isAfter(startTime) || currentTime.isBefore(endTime);
        } else {
            return currentTime.isAfter(startTime) && currentTime.isBefore(endTime);
        }
    }

    private LocalDateTime calculateNextAllowedTime(LocalDateTime currentTime) {
        LocalDateTime nextTime = currentTime;
        int startHour = noticeTimeProperties.getWindowStartHour();
        int startMinute = noticeTimeProperties.getWindowStartMinute();
        LocalTime startTime = LocalTime.of(startHour, startMinute);

        // 如果当前时间在当天的时间窗口之前，则设置为当天开始时间
        if (currentTime.toLocalTime().isBefore(startTime)) {
            nextTime = LocalDateTime.of(currentTime.toLocalDate(), startTime);
        } else {
            // 否则设置为第二天的开始时间
            nextTime = LocalDateTime.of(currentTime.toLocalDate().plusDays(1), startTime);
        }

        return nextTime;
    }

    private LocalDateTime addRandomOffset(LocalDateTime dateTime) {
        int maxSeconds = noticeTimeProperties.getRandomOffsetMaxSeconds();
        if (maxSeconds <= 0) {
            return dateTime;
        }
        Random random = new Random();
        int seconds = random.nextInt(maxSeconds);
        return dateTime.plusSeconds(seconds);
    }

}


