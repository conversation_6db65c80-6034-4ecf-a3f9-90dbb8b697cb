package com.zyhl.yun.member.mcdmc.activation.domain.enums.rai;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 权益退费里return的状态
 * 放于退费报文的extField1字段中
 *
 * <AUTHOR>
 * @since 2024/3/6 17:27
 */
@Getter
@AllArgsConstructor
public enum ReturnStatusEnum {

    /**
     * 核算补退
     */
    RE_UNSUBSCRIBE("2", "核算补退"),

    /**
     *
     */
    RIGHT_REDUCTION("3", "核算减免");

    /**
     * 状态值
     */
    private final String returnStatus;
    /**
     * 状态描述
     */
    private final String returnMsg;
}
