@startuml
'https://plantuml.com/activity-diagram-beta

|白名单服务|
start
: 解析传入报文;
switch (入参)
case ( account )
    if (uri = "notifyPaymentResultOfThirdParty") then (yes)
      : account为json报文，解析json报文;
      : 获取并解密telNumber，得到实际account;
    else (no)
    endif
case ( userId )
  :查询新用户表得到account;
  :映射关系写入缓存;
case ( userDomainId )
  :查询用户域得到account;
  :映射关系写入缓存;
endSwitch
if (account在__白名单表__) then (yes)
    if (account是白名单) then (yes)
        switch (用户状态)
            case (已生效)
                #cyan: 返回白名单;
                stop
            case (静默中/未生效)
                : 发送mq消息，更新用户操作时间;
                #red: 返回非白名单;
                stop

        endSwitch
    else (  no：(为黑名单用户))
        #red:返回非白名单;
        stop;
    endif
(no  )elseif (account在__白名单号段__里) then (no)
    #red: 返回非白名单;
    stop
else (yes)
    : 生成白名单用户;
    if (currentTime<=effectTime(白名单生效时间)?) then (yes)
      : 添加至缓存为__待静默用户__;
      : 发送mq更新数据库及effectTime时间;
      #red: 返回非白名单;
      stop
    (no  )elseif (用户存在于__待静默用户__缓存?) then (yes)
      : 重新将该用户缓存过期时长设置为初始值;
      : 发送mq更新数据库及effectTime时间;
      #red: 返回非白名单;
      stop
    else (no)
      : 发送mq将用户添加到白名单表\n（存在则更新状态，不存在则直接插入生效用户）;
      #cyan: 返回白名单;
      stop
    endif
endif
@enduml
