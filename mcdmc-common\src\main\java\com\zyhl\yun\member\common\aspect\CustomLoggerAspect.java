package com.zyhl.yun.member.common.aspect;

import com.zyhl.yun.member.common.properties.DriverProperties;
import com.zyhl.yun.member.common.util.MDCUtils;
import com.zyhl.yun.member.common.util.RequestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 日志切面
 *
 * <AUTHOR>
 * @since 2024/05/06 09:29
 */
@Slf4j
@Aspect
@Component
public class CustomLoggerAspect {
    private static final List<String> ASYNC_DOMAIN_EVENT_URI = Arrays.asList("/v2/gsdomain/asyncDomainEvent", "/v1/gsdomain/asyncDomainEvent");

    @Resource
    private DriverProperties driverProperties;

    @Pointcut("(@annotation(org.springframework.web.bind.annotation.RestController) || @within(org.springframework.web.bind.annotation.RestController)) " +
            "&& (@annotation(org.springframework.web.bind.annotation.PostMapping) || @within(org.springframework.web.bind.annotation.PostMapping)) ")
    public void controllerLoggerPointcut() {
        // 切点不做逻辑
    }

    @Around(value = "controllerLoggerPointcut()")
    public Object aroundMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取请求方法
        Signature signature = joinPoint.getSignature();
        Method method = ((MethodSignature) signature).getMethod();
        // 获取请求入参
        Object[] args = joinPoint.getArgs();
        String argsStr = Arrays.toString(args);

        // 获取请求URI
        String uri = RequestUtil.getRequestUri();
        // 判断是否为领域调度事件接口
        if (StringUtils.isNotBlank(uri)) {
            for (String asyncDomainEventUri : ASYNC_DOMAIN_EVENT_URI) {
                if (uri.endsWith(asyncDomainEventUri)) {
                    return joinPoint.proceed(args);
                }
            }
        }
        // 日志打印
        Object result;
        long startTime = System.currentTimeMillis();
        try {
            // 执行正常流程
            result = joinPoint.proceed(args);
            if (Boolean.FALSE.equals(driverProperties.isEnableLogInterfaceDetail())) {
                return result;
            }
            if (result instanceof Mono<?>) {
                Map<String, String> contextMap = MDC.getCopyOfContextMap();
                // 处理异步结果
                Mono<?> monoResult = (Mono<?>) result;
                monoResult = monoResult.doOnSuccess(rsp -> {
                    MDCUtils.setContextMap(contextMap);
                    LogInfo infoLogInfo = LogInfo.getInfoLogInfo(method, startTime, argsStr, rsp);
                    this.logResult(infoLogInfo);
                }).doOnError(rsp -> {
                    MDCUtils.setContextMap(contextMap);
                    LogInfo errorLogInfo = LogInfo.getErrorLogInfo(method, startTime, argsStr, rsp);
                    this.logResult(errorLogInfo);
                });
                return monoResult;
            } else {
                // 打印日志
                LogInfo infoLogInfo = LogInfo.getInfoLogInfo(method, startTime, argsStr, result);
                this.logResult(infoLogInfo);
            }
            return result;
        } catch (Exception e) {
            LogInfo errorLogInfo = LogInfo.getErrorLogInfo(method, startTime, argsStr, e);
            logResult(errorLogInfo);
            throw e;
        }
    }

    private void logResult(LogInfo logInfo) {
        String logStr = logInfo.toLogString();
        if (logInfo.getLogType().equals(LogInfo.LogType.INFO)) {
            log.info(logStr);
        } else {
            if (logInfo.isLogException()) {
                log.error("{}, detail exception is: ", logStr, logInfo.getException());
            } else {
                log.error(logStr);
            }
        }
    }

}